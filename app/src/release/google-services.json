{"project_info": {"project_number": "748621279622", "firebase_url": "https://fptplay2.firebaseio.com", "project_id": "fptplay2", "storage_bucket": "fptplay2.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:748621279622:android:d586f930c8b2a6ea", "android_client_info": {"package_name": "com.fplay.activity"}}, "oauth_client": [{"client_id": "748621279622-bjv9vjgi15lug6lnoec3tk9t2gvml14i.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.fplay.activity", "certificate_hash": "2eda2c9aa11630ffa971a55f49a2e9a72a62973b"}}, {"client_id": "748621279622-g8bio0vp681dvboitn8otcqs676jl2c7.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.fplay.activity", "certificate_hash": "6f1b887820d6dc52fbd2d30118fb3999fc9ea1f3"}}, {"client_id": "748621279622-se3bt1ft5efg9kuvinmdp6ta3ekj9nke.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyASqeO-0uyqrjb9F1zSvIzw14WMYi5Krqk"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "748621279622-se3bt1ft5efg9kuvinmdp6ta3ekj9nke.apps.googleusercontent.com", "client_type": 3}, {"client_id": "748621279622-i8clpfgu53cj1p4lacd7rqkk49ptage3.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "ftel.rad.fptplay", "app_store_id": "646297996"}}]}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:748621279622:android:1d8a24f11938e9c6", "android_client_info": {"package_name": "net.androidboxfptplay"}}, "oauth_client": [{"client_id": "748621279622-tnn728r2o2ltgual0j7n80ii16v0b7pu.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "net.androidboxfptplay", "certificate_hash": "b4505854d1377d09a72f0ccd8fb75c3fad25c08a"}}, {"client_id": "748621279622-se3bt1ft5efg9kuvinmdp6ta3ekj9nke.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyASqeO-0uyqrjb9F1zSvIzw14WMYi5Krqk"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "748621279622-se3bt1ft5efg9kuvinmdp6ta3ekj9nke.apps.googleusercontent.com", "client_type": 3}, {"client_id": "748621279622-i8clpfgu53cj1p4lacd7rqkk49ptage3.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "ftel.rad.fptplay", "app_store_id": "646297996"}}]}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:748621279622:android:0654bee55f2fb973", "android_client_info": {"package_name": "net.fptplay.ottbox"}}, "oauth_client": [{"client_id": "748621279622-v5asn4s21hja34hc0c0jjqe7rkd2n6lt.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "net.fptplay.ottbox", "certificate_hash": "9dff668b73e010b478f7b7530f6c93417f43a612"}}, {"client_id": "748621279622-se3bt1ft5efg9kuvinmdp6ta3ekj9nke.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyASqeO-0uyqrjb9F1zSvIzw14WMYi5Krqk"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "748621279622-se3bt1ft5efg9kuvinmdp6ta3ekj9nke.apps.googleusercontent.com", "client_type": 3}, {"client_id": "748621279622-i8clpfgu53cj1p4lacd7rqkk49ptage3.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "ftel.rad.fptplay", "app_store_id": "646297996"}}]}, "ads_service": {"status": 2}}}], "configuration_version": "1"}