package com.fptplay.mobile.features.floating_button

import androidx.annotation.Keep
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import java.io.Serializable

@Keep
@TypeConverters(value = [Coordinator::class])
data class FloatingButton(
    @SerializedName("icon")
    val icon: String = "",
    @SerializedName("page_key")
    val pageKey: String = "",
    @SerializedName("position")
    val position: String = "",
    @SerializedName("status")
    val status: String = "0",
    @SerializedName("url")
    val url: String = "",
    @SerializedName("coordinates")
    val coordinates: Coordinator = Coordinator()
) : Serializable

@Keep
data class Coordinator(
    @SerializedName("x_per")
    var xPercent: String = "",
    @SerializedName("y_per")
    var yPercent: String = ""
) : Serializable