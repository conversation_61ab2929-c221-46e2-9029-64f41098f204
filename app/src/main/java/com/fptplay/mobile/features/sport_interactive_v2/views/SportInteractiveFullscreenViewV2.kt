package com.fptplay.mobile.features.sport_interactive_v2.views

import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.Surface
import android.view.View
import androidx.annotation.UiThread
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.findFragment
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.databinding.SportInteractiveFullScreenContainerViewV2Binding
import com.fptplay.mobile.features.sport_interactive.SportInteractiveViewModel
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive_v2.SportInteractiveFirebaseProxy
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveFullScreenTabAdapterV2
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveOuterAdapter
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveUIData
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData
import com.fptplay.mobile.player.handler.PlayerHandler
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannelDetail
import com.xhbadxx.projects.module.domain.entity.fplay.premier.Details
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import timber.log.Timber

class SportInteractiveFullscreenViewV2@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
): ConstraintLayout(context, attrs, defStyleAttr), DefaultLifecycleObserver {

    private val binding = SportInteractiveFullScreenContainerViewV2Binding.inflate(LayoutInflater.from(context), this)

    private var lifecycleOwner: LifecycleOwner?= null
    private var viewModel: SportInteractiveViewModel? = null
    private var fragmentManager: FragmentManager? = null
    private val sportInteractiveFirebaseServices by lazy { SportInteractiveFirebaseProxy() }

    private val tabAdapter by lazy { SportInteractiveFullScreenTabAdapterV2() }
    private val innerAdapterItemClickEvent by lazy {
        object : IEventListener<UIData> {
            override fun onClickView(position: Int, view: View?, data: UIData) {
                if (data is SportInteractiveLiveScoreMatch) {
                    if (data.eventId.isNotBlank()) {
                        when (data.eventType) {
                            SportMatchLiveScores.LiveScore.Match.MatchEventType.Event -> {
                                logChangeLiveShow(data, tabAdapter.getCurrentSelectedItem())
                                navigateToContent(id = data.eventId, type = ItemType.Event)
                            }

                            SportMatchLiveScores.LiveScore.Match.MatchEventType.EventTv -> {
                                logChangeLiveShow(data, tabAdapter.getCurrentSelectedItem())
                                navigateToContent(id = data.eventId, type = ItemType.EventTV)

                            }

                            is SportMatchLiveScores.LiveScore.Match.MatchEventType.Unknown -> {
                                Timber.tag("tam-sport").i("onClickedItem: Unknown type. Do Nothing")
                            }
                        }
                    }
                }
            }
        }
    }
    private val mainAdapter by lazy { SportInteractiveOuterAdapter(ScreenContainerType.FULL_SCREEN).apply {
        setInnerEvenListener(innerAdapterItemClickEvent)
    } }
    // data
    private var screenType: PlayerHandler.ScreenType? = null
    private var contentId: String? = null
    private var detailLiveTv: TvChannelDetail? = null
    private var detailPremiere: Details? = null
    private var eventListeners: EventsListener? = null

    private var isFireStoreFirstInit = true

    // region DefaultLifecycleObserver

    override fun onCreate(owner: LifecycleOwner) {
        bindComponent()
        bindEvents()
    }

    override fun onStart(owner: LifecycleOwner) {}

    override fun onResume(owner: LifecycleOwner) {}

    override fun onPause(owner: LifecycleOwner) {}

    override fun onStop(owner: LifecycleOwner) {}

    override fun onDestroy(owner: LifecycleOwner) {
        sportInteractiveFirebaseServices.stopListenEvent()
    }

    // endregion DefaultLifecycleObserver


    private fun bindComponent() {
        binding.rvSportTab.apply {
            adapter = tabAdapter
            val spacingInPixels = resources.getDimensionPixelSize(R.dimen.sport_interactive_full_screen_tab_layout_item_spacing)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                    when (parent.getChildAdapterPosition(view)) {
                        0 -> { outRect.top = 0 }
                        else -> {
                            outRect.top = spacingInPixels
                        }
                    }
                }
            })
        }

        binding.rvMain.apply {
            adapter = mainAdapter
            val snapHelper = PagerSnapHelper()
            onFlingListener = null
            snapHelper.attachToRecyclerView(this)
        }
    }

    private fun bindEvents() {
        tabAdapter.eventListener = object : IEventListener<SportInteractiveMenuData> {
            override fun onClickedItem(position: Int, data: SportInteractiveMenuData) {
                binding.rvMain.scrollToPosition(position)
            }

            override fun onSelectedItem(position: Int, data: SportInteractiveMenuData) {
                try {
                    logAccessModule(data)
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
        }

        binding.root.onClickDelay {
            stop()
        }

        binding.ibClose.onClickDelay {
            stop()
        }

        binding.rvMain.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                val currentPosition = (binding.rvMain.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    tabAdapter.updateSelectedPosition(currentPosition)
                }
            }
        })
    }


    fun init(fragmentManager: FragmentManager?, lifeCycleOwner: LifecycleOwner?, viewModel: SportInteractiveViewModel?, screenType: PlayerHandler.ScreenType?, eventsListener: EventsListener?) {
        this.fragmentManager = fragmentManager
        this.lifecycleOwner = lifeCycleOwner
        this.viewModel = viewModel
        this.screenType = screenType
        this.eventListeners = eventsListener

    }

    fun removeListener() {
        this.eventListeners = null
    }

    private fun logAccessModule(currentMenu: SportInteractiveMenuData?) {
        Timber.d("*****log access function with parent: ${getParentFragment()}")
        val bundle: Bundle = bundleOf().apply {
            putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, currentMenu?.logName ?: "")
            putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "AccessFunction")
        }
        getParentFragment()?.setFragmentResult(
            Constants.SPORT_INTERACTIVE_LOG_KIBANA,
            bundle
        )
    }


    private fun logChangeLiveShow(
        data: SportInteractiveLiveScoreMatch? = null,
        currentMenu: SportInteractiveMenuData?
    ) {
        data?.let { matchData ->
            val bundle: Bundle = bundleOf().apply {
                putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, currentMenu?.logName ?: "")
                putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "ChangeLiveShow")
                putString(Constants.SPORT_INTERACTIVE_LOG_ITEM_NAME, matchData.eventId)
            }
            getParentFragment()?.setFragmentResult(
                Constants.SPORT_INTERACTIVE_LOG_KIBANA,
                bundle
            )
        }
    }

    private fun navigateToContent(id: String, type: ItemType) {
        val bundle = bundleOf(
            Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_CONTENT_ID to id
        )
        bundle.putString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_ITEM_TYPE, type.name)
        getParentFragment()?.setFragmentResult(
            Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_KEY,
            bundle
        )
    }

    private fun getParentFragment(): Fragment? {
        return try {
            <EMAIL><BaseFragment<*, *>>().parentFragment
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

    @UiThread
    fun bindViewPremiere(contentId: String?, detailPremiere: Details?) {
        this.contentId = contentId
        this.detailPremiere = detailPremiere
//        binding.tvTitle.checkToShowContent(detailPremiere?.title, goneViewWhenNoText = true)
    }


    fun start() {
        resetState()
        when(screenType) {
            PlayerHandler.ScreenType.Premiere -> {
                startPremiere()
            }
//            PlayerHandler.ScreenType.Live,
//            PlayerHandler.ScreenType.Vod,
            else -> {
                stop()
            }
        }
    }


    fun stop() {
        sportInteractiveFirebaseServices.stopListenEvent()
        eventListeners?.onSportInteractiveStop()
        hide()
    }


    private fun resetState() {
        tabAdapter.bind(listOf())
        mainAdapter.bind(listOf())
    }

    private fun startPremiere() {
        val currentContentId = contentId
        if(currentContentId.isNullOrEmpty()) {
            stop()
        } else {
            isFireStoreFirstInit = true
            bindFirestore(eventId = currentContentId)
            eventListeners?.onSportInteractiveStart()
        }
    }

    // region Data

    private fun bindFirestore(eventId: String) {
        lifecycleOwner?.let {
            sportInteractiveFirebaseServices.listenSportEventData(lifecycleOwner = it, eventId = eventId) {sportInteractiveData ->
                updateData(sportInteractiveData)
            }
        }
    }

    private fun updateData(sportInteractiveData: SportInteractiveUIData?) {
        if (isFireStoreFirstInit) {
            logAccessModule(sportInteractiveData?.titlesData?.firstOrNull())
            //
            sportInteractiveData?.titlesData?.firstOrNull()?.selected = true
            tabAdapter.bind(sportInteractiveData?.titlesData)
            //
            show()
            //
            isFireStoreFirstInit = false
        }

        mainAdapter.bind(sportInteractiveData?.toList()) {
            val curPosition = getCurrentContentDisplayPosition()
            if (curPosition != RecyclerView.NO_POSITION) {
                binding.rvMain.scrollToPosition(tabAdapter.getCurrentSelectedPosition())
            }
        }
    }

    private fun getCurrentContentDisplayPosition(): Int {
        return (binding.rvMain.layoutManager as? LinearLayoutManager)?.findFirstVisibleItemPosition() ?: RecyclerView.NO_POSITION
    }
    // endregion Data

    // region Handle Display Cutouts
    fun updatePaddingDisplayCutouts(padding: List<Int>, rotation: Int) {
        when (rotation) {
            Surface.ROTATION_0, // Bottom - reset the padding in portrait
            Surface.ROTATION_180, -> { // Top - reset the padding if upside down
                binding.apply {
                    guidelineControlSafeStart.setGuidelineBegin(0)
                    guidelineControlSafeEnd.setGuidelineEnd(0)
                }
            }
            Surface.ROTATION_90, // Left
            Surface.ROTATION_270, -> { // Right
                binding.apply {
                    guidelineControlSafeStart.setGuidelineBegin(padding[0])
                    guidelineControlSafeEnd.setGuidelineEnd(padding[1])
                }
            }
            else -> { }
        }
    }
    // endregion

    interface EventsListener {
        fun onSportInteractiveStart()
        fun onSportInteractiveStop()
        fun onBackPressed()
    }

    companion object {
        const val FRAGMENT_MANAGER_TAG = "SportInteractiveFullScreenFragment"
    }
}