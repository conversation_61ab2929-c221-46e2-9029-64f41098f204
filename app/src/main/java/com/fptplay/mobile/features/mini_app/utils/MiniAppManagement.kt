package com.fptplay.mobile.features.mini_app.utils

import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.provider.ContactsContract
import android.util.Base64
import android.view.KeyEvent
import androidx.core.net.toUri
import com.fptplay.mobile.features.mini_app.model.*
import com.fptplay.mobile.features.mini_app.utils.MiniAppUtils.toJsonString
import com.fptplay.mobile.features.mini_app.viewmodel.MiniAppViewModel
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import java.lang.reflect.Method

object MiniAppManagement {

    // region getSupportedMethods
    fun getDefaultSupportedMethods() = MiniAppGatewaySDK.listDefaultMethods
    fun getSupportedMethods(listFunctionMiniApp: List<String>): List<String> {
        val listFunctionNative = arrayListOf<String>()
        try {
            val thisClass: Class<*> = MiniAppGatewaySDK::class.java
            val methods: Array<Method> = thisClass.declaredMethods
            for (i in methods.indices) {
                listFunctionNative.add(methods[i].name)
            }

        } catch (e: Throwable) {
            Timber.e("getSupportedMethods getGatewayMethods error $e")
        }
        val listFunctionProvide = ArrayList<String>()
        for (function in listFunctionMiniApp) {
            if (listFunctionNative.contains(function)) {
                listFunctionProvide.add(function)
            }
        }
        return listFunctionProvide
    }

    fun getSupportedMethodsResponse(requestId: String, listJsonFunction: List<String>): String {
        return miniAppResponse(
            valueType = 2,
            valueCode = 1,
            requestId = requestId,
            jsonResult = listJsonFunction,
            jsonError = null
        )
    }
    // endregion getSupportedMethods

    // region getContactsList
    fun getContactList(context: Context?): List<MiniAppContact> {
        val contactList = arrayListOf<MiniAppContact>()
        val cr: ContentResolver? = context?.contentResolver
        val projectionContact = arrayOf(
            ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
            ContactsContract.CommonDataKinds.Phone.NUMBER,
            ContactsContract.Profile.PHOTO_THUMBNAIL_URI
        )
        val cursor: Cursor? = cr?.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            projectionContact,
            null,
            null,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
        )
        if (cursor != null) {
            val mobileNoSet = HashSet<String>()
            try {
                val nameIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
                val numberIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                val photoUriIndex = cursor.getColumnIndex(ContactsContract.Profile.PHOTO_THUMBNAIL_URI)
                var name: String
                var number: String
                var photoUri: String?
                var photo: String?
                var photoByteArray: ByteArray?

                while (cursor.moveToNext()) {
                    name = cursor.getString(nameIndex)
                    number = cursor.getString(numberIndex)
                    number = number.replace(" ", "")
                    photoUri = cursor.getString(photoUriIndex)
                    photo = null
                    photoByteArray = null
                    if(photoUri != null) {
                        //query contact photo thumbnail
                        val cursorPhoto = cr.query(
                            photoUri.toUri(),
                            arrayOf(ContactsContract.Contacts.Photo.PHOTO),
                            null,
                            null,
                            null
                        )
                        try {
                            if (cursorPhoto?.moveToFirst() == true) {
                                photoByteArray = cursorPhoto.getBlob(0)
                                photo = Base64.encodeToString(photoByteArray, Base64.NO_WRAP)
                            }
                        }catch(e: Exception) {
                            Timber.e(e, "error query contact photo")

                        }
                        finally {
                            cursorPhoto?.close()
                        }
                    }
                    if (!mobileNoSet.contains(number)) {
                        contactList.add(MiniAppContact(name, number, photo))
                        mobileNoSet.add(number)
                    }
                }
            } catch (e: Exception){
                Timber.e(e, "error query contact")
            } finally {
                cursor.close()
            }
        }
        return contactList
    }

    fun contactListResponse(requestId: String, listMiniAppContact: List<MiniAppContact>): String {
        return miniAppResponse(
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = 1,
            requestId = requestId,
            jsonResult = listMiniAppContact,
            jsonError = null
        )
    }


    // endregion getContactsList

    // region getKeyCode
    fun getListKeyCodes(): JSONObject {
        val obj = JSONObject()
        obj.put("backspace", KeyEvent.KEYCODE_BACK)
        return obj
    }

    fun keyCodeListResponse(requestId: String, keyCodeObj: JSONObject): String {
        return miniAppResponse(
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = 1,
            requestId = requestId,
            jsonResult = keyCodeObj,
            jsonError = null
        )
    }

    fun onKeyDownEventRequest(eventId: String, keyCode: Int): String {
        return miniAppEventRequest(
            valueType = MiniAppConstants.valueTypeEvent,
            requestId = eventId,
            args = listOf(keyCode)
        )
    }

    // endregion getKeyCode

    // region callPhoneNumber
    fun callPhoneNumberResponse(context: Context?, requestId: String, phoneNumber: String): String {
        return context?.let {
            try {
                val intent = Intent(Intent.ACTION_CALL)
                intent.data = Uri.parse("tel:$phoneNumber")
                it.startActivity(intent)
                miniAppResponse(
                    valueType = MiniAppConstants.valueTypeFunction,
                    valueCode = 1,
                    requestId = requestId,
                    jsonResult = "",
                    jsonError = null
                )
            } catch(e: Exception) {
                miniAppResponse(
                    valueType = MiniAppConstants.valueTypeFunction,
                    valueCode = 1,
                    requestId = requestId,
                    jsonResult = "",
                    jsonError = null
                )
            }
        } ?: miniAppError(
            requestId = requestId,
            code = MiniAppConstants.ERROR_CODE_DEFAULT,
            message = "Có lỗi xảy ra",
            detail = "Có lỗi xảy ra"
        )
    }
    // endregion callPhoneNumber

    fun deeplinkResponse(requestId: String, deeplink: String): String {
        return miniAppResponse(
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = 1,
            requestId = requestId,
            jsonResult = deeplink,
            jsonError = null
        )
    }
    fun userIdResponse(requestId: String, userId: String): String {
        return miniAppResponse(
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = 1,
            requestId = requestId,
            jsonResult = userId,
            jsonError = null
        )
    }
    fun getUserInfo(requestId: String, viewModel: MiniAppViewModel): String {
        var valueDataUserInfo: UserInfo? = null
        var valueCode = 0
        var valueMessage = ""
        var isSuccess = true
        runBlocking {
            val function: Deferred<Unit> = async {
                viewModel.getUserInfo().collect { state ->
                    when (state) {
                        is MiniAppViewModel.MiniAppViewState.Loading -> {}
                        is MiniAppViewModel.MiniAppViewState.Done -> {}
                        is MiniAppViewModel.MiniAppViewState.ResultUserInfo -> {
                            isSuccess = true
                            valueCode = 1
                            valueDataUserInfo = state.data
                        }
                        is MiniAppViewModel.MiniAppViewState.ErrorRequiredLogin -> {
                            isSuccess = false
                            valueCode = 0
                            valueMessage = state.message
                        }
                        is MiniAppViewModel.MiniAppViewState.Error -> {
                            isSuccess = false
                            valueCode = 0
                            valueMessage = state.message
                        }
                        is MiniAppViewModel.MiniAppViewState.ErrorNoInternet -> {
                            isSuccess = false
                            valueCode = 0
                            valueMessage = state.message
                        }
                        else -> {}
                    }
                }
            }
            function.await()
        }
        return if(isSuccess) {
            miniAppResponse(
                valueType = MiniAppConstants.valueTypeFunction,
                valueCode = valueCode,
                requestId = requestId,
                jsonResult = MiniAppUserInfoResponse(valueDataUserInfo),
                jsonError = null
            )
        } else {
            miniAppResponse(
                valueType = MiniAppConstants.valueTypeFunction,
                valueCode = valueCode,
                requestId = requestId,
                jsonResult = null,
                jsonError = MiniAppResponseError(
                    code = MiniAppConstants.ERROR_CODE_DEFAULT,
                    message = valueMessage,
                    detail = null
                )
            )
        }
//        return returnDataToHtml(
//            valueType = MiniAppConstants.valueTypeFunction,
//            valueCode = valueCode,
//            jsonResult = jsonDataUserInfo(valueDataUserInfo),
//            jsonError = jsonError(-1, valueMessage, ""))


//        return returnDataToHtml(
//            valueType = MiniAppConstants.valueTypeFunction,
//            valueCode = valueCode,
//            jsonResult = MiniAppUserInfoResponse(valueDataUserInfo),
//            jsonError = null
//        )
    }

    fun getVodDetail(requestId: String, viewModel: MiniAppViewModel, idVod: String): String {
        var valueDataVodDetail: Details? = null
        var valueCode = 0
        var valueMessage = ""
        var isSuccess = true
        runBlocking {
            val function: Deferred<Unit> = async {
                viewModel.getInfoVodDetail(idVod, "").collect { state ->
                    when (state) {
                        is MiniAppViewModel.MiniAppViewState.Loading -> {}
                        is MiniAppViewModel.MiniAppViewState.Done -> {}
                        is MiniAppViewModel.MiniAppViewState.ResultGetInfoVodDetail -> {
                            isSuccess = true
                            valueDataVodDetail = state.data
                            valueCode = 1
                        }
                        is MiniAppViewModel.MiniAppViewState.ErrorRequiredLogin -> {
                            isSuccess = false
                            valueCode = 0
                            valueMessage = state.message
                        }

                        is MiniAppViewModel.MiniAppViewState.Error -> {
                            isSuccess = false
                            valueCode = 0
                            valueMessage = state.message
                        }
                        is MiniAppViewModel.MiniAppViewState.ErrorNoInternet -> {
                            isSuccess = false
                            valueCode = 0
                            valueMessage = state.message
                        }
                        else -> {}
                    }
                }
            }
            function.await()
        }
        return if(isSuccess) {
            miniAppResponse(
                valueType = MiniAppConstants.valueTypeFunction,
                valueCode = valueCode,
                requestId = requestId,
                jsonResult = MiniAppVodDetailResponse(valueDataVodDetail),
                jsonError = null
            )
        } else {
            miniAppResponse(
                valueType = MiniAppConstants.valueTypeFunction,
                valueCode = valueCode,
                requestId = requestId,
                jsonResult = null,
                jsonError = MiniAppResponseError(
                    code = MiniAppConstants.ERROR_CODE_DEFAULT,
                    message = valueMessage,
                    detail = null
                )
            )
        }
    }

    fun miniAppError(requestId: String, code: Int = MiniAppConstants.ERROR_CODE_DEFAULT, message: String, detail: String): String  {
        val miniAppError =  MiniAppResponseError(
            code = code,
            message = message,
            detail = detail
        )

        return miniAppResponse(
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = 0,
            requestId = requestId,
            jsonResult = null,
            jsonError = miniAppError
        )
    }

    fun blockingHtmlThread(): String {
        return MiniAppBlockingResponse(block = true).toJsonString()
    }
    fun miniAppResponseDefault(requestId: String):String{
        return miniAppResponse(
            requestId = requestId,
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = 0,
            jsonResult = null,
            jsonError = null
        )
    }
    private fun miniAppResponse(valueType: Int, valueCode: Int, requestId: String, jsonResult: Any?, jsonError: MiniAppResponseError?): String {
        val miniAppResponse = MiniAppResponse(
            id = requestId,
            type = valueType,
            jsonResult = jsonResult,
            error = jsonError
        )
        Timber.tag("tam-miniapp").e("miniAppResponse: ${miniAppResponse.toJsonString()}")
//
        return miniAppResponse.toJsonString()
    }
    fun miniAppResponse(isSuccess: Boolean, valueType: Int, valueCode: Int, requestId:String, jsonResult: Any?, jsonError:MiniAppResponseError?):String{
        return if(isSuccess) {
            miniAppResponse(
                valueType = valueType,
                valueCode = valueCode,
                requestId = requestId,
                jsonResult = jsonResult,
                jsonError = null
            )
        } else {
            miniAppResponse(
                valueType = valueType,
                valueCode = valueCode,
                requestId = requestId,
                jsonResult = null,
                jsonError = jsonError
            )
        }
    }


    fun downloadResponse(isSuccess: Boolean, valueCode: Int, requestId:String, jsonResult: Boolean?, jsonError:MiniAppResponseError?):String{
        return miniAppResponse(
            isSuccess = isSuccess,
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = valueCode,
            requestId = requestId ?: "",
            jsonResult = jsonResult,
            jsonError = jsonError
        )
    }
    private fun miniAppEventRequest(valueType: Int, requestId: String, args: Any?): String {
        val miniAppEventRequest = MiniAppEventRequest(
            id = requestId,
            type = valueType,
            args = args,
        )
        Timber.tag("tam-miniapp").e("miniAppEventRequest: $miniAppEventRequest")
//
        return miniAppEventRequest.toJsonString()
    }


    // gen api token
    fun genTokenResponse(isSuccess: Boolean, valueCode: Int, requestId:String, jsonResult: String?, jsonError: MiniAppResponseError?):String{
        return miniAppResponse(
            isSuccess = isSuccess,
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = valueCode,
            requestId = requestId ?: "",
            jsonResult = jsonResult,
            jsonError = jsonError
        )
    }

    // game payment response

    fun getStoreProductResponse(isSuccess: Boolean, valueCode: Int, requestId:String, jsonResult: JSONArray?, jsonError: MiniAppResponseError?):String{
        Timber.tag("tam-miniapp").e("getStoreProductResponse: $jsonResult")
        return miniAppResponse(
            isSuccess = isSuccess,
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = valueCode,
            requestId = requestId ?: "",
            jsonResult = jsonResult,
            jsonError = jsonError
        )
    }

    fun purchaseItemResponse(isSuccess: Boolean, valueCode: Int, requestId:String, jsonResult: GamePurchaseItemResponse?, jsonError: MiniAppResponseError?):String{
        Timber.tag("tam-miniapp").e("getStoreProductResponse: $jsonResult")
        return miniAppResponse(
            isSuccess = isSuccess,
            valueType = MiniAppConstants.valueTypeFunction,
            valueCode = valueCode,
            requestId = requestId ?: "",
            jsonResult = jsonResult,
            jsonError = jsonError
        )
    }
}