package com.fptplay.mobile.features.pairing_control

import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.cast.SocketChannel
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.repository.fplay.CastRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LiveRepository
import com.xhbadxx.projects.module.domain.repository.fplay.VodRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PairingControlViewModel @Inject constructor(
    private val castRepository: CastRepository,
    private val liveRepository: LiveRepository,
    private val vodRepository: VodRepository
) : BaseViewModel<PairingControlViewModel.PairingControlIntent, PairingControlViewModel.PairingControlState>() {

    override fun dispatchIntent(intent: PairingControlIntent) {
        safeLaunch {
            when (intent) {
                is PairingControlIntent.GetChannelWebSocket -> {
                    castRepository.getWebSocketChannel(code = intent.code).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                          PairingControlState.ResultChannelWebSocket(isCached = isCached, data = data)
                        }
                    }
                }
                is PairingControlIntent.CheckTVRequirePackage -> {
                    liveRepository.getTvChannelStream(id = intent.id, bitrateId = intent.bitrateId, blockDataType = "").collect { resultStream ->
                        _state.value = resultStream.reduce(intent = intent) { isCached, data ->
                            if (checkKPlusRule(data)) {
                                PairingControlState.ResultCheckTVRequirePackage(isCached = isCached, intent = intent, data = data)
                            } else { // Case: K+
                                PairingControlState.ErrorRequiredVip(
                                    intent = intent,
                                    message = data.requireVipDescription,
                                    requiredVip = RequiredVip(
                                        requireVipTitle = data.requireVipTitle,
                                        requireVipDescription = data.requireVipDescription,
                                        btnSkip = data.btnSkip,
                                        btnActive = data.btnActive,
                                        requireVipPlan = data.vipPlan,
                                        message = data.message,
                                        requireVipImage = data.vipImage,
                                        requireVipName = data.name,
                                        trailerUrl = data.urlTrailer,

                                        )
                                )
                            }
                        }
                    }
                }
                is PairingControlIntent.CheckVODRequirePackage -> {
                    vodRepository.getStream(id = intent.id, episodeId = intent.episodeId, bitrateId = intent.bitrateId, dataType = "").collect { resultStream ->
                        _state.value = resultStream.reduce(intent = intent) { isCached, data ->
                            PairingControlState.ResultCheckVODRequirePackage(isCached = isCached, intent = intent, data = data)
                        }
                    }
                }
                is PairingControlIntent.CheckEventRequirePackage -> {
                    when (intent.type) {
                        "eventtv" -> {
                            liveRepository.getTvChannelStream(id = intent.id, bitrateId = intent.bitrateId, blockDataType = "").collect { resultStream ->
                                resultStream.reduce(intent = intent) { isCached, data ->
                                    PairingControlState.ResultCheckEventRequirePackage(isCached = isCached, intent = intent, data = data)
                                }
                            }
                        }
                        "event" -> {
                            if (intent.isPremiere == "1") {
                                vodRepository.getStream(id = intent.id, episodeId = intent.episodeId.ifEmpty { "0" }, bitrateId = intent.bitrateId, dataType = "").collect { resultStream ->
                                    _state.value = resultStream.reduce(intent = intent) { isCached, data ->
                                        PairingControlState.ResultCheckEventRequirePackage(isCached = isCached, intent = intent, data = data)
                                    }
                                }
                            } else {
                                liveRepository.getTvChannelStream(id = intent.id, bitrateId = intent.bitrateId, blockDataType = "").collect { resultStream ->
                                    resultStream.reduce(intent = intent) { isCached, data ->
                                        PairingControlState.ResultCheckEventRequirePackage(isCached = isCached, intent = intent, data = data)
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {}
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: PairingControlIntent?,
        successFun: (Boolean, T) -> PairingControlState
    ): PairingControlState {
        return when (this) {
            is Result.Init -> PairingControlState.Loading(data = intent)
            is Result.Success -> successFun(this.isCached, this.successData)
            is Result.UserError.RequiredLogin -> { PairingControlState.ErrorRequiredLogin(message = this.message, intent = intent, requiredLogin = this.requiredLogin) }
            is Result.UserError.RequiredVip -> { PairingControlState.ErrorRequiredVip(message = this.message, intent = intent, requiredVip = this.requiredVip) }
            is Result.ServerError.ItemNotFound -> { PairingControlState.ErrorItemNotFound(message = this.message, intent = intent) }
            is Result.Error -> PairingControlState.Error(message = this.message, data = intent)
            Result.Done -> PairingControlState.Done(data = intent)
        }
    }

    sealed class PairingControlState: ViewState {
        data class ResultChannelWebSocket(val isCached: Boolean, val data: SocketChannel) : PairingControlState()
        data class Loading(val data: PairingControlIntent? = null) : PairingControlState()
        data class Error(val message: String, val data: PairingControlIntent? = null) : PairingControlState()
        data class ErrorRequiredLogin(val message: String, val intent: PairingControlIntent?= null, val requiredLogin: RequiredLogin?) : PairingControlState()
        data class ErrorRequiredVip(val message: String, val intent: PairingControlIntent?= null, val requiredVip: RequiredVip?) : PairingControlState()
        data class ErrorItemNotFound(val message: String, val intent: PairingControlIntent?= null) : PairingControlState()
        data class Done(val data: PairingControlIntent? = null) : PairingControlState()
        data class ResultCheckTVRequirePackage(val isCached: Boolean, val intent: PairingControlIntent.CheckTVRequirePackage, val data: Stream) : PairingControlState()
        data class ResultCheckVODRequirePackage(val isCached: Boolean, val intent: PairingControlIntent.CheckVODRequirePackage, val data: Stream) : PairingControlState()
        data class ResultCheckEventRequirePackage(val isCached: Boolean, val intent: PairingControlIntent.CheckEventRequirePackage, val data: Stream) : PairingControlState()
        object Init : PairingControlState()
    }
    sealed class PairingControlIntent: ViewIntent {
        data class GetChannelWebSocket(val code: String) : PairingControlIntent()
        data class CheckTVRequirePackage(val id: String, val bitrateId: String) : PairingControlIntent()
        data class CheckVODRequirePackage(val id: String, val episodeId: String, val bitrateId: String) : PairingControlIntent()
        data class CheckEventRequirePackage(val id: String, val highlightId: String, val episodeId: String, val bitrateId: String, val type: String, val isPremiere: String) : PairingControlIntent()
    }


    //region Live TV
    private fun checkKPlusRule(stream: Stream) : Boolean {
        return when (stream.codeError) {
            4 -> false // KPlus Package've expired time
            5 -> false // Technical errors from KPlus
            6 -> false // Make privacy of KPlus
            else -> true
        }
    }
    //endregion

}