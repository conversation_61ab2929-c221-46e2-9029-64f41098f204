package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import coil.load
import com.fptplay.mobile.databinding.SportInteractiveLiveScoreLeagueBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreLeague
import com.tear.modules.util.Utils.checkToShowContent

class SportInteractiveLiveScoreLeagueViewHolder(private val binding: SportInteractiveLiveScoreLeagueBinding): BaseSportInteractiveViewHolder<SportInteractiveLiveScoreLeague>(binding) {
    override fun bind(data: SportInteractiveLiveScoreLeague) {
        binding.apply {
            ivLogo.load(data.leagueLogo)
            tvLeagueName.checkToShowContent(data.leagueName)
            tvLeagueDescription.checkToShowContent(data.leagueDescription)
        }
    }
}