package com.fptplay.mobile.features.package_validation

import android.util.TimeUtils
import android.view.View
import android.widget.Toast
import androidx.lifecycle.SavedStateHandle
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.global.SourcePlayObject
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Utils
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.History
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannel
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannelDetail
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.domain.entity.fplay.vod.VodBookmarkInfo
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LiveRepository
import com.xhbadxx.projects.module.domain.repository.fplay.PremierRepository
import com.xhbadxx.projects.module.domain.repository.fplay.VodRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.system.measureTimeMillis

/***
 * This class use check require package to play stream
 */
@HiltViewModel
class PackageValidationViewModel @Inject constructor(
    private val savedState: SavedStateHandle,
): BaseViewModel<PackageValidationViewModel.PackageValidationIntent, PackageValidationViewModel.PackageValidationState>() {

    override fun dispatchIntent(intent: PackageValidationIntent) {}

    override fun <T> Result<T>.reduce(
        intent: PackageValidationIntent?,
        successFun: (Boolean, T) -> PackageValidationState
    ): PackageValidationState {
        return PackageValidationState.Init
    }

    fun saveShowInternetView(isShow : Boolean){
        savedState.set("showInternetView",isShow)
    }
    val isShowInternetView get() = savedState.get("showInternetView") ?: false

    //region Intent, State, (Action)
    sealed class PackageValidationState : ViewState {
        object Init : PackageValidationState()
    }

    sealed class PackageValidationIntent : ViewIntent {
    }
    //endregion
}