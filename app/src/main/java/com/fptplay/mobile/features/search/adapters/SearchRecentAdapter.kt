package com.fptplay.mobile.features.search.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.databinding.SearchRecentItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.vod.StructureItem
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger

class SearchRecentAdapter : BaseAdapter<StructureItem, RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return RecentViewHolder(SearchRecentItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is RecentViewHolder)
            holder.bind(differ.currentList[position])
    }

    inner class RecentViewHolder(val binding: SearchRecentItemBinding) : BaseViewHolder(binding) {
        init {
            binding.root.setOnClickListener {
                if (absoluteAdapterPosition >= 0 && absoluteAdapterPosition < size()) {
                    eventListener?.onClickedItem(
                        position = absoluteAdapterPosition,
                        data = differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        fun bind(data: StructureItem) {
            ImageProxy.load(
                context = binding.root.context,
                url = data.horizontalImage,
                width = binding.root.context.resources.getDimensionPixelOffset(R.dimen.search_result_item_width),
                height = binding.root.context.resources.getDimensionPixelOffset(R.dimen.search_result_item_height),
                target = binding.ivThumb,
                placeHolderId = R.drawable.image_placeholder,
                errorDrawableId = R.drawable.image_placeholder,
            )

            // Talkback
            binding.ivThumb.contentDescription = data.titleVietnam
            //

            binding.posterOverlay.let {
                val isBindTL = binding.ivRibbonPayment.isVisible == false
                Logger.d("VodRelatedAdapter >> ${data.id} >> isBindTL: $isBindTL with ${data.posterOverlayGroup}")
                it.bindData(
                    itemData = data.posterOverlayGroup,
                    blockStyle = BlockStyle.HorizoltalSlider,
                    isBindTL = isBindTL,
                )
            }

//            if (data.ribbonPayment.isNotBlank()) {
//                binding.ivRibbonPayment.isVisible = true
//                ImageProxy.load(
//                    context = binding.root.context,
//                    url = data.ribbonPayment,
//                    width = 0,
//                    height = binding.root.context.resources.getDimensionPixelSize(R.dimen.block_ribbon_height),
//                    target = binding.ivRibbonPayment,
//                )
//            } else {
//                binding.ivRibbonPayment.isVisible = false
//            }
        }

    }
}