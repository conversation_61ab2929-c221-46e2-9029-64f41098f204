package com.fptplay.mobile.features.mqtt.database
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail

object MqttPlatformGroup {
    // Platform Group
    const val WEB = "web"
    const val MOBILE = "mobile"
    const val SMARTTV_ANDROID = "smarttv_android"
    const val SMARTTV_HTML = "smarttv_html"
    const val BOX = "box"
    // Platform
    const val ANDROID = "android"
    const val IOS = "ios"

    val userId get() = MainApplication.INSTANCE.sharedPreferences.userId()

    private fun getConfigPlatformGroup(): String {
        // get from config
        return MOBILE
        //return MainApplication.INSTANCE.appConfig.mqttPlatformGroup
    }
    private fun getConfigPlatform():String {
        return ANDROID
    }

    private fun getCurrentPlatformGroup(): PlatformType {
        return if (isValidPlatformGroup(getConfigPlatformGroup())) {
            PlatformType.fromString(getConfigPlatformGroup()) ?: PlatformType.MOBILE
        } else PlatformType.MOBILE
    }
    private fun getCurrentPlatform(): PlatformType {
        return if (isValidPlatform(getConfigPlatform())) {
            PlatformType.fromString(getConfigPlatform()) ?: PlatformType.ANDROID
        } else PlatformType.ANDROID
    }
    private fun getALlPlatform(): List<String> {
        return listOf(
            ANDROID,
            IOS,
        )
    }

    private fun getAllPlatformGroups(): List<String> {
        return listOf(
            WEB,
            MOBILE,
            SMARTTV_ANDROID,
            SMARTTV_HTML,
            BOX
        )
    }
    private fun isValidPlatform(platform: String): Boolean {
        return getALlPlatform().contains(platform)
    }

    private fun isValidPlatformGroup(platformGroup: String): Boolean {
        return getAllPlatformGroups().contains(platformGroup)
    }

    fun getFireStorePath(): String {
        return getConfigPlatform()
    }
    enum class PlatformType(val group: String) {
        WEB(MqttPlatformGroup.WEB),
        MOBILE(MqttPlatformGroup.MOBILE),
        SMART_TV_ANDROID(MqttPlatformGroup.SMARTTV_ANDROID),
        SMART_TV_HTML(MqttPlatformGroup.SMARTTV_HTML),
        BOX(MqttPlatformGroup.BOX),
        ANDROID(MqttPlatformGroup.ANDROID),
        IOS(MqttPlatformGroup.IOS);
        
        companion object {
            fun fromString(group: String): PlatformType? {
                return values().find { it.group == group }
            }
            fun getCurrentPlatform(): PlatformType {
                return MOBILE
            }
        }
    }
}
