package com.fptplay.mobile.features.mqtt.model

enum class MqttContentType(val value: Int) {
    VOD(0),
    LiveTV(1),
    Event(2),
    EventTV(3),
    Premiere(4),
    PreviewChannel(5),
    PreviewLive(6),
    Trailer(7),
    Pladio(8),
    PreviewVOD(9),
    TimeShift(10)
}
enum class MqttNotificationType(val value: String) {
    ALL("1"),
    ENABLE("2"),
    AUTOMATIC_RETRY("3"),
    OPTIONS("4"),
    ROOMS("5")
}

