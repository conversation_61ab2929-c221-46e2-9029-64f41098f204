package com.fptplay.mobile.features.sport_interactive_v2.adpters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.databinding.SportInteractiveHeaderWithCoachBinding
import com.fptplay.mobile.databinding.SportInteractiveHeaderWithScoredAuthorBinding
import com.fptplay.mobile.databinding.SportInteractiveHeaderWithoutScoredBinding
import com.fptplay.mobile.databinding.SportInteractiveLiveScoreLeagueBinding
import com.fptplay.mobile.databinding.SportInteractiveLiveScoreMatchBinding
import com.fptplay.mobile.databinding.SportInteractiveMatchProcessItemBinding
import com.fptplay.mobile.databinding.SportInteractiveMatchProcessTitleBinding
import com.fptplay.mobile.databinding.SportInteractiveNoDataViewBinding
import com.fptplay.mobile.databinding.SportInteractiveSeparatorViewBinding
import com.fptplay.mobile.databinding.SportInteractiveSquadMemberBinding
import com.fptplay.mobile.databinding.SportInteractiveSquadPageBinding
import com.fptplay.mobile.databinding.SportInteractiveSquadTitleBinding
import com.fptplay.mobile.databinding.SportInteractiveSquadTitleTeamBinding
import com.fptplay.mobile.databinding.SportMatchInfoItemBinding
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveNoData
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveHeaderData
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveSeparatorData
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreLeague
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.fptplay.mobile.features.sport_interactive_v2.models.match_process.SportInteractiveMatchProcessItem
import com.fptplay.mobile.features.sport_interactive_v2.models.match_process.SportInteractiveMatchProcessTitle
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadItem
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadPage
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTitle
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTitleTeam
import com.fptplay.mobile.features.sport_interactive_v2.models.statistic.SportInteractiveStatisticInfoData
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveHeaderWithCoachViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveHeaderWithScoredAuthorsViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveHeaderWithoutScoredViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveLiveScoreLeagueViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveLiveScoreMatchViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveNoDataViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveProcessItemViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveProcessTitleViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveSeparatorViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveSquadItemViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveSquadPageViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveSquadTitleTeamViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveSquadTitleViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.viewholders.SportInteractiveStatisticInfoItemViewHolder
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import timber.log.Timber

class SportInteractiveInnerAdapter(private val screenContainerType: ScreenContainerType)
    : BaseAdapter<UIData, RecyclerView.ViewHolder>() {
    companion object {
        //COMMON 1x
        private const val SPORT_INTERACTIVE_HEADER_WITH_SCORED_AUTHORS = 10
        private const val SPORT_INTERACTIVE_HEADER_WITH_COACH = 11
        private const val SPORT_INTERACTIVE_SEPARATOR = 12
        private const val SPORT_INTERACTIVE_NO_DATA = 13
        private const val SPORT_INTERACTIVE_HEADER_WITHOUT_SCORED = 14

        //STATIC TAB 2x
        private const val SPORT_INTERACTIVE_STATIC_INFO = 20

        //MATCH PROCESS 3x
        private const val SPORT_INTERACTIVE_PROCESS_TITLE = 30
        private const val SPORT_INTERACTIVE_PROCESS_ITEM = 31

        //SQUAD 4x
        private const val SPORT_INTERACTIVE_SQUAD_ITEM = 40
        private const val SPORT_INTERACTIVE_SQUAD_RESERVE_TITLE = 41
        private const val SPORT_INTERACTIVE_SQUAD_TITLE_TEAM = 42
        private const val SPORT_INTERACTIVE_SQUAD_PAGE = 43

        //LIVESCORE 5x
        private const val SPORT_INTERACTIVE_LIVE_SCORE_LEAGUE = 50
        private const val SPORT_INTERACTIVE_LIVE_SCORE_MATCH = 51
    }

    private var logTitle: String = ""
    fun setTitle(title: String) {
        logTitle = title
    }

    override fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return if (oldItem.javaClass.simpleName != newItem.javaClass.simpleName) false else {
            when (oldItem) {
                is SportInteractiveStatisticInfoData -> {
                    if(newItem is SportInteractiveStatisticInfoData) {
                        try {
                            val returndata =
                                (oldItem.actionName == newItem.actionName && oldItem.actionType == newItem.actionType)
                            Timber.d("*******are the same type: ${oldItem.javaClass.simpleName} - ${newItem.javaClass.simpleName})")
                            Timber.d("*******are the same for: ${oldItem.javaClass.simpleName} $returndata ($logTitle - ${oldItem.actionName})")
                            returndata
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                            false
                        }
                    } else {
                        false
                    }

                }
                else -> true
            }
        }
    }

    override fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        Timber.d("*******are content the same for ${oldItem.javaClass.simpleName} ${oldItem == newItem} ($logTitle)")
        return oldItem == newItem
    }

    override fun getChangePayload(oldItem: BaseObject, newItem: BaseObject): Any? {
        return when {
            newItem is SportInteractiveSquadPage -> newItem
            else -> null
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (val currentItem = data()[position]) {
            is SportInteractiveHeaderData -> when (currentItem.headerType) {
                SportInteractiveHeaderData.HeaderType.WITH_SCORE -> SPORT_INTERACTIVE_HEADER_WITH_SCORED_AUTHORS
                SportInteractiveHeaderData.HeaderType.WITH_COACH -> SPORT_INTERACTIVE_HEADER_WITH_COACH
                SportInteractiveHeaderData.HeaderType.WITHOUT_SCORE -> SPORT_INTERACTIVE_HEADER_WITHOUT_SCORED
                else -> { 1000 }
            }

            is SportInteractiveStatisticInfoData -> SPORT_INTERACTIVE_STATIC_INFO
            is SportInteractiveMatchProcessTitle -> SPORT_INTERACTIVE_PROCESS_TITLE
            is SportInteractiveNoData -> SPORT_INTERACTIVE_NO_DATA

            is SportInteractiveMatchProcessItem -> SPORT_INTERACTIVE_PROCESS_ITEM

            is SportInteractiveSquadPage -> SPORT_INTERACTIVE_SQUAD_PAGE
            is SportInteractiveSquadTitle -> SPORT_INTERACTIVE_SQUAD_RESERVE_TITLE
            is SportInteractiveSquadItem -> SPORT_INTERACTIVE_SQUAD_ITEM
            is SportInteractiveSquadTitleTeam -> SPORT_INTERACTIVE_SQUAD_TITLE_TEAM

            is SportInteractiveLiveScoreLeague -> SPORT_INTERACTIVE_LIVE_SCORE_LEAGUE
            is SportInteractiveLiveScoreMatch -> SPORT_INTERACTIVE_LIVE_SCORE_MATCH
            is SportInteractiveSeparatorData -> SPORT_INTERACTIVE_SEPARATOR
            else -> 1000
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup, viewType: Int
    ): RecyclerView.ViewHolder {
        return when (viewType) {
            SPORT_INTERACTIVE_HEADER_WITH_SCORED_AUTHORS -> SportInteractiveHeaderWithScoredAuthorsViewHolder(
                SportInteractiveHeaderWithScoredAuthorBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_NO_DATA -> SportInteractiveNoDataViewHolder(
                SportInteractiveNoDataViewBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_HEADER_WITH_COACH -> SportInteractiveHeaderWithCoachViewHolder(
                binding = SportInteractiveHeaderWithCoachBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_HEADER_WITHOUT_SCORED -> SportInteractiveHeaderWithoutScoredViewHolder(
                headerBinding = SportInteractiveHeaderWithoutScoredBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_STATIC_INFO -> SportInteractiveStatisticInfoItemViewHolder(
                SportMatchInfoItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_PROCESS_TITLE -> SportInteractiveProcessTitleViewHolder(
                SportInteractiveMatchProcessTitleBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_PROCESS_ITEM -> SportInteractiveProcessItemViewHolder(
                SportInteractiveMatchProcessItemBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_SQUAD_PAGE -> SportInteractiveSquadPageViewHolder(
                SportInteractiveSquadPageBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                ),
                screenContainerType
            )

            SPORT_INTERACTIVE_SQUAD_RESERVE_TITLE -> SportInteractiveSquadTitleViewHolder(
                SportInteractiveSquadTitleBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_SQUAD_ITEM -> SportInteractiveSquadItemViewHolder(
                SportInteractiveSquadMemberBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_SQUAD_TITLE_TEAM -> SportInteractiveSquadTitleTeamViewHolder(
                SportInteractiveSquadTitleTeamBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_LIVE_SCORE_LEAGUE -> SportInteractiveLiveScoreLeagueViewHolder(
                SportInteractiveLiveScoreLeagueBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            SPORT_INTERACTIVE_LIVE_SCORE_MATCH -> SportInteractiveLiveScoreMatchViewHolder(
                SportInteractiveLiveScoreMatchBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                ),
                eventListener,
                screenContainerType
            )

            SPORT_INTERACTIVE_SEPARATOR -> SportInteractiveSeparatorViewHolder(
                SportInteractiveSeparatorViewBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
            )

            else -> {
                SportInteractiveStatisticInfoItemViewHolder(
                    SportMatchInfoItemBinding.inflate(
                        LayoutInflater.from(parent.context), parent, false
                    )
                )
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        try {
            (holder as BaseSportInteractiveViewHolder<UIData>).bind(differ.currentList[position])
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: MutableList<Any>) {
        try {
            if (payloads.isNotEmpty()) {
                if (payloads[0] is SportInteractiveSquadPage) {
                    val newData = payloads[0] as? SportInteractiveSquadPage
                    if (holder is SportInteractiveSquadPageViewHolder) {
                        holder.bindPayloads(newData = newData)
                    }
                } else {
                    super.onBindViewHolder(holder, position, payloads)
                }
            } else {
                super.onBindViewHolder(holder, position, payloads)
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
            super.onBindViewHolder(holder, position, payloads)
        }
    }
}