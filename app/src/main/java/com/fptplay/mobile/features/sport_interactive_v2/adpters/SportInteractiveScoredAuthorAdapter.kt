package com.fptplay.mobile.features.sport_interactive_v2.adpters

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.databinding.SportInteractiveScoredAuthorItemBinding
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveHeaderData
import java.util.Locale

class SportInteractiveScoredAuthorAdapter :
    BaseAdapter<SportInteractiveHeaderData.SportInteractiveScoredAuthors, SportInteractiveScoredAuthorAdapter.SportInteractiveScoreAuthorViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): SportInteractiveScoreAuthorViewHolder {
        return SportInteractiveScoreAuthorViewHolder(
            SportInteractiveScoredAuthorItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: SportInteractiveScoreAuthorViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    inner class SportInteractiveScoreAuthorViewHolder(private val binding: SportInteractiveScoredAuthorItemBinding) :
        BaseViewHolder(binding) {
        fun bind(data: SportInteractiveHeaderData.SportInteractiveScoredAuthors) {
            if (data.teamType == TeamType.HomeTeam) {
                binding.tvData.gravity = Gravity.START
            } else {
                binding.tvData.gravity = Gravity.END
            }
            val dataString = String.format(Locale.US, "%s %s", data.playerName, data.time)
            val dataSpan = SpannableStringBuilder(dataString)
            dataSpan.setSpan(StyleSpan(Typeface.BOLD), 0, data.playerName.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.tvData.text = dataSpan
        }
    }
}