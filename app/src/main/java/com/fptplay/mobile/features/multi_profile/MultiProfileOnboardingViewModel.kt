package com.fptplay.mobile.features.multi_profile

import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.user.OnboardingData
import com.xhbadxx.projects.module.domain.entity.fplay.user.UpdateOnboarding
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class MultiProfileOnboardingViewModel @Inject constructor(
    private val userRepository: UserRepository
) : BaseViewModel<MultiProfileOnboardingViewModel.ProfileOnboardingIntent, MultiProfileOnboardingViewModel.ProfileOnboardingState>() {

    private var listProfileOnboarding: List<OnboardingData.Data.Component>? = null

    private var profileCode: String = ""

    private var profileId: String = ""

    fun saveProfileId(profileId:String){
        this.profileId = profileId
    }

    fun getProfileId():String = profileId


    fun saveListProfileOnboarding(data: List<OnboardingData.Data.Component>?) {
        listProfileOnboarding = data
    }

    fun getListProfileOnboarding(): List<OnboardingData.Data.Component>? {
        return listProfileOnboarding
    }

    fun saveCurProfileCode(code: String) {
        profileCode = code
    }

    fun getCurProfileCode(): String {
        return profileCode
    }

    fun clearProfileOnboarding(){
        listProfileOnboarding = null
        profileCode = ""
        profileId = ""
    }

    override fun dispatchIntent(intent: ProfileOnboardingIntent) {
        safeLaunch {
            when (intent) {
                is ProfileOnboardingIntent.GetProfileOnboarding -> {
                    userRepository.getOnboardingData().collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            ProfileOnboardingState.ResultGetProfileOnboarding(
                                isCached = isCached,
                                data = data.data,
                                intent = intent,
                                status = data.status,
                                message = data.msg
                            )
                        }
                    }
                }

                is ProfileOnboardingIntent.SubmittedProfileOnboarding -> {
                    userRepository.updateOnboardingData(data = intent.items).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            ProfileOnboardingState.ResultSubmittedProfileOnboarding(
                                isCached = isCached,
                                intent = intent,
                                status = data.status,
                                message = data.message
                            )
                        }
                    }
                }

            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: ProfileOnboardingIntent?,
        successFun: (Boolean, T) -> ProfileOnboardingState
    ): ProfileOnboardingState {
        return when (this) {
            is Result.Init -> ProfileOnboardingState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }

            is Result.UserError.RequiredLogin -> ProfileOnboardingState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )

            is Result.Error.Intenet -> ProfileOnboardingState.ErrorNoInternet(
                message = this.message,
                intent = intent
            )

            is Result.Error -> ProfileOnboardingState.Error(message = this.message, intent = intent)

            Result.Done -> ProfileOnboardingState.Done(intent = intent)
        }
    }

    sealed class ProfileOnboardingState : ViewState {

        data class Loading(val intent: ProfileOnboardingIntent? = null) : ProfileOnboardingState()

        data class ErrorRequiredLogin(val message: String, val intent: ProfileOnboardingIntent? = null) : ProfileOnboardingState()

        data class ErrorNoInternet(val intent: ProfileOnboardingIntent? = null, val message: String) : ProfileOnboardingState()

        data class Error(val message: String, val intent: ProfileOnboardingIntent? = null) : ProfileOnboardingState()

        data class Done(val intent: ProfileOnboardingIntent? = null) : ProfileOnboardingState()

        data class ResultGetProfileOnboarding(val status:String,val isCached: Boolean, val intent: ProfileOnboardingIntent? = null, val data  : OnboardingData.Data,val message: String ) : ProfileOnboardingState()

        data class ResultSubmittedProfileOnboarding(val status:String,val isCached: Boolean, val intent: ProfileOnboardingIntent? = null,val message: String,) : ProfileOnboardingState()

    }

    sealed class ProfileOnboardingIntent : ViewIntent {

        object GetProfileOnboarding : ProfileOnboardingIntent()

        data class SubmittedProfileOnboarding(val profileId:String,val items: List<UpdateOnboarding>) : ProfileOnboardingIntent()

    }
}