package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.SportInteractiveHeaderWithScoredAuthorBinding
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveScoredAuthorAdapter
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveHeaderData
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.util.image.ImageProxy
import timber.log.Timber

class SportInteractiveHeaderWithScoredAuthorsViewHolder(private val headerBinding: SportInteractiveHeaderWithScoredAuthorBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveHeaderData>(headerBinding) {
    private val homeScoredAuthorAdapter by lazy { SportInteractiveScoredAuthorAdapter() }
    private val awayScoredAuthorAdapter by lazy { SportInteractiveScoredAuthorAdapter() }

    init {
        headerBinding.rvHomeAuthor.apply {
            adapter = homeScoredAuthorAdapter
        }

        headerBinding.rvAwayAuthor.apply {
            adapter = awayScoredAuthorAdapter
        }
    }

    override fun bind(data: SportInteractiveHeaderData) {
        homeScoredAuthorAdapter.bind(data.listScores.filter { it.teamType == TeamType.HomeTeam })
        awayScoredAuthorAdapter.bind(data.listScores.filter { it.teamType == TeamType.AwayTeam })

        headerBinding.llHeaderGoalTeam.isVisible =
            (data.listScores.filter { it.teamType !is TeamType.Unknown }).isNotEmpty()


        headerBinding.txtNameTeamAway.visible()
        headerBinding.txtNameTeamAway.text = data.info.awayTeamName


        headerBinding.txtNameTeamHome.visible()
        headerBinding.txtNameTeamHome.text = data.info.homeTeamName

        headerBinding.txtMatchScore.text =
            data.info.homeScore.ifBlank { "0" } + " - " + data.info.awayScore.ifBlank { "0" }


        headerBinding.txtMatchTime.visible()
        headerBinding.txtMatchTime.text = data.info.time


        if (data.info.awayLogo.isNotBlank()) {
            ImageProxy.load(
                context = headerBinding.root.context,
                width = Utils.getSizeInPixel(
                    context = headerBinding.root.context,
                    resId = if (headerBinding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = 0,
                target = headerBinding.imgAway,
                url = data.info.awayLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )

        } else {
            ImageProxy.loadLocal(
                context = headerBinding.root.context,
                width = Utils.getSizeInPixel(
                    context = headerBinding.root.context,
                    resId = if (headerBinding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = headerBinding.root.context,
                    resId = if (headerBinding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = headerBinding.imgAway,
                data = R.drawable.ic_default_logo_team,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )
        }

        if (data.info.homeLogo.isNotBlank()) {
            Timber.d("***********bind logo: ${data.info.homeLogo}")
            ImageProxy.load(
                context = headerBinding.root.context,
                width = Utils.getSizeInPixel(
                    context = headerBinding.root.context,
                    resId = if (headerBinding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = 0,
                target = headerBinding.imgHome,
                url = data.info.homeLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )
        } else {
            ImageProxy.loadLocal(
                context = headerBinding.root.context,
                width = Utils.getSizeInPixel(
                    context = headerBinding.root.context,
                    resId = if (headerBinding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = headerBinding.root.context,
                    resId = if (headerBinding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = headerBinding.imgHome,
                data = R.drawable.ic_default_logo_team,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )
        }
    }
}