package com.fptplay.mobile.features.notification

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.databinding.NotificationFilterFragmentBinding
import com.fptplay.mobile.features.notification.adapters.NotificationFilterAdapter
import com.fptplay.mobile.features.notification.decorations.DividerItemDecorator
import com.xhbadxx.projects.module.util.common.IEventListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NotificationFilterFragment :
    BaseFullDialogFragment<NotificationViewModel.NotificationState, NotificationViewModel.NotificationIntent>() {

    override val viewModel: NotificationViewModel by activityViewModels()

    private var _binding: NotificationFilterFragmentBinding? = null
    private val binding get() = _binding!!

    private val filterAdapter: NotificationFilterAdapter by lazy { NotificationFilterAdapter() }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = NotificationFilterFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    override fun NotificationViewModel.NotificationState.toUI() {

    }

    override fun bindComponent() {
        binding.rcvFilter.apply {
            adapter = filterAdapter
            layoutManager =
                LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
            addItemDecoration(
                DividerItemDecorator(
                    ContextCompat.getDrawable(
                        binding.root.context,
                        R.drawable.divider
                    )
                )
            )
        }
    }

    override fun bindData() {
        filterAdapter.add(setData())
    }

    override fun bindEvent() {
        filterAdapter.eventListener = object : IEventListener<NotificationFilterItem> {
            override fun onClickedItem(position: Int, data: NotificationFilterItem) {
                setFragmentResult("bundle", bundleOf("value" to data.value))
                findNavController().popBackStack()
            }
        }

        binding.tvSkip.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun setData() : List<NotificationFilterItem>{
        val listTitle = arrayListOf<NotificationFilterItem>()
        listTitle.add(NotificationFilterItem(getString(R.string.all_notification), "0"))
        listTitle.add(NotificationFilterItem(getString(R.string.noti_unread), "1"))
        return listTitle
    }
}