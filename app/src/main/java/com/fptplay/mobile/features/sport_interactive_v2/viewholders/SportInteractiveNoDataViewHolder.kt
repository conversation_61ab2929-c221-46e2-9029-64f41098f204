package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.R
import com.fptplay.mobile.databinding.SportInteractiveNoDataViewBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveNoData

class SportInteractiveNoDataViewHolder(private val binding: SportInteractiveNoDataViewBinding): BaseSportInteractiveViewHolder<SportInteractiveNoData>(binding) {
    override fun bind(data: SportInteractiveNoData) {
        binding.tvDescription.text = data.description.ifBlank { binding.root.context.getString(R.string.interactive_not_data) }
    }
}