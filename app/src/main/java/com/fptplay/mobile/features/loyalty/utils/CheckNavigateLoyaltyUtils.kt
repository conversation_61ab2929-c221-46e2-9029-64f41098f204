package com.fptplay.mobile.features.loyalty.utils

import android.content.Context
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.LoyGetUserInfoEntity
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class CheckNavigateLoyaltyUtils(
    private val context: Context,
    private val sharedPreferences: SharedPreferences,
    private val packageLoyaltyValidationViewModel: PackageLoyaltyValidationViewModel,
    private val navHostFragment: NavHostFragment?,
    private val trackingInfo: Infor? = null
) : LifecycleEventObserver {
    companion object {
        const val STATUS_NON_LOYAL = "0"
        const val STATUS_HAVE_LOYAL = "1"
        const val STATUS_IS_PENDING = "2"
        const val STATUS_IS_BUSINESS = "3"
        const val STATUS_IS_ERROR_EKYS = "4"
    }

    private var alertDialog: AlertDialog? = null
    private var screenProvider: String = ""
    private var isChecking = false
    private var shouldProcessObserve = true
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> onCreate(source)
            Lifecycle.Event.ON_START -> onStart()
            Lifecycle.Event.ON_STOP -> onStop()
            Lifecycle.Event.ON_DESTROY -> onDestroy()
            else -> {}
        }
    }

    private fun onCreate(lifecycleOwner: LifecycleOwner) {
        observeData(lifecycleOwner)
    }

    private fun onStart() {

    }

    private fun onStop() {
    }

    private fun onDestroy() {

    }

    private fun navigateToLogin(
        navigationId: Int? = null
    ) {
        if (context.isTablet()) {
            navigationId?.let {
            } ?: run {
                navHostFragment?.navigateToLoginWithParams(isDirect = true)
            }
        } else {
            navigationId?.let {
                navHostFragment?.navigateToLoginWithParams(isDirect = true, navigationId = it)
            } ?: run {
                navHostFragment?.navigateToLoginWithParams(isDirect = true)
            }
        }
    }

    fun checkNavigationLoyalty() {
        navHostFragment?.findNavController()
            ?.navigate(NavHomeMainDirections.actionGlobalToLoyalty())
    }
    fun userInfoLoyalty() = UserInfoLoyaltyUtils.getUserInfoLoyalty()
    fun updateUserInfoLoyalty(loyaltyInfo: LoyGetUserInfoEntity.Data?) =
        UserInfoLoyaltyUtils.setUserInfoLoyalty(loyaltyInfo)
    fun refreshLoyaltyUserInfo() {
        /** HDSD :
         *  refresh user info
         * **/
        packageLoyaltyValidationViewModel.dispatchIntent(PackageLoyaltyValidationViewModel.PackageLoyaltyValidationIntent.RefreshGetUserInfor)
    }
    fun navigateToMega() {
        navHostFragment?.findNavController()
            ?.navigate(NavHomeMainDirections.actionGlobalToHome())
    }
    fun showWarningDialog(
        message: String,
        textClose: String? = null,
        onClose: (() -> Unit)? = null
    ) {
        navHostFragment?.run {
            alertDialog?.dismissAllowingStateLoss()
            alertDialog = AlertDialog().apply {
                setMessage(message)
                setShowTitle(true)
                setOnlyConfirmButton(true)
                textClose?.let { setTextConfirm(it) }
                setListener(object : AlertDialogListener {
                    override fun onConfirm() {
                        onClose?.invoke()
                    }
                })
                isCancelable = false
            }
            alertDialog?.show(navHostFragment.childFragmentManager, "AlertDialog")
        }
    }
    private fun observeData(lifecycleOwner: LifecycleOwner) {
        if (packageLoyaltyValidationViewModel.state.hasObservers()) packageLoyaltyValidationViewModel.resetState()
        packageLoyaltyValidationViewModel.state.observe(lifecycleOwner) {
            if (shouldProcessObserve) {
                when (it) {
                    is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.Loading -> {
                        isChecking = true
//                        navHostFragment?.activity?.showLoading(
//                            packageLoyaltyValidationViewModel.loadingViewId
//                        )
                    }
                    else -> {
                        isChecking = false
                        //navHostFragment?.activity?.hideLoading(packageLoyaltyValidationViewModel.loadingViewId)
                        when (it) {
                            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ErrorRequiredLogin -> {
                                navigateToLogin(navigationId = R.id.action_global_to_loyalty)
                            }
                            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.Error -> {

                            }
                            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ResultRefreshGetUserInfo -> {
                                if(it.data.code =="0"){
                                    if (it.data != null) {
                                        UserInfoLoyaltyUtils.setUserInfoLoyalty(it.data.data)
                                    }
                                }else if(it.data.code !="0"){
                                    showWarningDialog(
                                        message = it.data.errors, textClose = context.getString(
                                            R.string.understood
                                        )
                                    )
                                }
                            }
                            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ErrorNoInternet -> {
                            }
                            else->{}
                        }
                    }
                }
            }
        }
    }
}