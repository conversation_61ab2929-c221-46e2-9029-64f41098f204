package com.fptplay.mobile.features.sport_interactive_v2.adpters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.SportInteractiveFullScreenTabItemBinding
import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

class SportInteractiveFullScreenTabAdapterV2: BaseAdapter<SportInteractiveMenuData, SportInteractiveFullScreenTabAdapterV2.TabHolder>() {

    private var currentSelectedPos = 0

    override fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return (oldItem as SportInteractiveMenuData).title == (newItem as SportInteractiveMenuData).title
    }

    override fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return oldItem == newItem
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabHolder {
        return TabHolder(
            SportInteractiveFullScreenTabItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: TabHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    fun updateSelectedPosition(position: Int) {
        if (currentSelectedPos != position) {
            item(position)?.let { updatedItem ->
                notifyItemChanged(currentSelectedPos)
                currentSelectedPos = position
                notifyItemChanged(position)
                eventListener?.onSelectedItem(position, updatedItem)
            }

        }
    }

    fun getCurrentSelectedItem(): SportInteractiveMenuData? {
        return try {
            return item(currentSelectedPos)
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

    fun getCurrentSelectedPosition() = currentSelectedPos

    inner class TabHolder(private val binding: SportInteractiveFullScreenTabItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.onClickDelay {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(absoluteAdapterPosition, binding.root, it)
                    updateSelectedPosition(absoluteAdapterPosition)
                }
            }
        }

        fun bind(tabItem: SportInteractiveMenuData) {
            binding.ivIcon.load(tabItem.iconId)
            binding.root.isSelected = absoluteAdapterPosition == currentSelectedPos
            binding.ivIcon.isSelected = absoluteAdapterPosition == currentSelectedPos
        }
    }
}