package com.fptplay.mobile.features.moments.chat

import android.app.Dialog
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Patterns
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.Window
import android.view.inputmethod.EditorInfo
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.*
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.FragmentMomentChatBottomSheetBinding
import com.fptplay.mobile.features.comment.views.CommentView
import com.fptplay.mobile.features.moments.MomentsFragmentDirections
import com.fptplay.mobile.features.moments.MomentsViewModel
import com.fptplay.mobile.features.moments.ShortVideoLikeCommentCache
import com.fptplay.mobile.features.moments.adapter.MomentCommentParentAdapter
import com.fptplay.mobile.features.short_video.DataCacheObject
import com.fptplay.mobile.features.short_video.ShortVideosContentFragment
import com.fptplay.mobile.features.short_video.ShortVideosFragment
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentComment
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentCommentPermission
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentCommentSortType
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.LoadMoreHandler
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.concurrent.schedule

@AndroidEntryPoint
class MomentCommentBottomSheetFragment :
    BaseFullDialogFragment<MomentCommentViewModel.MomentChatState, MomentCommentViewModel.MomentChatIntent>() {
    override val hasEdgeToEdge = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override val viewModel: MomentCommentViewModel by activityViewModels()
    private var _binding: FragmentMomentChatBottomSheetBinding? = null
    private val binding get() = _binding!!
    private val totalItemInPage by lazy { 5 }
    private val mAdapter: MomentCommentParentAdapter by lazy { MomentCommentParentAdapter(userLogin = sharedPreferences.userLogin())}
    private var parentId = ""
    private var isAutoSendComment = false
    private var sortTypeCurrent = MomentCommentSortType.NEWEST.id
    private val safeArgs: MomentCommentBottomSheetFragmentArgs by navArgs()
    private var totalCommentParent = ""

    @Inject
    lateinit var trackingInfo: Infor
    private var enable = true
    private var userCom = true
    private var isFirstLoading = true

    private val orientationCurrent: Boolean
        get() =
            MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE

    private val loadMoreHandler: LoadMoreHandler by lazy {
        LoadMoreHandler(
            totalItem = mAdapter.size(),
            totalItemInPage = totalItemInPage,
            totalItemInRow = 1,
            onScroll = { page ->
//                viewModel.dispatchIntent(
//                    MomentCommentViewModel.MomentChatIntent.GetComments(
//                        "", safeArgs.momentId,
//                        page, totalItemInPage, sortTypeCurrent
//                    )
//                )
                viewModel.dispatchIntent(
                    MomentCommentViewModel.MomentChatIntent.GetCommentsV2(
                        "", safeArgs.momentId, safeArgs.chapterId,
                        page, totalItemInPage, sortTypeCurrent
                    )
                )
            })
    }
    private val maxCommentCharacterCount = MainApplication.INSTANCE.appConfig.limitCommentMax
    private val minCommentCharacterCount = MainApplication.INSTANCE.appConfig.limitCommentMin

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMomentChatBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CommentBottomSheetTheme)
    }

    override fun bindData() {
        if (safeArgs.autoComment.isNotBlank()) {
            isAutoSendComment = true
        }

//        viewModel.dispatchIntent(
//            MomentCommentViewModel.MomentChatIntent.GetPermissionComment(
//                "",
//                momentId = safeArgs.momentId,
//                1,
//                totalItemInPage,
//                sortTypeCurrent, userId = sharedPreferences.userId()
//            )
//        )
        viewModel.dispatchIntent(
            MomentCommentViewModel.MomentChatIntent.GetPermissionCommentV2(
                parentId = "",
                momentId = safeArgs.momentId,
                episodeId = safeArgs.chapterId,
                page = 1,
                perPage = totalItemInPage,
                sortType = sortTypeCurrent, userId = sharedPreferences.userId()
            )
        )
    }
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                if (context.isTablet()) {
                    changeLayoutConstraint(true)
                }
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                if (context.isTablet()) {
                    changeLayoutConstraint(false)
                }
            }
            else -> {}
        }
    }

    override fun onStart() {
        super.onStart()
        handleOutsideClick()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val bottomSheetDialog = object : Dialog(requireContext(), R.style.CommentBottomSheetTheme) {
            override fun onBackPressed() {
                handleBack()
            }
        }
        bottomSheetDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        bottomSheetDialog.setCanceledOnTouchOutside(true)
        bottomSheetDialog.setCancelable(true)
        return bottomSheetDialog
    }

    private fun handleOutsideClick() {
        val touchOutsideView =
            dialog?.window?.decorView?.findViewById<View>(com.google.android.material.R.id.touch_outside)
        touchOutsideView?.setOnClickListener {
            handleBack()
        }
    }

    private fun handleBack() {
        setFragmentResult(
            Utils.MOMENT_COMMENT_BUNDLE_KEY,
            bundleOf(
                Utils.MOMENT_COMMENT_BUNDLE_ID_KEY to safeArgs.momentId,
                Utils.MOMENT_COMMENT_BUNDLE_TOTAL_KEY to totalCommentParent
            )
        )
        binding.chatBox.tvChat.hideKeyboard()
        dismissAllowingStateLoss()
    }

    private fun changeLayoutConstraint(isLayoutLandscape: Boolean = false) {
        val lp = (binding.vBg.layoutParams as? MarginLayoutParams)
        Timber.d("Change layout before: height = ${lp?.height}")
        lp?.apply {
            topMargin = if (isLayoutLandscape) {
                Utils.getSizeInPixel(binding.vBg.context, R.dimen.moment_comment_margin_top_landscape, 1f)
            } else {
                Utils.getSizeInPixel(binding.vBg.context, R.dimen.moment_comment_margin_top, 1f)
            }
        }
        Timber.d("Change layout top margin to ${lp?.topMargin}")
        Timber.d("Change layout after: height = ${lp?.height}")
        binding.vBg.layoutParams = lp
    }

    override fun bindComponent() {
        if (orientationCurrent) {
            changeLayoutConstraint(orientationCurrent)
        }
        binding.vOutside.onClickDelay {
            handleBack()
        }

        binding.rcvComment.run {
            mAdapter.setHyperLinkListener(object : CommentView.OnCommentListener {
                override fun onHyperlinkClick(link: String) {
                    // process deeplink or open browser
                    DeeplinkUtils.parseDeepLinkAndExecute(
                        deeplink = link,
                        useWebViewInApp = false,
                        trackingInfo = trackingInfo,
                        isDeeplinkCalledInApp = true
                    )
                }
            })
            adapter = mAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        }
        if (sharedPreferences.userAvatar() != "") {
            ImageProxy.loadLocal(
                context = context,
                data = sharedPreferences.userAvatar(),
                width = 0,
                height = 0,
                target = binding.chatBox.ivAvatar
            )
        } else {
            binding.chatBox.ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
        }
        binding.tvSortType.text = getString(R.string.vod_comment_new)
        binding.tvSortType.onClickDelay {
            binding.chatBox.tvChat.hideKeyboard()
            activity?.currentFocus?.hideKeyboard()
            findNavController().navigateSafe(MomentCommentBottomSheetFragmentDirections.actionGlobalMomentCommentBottomSheetToArrangeOption())
        }
        setFragmentResultListener(Utils.MOMENT_DETAIL_COMMENT_ARRANGE) { _, bundle ->
            val sortType = bundle.getString(Utils.MOMENT_DETAIL_COMMENT_ARRANGE_KEY, "")
            if (sortType != sortTypeCurrent && sortType.isNotBlank()) {
                sortTypeCurrent = sortType
//                viewModel.dispatchIntent(
//                    MomentCommentViewModel.MomentChatIntent.GetComments(
//                        "", safeArgs.momentId, 1, totalItemInPage, sortTypeCurrent, isCallSort = true
//                    )
//                )
                viewModel.dispatchIntent(
                    MomentCommentViewModel.MomentChatIntent.GetCommentsV2(
                        "", safeArgs.momentId, safeArgs.chapterId, 1, totalItemInPage, sortTypeCurrent, isCallSort = true
                    )
                )

                when (sortType) {
                    MomentCommentSortType.NEWEST.id -> {
                        binding.tvSortType.text = getString(R.string.vod_comment_new)
                    }
                    MomentCommentSortType.OLDEST.id -> {
                        binding.tvSortType.text = getString(R.string.vod_comment_old)
                    }
                    else -> {
                    }
                }
            }
        }

    }

    override fun bindEvent() {
        binding.rcvComment.apply {
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    if (!recyclerView.canScrollVertically(1)) {
                        Timber.d("Load morere")
                        loadMoreHandler.canScroll(mAdapter.size() - 1)
                    }
                }
            })
        }

        binding.chatBox.tvChat.setOnFocusChangeListener { _, hasFocus ->
            binding.chatBox.ivSend.isVisible = hasFocus
            if (binding.chatBox.tvChat.text.toString().isBlank()) {
                binding.chatBox.ivSend.setImageDrawable(
                    ContextCompat.getDrawable(
                        binding.root.context,
                        R.drawable.ic_vector__send_dis_
                    )
                )
            } else {
                binding.chatBox.ivSend.setImageDrawable(
                    ContextCompat.getDrawable(
                        binding.root.context,
                        R.drawable.ic_vector__send_
                    )
                )
            }
        }

        binding.root.setOnClickListener {
            if (binding.chatBox.tvChat.isFocused) {
                binding.chatBox.tvChat.hideKeyboard()
                activity?.currentFocus?.hideKeyboard()
            }
        }
        binding.chatBox.clCommentContainer.setOnClickListener {
            Timber.d("*** do nothing")
        }

        binding.ivClose.setOnClickListener {
            handleBack()
        }
        binding.chatBox.tvChat.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (binding.chatBox.tvChat.text.isNotEmpty()) {
                    binding.chatBox.ivSend.setImageDrawable(
                        ContextCompat.getDrawable(
                            binding.root.context,
                            R.drawable.ic_vector__send_
                        )
                    )
                    binding.chatBox.ivSend.setOnClickListener {
                        if (NetworkUtils.isNetworkAvailable())
                            checkRequireLogin()
                        else binding.root.showSnackBarForBottomSheet(getString(R.string.error_no_internet))
                    }
                } else {
                    binding.chatBox.ivSend.setImageDrawable(
                        ContextCompat.getDrawable(
                            binding.root.context,
                            R.drawable.ic_vector__send_dis_
                        )
                    )
                    binding.chatBox.ivSend.setOnClickListener {
                        // do nothing
                    }
                }
            }

        })

        binding.chatBox.tvChat.setOnEditorActionListener { v, actionId, _ ->
            return@setOnEditorActionListener when (actionId) {
                EditorInfo.IME_ACTION_SEND -> {
                    if (NetworkUtils.isNetworkAvailable())
                        checkRequireLogin()
                    else binding.root.showSnackBarForBottomSheet(getString(R.string.error_no_internet))
                    true
                }
                else -> false
            }
        }
        mAdapter.eventListener = object : EventListener,
            IEventListener<Pair<MomentComment, ArrayList<MomentComment>>> {
            override fun onClickView(
                position: Int,
                view: View?,
                data: Pair<MomentComment, ArrayList<MomentComment>>
            ) {
                when (view?.id) {
                    R.id.iv_comment -> {
                        binding.chatBox.liReply.visibility = View.VISIBLE
                        binding.chatBox.tvName.text =
                            if (isValidPhone(data.first.userName)) { "***" + data.first.userName.takeLast(3) }
                            else { data.first.userName }

                        if (data.first.pinTop.toBooleanStrictOrNull() == true) {
                            binding.chatBox.tvName.setTextColor(Color.parseColor("#FE592A"))
                        } else {
                            binding.chatBox.tvName.setTextColor(Color.parseColor("#DEFFFFFF"))
                        }
                        binding.chatBox.tvCancel.setOnClickListener {
                            parentId = ""
                            binding.chatBox.liReply.visibility = View.GONE
                            activity?.currentFocus?.hideKeyboard()
                        }
                        binding.chatBox.tvChat.showKeyboard()
                        if (data.first.parentId == "")
                            parentId = data.first.id
                        else {
                            parentId = data.first.parentId
                            binding.chatBox.tvChat.setSelection(binding.chatBox.tvChat.length())
                        }
                        Timber.d("thien test cmt parentID $parentId")
                    }
                    R.id.tv_view_more_next_page -> {
                        try {
                            val viewHolder =
                                binding.rcvComment.findViewHolderForAdapterPosition(position) as MomentCommentParentAdapter.CommentParentViewHolder
                            if (viewHolder != null) {
//                                viewModel.dispatchIntent(
//                                    MomentCommentViewModel.MomentChatIntent.GetChildComments(
//                                        position,
//                                        data.first.id,
//                                        safeArgs.momentId,
//                                        viewHolder?.page ?: 1, totalItemInPage, sortTypeCurrent
//                                    )
//                                )
                                viewModel.dispatchIntent(
                                    MomentCommentViewModel.MomentChatIntent.GetChildCommentsV2(
                                        position = position,
                                        parentId = data.first.id,
                                        momentId = safeArgs.momentId,
                                        episodeId = safeArgs.chapterId,
                                        page = viewHolder?.page ?: 1, perPage = totalItemInPage, sortType = sortTypeCurrent
                                    )
                                )
                            }
                        } catch (e: Exception) {
//                            viewModel.dispatchIntent(
//                                MomentCommentViewModel.MomentChatIntent.GetChildComments(
//                                    position,
//                                    data.first.id,
//                                    safeArgs.momentId,
//                                    1, totalItemInPage, sortTypeCurrent
//                                )
//                            )
                            viewModel.dispatchIntent(
                                MomentCommentViewModel.MomentChatIntent.GetChildCommentsV2(
                                    position = position,
                                    parentId = data.first.id,
                                    momentId = safeArgs.momentId,
                                    episodeId = safeArgs.chapterId,
                                    page = 1, perPage = totalItemInPage, sortType = sortTypeCurrent
                                )
                            )
                        }
                    }
                    else -> {}
                }
            }

            override fun onSelectedItem(position: Int, data: Pair<MomentComment, ArrayList<MomentComment>>) {
//                viewModel.dispatchIntent(
//                    MomentCommentViewModel.MomentChatIntent.GetChildComments(
//                        position,
//                        data.first.id,
//                        safeArgs.momentId,
//                        1, totalItemInPage, sortTypeCurrent
//                    )
//                )
                viewModel.dispatchIntent(
                    MomentCommentViewModel.MomentChatIntent.GetChildCommentsV2(
                        position = position,
                        parentId = data.first.id,
                        momentId = safeArgs.momentId,
                        episodeId = safeArgs.chapterId,
                        page = 1, perPage = totalItemInPage, sortType = sortTypeCurrent
                    )
                )
            }
        }
    }

    private fun allowComment() {
        binding.chatBoxContainer.visible()
        binding.tvBlockComment.gone()
    }

    private fun dontAllowComment(messageBlock: String) {
        binding.chatBoxContainer.gone()
        binding.tvBlockComment.visible()
        binding.tvBlockComment.text = messageBlock.ifBlank { getString(R.string.vod_account_block_comment) }
    }

    private fun blockComment(messageBlock: String) {
        binding.chatBoxContainer.gone()
        binding.tvBlockComment.visible()
        binding.tvBlockComment.text = messageBlock.ifBlank { getString(R.string.vod_block_comment) }
    }

    private fun isValidPhone(phone: CharSequence?): Boolean {
        return if (TextUtils.isEmpty(phone)) {
            false
        } else {
            Patterns.PHONE.matcher(phone).matches()
        }
    }

    override fun MomentCommentViewModel.MomentChatState.toUI() {
        when (this) {
            is MomentCommentViewModel.MomentChatState.ResultCommentAction -> {
            }
            is MomentCommentViewModel.MomentChatState.ResultGetPermissionComment ->{
                initComments(this.permissions)
                binding.tvErrorMessage.isVisible = false
                mAdapter.setIsAllowComment(this.permissions.uComment =="1" && this.permissions.enable =="1")
                data.totalCommentLocal = data.totalComment
                totalCommentParent = data.totalComment
                binding.tvCommentTotal.text = getString(R.string.vod_comment, data.totalComment)
                mAdapter.childAdapters.clear()
                if (isCallSort) {
                    mAdapter.bind(listOf()) {
                        mAdapter.add(this.data.listComment.map { it ->
                            Pair(it, arrayListOf())
                        }, isBind = mAdapter.size() == 0) {
                            loadMoreHandler.refresh(
                                totalItem = mAdapter.size(),
                                endPage = this.data.listComment.size < totalItemInPage
                            )
                        }
                    }
                } else {
                    mAdapter.add(this.data.listComment.map {
                        Pair(it, arrayListOf())
                    }, isBind = mAdapter.size() == 0) {
                        loadMoreHandler.refresh(
                            totalItem = mAdapter.size(),
                            endPage = this.data.listComment.size < totalItemInPage
                        )
                    }
                }
                if (isAutoSendComment) {
                    parentId = safeArgs.parentIdForAutoComment
                    sendText(safeArgs.autoComment)
                    isAutoSendComment = false
                }
            }
            is MomentCommentViewModel.MomentChatState.ResultCommentPage -> {
                binding.tvErrorMessage.isVisible = false
                data.totalCommentLocal = data.totalComment
                totalCommentParent = data.totalComment
                binding.tvCommentTotal.text = getString(R.string.vod_comment, data.totalComment)
                mAdapter.childAdapters.clear()
                if (isCallSort) {
                    mAdapter.bind(listOf()) {
                        mAdapter.add(this.data.listComment.map { it ->
                            Pair(it, arrayListOf())
                        }, isBind = mAdapter.size() == 0) {
                            loadMoreHandler.refresh(
                                totalItem = mAdapter.size(),
                                endPage = this.data.listComment.size < totalItemInPage
                            )
                        }
                    }
                } else {
                    mAdapter.add(this.data.listComment.map {
                        Pair(it, arrayListOf())
                    }, isBind = mAdapter.size() == 0) {
                        loadMoreHandler.refresh(
                            totalItem = mAdapter.size(),
                            endPage = this.data.listComment.size < totalItemInPage
                        )
                    }
                }
            }
            is MomentCommentViewModel.MomentChatState.ResultCreateComment -> {
                binding.chatBox.liReply.visibility = View.GONE
                // Add new comment
                try {
                    val date = Date(System.currentTimeMillis()).time
                    val formatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS", Locale.US)

                    val dateFormatted: String = formatter.format(date)
                    var userName = ""
                    userName = if (isValidPhone(sharedPreferences.displayName())) {
                        "***" + sharedPreferences.displayName().takeLast(3)
                    } else {
                        sharedPreferences.displayName()
                    }
                    var userAvatar = ""
                    userAvatar = if (sharedPreferences.userAvatar() != "") {
                        sharedPreferences.userAvatar()
                    } else {
                        R.drawable.ic_user_default_avatar.toString()
                    }
                    val comment = MomentComment(
                        id = data.commentId,
                        userName = userName,
                        momentId = safeArgs.momentId,
                        content = this.content,
                        parentId = this.parentId,
                        createdTime = dateFormatted,
                        userAvatar = userAvatar,
                        countCommentReply = if (parentId.isBlank()) "0" else "",
                        countCommentReplyLocal = if (parentId.isBlank()) "0" else ""
                    )
                    mAdapter.addComment(comment = comment, sortType = sortTypeCurrent)
                    setFragmentResult(
                        Utils.MOMENT_SEND_COMMENT_BUNDLE_KEY,
                        bundleOf(Utils.MOMENT_SEND_COMMENT_STATUS_BUNDLE_KEY to safeArgs.momentId)
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            is MomentCommentViewModel.MomentChatState.ResultCommentChildPage -> {
                if (this.data.listComment.isNotEmpty() && this.data.listComment.isNotEmpty()) {
                    Timber.d("Load more refresh with ${data.listComment[0].id} adapter size: ${mAdapter.size()}")
                    mAdapter.loadReply(this.parentId, this.position, this.data.listComment)
                    val viewHolder =
                        binding.rcvComment.findViewHolderForAdapterPosition(this.position) as MomentCommentParentAdapter.CommentParentViewHolder
                    if (viewHolder != null) {
                        if (this.data.listComment.size <= totalItemInPage) {
                            viewHolder.disableLoadMoreCommentChild()
                        }
                    }
                } else {
                    val viewHolder =
                        binding.rcvComment.findViewHolderForAdapterPosition(this.position) as MomentCommentParentAdapter.CommentParentViewHolder
                    if (viewHolder != null) {
                        viewHolder.disableLoadMoreCommentChild()
                    }
                }
            }
            is MomentCommentViewModel.MomentChatState.ErrorRequiredLogin -> {
                navigateToLoginWithParams(
                    isDirect = false
                )
            }
            is MomentCommentViewModel.MomentChatState.ErrorNoInternet -> {
                if (isFirstLoading) {
                    binding.tvErrorMessage.isVisible = true
                    isFirstLoading = false
                } else binding.root.showSnackBarForBottomSheet(getString(R.string.error_no_internet))
            }
            is MomentCommentViewModel.MomentChatState.Error -> {
                Timber.d("thien test intent ${this.intent}")
                when (this.intent) {
                    is MomentCommentViewModel.MomentChatIntent.GetComments -> {
                        binding.tvErrorMessage.isVisible = true
                    }
                    is MomentCommentViewModel.MomentChatIntent.GetCommentsV2 -> {
                        binding.tvErrorMessage.isVisible = true
                    }
                    is MomentCommentViewModel.MomentChatIntent.GetPermissionComment -> {
                        binding.tvErrorMessage.isVisible = true
                    }
                    is MomentCommentViewModel.MomentChatIntent.GetPermissionCommentV2 -> {
                        binding.tvErrorMessage.isVisible = true
                        if (isAutoSendComment) {
                            parentId = safeArgs.parentIdForAutoComment
                            sendText(safeArgs.autoComment)
                            isAutoSendComment = false
                        }
                    }

                    else ->
                        binding.root.showSnackBarForBottomSheet(getString(R.string.comment_error_toast))
                }
            }
            is MomentCommentViewModel.MomentChatState.ErrorRequiredVip ->{
                binding.root.showSnackBarForBottomSheet(getString(R.string.comment_error_toast))
            }
            is MomentCommentViewModel.MomentChatState.ErrorGeneral ->{
                if(this.detail.statusCode==406){
                    enable = true
                    userCom = false
                    val convertTotalComment = convertToInt(totalCommentParent)
                    if (convertTotalComment != null) {
                        var totalCommentNew = convertTotalComment - 1
                        totalCommentParent = totalCommentNew.toString()
                        binding.tvCommentTotal.text = getString(R.string.vod_comment, totalCommentParent)
                    }
                    dontAllowComment(this.detail.message)
                }
            }
            else -> {}
        }
    }
    private fun sendText(comment: String) {
        if(isCommentValid(comment)) {
            binding.tvNoti.visibility = View.GONE
            if (parentId.isBlank()) {
                val convertTotalComment = convertToInt(totalCommentParent)
                if (convertTotalComment != null) {
                    var totalCommentNew = convertTotalComment + 1
                    totalCommentParent = totalCommentNew.toString()
                    binding.tvCommentTotal.text = getString(R.string.vod_comment, totalCommentParent)
                }
            }
//            viewModel.dispatchIntent(
//                MomentCommentViewModel.MomentChatIntent.CreateComment(
//                    safeArgs.momentId,
//                    comment,
//                    parentId,
//                    "0"
//                )
//            )
            viewModel.dispatchIntent(
                MomentCommentViewModel.MomentChatIntent.CreateCommentV2(
                    safeArgs.momentId,
                    safeArgs.chapterId,
                    comment,
                    parentId,
                    "0"
                )
            )
            binding.chatBox.tvChat.setText("")
            parentId = ""
            binding.chatBox.tvChat.hideKeyboard()
        } else {
            binding.tvNoti.visibility = View.VISIBLE
            binding.tvNoti.text = getInvalidCommentMessage()
            Timer("thien test Noti", false).schedule(2000) {
                runOnUiThread {
                    binding.tvNoti.visibility = View.GONE
                }
            }
        }
    }

    private fun isCommentValid(comment: String): Boolean {
        return comment.length in minCommentCharacterCount..maxCommentCharacterCount
                || minCommentCharacterCount - maxCommentCharacterCount == 0
    }

    private fun getInvalidCommentMessage(): String {
        val messageWithParams = sharedPreferences.getMsgLimitCommentCharacter().ifBlank {
            getString(R.string.default_limit_comment_character)
        }

        return String.format(
            messageWithParams,
            minCommentCharacterCount,
            maxCommentCharacterCount
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    private fun initComments(permissionComment: MomentCommentPermission){
       enable = permissionComment.enable == "1"
        userCom = permissionComment.uComment == "1"
        if (enable) {
            if (userCom) {
                allowComment()
            } else {
                dontAllowComment(permissionComment.messageBlock)
            }
        } else {
            blockComment(permissionComment.messageOffComment)
        }
    }
    private fun convertToInt(value: String): Int? = try {
        value.toInt()
    } catch (e: Exception) {
        null
    }

    private fun checkRequireLogin() {
        if (!sharedPreferences.userLogin()) {
            DataCacheObject.dataLikeCommentCache = ShortVideoLikeCommentCache(
                dataActive = false,
                tabId = DataCacheObject.dataCache.getCurTabId()?:"",
                actionStatus = 1,
                itemId = safeArgs.momentId,
                chapterId = safeArgs.chapterId,
                statusLikeLocal = ""
            )
            val comment = bundleOf(
                Constants.MOMENT_COMMENT_DATA to binding.chatBox.tvChat.text.toString(),
                Constants.MOMENT_COMMENT_PARENT_ID to parentId
            )
            val extraData = bundleOf(Constants.MOMENT_AUTO_COMMENT to comment)
            if(parentFragment?.parentFragment is ShortVideosContentFragment){ //viewModel.isTabHomeLayout
                parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = false, displayInDialogForMobile = true, extendsArgs = extraData)
            }else navigateToLoginWithParams(isDirect = false, displayInDialogForMobile = true, extendsArgs = extraData)
        } else {
            sendText(binding.chatBox.tvChat.text.toString())
        }
    }
}
