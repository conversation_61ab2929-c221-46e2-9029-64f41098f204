package com.fptplay.mobile.features.ai_chatbot.model

import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class AIChatBotConfig (
    val iconUrl:String? = "",
    val enable:Boolean = false,
    val url :String= "",
    val title:String? =""
):BaseObject(){
    companion object {
        @JvmStatic
        val emptyAIChatBotConfig = AIChatBotConfig(
            iconUrl = "",
            enable = false,
            url= "",
            title = ""
        )
    }
}
sealed class AiChatTypeSendLog {
    object RequestType : AiChatTypeSendLog()
    object EnterChatBotType : AiChatTypeSendLog()
    object FeedbackType : AiChatTypeSendLog()
    object Other : AiChatTypeSendLog()

}
data class MiniAppResponseMetaDataValueSendLog (
    val status:String? = "",
    val itemName:String? = "",
)

data class MiniAppResponseMetaDataSendLog (
    val valueLog:MiniAppResponseMetaDataValueSendLog?,
    val typeLog:AiChatTypeSendLog?
)


