package com.fptplay.mobile.features.pladio.ui

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.WindowInsets
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.fptplay.mobile.features.pladio.util.coordinatorLayoutBehavior
import com.fptplay.mobile.features.pladio.util.replaceSystemBarInsetsCompat
import com.fptplay.mobile.features.pladio.util.systemBarInsetsCompat
import com.google.android.material.bottomsheet.BottomSheetBehavior
import kotlin.math.abs

/**
 * A behavior that automatically re-layouts and re-insets content to align with the parent layout's
 * bottom sheet. Ideally, we would only want to re-inset content, but that has too many issues to
 * sensibly implement.
 */
class BottomSheetContentBehavior<V : View>(context: Context, attributeSet: AttributeSet?) :
    CoordinatorLayout.Behavior<V>(context, attributeSet) {
    private var dep: View? = null
    private var lastInsets: WindowInsets? = null
    private var lastConsumed = -1
    private var setup = false

    override fun layoutDependsOn(parent: CoordinatorLayout, child: V, dependency: View): Boolean {
        if (isHandleLayoutDepended(dependency)) {
            dep?.let {
                val oldDepLocation = getViewLocationInWindow(it)
                val newDepLocation = getViewLocationInWindow(dependency)
                if (newDepLocation[1] < oldDepLocation[1]) {
                    dep = dependency
                }
            } ?: kotlin.run {
                dep = dependency
            }
            return true
        }

        return false
    }

    override fun onDependentViewChanged(
        parent: CoordinatorLayout,
        child: V,
        dependency: View
    ): Boolean {
        val consumed = getConsumed(dependency = dependency)
        if (consumed == Int.MIN_VALUE) {
            return false
        }

        if (consumed != lastConsumed) {
            lastConsumed = consumed
            lastInsets?.let(child::dispatchApplyWindowInsets)
            measureContent(parent, child, consumed)
            layoutContent(child)
            return true
        }

        return false
    }

    override fun onMeasureChild(
        parent: CoordinatorLayout,
        child: V,
        parentWidthMeasureSpec: Int,
        widthUsed: Int,
        parentHeightMeasureSpec: Int,
        heightUsed: Int
    ): Boolean {
        val dep = dep ?: return false
        val consumed = getConsumed(dependency = dep)
        if (consumed == Int.MIN_VALUE) {
            return false
        }

        measureContent(parent, child, consumed)

        return true
    }

    override fun onLayoutChild(parent: CoordinatorLayout, child: V, layoutDirection: Int): Boolean {
        super.onLayoutChild(parent, child, layoutDirection)
        layoutContent(child)

        if (!setup) {
            child.setOnApplyWindowInsetsListener { _, insets ->
                lastInsets = insets
                val dep = dep ?: return@setOnApplyWindowInsetsListener insets
                val consumed = getConsumed(dependency = dep)
                if (consumed == Int.MIN_VALUE) {
                    return@setOnApplyWindowInsetsListener insets
                }

                val bars = insets.systemBarInsetsCompat

                insets.replaceSystemBarInsetsCompat(
                    bars.left, bars.top, bars.right, (bars.bottom - consumed).coerceAtLeast(0))
            }

            setup = true
        }

        return true
    }

    private fun measureContent(parent: View, child: View, consumed: Int) {
        val contentWidthSpec =
            View.MeasureSpec.makeMeasureSpec(parent.measuredWidth, View.MeasureSpec.EXACTLY)
        val contentHeightSpec =
            View.MeasureSpec.makeMeasureSpec(
                parent.measuredHeight - consumed, View.MeasureSpec.EXACTLY)

        child.measure(contentWidthSpec, contentHeightSpec)
    }

    private fun layoutContent(child: View) {
        child.layout(0, 0, child.measuredWidth, child.measuredHeight)
    }

    private fun BottomSheetBehavior<*>.calculateConsumedByBar(): Int {
        val offset = calculateSlideOffset()
        if (offset == Float.MIN_VALUE || peekHeight < 0) {
            return Int.MIN_VALUE
        }

        return if (offset >= 0) {
            peekHeight
        } else {
            (peekHeight * (1 - abs(offset))).toInt()
        }
    }

    private fun isHandleLayoutDepended(dependency: View): Boolean {
        return if (dependency is CustomBottomNavigationView) {
            true
        } else {
            dependency.coordinatorLayoutBehavior is BottomSheetBehavior
        }
    }

    private fun getConsumed(dependency: View): Int {
        return if (dependency is CustomBottomNavigationView) {
            dependency.height
        } else {
            if (dependency.coordinatorLayoutBehavior is BottomSheetBehavior) {
                val behavior = dependency.coordinatorLayoutBehavior as BottomSheetBehavior
                behavior.calculateConsumedByBar()
            } else {
                0
            }
        }
    }

    private fun getViewLocationInWindow(view: View): IntArray {
        val location = IntArray(2)
        view.getLocationInWindow(location)
        return location
    }

}
