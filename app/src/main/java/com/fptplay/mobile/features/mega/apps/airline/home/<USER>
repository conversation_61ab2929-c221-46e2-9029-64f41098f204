package com.fptplay.mobile.features.mega.apps.airline.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.forEach
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.NavigationUI
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.AirlineHomeFragmentBinding
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.*
import com.fptplay.mobile.features.mega.apps.airline.model.AirlineBrand
import com.google.android.material.navigation.NavigationBarView
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.lang.ref.WeakReference


@AndroidEntryPoint
class AirlineHomeFragment: BaseFragment<AirlineViewModel.AirlineState, AirlineViewModel.AirlineIntent>() {

    //region Variables
    override val viewModel by activityViewModels<AirlineViewModel>()
    private var _binding: AirlineHomeFragmentBinding? = null
    private val binding get() = _binding!!
    //endregion

    //region Overrides
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = AirlineHomeFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun initData() {
        val brand = activity?.intent?.getSerializableExtra(AirlineActivity.AIRLINE_BRAND_KEY) as? AirlineBrand
        viewModel.saveAirlineBrand(brand)
    }

    override fun bindComponent() {
        bindAirlineLogo()

        if (viewModel.airlineBrand() == AirlineBrand.VIETJET_AIR) {
            binding.apply {
                bnvAirline.itemIconTintList = context?.getColorStateList(R.color.airline_vietjet_bottom_navigation_item_color_state)
                bnvAirline.itemTextColor = context?.getColorStateList(R.color.airline_vietjet_bottom_navigation_item_color_state)
            }
        }

        bindAirlineBottomMenu()
        (childFragmentManager.findFragmentById(R.id.nav_host_fragment) as? NavHostFragment)?.navController?.run {
            binding.bnvAirline.setupWithNavController(this)
        }

//        binding.airlineAppbar.swLanguage.apply {
//            trackDrawable = SwitchLanguageTrackDrawable(
//                requireContext(),
//                getString(R.string.language_vi),
//                getString(R.string.language_en)
//            )
//            thumbDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.airline_switch_language_thumb)
//            isChecked = LocaleLanguageManager.getLanguagePref(requireContext()) == LocaleLanguageManager.ENGLISH
//        }
    }

    override fun bindEvent() {
        binding.airlineAppbar.apply {
            ibBack.setOnClickListener {
                activity?.finish()
            }

//            swLanguage.setOnCheckedChangeListener { _, isChecked ->
//                if (isChecked) {
//                    LocaleLanguageManager.setNewLocale(requireContext(), LocaleLanguageManager.ENGLISH)
//                } else {
//                    LocaleLanguageManager.setNewLocale(requireContext(), LocaleLanguageManager.VIETNAM)
//                }
//
//                AlertDialog().apply {
//                    setMessage(<EMAIL>(R.string.restart_application_notify))
//                    setTextConfirm(<EMAIL>(R.string.restart_application))
//                    setOnlyConfirmButton(true)
//                    setHandleBackPress(true)
//                    setListener(object : AlertDialogListener {
//                        override fun onConfirm() {
//                            activity?.recreate()
//                        }
//                    })
//                }.show(childFragmentManager, "RestartDialog")
//            }
        }
        observerData()
    }

    private fun observerData() {
        viewModel.updateNoInternetView.observe(viewLifecycleOwner) {
            it?.let { result ->
                if(result.isShowView) {
                    binding.airlineAppbar.ivAirlineLogo.show()
                } else {
                    binding.airlineAppbar.ivAirlineLogo.hide()
                }
            }
        }
    }

    override fun AirlineViewModel.AirlineState.toUI() {
        Timber.d("AirlineState: $this")
        when (this) {
            is Loading -> {}
            is Error -> {}
            is ErrorRequiredLogin -> {}
            is Done -> {}
            else -> {}
        }
    }
    //endregion

    //Commons
    private fun bindAirlineLogo() {
        val airlineLogo = when (viewModel.airlineBrand()) {
            AirlineBrand.VN_AIRLINE -> R.drawable.ic_vn_airline
            AirlineBrand.BAMBOO_AIRWAYS -> R.drawable.ic_bamboo_airways
            AirlineBrand.VIETJET_AIR -> R.drawable.ic_vietjet_air
            else -> null
        }

        airlineLogo?.let {
            ImageProxy.loadLocal(
                context = context,
                data = airlineLogo,
                width = 0,
                height = 0,
                target = binding.airlineAppbar.ivAirlineLogo
            )
        } ?: binding.airlineAppbar.ivAirlineLogo.hide()
    }

    private fun bindAirlineBottomMenu() {
        binding.bnvAirline.menu.clear()
        if(viewModel.airlineBrand() == AirlineBrand.VIETJET_AIR) {
            binding.bnvAirline.inflateMenu(R.menu.airline_vietjetair_bottom_tabs)
        } else {
            binding.bnvAirline.inflateMenu(R.menu.airline_bottom_tabs)
        }
    }
    private fun NavigationBarView.setupWithNavController(navController: NavController) {
        setOnItemSelectedListener { item ->
            NavigationUI.onNavDestinationSelected(item, navController)
        }
        val weakReference = WeakReference(this)
        navController.addOnDestinationChangedListener(
            object : NavController.OnDestinationChangedListener {
                override fun onDestinationChanged(
                    controller: NavController,
                    destination: NavDestination,
                    arguments: Bundle?
                ) {
                    if (destination.id == R.id.airline_download_fragment) {
                        binding.airlineAppbar.rootView.hide()

                        /*val constraintLayout: ConstraintLayout = binding.root
                        val constraintSet = ConstraintSet()
                        constraintSet.clone(constraintLayout)
                        constraintSet.connect(
                            binding.navHostFragment.id,
                            ConstraintSet.BOTTOM,
                            binding.bnvAirline.id,
                            ConstraintSet.TOP,
                            0
                        )
                        constraintSet.applyTo(constraintLayout)*/

                        /*val params = binding.navHostFragment.layoutParams as ConstraintLayout.LayoutParams
                        params.bottomToTop = binding.bnvAirline.id
                        params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                        binding.navHostFragment.requestLayout()*/
                    } else {
                        binding.airlineAppbar.rootView.show()

                        /*val params = binding.navHostFragment.layoutParams as ConstraintLayout.LayoutParams
                        params.bottomToTop = ConstraintLayout.LayoutParams.UNSET
                        params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        binding.navHostFragment.requestLayout()*/
                    }
                    val view = weakReference.get()
                    if (view == null) {
                        navController.removeOnDestinationChangedListener(this)
                        return
                    }
                    view.menu.forEach { item ->
                        if (destination.matchDestination(item.itemId)) {
                            item.isChecked = true
                        }
                    }
                }
            })
    }

    private fun NavDestination.matchDestination(@IdRes destId: Int): Boolean = hierarchy.any { it.id == destId }
    //endregion
}