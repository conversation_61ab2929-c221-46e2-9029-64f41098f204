package com.fptplay.mobile.features.mega.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavDowloadV2Directions
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.hideLoading
import com.fptplay.mobile.common.extensions.ActivityExtensions.showLoading
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.view.WarningDialogFragment
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.Utils.isActive
import com.fptplay.mobile.common.utils.Utils.requireLogin
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.features.about.AboutViewModel
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.download.model.StartingTargetScreenV2Type
import com.fptplay.mobile.features.loyalty.utils.CheckNavigateLoyaltyUtils
import com.fptplay.mobile.features.mega.MegaNavigateViewModel
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.account.util.AccountMegaScreen
import com.fptplay.mobile.features.mega.adapter.MegaBlockDownloadAdapter
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.mega.apps.airline.model.AirlineBrand
import com.fptplay.mobile.features.mega.apps.fptpplayshop.FPTPlayShopFragment
import com.fptplay.mobile.features.mini_app.CloseInfoAppMiniAppData
import com.fptplay.mobile.features.multi_profile.utils.StartingTargetScreenType
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.ftel.foxpay.foxsdk.feature.FoxSdkManager
import com.ftel.foxpay.foxsdk.feature.TypeScreen
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.AutoScrollConfig
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenu
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMiniAppManifest
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class CheckNavigateMegaUtils(
    private val context: Context,
    private val sharedPreferences: SharedPreferences,
    private val megaViewModel: MegaViewModel,
    private val megaNavigateViewModel: MegaNavigateViewModel,
    private val fragment: Fragment?,
    private val navHostFragment: NavHostFragment?,
    private val checkNavigateLoyaltyUtils: CheckNavigateLoyaltyUtils,
    private val foxPaySdkManager: FoxSdkManager.Companion,
    private val popupAfterNavigate: Boolean = false

) : LifecycleEventObserver {
    private var warningDialogFragment: AlertDialog? = null
    var shouldProcessObserve = true
    private var alertDialog: AlertDialog? = null

    private var isChecking = false

    // region navigate
    fun navigateToSelectedContent(data: BaseObject, extraData: NavExtraData? = null) {
        Timber.tag("tam-mega").i("navigateToSelectedContent $data")
        when (data) {
            is MegaMenu.Block -> {
                when (data) {
                    is MegaMenu.BlockProfileLogin -> {
                        navigateToLogin(
                            navigationId = R.id.nav_multi_profile,
                            requestRestartApp = true,
                            extendsArgs = bundleOf(
                                "targetScreen" to StartingTargetScreenType.SelectProfile
                            ))
                    }
                    is MegaMenu.BlockProfile -> {
                        checkUserLoginBeforeNavigate(navigationId = R.id.action_global_to_account_info) {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToAccountInfo()
                            )
                        }
                    }
                    is MegaMenu.BlockMultiProfile -> {
                        checkUserLoginBeforeNavigate(navigationId = R.id.action_global_to_multi_profile) {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToMultiProfile(
                                    targetScreen = StartingTargetScreenType.SelectProfile
                                )
                            )
                        }
                    }
                    else -> {
                    }
                }
            }
            is MegaMenuItem -> {
                var navigationId: Int? = null
                var navigateFun: () -> Unit = {}
                var extendsArgs: Bundle? = null
                var navigateAlreadyHandled = false
                Timber.tag("tam-mega").d("data.actionType ${data}")
                when (data.actionType) {
                    // account info
                    MegaMenuItem.ActionType.UserHistoryTransaction -> {
//                        if (context.isTablet()) {
//                            navigationId = R.id.action_global_to_account_action_dialog
//                            navigateFun = {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToPaymentHistoryDialog()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountActionDialog(
//                                        targetScreen = AccountMegaScreen.PaymentHistory
//                                    )
//                                )

//                            }
//                        } else {
//                            navigationId = R.id.action_global_to_account_action
//                            navigateFun = {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToPaymentHistory()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountAction(
//                                        targetScreen = AccountMegaScreen.PaymentHistory
//
//                                    )
//                                )
//                            }
//                        }
                        navigationId = R.id.action_global_to_account_action
                        navigateFun = {
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToPaymentHistory()
//                                )
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.PaymentHistory

                                )
                            )
                        }
                        extendsArgs = bundleOf(
                            "targetScreen" to AccountMegaScreen.PaymentHistory,
                            "title" to data.title
                        )
                    }
                    MegaMenuItem.ActionType.UserInformation -> {
                        navigationId = R.id.action_global_to_account_info
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToAccountInfo()
                            )
                        }
                        extendsArgs = bundleOf("title" to data.title)

                    }
                    MegaMenuItem.ActionType.UserInputActiveCode -> {
//                        navigationId = if (context.isTablet()) {
//                            R.id.action_global_to_account_action_dialog
//                        } else {
//                            R.id.action_global_to_account_action
//                        }
//                        extendsArgs = bundleOf(
//                            "targetScreen" to AccountMegaScreen.PromotionCode
//                        )
//                        navigateFun = {
//                            if (context.isTablet()) {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToPromotionCodeDialog()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountActionDialog(
//                                        targetScreen = AccountMegaScreen.PromotionCode
//                                    )
//                                )
//                            } else {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToPromotionCode()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountAction(
//                                        targetScreen = AccountMegaScreen.PromotionCode
//
//                                    )
//                                )
//                            }
//                        }
                        navigationId =  R.id.action_global_to_account_action
                        extendsArgs = bundleOf(
                            "targetScreen" to AccountMegaScreen.PromotionCode,
                            "title" to data.title
                        )

                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.PromotionCode

                                )
                            )
                        }

                    }
                    MegaMenuItem.ActionType.UserManageDevice -> {
//
//                        if (context.isTablet()) {
//                            navigationId = R.id.action_global_to_account_action_dialog
//                            navigateFun = {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToDeviceManagerDialog()
////                                )
//
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountActionDialog(
//                                        targetScreen = AccountMegaScreen.DeviceManager
//                                    )
//                                )
//
//                            }
//                        } else {
//                            navigationId = R.id.action_global_to_account_action
//                            navigateFun = {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToDeviceManager()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountAction(
//                                        targetScreen = AccountMegaScreen.DeviceManager
//                                    )
//                                )
//                            }
//                        }
                        navigationId = R.id.action_global_to_account_action
                        navigateFun = {
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToDeviceManager()
//                                )
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.DeviceManager
                                )
                            )
                        }
                        extendsArgs = bundleOf(
                            "targetScreen" to AccountMegaScreen.DeviceManager,
                            "title" to data.title
                        )
                    }
                    MegaMenuItem.ActionType.UserPurchaseService -> {
//                        if (context.isTablet()) {
//                            navigationId = R.id.action_global_to_account_action_dialog
//                            navigateFun = {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToPackageUserDialog()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountActionDialog(
//                                        targetScreen = AccountMegaScreen.PackageUser
//                                    )
//                                )
//                            }
//                        } else {
//                            navigationId = R.id.action_global_to_account_action
//                            navigateFun = {
////                                navHostFragment?.findNavController()?.navigate(
////                                    NavHomeMainDirections.actionGlobalToPackageUser()
////                                )
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToAccountAction(
//                                        targetScreen = AccountMegaScreen.PackageUser
//                                    )
//                                )
//                            }
//                        }
                        navigationId = R.id.action_global_to_account_action
                        navigateFun = {
//                                navHostFragment?.findNavController()?.navigate(
//                                    NavHomeMainDirections.actionGlobalToPackageUser()
//                                )
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.PackageUser
                                )
                            )
                        }
                        extendsArgs = bundleOf(
                            "targetScreen" to AccountMegaScreen.PackageUser,
                            "title" to data.title
                        )
                    }
                    MegaMenuItem.ActionType.UserReferFriends -> {
                        navigationId = R.id.action_global_to_fpt_invite_friends
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToFptInviteFriends()
                            )
                        }

                    }
                    MegaMenuItem.ActionType.AppInfo -> {
                        val type = data.actionValue
                        if(type.equals(AboutViewModel.INTRODUCE_TYPE, ignoreCase = true)) {

                            navigationId = R.id.action_global_to_about_app_introduce
                            navigateFun = {
                                fragment?.findNavController()?.navigate(
                                    NavHomeMainDirections.actionGlobalToAboutAppIntroduce(
                                        type = data.actionValue,
                                        title = data.title
                                    )
                                )
                            }
                        } else {
                            navigationId = R.id.action_global_to_about_app_info
                            navigateFun = {
                                fragment?.findNavController()?.navigate(
                                    NavHomeMainDirections.actionGlobalToAboutAppInfo(
                                        type = data.actionValue,
                                        title = data.title
                                    )
                                )
                            }
                        }
                    }
                    MegaMenuItem.ActionType.CustomerServiceZendesk -> {
                        if(popupAfterNavigate)
                            fragment?.findNavController()?.navigateUp()

                        launchSupportCenter(sharedPreferences)
                        navigateAlreadyHandled = true
                    }
                    MegaMenuItem.ActionType.Download -> {
                        navigationId = R.id.action_global_to_download_v2
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToDownloadV2(
                                    title = data.title
                                )
                            )
                        }
                    }
                    MegaMenuItem.ActionType.OpenSubMenus -> {
                        // not setup navigateId, if requiredLogin && user not login -> navigate to login -> navigate back -> not doing anything else
                        navigateFun = {
                            megaViewModel.saveListSubmenus(data.subMenus)
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToMegaSubmenu(
                                    id = data.id,
                                    title = data.title
                                )
                            )
                        }

                    }

                    // mega app
                    MegaMenuItem.ActionType.AirlineBambooAirways -> {
                        if(popupAfterNavigate)
                            fragment?.findNavController()?.navigateUp()

                        navigateToAirline(AirlineBrand.BAMBOO_AIRWAYS)
                        navigateAlreadyHandled = true
                    }
                    MegaMenuItem.ActionType.AirlineVnAirlines -> {
                        if(popupAfterNavigate)
                            fragment?.findNavController()?.navigateUp()

                        navigateToAirline(AirlineBrand.VN_AIRLINE)
                        navigateAlreadyHandled = true
                    }
                    MegaMenuItem.ActionType.AirlineVietjet -> {
                        if(popupAfterNavigate)
                            fragment?.findNavController()?.navigateUp()

                        navigateToAirline(AirlineBrand.VIETJET_AIR)
                        navigateAlreadyHandled = true
                    }
                    MegaMenuItem.ActionType.EWalletFoxpay -> {
                        // not setup navigateId, if requiredLogin && user not login -> navigate to login -> navigate back -> not doing anything else
                        navigateFun = {
                            FoxpayUtils.openFoxpay(context, foxPaySdkManager, TypeScreen.Home)
                        }

                    }
                    MegaMenuItem.ActionType.EWalletFoxpayPhoneRecharge -> {
                        // not setup navigateId, if requiredLogin && user not login -> navigate to login -> navigate back -> not doing anything else
                        navigateFun = {
                            FoxpayUtils.openFoxpay(
                                context,
                                foxPaySdkManager,
                                TypeScreen.PhoneRecharge
                            )
                        }
                    }
                    MegaMenuItem.ActionType.FptPlayLoyalty -> {
                        if(popupAfterNavigate)
                            fragment?.findNavController()?.navigateUp()
                        checkNavigateLoyaltyUtils.checkNavigationLoyalty()
                        navigateAlreadyHandled = true
                    }
                    MegaMenuItem.ActionType.FptPlayRewards -> {
                        navigationId = R.id.action_global_to_fpt_play_reward
                        navigateFun = {
                            fragment?.findNavController()?.navigate(NavHomeMainDirections.actionGlobalToFptPlayReward(
                                title = data.title
                            ))

                        }
                    }
                    MegaMenuItem.ActionType.FptPlayShop -> {
//                        navigationId = R.id.action_global_to_fpt_play_shop
//                        navigateFun = {
//                            navHostFragment?.findNavController()?.navigate(
//                                NavHomeMainDirections.actionGlobalToFptPlayShop(screenType = FPTPlayShopFragment.SCREEN_LIST_ORDER)
//                            )
//
//                        }
                        navigateToFptPlayShop(megaMenuItem = data, extraData = extraData)
                        navigateAlreadyHandled = true
                    }
                    MegaMenuItem.ActionType.Game30s -> {
                        navigationId = R.id.action_global_to_game_play_start_30s
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToGamePlayStart30s()
                            )
                        }
                    }
                    MegaMenuItem.ActionType.GameLSTSB -> {
                        navigationId = R.id.action_global_to_game_mdbd
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToGameMdbd()
                            )
                        }

                    }
                    MegaMenuItem.ActionType.GamePlayOrShare -> {
                        navigationId = R.id.action_global_to_choihaychia
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToChoihaychia()
                            )
                        }
                    }
                    MegaMenuItem.ActionType.HipFest -> {
                        navigationId = R.id.action_global_to_qr_code_ticket
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToQrCodeTicket()
                            )
                        }
                    }
                    MegaMenuItem.ActionType.OmniShop -> {
                        // not setup navigateId, if requiredLogin && user not login -> navigate to login -> navigate back -> not open dialog
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToOmniBottomSheetDialog(
                                    loadListProduct = true
                                )
                            )
                        }
                    }
                    MegaMenuItem.ActionType.OpenWebview -> {
                        if (data.actionValue.isBlank()) {
                            return
                        }
                        // not setup navigateId, if requiredLogin && user not login -> navigate to login -> navigate back -> not doing anything
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToWebViewFragment(
                                    url = data.actionValue,
                                    title = data.title
                                )
                            )
                        }
                    }

                    is MegaMenuItem.ActionType.Unknown -> {
                        // do nothing
                        Timber.e("Mega Item Action undefined ${data.actionType.id} - $data")
                    }
                    is MegaMenuItem.ActionType.MegaApp -> {
                        Timber.e("Mega Item Action MegaApp $data")

//                        navigateToMiniApp(miniAppMegaMenu = data)
//                        showPopupError(message = context.getString(R.string.mini_app_unsupported_error_message))
                        if(data.validateMiniApp()) {
                            megaNavigateViewModel.dispatchIntent(
                                MegaNavigateViewModel.MegaNavigateIntent.GetMiniAppManifest(
                                    url = data.miniAppManifestUrl,
                                    megaMenuItem = data,
                                    deeplinkExtraData = extraData
                                )
                            )
                        } else {
                            showPopupError(message = context.getString(R.string.mini_app_unsupported_error_message))
                        }

                        navigateAlreadyHandled = true

                    }

                    MegaMenuItem.ActionType.EntryGame -> {
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToCategoryDetail(id = data.actionValue)
                            )
                        }
                    }

                    MegaMenuItem.ActionType.UserAudioVideoConfigure -> {

                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToMegaAudioVideoConfigureFragment(
                                    id = data.id,
                                    title = data.title
                                )
                            )
                        }
                    }

                    MegaMenuItem.ActionType.AudioVideoAutoScrollConfigure -> {
                        val currentSelected = Utils.getCurrentAutoScrollStatus(sharedPreferences)
                        sharedPreferences.saveCurrentVideoAutoScrollConfigureSelected(
                            if(currentSelected == AutoScrollConfig.Off) AutoScrollConfig.On.rawValue
                            else AutoScrollConfig.Off.rawValue
                        )
                        Timber.tag("tam-mega").d("After save")
                        Utils.getCurrentAutoScrollStatus(sharedPreferences)
                    }

                    MegaMenuItem.ActionType.UserManageProfile -> {
                        navigationId = R.id.action_global_to_multi_profile
                        extendsArgs = bundleOf(
                            "targetScreen" to StartingTargetScreenType.ManageProfile
                        )

                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToMultiProfile(
                                    targetScreen = StartingTargetScreenType.ManageProfile
                                )
                            )
                        }

                    }

                    MegaMenuItem.ActionType.OpenDeeplink -> {
                        navigationId = DeeplinkUtils.NAVIGATION_ID_DEEP_LINK_HANDLE
                        extendsArgs = bundleOf(
                            DeeplinkUtils.NAVIGATION_LINK_DEEP_LINK_KEY to data.actionValue
                        )
                        navigateFun = {
                            DeeplinkUtils.parseDeepLinkAndExecute(data.actionValue)
                        }
                    }
                    MegaMenuItem.ActionType.Library -> {
                        navigationId = R.id.action_global_to_library
                        extendsArgs = bundleOf(
                            "title" to data.title,
                            "profileId" to sharedPreferences.profileId()
                        )

                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                NavHomeMainDirections.actionGlobalToLibrary(
                                    title = data.title,
                                    profileId = sharedPreferences.profileId()
                                )
                            )
                        }
                    }

                }

                if (!navigateAlreadyHandled) {
                    if (data.requireLogin()) {
                        checkUserLoginBeforeNavigate(
                            navigationId = navigationId,
                            navigateFun = navigateFun,
                            extendsArgs = extendsArgs
                        )

                    } else {
                        if(popupAfterNavigate)
                            fragment?.findNavController()?.navigateUp()
                        navigateFun()
                    }
                }
            }
        }

    }

    fun navigateToDownloadContent(data: CollectionVideoTaskItem) {
        if(data.movieId == MegaBlockDownloadAdapter.DOWNLOADING_ITEM_ID) {
            fragment?.findNavController()?.navigate(
                NavHomeMainDirections.actionGlobalToDownloadV2(
                    targetScreen = StartingTargetScreenV2Type.Download.id,
                    title = data.title
                )
            )
            return
        }

        if(data.isSeries) {
            // Click on series
            fragment?.findNavController()?.navigate(
                NavHomeMainDirections.actionGlobalToDownloadV2(
                    targetScreen = StartingTargetScreenV2Type.DownloadDetail.id,
                    movieId = data.movieId,
                    title = data.title,
                    isAirline = data.listChapters[0].isAirline
                )
            )
            return
        }
        // Click on movie
        if(data.listChapters.isEmpty()) return
        if (data.listChapters[0].taskState == VideoTaskState.SUCCESS) {
            if (DownloadUtils.calculateTimeLeft(
                    data.listChapters[0].lastUpdateTime,
                    data.listChapters[0].expiredTime
                ) > 0
            ) {
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Cast -> {
                        //TODO(): Handle when cast
                        MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                            navHostFragment = navHostFragment,
                            onStopCastAndNavigate = {
                                MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                fragment?.findNavController()?.navigate(
                                    NavHomeMainDirections.actionGlobalToDownloadV2(
                                        targetScreen = StartingTargetScreenV2Type.VodDetailOffline.id,
                                        movieId = data.movieId,
                                        chapterId = data.listChapters[0].chapterId,
                                        fileHash = data.listChapters[0].fileHash ?: ""
                                    )
                                )
                            },
                            onNavigate = {
                                fragment?.findNavController()?.navigate(
                                    NavHomeMainDirections.actionGlobalToDownloadV2(
                                        targetScreen = StartingTargetScreenV2Type.VodDetailOffline.id,
                                        movieId = data.movieId,
                                        chapterId = data.listChapters[0].chapterId,
                                        fileHash = data.listChapters[0].fileHash ?: ""
                                    )
                                )
                            },
                            onCancel = {}
                        )
                    }
                    else -> {
                        fragment?.findNavController()?.navigate(
                            NavHomeMainDirections.actionGlobalToDownloadV2(
                                targetScreen = StartingTargetScreenV2Type.VodDetailOffline.id,
                                movieId = data.movieId,
                                chapterId = data.listChapters[0].chapterId,
                                fileHash = data.listChapters[0].fileHash ?: ""
                            )
                        )

                    }
                }
            }
            else {
                Toast.makeText(context, MainApplication.INSTANCE.appConfig.d2gMsgExpires, Toast.LENGTH_SHORT).show()
            }
        }

    }

    private fun navigateToAirline(airlineBrand: AirlineBrand) {
        val intent = Intent(context, AirlineActivity::class.java)
        intent.putExtra(AirlineActivity.AIRLINE_BRAND_KEY, airlineBrand)
        context.startActivity(intent)
    }

    private fun checkUserLoginBeforeNavigate(
        showWarningDialog: Boolean = false,
        navigationId: Int? = null,
        extendsArgs: Bundle? = null,
        navigateFun: () -> Unit,
    ) {
        if (sharedPreferences.userLogin()) {
            if(popupAfterNavigate)
                fragment?.findNavController()?.navigateUp()
            navigateFun()
        } else {
            if (showWarningDialog) {
                fragment?.run {
                    warningDialogFragment?.dismiss()
                    warningDialogFragment = AlertDialog().apply {
                        setTextTitle(sharedPreferences.getAccountLoginTitle().ifBlank { getString(R.string.login_title_default) })
                        setMessage(sharedPreferences.getAccountLoginMsgRequire().ifBlank { getString(R.string.login_description_default) })
                        setTextExit(sharedPreferences.getAccountLoginBtnNotOk().ifBlank { getString(R.string.login_description_cancel_default) })
                        setTextConfirm(sharedPreferences.getAccountLoginBtnOk().ifBlank { getString(R.string.login_description_confirm_default) })
                        setOnlyConfirmButton(false)
                        setShowTitle(true)
                        setListener(object : AlertDialogListener {
                            override fun onExit() {
                                if(popupAfterNavigate)
                                    fragment.findNavController()?.navigateUp()

                            }
                            override fun onConfirm() { navigateToLogin(navigationId = navigationId, extendsArgs = extendsArgs) }
                            override fun onBackPress() {
                                if (popupAfterNavigate)
                                    fragment.findNavController()?.navigateUp()
                            }
                        })
                    }

                    warningDialogFragment?.show(fragment.childFragmentManager, "WarningDialogLogin")
                }
            } else {
                navigateToLogin(navigationId = navigationId, extendsArgs = extendsArgs)
            }
        }
    }

    private fun showPopupError(message: String, textConfirm: String? = null) {
        fragment?.let {
            alertDialog?.dismiss()
            alertDialog = AlertDialog().apply {
                setShowTitle(true)
                setTextTitle(it.getString(R.string.notification))
                setMessage(message)
                setTextConfirm(textConfirm ?: it.getString(R.string.mini_app_alert_dialog_error_confirm_text)
                )
                setListener(object : AlertDialogListener {
                    override fun onConfirm() {
                        if(popupAfterNavigate) {
                            fragment?.findNavController()?.navigateUp()
                        }
                    }
                })
                isCancelable = false
                setOnlyConfirmButton(true)

            }
            alertDialog?.show(it.childFragmentManager, "PopupErrorApi")

        }

    }
    private fun navigateToLogin(navigationId: Int? = null, extendsArgs: Bundle? = null, requestRestartApp:Boolean = false) {
        if(popupAfterNavigate)
            fragment?.findNavController()?.navigateUp()

        navigationId?.let {
            fragment?.navigateToLoginWithParams(
                isDirect = true,
                navigationId = it,
                extendsArgs = extendsArgs,
                requestRestartApp = requestRestartApp
            )
        } ?: run {
            fragment?.navigateToLoginWithParams(isDirect = true, requestRestartApp = requestRestartApp)
        }
    }

    private fun launchSupportCenter(sharedPreferences: SharedPreferences) {
        Timber.tag("tam-mega").d("navHostFragment $fragment")
        Timber.tag("tam-mega").d("navHostFragment?.lifecycleScope? ${fragment?.lifecycleScope}")
        fragment?.lifecycleScope?.launch(Dispatchers.Main) {
            if (sharedPreferences.userLogin()) {
                ZendeskUtils.initZendeskFullAction(context, sharedPreferences)
                ZendeskUtils.showCenterHelperActiviyWithContextEnableTicketConversation(context)
            } else {
                fragment.navigateToLoginWithParams(
                    isDirect = true,
                    navigationId = R.id.start_zendesk_dialog,
                )
            }
        }
    }


    private fun navigateToMiniApp(miniAppManifest: MegaMiniAppManifest, miniAppMegaMenu: MegaMenuItem, extraData: NavExtraData? = null) {
        val navigationId = R.id.action_global_to_nav_mini_app
        val navigateFun:() -> Unit = {
            fragment?.findNavController()?.navigate(
                NavHomeMainDirections.actionGlobalToNavMiniApp(
                    megaAppId = miniAppMegaMenu.id,
                    title = miniAppManifest.title,
                    url = miniAppManifest.scope.megaApp.sourceUrl ?: "",
                    listFunctionMiniApp = miniAppManifest.scope.megaApp.methods.toTypedArray(),
                    listEventsMiniApp = miniAppManifest.scope.megaApp.events.toTypedArray(),
                    listPermissionsMiniApp = miniAppManifest.permissions.toTypedArray(),
                    deeplinkUrl = extraData?.deeplinkUrl,
                    allowWildCard = miniAppManifest.allowWildCard?.toTypedArray(),
                    closeAppInfo = miniAppManifest.closeAppInfo?.let {
                        CloseInfoAppMiniAppData(
                            title = it.title,
                            message = it.message
                        )
                    },
                    theme = miniAppManifest.theme
                )
            )
        }

        val extendsArgs = bundleOf(
            "megaAppId" to (miniAppMegaMenu.id ?: ""),
            "title" to (miniAppMegaMenu.title ?: ""),
            "url" to (miniAppManifest.scope.megaApp.sourceUrl ?: ""),
            "listFunctionMiniApp" to miniAppManifest.scope.megaApp.methods.toTypedArray(),
            "listEventsMiniApp" to miniAppManifest.scope.megaApp.events.toTypedArray(),
            "deeplinkUrl" to extraData?.deeplinkUrl,
            "closeAppInfo" to miniAppManifest.closeAppInfo?.let {
                CloseInfoAppMiniAppData(
                    title = it.title,
                    message = it.message
                )
            },
            "theme" to miniAppManifest.theme,
        )

        if (miniAppMegaMenu.requireLogin()) {
            checkUserLoginBeforeNavigate(
                navigationId = navigationId,
                navigateFun = navigateFun,
                extendsArgs = extendsArgs
            )

        } else {
            if(popupAfterNavigate)
                fragment?.findNavController()?.navigateUp()
            navigateFun()
        }

    }


    private fun navigateToFptPlayShop(megaMenuItem: MegaMenuItem, extraData: NavExtraData?) {
        // https://dev.fptplay.vn/megazone/fptplayshop
        // https://dev.fptplay.vn/megazone/fptplayshop/danh-sach-don-hang
        // https://dev.fptplay.vn/megazone/fptplayshop/chi-tiet-don-hang/8869
        var navigationId: Int? = null
        var navigateFun: () -> Unit = {}
        var extendsArgs: Bundle? = null
        val deeplink = extraData?.deeplinkUrl
        if(deeplink.isNullOrBlank()) {
            navigationId = R.id.action_global_to_fpt_play_shop
            navigateFun = {
                fragment?.findNavController()?.navigate(
                    NavHomeMainDirections.actionGlobalToFptPlayShop(screenType = FPTPlayShopFragment.SCREEN_LIST_ORDER)
                )

            }
            extendsArgs = bundleOf(
                "screenType" to FPTPlayShopFragment.SCREEN_LIST_ORDER
            )
        } else {
            var uri: Uri? = null
            val deeplinkPath = try {
                uri = Uri.parse(deeplink)
                uri.pathSegments

            } catch(exception: Exception) {
                Timber.tag("tam-mega").e(exception, "parseDeeplink error")
                emptyList()
            }

            val fptplayshopScreen = if (deeplinkPath.size >= 3) deeplinkPath[2] else ""
            when(fptplayshopScreen) {
                "",
                DeeplinkConstants.DEEPLINK__FPTPLAYSHOP__LIST__ORDER -> {
                    // https://fptplay.vn/megazone/fptplayshop
                    // https://fptplay.vn/megazone/fptplayshop/danh-sach-don-hang

                    if(uri?.queryParameterNames?.isNotEmpty() == true) {
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                directions = NavHomeMainDirections.actionGlobalToFptPlayShop(
                                    screenType = FPTPlayShopFragment.SCREEN_LOAD_URL,
                                    url = uri.toString()
                                )
                            )

                        }
                        navigationId = R.id.action_global_to_fpt_play_shop
                        extendsArgs = bundleOf(
                            "screenType" to FPTPlayShopFragment.SCREEN_LOAD_URL,
                            "url" to uri.toString()
                        )
                    } else {
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                directions = NavHomeMainDirections.actionGlobalToFptPlayShop(
                                    screenType = FPTPlayShopFragment.SCREEN_LIST_ORDER
                                )
                            )

                        }
                        navigationId = R.id.action_global_to_fpt_play_shop
                        extendsArgs = bundleOf(
                            "screenType" to FPTPlayShopFragment.SCREEN_LIST_ORDER
                        )
                    }
                }

                DeeplinkConstants.DEEPLINK__FPTPLAYSHOP__DETAIL__ORDER -> {
                    // https://fptplay.vn/megazone/fptplayshop/chi-tiet-don-hang/8869
                    val detailId = if (deeplinkPath.size >= 4) deeplinkPath[3] else ""
                    if(uri?.queryParameterNames?.isNotEmpty() == true) {
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                directions = NavHomeMainDirections.actionGlobalToFptPlayShop(
                                    screenType = FPTPlayShopFragment.SCREEN_LOAD_URL,
                                    url = uri.toString()
                                )
                            )

                        }
                        navigationId = R.id.action_global_to_fpt_play_shop
                        extendsArgs = bundleOf(
                            "screenType" to FPTPlayShopFragment.SCREEN_LOAD_URL,
                            "url" to uri.toString()

                        )
                    } else {
                        navigateFun = {
                            fragment?.findNavController()?.navigate(
                                directions = NavHomeMainDirections.actionGlobalToFptPlayShop(
                                    screenType = FPTPlayShopFragment.SCREEN_DETAIL_ORDER,
                                    detailId = detailId
                                )
                            )

                        }
                        navigationId = R.id.action_global_to_fpt_play_shop
                        extendsArgs = bundleOf(
                            "screenType" to FPTPlayShopFragment.SCREEN_DETAIL_ORDER,
                            "detailId" to detailId

                        )
                    }
                }
                else -> {
                    // not navigate anywhere
                }
            }


        }

        if (megaMenuItem.requireLogin()) {
            checkUserLoginBeforeNavigate(
                navigationId = navigationId,
                navigateFun = navigateFun,
                extendsArgs = extendsArgs
            )

        } else {
            if(popupAfterNavigate)
                fragment?.findNavController()?.navigateUp()
            navigateFun()
        }

    }


    // endregion navigate

    // region LifecycleObserver
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> onCreate(source)
            Lifecycle.Event.ON_START -> onStart()
            Lifecycle.Event.ON_STOP -> onStop()
            Lifecycle.Event.ON_DESTROY -> onDestroy()
            else -> {}
        }
    }

    private fun onCreate(lifecycleOwner: LifecycleOwner) {
        observeData(lifecycleOwner)
    }

    private fun onStart() {}

    private fun onStop() {}

    private fun onDestroy() {}


    private fun observeData(lifecycleOwner: LifecycleOwner) {
        Timber.tag("tam-mega").d("observeData ${this.javaClass.simpleName}")
        if(megaNavigateViewModel.state.hasObservers()) megaNavigateViewModel.resetState()
        megaNavigateViewModel.state.observe(lifecycleOwner) {
            if(!shouldProcessObserve) return@observe
            if(it is MegaNavigateViewModel.MegaNavigateState.Loading) {
                isChecking = !MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile
                fragment?.activity?.apply {
                    if (isChecking) showLoading(megaNavigateViewModel.loadingViewId)
                    else hideLoading(megaNavigateViewModel.loadingViewId)
                }
                return@observe
            }
            isChecking = false
            fragment?.activity?.hideLoading(
                megaNavigateViewModel.loadingViewId
            )
            when(it) {
                is MegaNavigateViewModel.MegaNavigateState.Done -> {

                }
                is MegaNavigateViewModel.MegaNavigateState.Error -> {
                    Timber.tag("tam-mega").e("MegaNavigateState.Error ${this.javaClass.simpleName} $it")
                    if(it.intent is MegaNavigateViewModel.MegaNavigateIntent.GetMiniAppManifest) {
//                        Toast.makeText(context, "Get manifest error", Toast.LENGTH_SHORT).show()
                        showPopupError(message = context.getString(R.string.mini_app_api_error_message))

                    }
                }
                is MegaNavigateViewModel.MegaNavigateState.ErrorRequiredLogin -> {
                    Timber.tag("tam-mega").e("MegaNavigateState.ErrorRequiredLogin ${this.javaClass.simpleName} $it")
                    if(it.intent is MegaNavigateViewModel.MegaNavigateIntent.GetMiniAppManifest) {
                        showPopupError(message = context.getString(R.string.mini_app_api_error_message))
                    }
                }
                is MegaNavigateViewModel.MegaNavigateState.ErrorNoInternet -> {
                    Timber.tag("tam-mega").e("MegaNavigateState.ErrorNoInternet ${this.javaClass.simpleName} $it")
                    if(it.intent is MegaNavigateViewModel.MegaNavigateIntent.GetMiniAppManifest) {
                        showPopupError(message = context.getString(R.string.mini_app_message_error_internet))
                    }
                }
                is MegaNavigateViewModel.MegaNavigateState.ResultMiniAppManifest -> {
                    Timber.tag("tam-mega").d("ResultMiniAppManifest ${it.data}")
                    navigateToMiniApp(it.data, it.megaMenuItem, it.deeplinkExtraData)
                }

                else -> {}
            }
        }
    }
    // endregion LifecycleObserver

    private fun MegaMenuItem.validateMiniApp(): Boolean {
        return (id.isNotBlank()
                && isActive()
                && actionType == MegaMenuItem.ActionType.MegaApp
                && miniAppManifestUrl.isNotBlank()
                )
    }


    //region handle mega app deeplink
    private fun parseDeeplink(deeplink: String?): List<String> {
        // https://dev.fptplay.vn/megazone/<id>/<extra-data>/<extra-data>
        return if(deeplink.isNullOrBlank()) {
            emptyList()
        } else {
            try {
                val uri = Uri.parse(deeplink)
                uri.pathSegments

            } catch(exception: Exception) {
                Timber.tag("tam-mega").e(exception, "parseDeeplink error")
                emptyList()
            }
        }
    }
    private fun deeplinkMegaAppId(deeplinkPath: List<String>): String =  if (deeplinkPath.size >= 2) deeplinkPath[1] else ""

    //endregion handle mega app deeplink

    data class NavExtraData(val deeplinkUrl: String? = null) {
//        class NavMiniAppData(val deeplinkUrl: String? = null): NavExtraData
//        class NavFptPlayShopData(val deeplinkUrl: String? = null): NavExtraData
    }



}