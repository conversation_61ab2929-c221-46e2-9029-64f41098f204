package com.fptplay.mobile.features.sport_interactive_v2

import android.animation.ValueAnimator
import android.app.Dialog
import android.content.DialogInterface
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Surface
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.hilt.navigation.fragment.hiltNavGraphViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseDialogFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DisplayCutoutsHelper
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.SportInteractiveFullScreenV2FragmentDialogBinding
import com.fptplay.mobile.features.sport_interactive.SportInteractiveViewModel
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveFullScreenTabAdapterV2
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveOuterAdapter
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.util.common.IEventListener
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject


@AndroidEntryPoint
class SportInteractiveFullScreenV2DialogFragment :
    BaseDialogFragment<SportInteractiveViewModel.SportInteractiveState, SportInteractiveViewModel.SportInteractiveIntent>() {
    override val viewModel: SportInteractiveViewModel by hiltNavGraphViewModels(R.id.nav_sport_interactive_full_screen)
    override val hasEdgeToEdge = true

    @Inject
    lateinit var cutoutsHelper: DisplayCutoutsHelper

    private var _binding: SportInteractiveFullScreenV2FragmentDialogBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: SportInteractiveFullScreenV2DialogFragmentArgs by navArgs()
    private val fireStoreService by lazy { SportInteractiveFirebaseProxy() }
    private val menuAdapter: SportInteractiveFullScreenTabAdapterV2 by lazy { SportInteractiveFullScreenTabAdapterV2() }
    private val mainAdapter: SportInteractiveOuterAdapter by lazy {
        SportInteractiveOuterAdapter(ScreenContainerType.FULL_SCREEN).apply { setInnerEvenListener(innerAdapterItemClickEvent) }
    }
    private val animateCutoutPaddingDuration = 200L

    private val innerAdapterItemClickEvent by lazy {
        object : IEventListener<UIData> {
            override fun onClickView(position: Int, view: View?, data: UIData) {
                Timber.tag("tam-sport").i("onClickedItem: $data")
                Timber.tag("tam-sport")
                    .i("onClickedItem: ${parentFragment} - ${parentFragment?.parentFragment}")
                if (data is SportInteractiveLiveScoreMatch) {
                    if (data.eventId.isNotBlank()) {
                        when (data.eventType) {
                            SportMatchLiveScores.LiveScore.Match.MatchEventType.Event -> {
                                logChangeLiveShow(data, menuAdapter.getCurrentSelectedItem())
                                navigateToContent(id = data.eventId, type = ItemType.Event)
                            }

                            SportMatchLiveScores.LiveScore.Match.MatchEventType.EventTv -> {
                                logChangeLiveShow(data, menuAdapter.getCurrentSelectedItem())
                                navigateToContent(id = data.eventId, type = ItemType.EventTV)

                            }

                            is SportMatchLiveScores.LiveScore.Match.MatchEventType.Unknown -> {
                                Timber.tag("tam-sport").i("onClickedItem: Unknown type. Do Nothing")
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogWithoutDim)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
            window?.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            )
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        Timber.d("-----onDismiss")
        this.dialog?.window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        setFragmentResult(Constants.SPORT_INTERACTIVE_CLOSE_REQUEST, bundleOf())
        super.onDismiss(dialog)
    }

    override fun onStart() {
        super.onStart()
        dialog?.run {
            window?.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding =
            SportInteractiveFullScreenV2FragmentDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    private var padding: List<Int> = emptyList()
    private var rotation: Int = Surface.ROTATION_0


    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Timber.d("------onconfigchanged")
//        dismiss()
        if(newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            dismiss()
        }
    }

    override fun bindData() {
        bindFireStore(safeArgs.idToPlay)
    }

    override fun bindEvent() {
        initDisplayCutouts()
        binding.clRoot.onClickDelay { findNavController().navigateUp() }
        viewLifecycleOwner.lifecycle.addObserver(cutoutsHelper)
    }

    override fun bindComponent() {
        menuAdapter.eventListener = object : IEventListener<SportInteractiveMenuData> {
            override fun onClickView(position: Int, view: View?, data: SportInteractiveMenuData) {
                if ((binding.rvMain.layoutManager as? LinearLayoutManager)?.findFirstVisibleItemPosition() != position) {
                    binding.rvMain.scrollToPosition(position)
                }
            }

            override fun onSelectedItem(position: Int, data: SportInteractiveMenuData) {
                logAccessModule(data)
            }
        }

        binding.rvMain.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                val currentPosition =
                    (binding.rvMain.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    menuAdapter.updateSelectedPosition(currentPosition)
                    binding.rvMenu.smoothScrollToPosition(currentPosition)
                }
            }
        })

        binding.rvMenu.apply {
            adapter = menuAdapter
            val spacingInPixels = resources.getDimensionPixelSize(R.dimen.sport_interactive_full_screen_tab_layout_item_spacing)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                    when (parent.getChildAdapterPosition(view)) {
                        0 -> { outRect.top = 0 }
                        else -> {
                            outRect.top = spacingInPixels
                        }
                    }
                }
            })
        }

        binding.rvMain.apply {
            layoutManager = object : LinearLayoutManager(binding.root.context, RecyclerView.HORIZONTAL, false) { override fun canScrollHorizontally() = false }
            adapter = mainAdapter
            val snapHelper = PagerSnapHelper()
            onFlingListener = null
            snapHelper.attachToRecyclerView(this)
        }
    }

    private fun initDisplayCutouts() {
        cutoutsHelper.initialize(
            rootView = binding.root,
            windowManager = activity?.windowManager,
            window = activity?.window
        )
        cutoutsHelper.setListener(object : DisplayCutoutsHelper.OnRotateDisplayCutouts {
            override fun onChange(padding: List<Int>, rotation: Int) {
//                <EMAIL> = padding
//                <EMAIL> = rotation
                updatePaddingDisplayCutouts(padding = padding, rotation = rotation)
            }
        })
        Timber.d("------currentcutout: ${safeArgs.paddingStart} - ${safeArgs.paddingEnd} - rotation ${safeArgs.rotation}")
        updatePaddingDisplayCutouts(listOf(safeArgs.paddingStart, safeArgs.paddingEnd, 0, 0), safeArgs.rotation)
    }

    private fun updatePaddingDisplayCutouts(padding: List<Int>, rotation: Int) {
        if(_binding != null) {
            when (rotation) {
                Surface.ROTATION_0, // Bottom - reset the padding in portrait
                Surface.ROTATION_180,
                -> { // Top - reset the padding if upside down
                    binding.apply {
                        guidelineControlSafeStart.setGuidelineBegin(0)
                        guidelineControlSafeEnd.setGuidelineEnd(0)
                    }
                }

                Surface.ROTATION_90, // Left
                Surface.ROTATION_270,
                -> { // Right
                    if (padding.size > 1) {
                        binding.apply {
                            animateGuideline(guidelineControlSafeStart, targetPaddingStart = padding[0])
                            animateGuideline(guidelineControlSafeEnd, targetPaddingEnd = padding[1])
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private fun animateGuideline(
        guideline: Guideline,
        targetPaddingStart: Int? = null,
        targetPaddingEnd: Int? = null
    ) {
        try {
            val layoutParams = (guideline.layoutParams as ConstraintLayout.LayoutParams)

            when {
                targetPaddingStart != null -> {
                    val animator = ValueAnimator.ofInt(layoutParams.guideBegin, targetPaddingStart)
                    animator?.addUpdateListener { valueAnimator ->
                        guideline.setGuidelineBegin((valueAnimator.animatedValue as? Int) ?: 0)
                    }
                    animator.duration = animateCutoutPaddingDuration
                    animator.start()
                }

                targetPaddingEnd != null -> {
                    val animator = ValueAnimator.ofInt(layoutParams.guideEnd, targetPaddingEnd)
                    animator?.addUpdateListener { valueAnimator ->
                        guideline.setGuidelineEnd((valueAnimator.animatedValue as? Int) ?: 0)
                    }
                    animator.duration = animateCutoutPaddingDuration
                    animator.start()
                }

                else -> {}
            }
        } catch (ex: Exception) {

        }
    }

    private var isFireStoreFirstInit = true
    private fun bindFireStore(eventId: String) {
        fireStoreService.listenSportEventData(
            lifecycleOwner = viewLifecycleOwner,
            eventId = eventId
        ) {
            Timber.tag("tam-sport").e("SportInteractiveData $it")
            menuAdapter.bind(it?.titlesData) {
                if (isFireStoreFirstInit) {
                    logAccessModule(it?.titlesData?.firstOrNull())
                    isFireStoreFirstInit = false
                }
            }
            mainAdapter.bind(it?.toList()) {
                val curPosition = getCurrentContentDisplayPosition()
                Timber.d("*****are change tab: curtab:$curPosition - toPosition: ${menuAdapter.getCurrentSelectedPosition()}")
                if (curPosition != RecyclerView.NO_POSITION) {
                    binding.rvMain.scrollToPosition(menuAdapter.getCurrentSelectedPosition())
                }
            }
        }
    }

    private fun getCurrentContentDisplayPosition(): Int {
        return if (_binding != null) (binding.rvMain.layoutManager as? LinearLayoutManager)?.findFirstVisibleItemPosition()
            ?: RecyclerView.NO_POSITION else RecyclerView.NO_POSITION
    }

    private fun logChangeLiveShow(
        data: SportInteractiveLiveScoreMatch? = null,
        currentMenu: SportInteractiveMenuData?
    ) {
        data?.let { matchData ->
            val bundle: Bundle = bundleOf().apply {
                putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, "ChangeLiveshow")
                putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "ChangeLiveshow")
                putString(Constants.SPORT_INTERACTIVE_LOG_ITEM_NAME, matchData.eventId)
            }
            TrackingUtil.screen = TrackingUtil.screenRelated
            setFragmentResult(
                Constants.SPORT_INTERACTIVE_LOG_KIBANA,
                bundle
            )
        }
    }

    private fun logAccessModule(currentMenu: SportInteractiveMenuData?) {
        val bundle: Bundle = bundleOf().apply {
            putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, currentMenu?.logName ?: "")
            putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "AccessFunction")
        }
        setFragmentResult(
            Constants.SPORT_INTERACTIVE_LOG_KIBANA,
            bundle
        )
    }

    private fun navigateToContent(id: String, type: ItemType) {
        val bundle = bundleOf(
            Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_CONTENT_ID to id
        )
        bundle.putString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_ITEM_TYPE, type.name)
        setFragmentResult(
            Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_KEY,
            bundle
        )
        dismiss()
    }


    override fun onDestroyView() {
        super.onDestroyView()
        cutoutsHelper.setListener(null)
        viewLifecycleOwner.lifecycle.removeObserver(cutoutsHelper)
        _binding = null
    }

    override fun SportInteractiveViewModel.SportInteractiveState.toUI() {

    }
}