package com.fptplay.mobile.features.user_realtime_playing

import android.os.Handler
import android.os.Looper
import com.tear.modules.player.util.IPlayer
import com.xhbadxx.projects.module.util.logger.Logger
import javax.inject.Inject

class UserRealtimePlayingTracker @Inject constructor() {

    val TAG = this::class.java.simpleName

    companion object {
        const val INTERVAL_TIME = 1000L
    }

    val playerEvents get() = playerCallback

    private var contentId: String = ""
    private var extraId: String = ""
    private val listeners = mutableListOf<UserRealtimePlayingTrackerListener>()
    private var haveChangeContent = false

    private var _timeWatched = 0L
    private var handlerTimeWatch: Handler? = null
    private var runnableTimeWatch = Runnable {
        calculateTimeWatch()
    }

    private var playerIsPlaying = true
    private var isReleaseSource = false

    fun setData(contentId: String, extraId: String) {
        //
        haveChangeContent =  checkHaveChangeContent(contentId = contentId, extraId = extraId)
        //
        this.contentId = contentId
        this.extraId = extraId
    }

    fun clearData() {
        contentId = ""
        extraId = ""
    }

    /**
     * Get real time playing of current movie
     */
    fun getRealtimePlaying():Long{
        return _timeWatched
    }

    /**
     * Get real time playing of movie with contentId and extraId
     */
    fun getRealTimePlaying(contentId: String? = null, extraId: String? = null): Long {
        Logger.d("logid getRealTimePlaying = $contentId ==== ${this.contentId}")
        Logger.d("logid getRealTimePlaying = $extraId ==== ${this.extraId}")
        return if (contentId != null && extraId != null) {
            if (checkHaveChangeContent(contentId = contentId, extraId = extraId)) {
                0
            } else {
                _timeWatched
            }
        } else {
            _timeWatched
        }
    }

    //region Callback
    fun addRealtimePlayingTrackingCallback(listener: UserRealtimePlayingTrackerListener) {
        synchronized(listeners) {
            if (!listeners.contains(listener)) {
                listeners.add(listener)
            }
        }
    }

    fun removeRealtimePlayingTrackingCallback(listener: UserRealtimePlayingTrackerListener) {
        synchronized(listeners) {
            if (listeners.contains(listener)) {
                listeners.remove(listener)
            }
        }
    }
    //endregion


    //region Handle -> Realtime playing
    private fun checkHaveChangeContent(contentId: String, extraId: String) : Boolean {
        return this.contentId != contentId || this.extraId != extraId
    }

    private val playerCallback = object : IPlayer.IPlayerCallback {

        override fun onBuffering() {
            pauseCalculateRealtimePlaying()
        }

        override fun onReady() {
            if (contentId.isNotEmpty() && extraId.isNotEmpty()) {
                isReleaseSource = false
                if (haveChangeContent) {
                    haveChangeContent = false
                    _timeWatched = 0
                    startCalculateRealtimePlaying()
                } else {
                    resumeCalculateRealtimePlaying()
                }
                //
                playerIsPlaying = true
            }
        }

        override fun onStart() {
            if (contentId.isNotEmpty() && extraId.isNotEmpty()) {
                resumeCalculateRealtimePlaying()
            }
        }

        override fun onPause() {
            pauseCalculateRealtimePlaying()
            //
            playerIsPlaying = false
        }

        override fun onPlay() {
            if (contentId.isNotEmpty() && extraId.isNotEmpty()) {
                if (!isReleaseSource) {
                    resumeCalculateRealtimePlaying()
                    //
                    playerIsPlaying = true
                }
            }
        }

        override fun onRelease() {
            isReleaseSource = true
            pauseCalculateRealtimePlaying()
        }

        override fun onError(
            code: Int,
            name: String,
            detail: String,
            error403: Boolean,
            responseCode: Int
        ) {
            pauseCalculateRealtimePlaying()
        }

        override fun onErrorCodec(
            code: Int,
            name: String,
            detail: String,
            responseCode: Int,
            isDrm: Boolean,
            codec: IPlayer.CodecType
        ) {
            pauseCalculateRealtimePlaying()
        }

        override fun onError6006WhenPreview(code: Int, name: String, detail: String, responseCode: Int) {
            pauseCalculateRealtimePlaying()
        }

        override fun onError6006(code: Int, name: String, detail: String) {
            pauseCalculateRealtimePlaying()
        }

        override fun onStop() {
            stopCalculateRealtimePlaying()
        }

        override fun onEnd() {
            stopCalculateRealtimePlaying()
        }
    }

    private fun startCalculateRealtimePlaying() {
        stopCalculateRealtimePlaying()
        if (handlerTimeWatch == null) {
            Looper.getMainLooper()?.run { handlerTimeWatch = Handler(this) }
        }
        handlerTimeWatch?.postDelayed(runnableTimeWatch, INTERVAL_TIME)
        //
        listeners.forEach { listener -> listener.onStartRealtimePlayingTracker() }
        //
    }

    private fun pauseCalculateRealtimePlaying() {
        handlerTimeWatch?.removeCallbacks(runnableTimeWatch)
        //
        listeners.forEach { listener -> listener.onPauseRealtimePlayingTracker() }
        //
    }

    private fun resumeCalculateRealtimePlaying() {
        handlerTimeWatch?.removeCallbacks(runnableTimeWatch)
        handlerTimeWatch?.postDelayed(runnableTimeWatch, INTERVAL_TIME)
        //
        listeners.forEach { listener -> listener.onResumeRealtimePlayingTracker() }
        //
    }

    private fun stopCalculateRealtimePlaying() {
        handlerTimeWatch?.removeCallbacks(runnableTimeWatch)
        //
        listeners.forEach { listener -> listener.onStopRealtimePlayingTracker() }
        //
    }

    private fun calculateTimeWatch() {
        if (playerIsPlaying) {
            _timeWatched += 1
            Logger.d("$TAG => CalculateTimeWatch => Time = $_timeWatched")
            //
            triggerNotify()
            handlerTimeWatch?.postDelayed(runnableTimeWatch, INTERVAL_TIME)
        }
    }

    private fun triggerNotify() {
        listeners.forEach { listener ->
            //
            listener.onRealtimePlayingChanged(timeWatched = _timeWatched)
            //
            val checkPoints = listener.getCheckpointForRealtimeTracking()
            if (checkPoints.isNotEmpty()) {
                checkPoints.forEach { checkPoint ->
                    if (_timeWatched == checkPoint) {
                        listener.onRealtimePlayingAtCheckpoint(timeWatched = _timeWatched, checkPoint = checkPoint)
                    }
                }
            }

        }
    }
    //endregion

    interface UserRealtimePlayingTrackerListener {
        fun onRealtimePlayingChanged(timeWatched: Long) // Second
        fun onRealtimePlayingAtCheckpoint(timeWatched: Long, checkPoint: Long) // Second, Second
        fun getCheckpointForRealtimeTracking(): List<Long> // List mark time need tracking (Second)
        //
        fun onStartRealtimePlayingTracker() {}
        fun onPauseRealtimePlayingTracker() {}
        fun onResumeRealtimePlayingTracker() {}
        fun onStopRealtimePlayingTracker() {}
        //
    }
}