package com.fptplay.mobile.features.sport_interactive_v2.cook_data

import com.fptplay.mobile.R
import com.fptplay.mobile.features.sport_interactive.model.SportInteractiveData
import com.fptplay.mobile.features.sport_interactive.model.SportMatchDetail
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive.model.SportMatchProcess
import com.fptplay.mobile.features.sport_interactive.model.SportMatchStatistic
import com.fptplay.mobile.features.sport_interactive.model.SportTeamSquad
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveConstants
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveGeneralData
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveNoData
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveHeaderData
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveSeparatorData
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreLeague
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.fptplay.mobile.features.sport_interactive_v2.models.match_process.SportInteractiveMatchProcessItem
import com.fptplay.mobile.features.sport_interactive_v2.models.match_process.SportInteractiveMatchProcessTitle
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadBlock
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadItem
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadPage
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTabHeader
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTitle
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTitleTeam
import com.fptplay.mobile.features.sport_interactive_v2.models.statistic.SportInteractiveStatisticInfoData
import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData

object SportInteractiveDataMapper {

    //region common
    fun SportMatchDetail.toHeaderData(useShortName: Boolean = false): SportInteractiveHeaderData? {
        val homeName = if (useShortName) info.homeShortName else info.homeTeamName
        val awayName = if (useShortName) info.awayShortName else info.awayTeamName
        return if (homeName.isBlank() || awayName.isBlank()) {
            null
        } else {
            SportInteractiveHeaderData(
                info = SportInteractiveHeaderData.SportInteractiveHeader(
                    time = info.time,

                    homeTeamName = info.homeTeamName,
                    homeCoach = info.homeCoach,
                    homeColor = info.homeColor,
                    homeLogo = info.homeLogo,
                    homeScore = info.homeScore,
                    homeShortName = info.homeShortName,

                    awayTeamName = info.awayTeamName,
                    awayCoach = info.awayCoach,
                    awayColor = info.awayColor,
                    awayLogo = info.awayLogo,
                    awayScore = info.awayScore,
                    awayShortName = info.awayShortName
                ),
                listScores = listScores.map { item ->
                    SportInteractiveHeaderData.SportInteractiveScoredAuthors(
                        teamType = item.teamType,
                        playerName = item.playerName,
                        time = item.time
                    )
                }
            )
        }
    }
    //endregion

    //region statics
    fun SportMatchStatistic.toSportInteractiveUIData(header: SportInteractiveHeaderData?): SportInteractiveGeneralData {
        val listData: ArrayList<UIData> = arrayListOf()

        header?.let { headerData ->
            listData.add(headerData.copy(headerType = SportInteractiveHeaderData.HeaderType.WITH_SCORE))

            listData.add(SportInteractiveSeparatorData)
            if (listStats.isEmpty()) {
                listData.add(SportInteractiveNoData())
            } else {
                listStats.forEach { item ->
                    listData.add(item.toSportStatisticInfoData())
                }
            }
        } ?: run {
            listData.add(SportInteractiveNoData())
        }

        return SportInteractiveGeneralData(
            title = title,
            data = listData
        )
    }

    fun SportMatchStatistic.Statistic.toSportStatisticInfoData(): SportInteractiveStatisticInfoData {
        return SportInteractiveStatisticInfoData(
            actionType = actionType,
            actionName = actionName,
            homeTeamScore = homeTeamScore,
            awayTeamScore = awayTeamScore
        )
    }
    //endregion

    //region match process
    fun SportMatchProcess.toSportInteractiveUIData(header: SportInteractiveHeaderData?): SportInteractiveGeneralData {
        val listMatchProcess: ArrayList<UIData> = arrayListOf()

        header?.let { headerData ->
            listMatchProcess.add(headerData.copy(headerType = SportInteractiveHeaderData.HeaderType.WITHOUT_SCORE))

            if (listRounds.isEmpty()) {
                listMatchProcess.add(SportInteractiveSeparatorData)
                listMatchProcess.add(SportInteractiveNoData())
            } else {
                listRounds.map { round ->
                    listMatchProcess.add(SportInteractiveSeparatorData)
                    listMatchProcess.add(SportInteractiveMatchProcessTitle(round.title))
                    round.processList.map { roundItem ->
                        listMatchProcess.add(
                            roundItem.toSportInteractiveMatchProcessItem()
                        )
                    }
                }
            }
        } ?: run {
            listMatchProcess.add(SportInteractiveNoData())
        }


        return SportInteractiveGeneralData(
            title = title,
            data = listMatchProcess
        )
    }

    fun SportMatchProcess.Round.Process.toSportInteractiveMatchProcessItem(): SportInteractiveMatchProcessItem {
        return SportInteractiveMatchProcessItem(
            playerName = playerName,
            transferPlayerName = transferPlayerName,
            description = description,
            teamType = teamType,
            action = action,
            time = time,
        )
    }
    //endregion

    //region squad
    fun SportTeamSquad.toSportInteractiveUIData(header: SportInteractiveHeaderData?): SportInteractiveGeneralData {
        val squadPageData: ArrayList<UIData> = arrayListOf()

        header?.let { headerData ->
            val squadList: ArrayList<UIData> = arrayListOf()
            squadList.add(SportInteractiveSquadTabHeader(data = headerData.copy(headerType = SportInteractiveHeaderData.HeaderType.TAB_STYLE)))

            val homeSquadIDList = arrayListOf<String>()
            val awaySquadIDList = arrayListOf<String>()
            homeTeamSquads.forEach { squad ->
                if (!homeSquadIDList.contains(squad.id)) {
                    homeSquadIDList.add(squad.id)
                }
            }

            awayTeamSquads.forEach { squad ->
                if (!awaySquadIDList.contains(squad.id)) {
                    awaySquadIDList.add(squad.id)
                }
            }

            val homeTitleTeam = SportInteractiveSquadTitleTeam(
                fullTitle = header.info.homeTeamName,
                coachName = header.info.homeCoach
            )
            val awayTitleTeam = SportInteractiveSquadTitleTeam(
                fullTitle = header.info.awayTeamName,
                coachName = header.info.awayCoach
            )
            val homeBlocks = if (homeSquadIDList.size == 0) {
                listOf(
                    homeTitleTeam,
                    SportInteractiveSeparatorData,
                    SportInteractiveNoData()
                )
            } else {

                val blockItems = mutableListOf<UIData>()
                blockItems.add(homeTitleTeam)

                homeSquadIDList.forEach { squadId ->
                    val squadTitle = (homeTeamSquads.getSquadData(squadId)?.title ?: "")

                    // Add separator
                    blockItems.add(SportInteractiveSeparatorData)
                    //

                    if (squadTitle.isNotBlank()) { //Check if squad have title or not => Show view or not
                        blockItems.add(
                            SportInteractiveSquadTitle(squadTitle)
                        )
                    }

                    val homeSquadList =
                        homeTeamSquads.getSquadData(squadId)?.processList ?: emptyList()
                    for (squadMemberIndex in homeSquadList.indices) {
                        blockItems.add(
                            SportInteractiveSquadItem(
                                member = homeSquadList.getOrNull(
                                    squadMemberIndex
                                ).toSportInteractiveSquadMember()
                            )
                        )
                    }
                }
                //
                blockItems
            }

            val awayBlocks = if (awaySquadIDList.size == 0) {
                listOf(
                    awayTitleTeam,
                    SportInteractiveSeparatorData,
                    SportInteractiveNoData()
                )
            } else {

                val blockItems = mutableListOf<UIData>()
                blockItems.add(awayTitleTeam)

                awaySquadIDList.forEach { squadId ->
                    val squadTitle = (awayTeamSquads.getSquadData(squadId)?.title ?: "")

                    // Add separator
                    blockItems.add(SportInteractiveSeparatorData)
                    //

                    if (squadTitle.isNotBlank()) { //Check if squad have title or not => Show view or not
                        blockItems.add(
                            SportInteractiveSquadTitle(squadTitle)
                        )
                    }

                    val awaySquadList =
                        awayTeamSquads.getSquadData(squadId)?.processList ?: emptyList()
                    for (squadMemberIndex in awaySquadList.indices) {
                        blockItems.add(
                            SportInteractiveSquadItem(
                                member = awaySquadList.getOrNull(
                                    squadMemberIndex
                                ).toSportInteractiveSquadMember()
                            )
                        )
                    }
                }

                blockItems
            }

            squadList.add(SportInteractiveSquadBlock(blocks = listOf(homeBlocks, awayBlocks)))
            //
            squadPageData.add(SportInteractiveSquadPage(data = squadList))
        } ?: run {
            squadPageData.add(SportInteractiveNoData())
        }

        //
        return SportInteractiveGeneralData(
            title = title,
            data = squadPageData
        )
    }

    private fun List<SportTeamSquad.Squad>.getSquadData(id: String): SportTeamSquad.Squad? {
        return this.firstOrNull { it.id == id }
    }

    fun SportTeamSquad.Squad.SquadMember?.toSportInteractiveSquadMember(): SportInteractiveSquadItem.SportInteractiveSquadMember {
        return this?.let { squadMember ->
            SportInteractiveSquadItem.SportInteractiveSquadMember(
                playerName = squadMember.playerName,
                playerNumber = squadMember.playerNumber,
                playerTime = squadMember.playerTime,
                actions = squadMember.actions
            )
        } ?: SportInteractiveSquadItem.SportInteractiveSquadMember()
    }

    //region livescore
    fun SportMatchLiveScores.toSportInteractiveUIData(): SportInteractiveGeneralData {
        val listData: ArrayList<UIData> = arrayListOf()
        if (listLiveScores.isEmpty()) {
            listData.add(SportInteractiveNoData())
        } else {
            listLiveScores.map { liveScore ->
                if (liveScore.leagueName.isNotBlank()) {
                    val league = SportInteractiveLiveScoreLeague(
                        id = liveScore.id,
                        leagueName = liveScore.leagueName,
                        leagueDescription = liveScore.leagueDescription,
                        leagueLogo = liveScore.leagueLogo
                    )

                    val leagueMatches = arrayListOf<UIData>()
                    liveScore.listMatches.map { match ->
                        match.toSportInteractiveLiveScoreMatch()?.let { data ->
                            leagueMatches.add(data)
                        }
                    }

                    if (leagueMatches.isNotEmpty()) {
                        listData.add(league)
                        listData.addAll(leagueMatches)
                    }
                }
            }

            if (listData.isEmpty()) {
                listData.add(SportInteractiveNoData())
            }
        }
        return SportInteractiveGeneralData(
            title = title,
            data = listData
        )
    }

    fun SportMatchLiveScores.LiveScore.Match.toSportInteractiveLiveScoreMatch(): SportInteractiveLiveScoreMatch? {
        return if (
            (homeTeamShortName.isBlank() && homeTeamName.isBlank()) ||
            (awayTeamShortName.isBlank() && awayTeamName.isBlank())
        ) {
            null
        } else {
            SportInteractiveLiveScoreMatch(
                time = time,
                eventId = eventId,
                eventType = eventType,
                id = id,

                homeTeamName = homeTeamName,
                homeTeamShortName = homeTeamShortName,
                homeTeamLogo = homeTeamLogo,
                homeTeamScore = homeTeamScore,

                awayTeamName = awayTeamName,
                awayTeamShortName = awayTeamShortName,
                awayTeamScore = awayTeamScore,
                awayTeamLogo = awayTeamLogo,
            )
        }
    }

    fun SportInteractiveData?.toListMenuData(): List<SportInteractiveMenuData> {
        val listTabItems = arrayListOf<SportInteractiveMenuData>()
        listTabItems.add(
            SportInteractiveMenuData(
                title = this?.statistic?.title?.ifBlank { SportInteractiveConstants.TAB_STATISTICS_TITLE_DEFAULT }
                    ?: SportInteractiveConstants.TAB_STATISTICS_TITLE_DEFAULT,
                id = "",
                iconId = R.drawable.sport_interactive_statistics_ic,
                logName = "Metrics"
            )
        )
        listTabItems.add(
            SportInteractiveMenuData(
                title = this?.matchProcess?.title?.ifBlank { SportInteractiveConstants.TAB_MATCH_PROCESS_TITLE_DEFAULT }
                    ?: SportInteractiveConstants.TAB_MATCH_PROCESS_TITLE_DEFAULT,
                iconId = R.drawable.sport_interactive_match_process_ic,
                id = "",
                logName = "Progress"
            )
        )
        listTabItems.add(
            SportInteractiveMenuData(
                title = this?.squad?.title?.ifBlank { SportInteractiveConstants.TAB_SQUAD_TITLE_DEFAULT }
                    ?: SportInteractiveConstants.TAB_SQUAD_TITLE_DEFAULT,
                iconId = R.drawable.sport_interactive_squad_ic,
                id = "",
                logName = "Lineup"
            )
        )
        listTabItems.add(
            SportInteractiveMenuData(
                title = this?.liveScore?.title?.ifBlank { SportInteractiveConstants.TAB_LIVE_SCORE_TITLE_DEFAULT }
                    ?: SportInteractiveConstants.TAB_LIVE_SCORE_TITLE_DEFAULT,
                iconId = R.drawable.sport_interactive_live_score_ic,
                id = "",
                logName = "Livescore"
            )
        )
        return listTabItems
    }


}