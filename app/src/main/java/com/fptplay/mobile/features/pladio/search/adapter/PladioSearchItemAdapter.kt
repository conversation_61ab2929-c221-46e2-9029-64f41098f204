package com.fptplay.mobile.features.pladio.search.adapter
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.StringUtils
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.playback.PlaybackPlayerRemote
import com.fptplay.mobile.features.pladio.search.entity.SearchPladioSuggestItem
import com.fptplay.mobile.features.pladio.search.entity.FilterOption
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.search.Search
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchSuggest
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger

class PladioSearchItemAdapter(private val context: Context, private val type: Type) :
    BaseAdapter<BaseObject, PladioSearchItemAdapter.PladioSearchItemViewHolder>(),
    DefaultLifecycleObserver {
    private val thumbWidth by lazy { bindThumbWidth() }
    private val thumbHeight by lazy { bindThumbHeight() }
    private val placeholder by lazy { bindPlaceHolder() }
    var playingPosition = -1
    var isPlaying = false
    private fun bindThumbWidth(): Int {
        return when (type) {
            Type.SquareSliderMedium-> {
                getDimen(R.dimen.pladio_search_block_recent_topic_item_width)
            }
            Type.SquareSliderSmallOneColumnItems-> {
                getDimen(R.dimen.pladio_detail_music_item_image_size)
            }
            Type.RESULT-> {
                getDimen(R.dimen.pladio_detail_music_item_image_size)
            }
            Type.LOADING_RESULT-> {
                getDimen(R.dimen.pladio_detail_music_item_image_size)
            }
            else->{
                0
            }
        }
    }
    private fun bindThumbHeight(): Int {
        return when (type) {
            Type.SquareSliderMedium-> {
                getDimen(R.dimen.pladio_search_block_recent_topic_thumb_height)
            }
            Type.SquareSliderSmallOneColumnItems-> {
                getDimen(R.dimen.pladio_detail_music_item_image_size)
            }
            Type.RESULT-> {
                getDimen(R.dimen.pladio_detail_music_item_image_size)
            }
            Type.LOADING_RESULT-> {
                getDimen(R.dimen.pladio_detail_music_item_image_size)
            }
            else->{
                0
            }
        }
    }

    private fun bindPlaceHolder(): Int {
        return when (type) {
            Type.SquareSliderMedium-> {
                R.drawable.placeholder_pladio_search_block_horizontal_item
            }
            Type.SquareSliderSmallOneColumnItems-> {
                R.drawable.placeholder_pladio_search_block_podcast_item
            }
            Type.RESULT-> {
                R.drawable.placeholder_pladio_search_block_podcast_item
            }
            Type.LOADING_RESULT-> {
                R.drawable.placeholder_pladio_search_block_podcast_item
            }
            else->{
                R.drawable.placeholder_pladio_search_block_podcast_item
            }
        }
    }

    private var currentPlayingSong: Song? = null
    fun updateCurrentPlayingSong(song: Song?, songPosition: Int = RecyclerView.NO_POSITION) {
        Logger.d("DebugHighLight > PladioSearchItemAdapter > current playing pos: $playingPosition updateCurrentPlayingSong: $song")
        currentPlayingSong = song
        val newPlayingPos = if(songPosition != RecyclerView.NO_POSITION && songPosition in data().indices) songPosition else this.data().indexOfFirst { it.id == song?.id }

        if(newPlayingPos != RecyclerView.NO_POSITION) Logger.d("DebugNotClear > PladioSearchItemAdapter > find id: ${song?.id} - newPosSongId: ${this.data().get(newPlayingPos).id}")
        Logger.d("DebugNotClear > PladioSearchItemAdapter > current playing pos: $playingPosition - new pos: $newPlayingPos firstItemId: ${this.data().firstOrNull()?.id}")

        if (newPlayingPos == RecyclerView.NO_POSITION) {
            if(playingPosition != RecyclerView.NO_POSITION) notifyItemChanged(playingPosition)
            playingPosition = newPlayingPos
            return
        }
        val oldPlayingPosition = playingPosition
        if (oldPlayingPosition != RecyclerView.NO_POSITION) notifyItemChanged(oldPlayingPosition)

        playingPosition = newPlayingPos
        notifyItemChanged(newPlayingPos)
    }

    inner class PladioSearchItemViewHolder(private val viewBinding: PladioBlockViewBinding) :
        RecyclerView.ViewHolder(viewBinding.root) {
        var data: BaseObject? = null

        init {
            viewBinding.root.apply {
                onClickDelay {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        data = item(absoluteAdapterPosition) ?: object : BaseObject() {})
                }
            }
            viewBinding.btnClose?.onClickDelay {
                eventListener?.onClickView(
                    position = absoluteAdapterPosition,
                    view = viewBinding.btnClose,
                    data = item(absoluteAdapterPosition) ?: object : BaseObject() {})
            }
        }

        /**
         * @param isPlayChange: true: song is playing, false: song is pause
         *
         */
        private fun setPladioItemPlaying(itemData: BaseObject, currentPlayingSong: Song?, isPlaying: Boolean){
            Logger.d("DebugHighLight > PladioSearchItemAdapter > setPladioItemPlaying: ${itemData.id}")
             if (type in listOf(Type.SquareSliderSmallOneColumnItems, Type.TRENDING_KEYWORD, Type.RESULT)) {
                 val context = viewBinding.root.context
                 val defaultColor = context.getColor(R.color.pladio_detail_item_title_default_color)
                 val playingColor = context.getColor(R.color.pladio_detail_item_title_playing_color)
                 val isDefaultState = when (itemData) {
                     is StructureItem -> {
                         itemData.id != currentPlayingSong?.id
                     }

                     is Search -> {
                         itemData.id != currentPlayingSong?.id
                     }

                     else -> {
                         true
                     }
                 }
                 Logger.d("DebugHighLight > PladioSearchItemAdapter > setPladioItemPlaying: itemName: ${(itemData as? StructureItem)?.title} isDefaultState: $isDefaultState")
                 Logger.d("DebugHighLight > PladioSearchItemAdapter > setPladioItemPlaying: item id: ${itemData.id} currentPlayingSong id: ${currentPlayingSong?.id}")
                 val titleColor = if (isDefaultState) defaultColor else playingColor
                 val playingVisibility = if (isDefaultState) View.GONE else { View.VISIBLE }
                 viewBinding.tvTitle?.setTextColor(titleColor)
                 viewBinding.ivPlaying?.apply {
                     visibility = playingVisibility
                     if (isDefaultState) {
                         cancelAnimation()
                     } else {
                         progress = 0f
                         if (isPlaying) resumeAnimation() else pauseAnimation()
                     }
                 }
             }
         }
        fun bind(data: BaseObject) {
            this.data = data
            if (data is StructureItem) {
                val image = data.squareImage
                if (image.isBlank()) {
                    ImageProxy.loadLocal(
                        context = viewBinding.root.context,
                        data = image ?: "",
                        width = thumbWidth,
                        height = thumbHeight,
                        target = viewBinding.ivThumb,
                        placeHolderId = placeholder,
                        errorDrawableId = placeholder
                    )
                } else {
                    ImageProxy.load(
                        context = viewBinding.root.context,
                        url = image ?: "",
                        width = thumbWidth,
                        height = thumbHeight,
                        target = viewBinding.ivThumb,
                        placeHolderId = placeholder,
                        errorDrawableId = placeholder
                    )
                }
                viewBinding.tvTitle?.text = data.title
                when (type) {
                    Type.SquareSliderSmallOneColumnItems-> {
                        viewBinding.tvSinger?.isVisible = data.detail.metaData.isNotEmpty()
                        viewBinding.tvSinger?.text =  StringUtils.getMetaSeachHomeText(context, data)
                    }
                    else -> {
                    }
                }

                viewBinding.posterOverlay?.let {
                    it.bindData(
                        itemData = data.posterOverlayGroup,
                        blockStyle = BlockStyle.SquareSliderMedium,
                    )
                }
            }
            if (data is SearchPladioSuggestItem) {
                val image = data.horizontalImage
                if (image.isNullOrBlank()) {
                    ImageProxy.loadLocal(
                        context = viewBinding.root.context,
                        data = image ?: "",
                        width = thumbWidth,
                        height = thumbHeight,
                        target = viewBinding.ivThumb,
                        placeHolderId = placeholder,
                        errorDrawableId = placeholder
                    )
                } else {
                    ImageProxy.load(
                        context = viewBinding.root.context,
                        url = image ?: "",
                        width = thumbWidth,
                        height = thumbHeight,
                        target = viewBinding.ivThumb,
                        placeHolderId = placeholder,
                        errorDrawableId = placeholder
                    )
                }
                viewBinding.tvTitle?.text = data.name
                when (type) {
                    Type.SquareSliderMedium-> {
                        viewBinding.tvSinger?.text = data.description
                        viewBinding.tvDuration?.text = data.durationTotal
                    }
                    else -> {

                    }
                }
            }
            if(data is SearchSuggest){
                viewBinding.tvTitle?.text = data.title
            }
            if(data is Search){
                val image =data.imgSquare
                if (image.isBlank()) {
                    ImageProxy.loadLocal(
                        context = viewBinding.root.context,
                        data = image ?: "",
                        width = thumbWidth,
                        height = thumbHeight,
                        target = viewBinding.ivThumb,
                        placeHolderId = placeholder,
                        errorDrawableId = placeholder
                    )
                } else {
                    ImageProxy.load(
                        context = viewBinding.root.context,
                        url = image ?: "",
                        width = thumbWidth,
                        height = thumbHeight,
                        target = viewBinding.ivThumb,
                        placeHolderId = placeholder,
                        errorDrawableId = placeholder
                    )
                }
                viewBinding.tvTitle?.text = data.title
                viewBinding.tvSinger?.isVisible = data.metadata.isNotEmpty()
                viewBinding.tvSinger?.text = StringUtils.getMetaTextSearchPladio(context, data)
            }
            if(data is FilterOption) {
                if (data.type == "close"){
                    viewBinding.btnClose?.isVisible = true
                    viewBinding.filterButton?.isVisible = false

                }
                else{
                    viewBinding.btnClose?.isVisible = false
                    viewBinding.filterButton?.apply {
                        isVisible = true
                        text = data.name
                        isSelected = data.isSelected
                    }
                }
            }
            Logger.d("DebugHighlight > PladioSearchItemAdapter > bind: $data")
            setPladioItemPlaying(itemData = data, currentPlayingSong = currentPlayingSong, isPlaying = PlaybackPlayerRemote.isPlaying)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PladioSearchItemViewHolder {
        return when (type) {
            Type.SquareSliderMedium -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(
                                R.layout.pladio_search_recent_block_horizontal_item,
                                parent,
                                false
                            )
                    )
                )
            }
            Type.SquareSliderSmallOneColumnItems -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_album, parent, false)
                    )
                )
            }
            Type.TRENDING_BLOCK_TEXT -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_keyword, parent, false)
                    )
                )
            }
            Type.SUGGEST_KEYWORD -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_keyword, parent, false)
                    )
                )
            }

            Type.TRENDING_KEYWORD -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_album, parent, false)
                    )
                )
            }

            Type.RESULT -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_album, parent, false)
                    )
                )
            }
            Type.LOADING_RESULT -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_album, parent, false)
                    )
                )
            }
            Type.OTHER -> {
                PladioSearchItemViewHolder(
                    PladioBlockViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.pladio_search_block_keyword, parent, false)
                    )
                )
            }
        }
    }
    private fun getDimen(id: Int): Int {
        return Utils.getSizeInPixel(context = context, resId = id, reducePercent = 1f)
    }

    sealed interface Type {
        object SquareSliderSmallOneColumnItems : Type
        object SquareSliderMedium : Type
        object TRENDING_BLOCK_TEXT : Type
        object SUGGEST_KEYWORD : Type
        object TRENDING_KEYWORD : Type
        object RESULT : Type
        object LOADING_RESULT : Type
        object OTHER : Type
    }
    override fun onBindViewHolder(holder: PladioSearchItemViewHolder, position: Int) {
        val currentData = differ.currentList[position]
        holder.bind(currentData)
    }
}
