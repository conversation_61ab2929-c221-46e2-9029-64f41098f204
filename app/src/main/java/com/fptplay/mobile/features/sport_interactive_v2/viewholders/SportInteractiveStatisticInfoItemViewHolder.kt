package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.databinding.SportMatchInfoItemBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.statistic.SportInteractiveStatisticInfoData
import timber.log.Timber
import kotlin.math.roundToInt

class SportInteractiveStatisticInfoItemViewHolder(private val binding: SportMatchInfoItemBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveStatisticInfoData>(binding) {
    private fun safeDouble(text: String, default: Float): Float {
        return try {
            if (text.contains("%")) {
                val result = text.replace("%", "");
                result.toFloat() ?: default

            } else {
                text.toFloat() ?: default
            }
        } catch (e: Exception) {
            default
        }
    }

    override fun bind(data: SportInteractiveStatisticInfoData) {
        Timber.d("*******are item rebinding? ${data.actionName}")
        var sumMatch: Float =
            safeDouble(data.awayTeamScore, 0f) + safeDouble(data.homeTeamScore, 0f)
        if (sumMatch > 0) {
            try {
                val processAway = (safeDouble(data.awayTeamScore, 0f)) / sumMatch * 100
                binding.proccessBarAway.progress = processAway.roundToInt()
                val processHome = (safeDouble(data.homeTeamScore, 0f) / sumMatch * 100)
                binding.proccessBarHome.progress = processHome.roundToInt()
            } catch (e: Exception) {
                binding.proccessBarAway.progress = 0
                binding.proccessBarHome.progress = 0
            }
        } else {
            binding.proccessBarAway.progress = 0
            binding.proccessBarHome.progress = 0
        }
        binding.txtAway.text = data.awayTeamScore
        binding.txtHome.text = data.homeTeamScore
        binding.txtTypeMatchInfo.text = data.actionName

    }
}