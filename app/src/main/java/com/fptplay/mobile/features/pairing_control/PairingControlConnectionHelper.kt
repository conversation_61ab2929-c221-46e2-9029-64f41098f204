package com.fptplay.mobile.features.pairing_control

import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.navigation.fragment.NavHostFragment
import com.fptplay.dial.connection.FConnectionBuilder
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.dial.connection.android_tv.model.*
import com.fptplay.dial.connection.models.*
import com.fptplay.dial.connection.models.transfer_model.*
import com.fptplay.dial.model.*
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.global.GlobalEventListener
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.StringUtils.safeDashSymbol
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.pairing_control.Utils.getMySenderId
import com.fptplay.mobile.features.pairing_control.Utils.isMyMessage
import com.fptplay.mobile.features.pairing_control.Utils.isSuccess
import com.fptplay.mobile.features.pairing_control.model.CastingData
import com.fptplay.mobile.features.pairing_control.model.CastingItemState
import com.fptplay.mobile.features.pairing_control.model.DeviceData
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerData
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerState
import com.fptplay.mobile.features.pairing_control.model.mapToStructureItem
import com.fptplay.mobile.features.pairing_control.model.toDeviceData
import com.fptplay.mobile.features.pairing_control.navigation.PairingControlNavigation
import com.google.android.gms.cast.MediaTrack
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.TrackType
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.logger.Logger
import timber.log.Timber

class PairingControlConnectionHelper {

    private val TAG = this::class.java.simpleName

    private var _isConnected = false
    val isConnected get() = _isConnected

    private lateinit var pairingConnection: FConnectionManager

    private val connectionListeners = mutableListOf<FConnectionManager.FConnectionListener>()
    private val communicationListeners = mutableListOf<FConnectionManager.FCommunicationListener>()
    private val remotePlayerStateListeners = mutableListOf<RemotePlayerStateListener>()

    private var currentConnection : DeviceInfo ?= null
    private var selectDevice : DeviceInfo ?= null
    private var remoteDeviceData : DeviceData ?= null

    private var navigation: PairingControlNavigation ?= null
    private var castingData : CastingData ?= null

    private var _isSessionRunning = false
    val isSessionRunning get() = _isSessionRunning

    private var _shouldShowStopSessionToast = true
    val shouldShowStopSessionToast get() = _shouldShowStopSessionToast

    private var receiverName = ""

    private var connectSuccessMessage = ""

    // region Public methods
    fun registerConnection(applicationContext: Context) {
        if (this::pairingConnection.isInitialized) return
        pairingConnection = FConnectionBuilder().init(context = applicationContext)
            .setConnectionListener(listener = connectionListener)
            .setCommunicationListener(listener = communicationListener).build()

        // Navigation
        navigation = PairingControlNavigation(context = applicationContext)
    }

    fun navigate(navHostFragment: NavHostFragment?, data: BaseObject, onStopCastAndNavigate: (BaseObject) -> Unit = {}, onNavigate: (BaseObject) -> Unit = {}, onCancel: () -> Unit = {}) {
        navigation?.let {
            it.navigate(navHostFragment = navHostFragment, data = data, onStopCastAndNavigate = onStopCastAndNavigate, onNavigate = onNavigate, onCancel = onCancel)
        } ?: kotlin.run {
            onNavigate(data)
        }
    }

    fun showQuestionDialog(navHostFragment: NavHostFragment?, data: BaseObject, onNavigate: (BaseObject) -> Unit = {}, onCancel: () -> Unit = {}) {
        navigation?.let {
            it.showQuestionDialog(navHostFragment = navHostFragment, data = data, onNavigate = onNavigate, onCancel = onCancel)
        } ?: kotlin.run {
            onNavigate(data)
        }
    }

    fun showNotSupportDialog(navHostFragment: NavHostFragment?, message: String = "", onStopCastAndNavigate: () -> Unit = {}, onNavigate: () -> Unit = {}, onCancel: () -> Unit = {}, allowCancelByOutside : Boolean = true) {
        navigation?.let {
            it.showNotSupportDialog(navHostFragment = navHostFragment, message = message, onStopCastAndNavigate = onStopCastAndNavigate, onNavigate = onNavigate, onCancel = onCancel, allowCancelByOutside = allowCancelByOutside)
        } ?: kotlin.run {
            onNavigate()
        }
    }

    fun saveProcessingData(type: String, id: String, actionType: String) {
        com.fptplay.mobile.features.pairing_control.Utils.saveProcessingData(type = type, id = id, actionType = actionType)
    }

    /**
     * Determine, send stop session when cancel process login or buy package
     */
    fun isSendStopSession() : Boolean {
        return com.fptplay.mobile.features.pairing_control.Utils.isSendStopSession(castingId = castingData?.id ?: "")
    }

    fun isProcessHandleException() = com.fptplay.mobile.features.pairing_control.Utils.isProcessHandleException(castingId = castingData?.id ?: "")

    fun clearProcessingData() {
        com.fptplay.mobile.features.pairing_control.Utils.clearProcessingData()
    }

    //endregion

    //region Toast Message
    fun showConnectingToastMessage() {
        Toast.makeText(MainApplication.INSTANCE.applicationContext, MainApplication.INSTANCE.applicationContext.getString(R.string.pairing_control_waiting_connection, getReceiverName()), Toast.LENGTH_SHORT).show()
    }

    fun showToast(message: String) {
        Toast.makeText(MainApplication.INSTANCE.applicationContext, message, Toast.LENGTH_SHORT).show()
    }
    //endregion

    //region Global Event
    private fun registerGlobalEvent() {
        GlobalEvent.registerSingleEvent(GlobalEvent.LOGIN_EVENT, globalLoginEvent)
        GlobalEvent.registerSingleEvent(GlobalEvent.PAYMENT_EVENT, globalPaymentEvent)
    }

    private fun unregisterGlobalEvent() {
        GlobalEvent.unRegisterEvent(GlobalEvent.LOGIN_EVENT, globalLoginEvent)
        GlobalEvent.unRegisterEvent(GlobalEvent.PAYMENT_EVENT, globalPaymentEvent)
    }

    private val globalLoginEvent = object : GlobalEventListener {
        override fun onEventReceive(data: Any?) {
            when (data) {
                is Boolean -> {
                    Logger.d("$TAG -> GlobalLoginEvent -> onEventReceive -> $data")

                    if (!data) {
                        sendEventStopSession()
                    }

                    //
                    clearProcessingData()
                }
            }
        }
    }

    private val globalPaymentEvent = object : GlobalEventListener {
        override fun onEventReceive(data: Any?) {
            when (data) {
                is Boolean -> {
                    Logger.d("$TAG -> GlobalPaymentEvent -> onEventReceive -> $data")

                    if (!data) {
                        if (isSendStopSession()) {
                            sendEventStopSession()
                        }
                    }
                    //
                    clearProcessingData()
                }
            }
        }
    }
    //endregion

    // region Connection

    fun isServiceConnected() : Boolean? {
        if (!this::pairingConnection.isInitialized) return null
        return pairingConnection.isConnected()
    }

    fun connect(data: DeviceInfo, isShowToast: Boolean = true) {
        if (!this::pairingConnection.isInitialized) return
        //
        removeIntervalReceiverAppStarted()
        //
        if (data is FSamsungTVDeviceInfo) {
            data.senderId = MainApplication.INSTANCE.sharedPreferences.androidId()
        }
        //
        if (isShowToast) {
            showConnectingToastMessage()
        }
        //
        disconnect()
        //
        currentConnection = data

        Logger.d("PairingControlConnectionHelper -> Connect -> Data: $data")

        pairingConnection.connect(service = data, androidBoxModels = MainApplication.INSTANCE.sharedPreferences.getListBoxModelName())

        // Update connected time
        updateConnectedTime()
    }

    fun reconnect() {
        if (!this::pairingConnection.isInitialized) return
        //
        Logger.d("PairingControlConnectionHelper => Reconnect: ${currentConnection}")
        //
        removeIntervalReceiverAppStarted()
        //
        //
        currentConnection?.let {
            pairingConnection.connect(service = it)
        }
    }

    private fun sendMessage(data: ObjectSender) {
        if (!this::pairingConnection.isInitialized) return
        pairingConnection.sendMessage(data = data)
    }

    fun disconnect(force: Boolean = false, sendEventToReceiver: Boolean = true) {
        if (!this::pairingConnection.isInitialized) return

        //
        Logger.d("PairingControlConnectionHelper -> Disconnect")
        if (sendEventToReceiver) {
            sendEventDisconnectReceiver()
        }

        setCastingItem(data = null)

        // Reset connect time
        resetConnectTime()

        if (force) {
            pairingConnection.forceDisconnect()
        } else {
            pairingConnection.disconnect()
        }

        //
        removeIntervalReceiverAppStarted()
        //
        removeIntervalKeepConnectionForFSamsungTV()
    }

    fun destroy() {
        if (!this::pairingConnection.isInitialized) return

        Logger.d("PairingControlConnectionHelper -> Destroy")

        pairingConnection.destroy()

        //
        removeIntervalReceiverAppStarted()
        //
        removeIntervalKeepConnectionForFSamsungTV()
        //
    }

    fun addConnectionListener(listener: FConnectionManager.FConnectionListener) {
        if (!connectionListeners.contains(listener)) {
            connectionListeners.add(listener)
        }
    }

    fun addCommunicationListener(listener: FConnectionManager.FCommunicationListener) {
        if (!communicationListeners.contains(listener)) {
            communicationListeners.add(listener)
        }
    }

    fun removeConnectionListener(listener: FConnectionManager.FConnectionListener) {
        if (connectionListeners.contains(listener)) {
            connectionListeners.remove(listener)
        }
    }

    fun removeCommunicationListener(listener: FConnectionManager.FCommunicationListener) {
        if (communicationListeners.contains(listener)) {
            communicationListeners.remove(listener)
        }
    }

    fun addPlayerStateListener(listener: RemotePlayerStateListener) {
        if (!remotePlayerStateListeners.contains(listener)) {
            remotePlayerStateListeners.add(listener)
        }
    }

    fun removePlayerStateListener(listener: RemotePlayerStateListener) {
        if (remotePlayerStateListeners.contains(listener)) {
            remotePlayerStateListeners.remove(listener)
        }
    }

    ///

    fun getCurrentConnection() : DeviceInfo? {
        return currentConnection
    }

    @Synchronized
    fun setRemoteData(data: DeviceData?) {
        val oldData = this.remoteDeviceData
        this.remoteDeviceData = data
        triggerUpdateState(oldData = oldData)
    }

    fun getRemoteData() = remoteDeviceData

    fun setReceiverName(name: String) {
        receiverName = name
    }
    fun getReceiverName() : String {
        remoteDeviceData?.let {
            receiverName = it.receiverName
        }
        return receiverName
    }

    fun setSelectDevice(data: DeviceInfo?) {
        this.selectDevice = data

        val name = when (data) {
            is FBoxDeviceInfoV2 -> data.device?.friendlyName ?: data.response.name ?: ""
            is FAndroidTVDeviceInfo -> data.route?.name ?: ""
            is FAndroidTVDeviceInfoExternal -> data.name
            is FSamsungTVDeviceInfo -> data.service?.name ?:""
            is FSamsungTVDeviceInfoExternal -> data.name
            else -> ""
        }
        setReceiverName(name = name)
    }

    fun getSelectDevice() = selectDevice

    private fun setCastingItem(data: CastingData?) {
        castingData = data
        Logger.d("PairingControlConnectionHelper -> SetCastingItem: $data")
    }
    fun updateCastingItemState(state: CastingItemState) {
        if (castingData == null) return
        castingData?.state = state
    }
    fun getCastingItem() = castingData
    fun getCastingItemMapped() = castingData?.mapToStructureItem()
    fun getCastingItemState() = castingData?.state
    fun findCastingDataBitrateId(episodeIndex: String) : String {
        val data = castingData ?: com.fptplay.mobile.features.pairing_control.Utils.getTempCastingData()
        return data?.let {
            if (it.autoProfiles.isNotEmpty()) {
                val index = Utils.convertStringToInt(episodeIndex, 0)
                it.autoProfiles[if (index < it.autoProfiles.size) index else 0]
            } else ""
        } ?: ""
    }
    fun updateCastingItemEpisodeIndex(episodeIndex: String) {
        if (castingData == null) return
        castingData?.episodeId = episodeIndex
    }
    fun setSessionRunning(isRunning: Boolean) {
        _shouldShowStopSessionToast = _isSessionRunning
        _isSessionRunning = isRunning
        notifyCastSessionChanged()
    }

    fun switchProfileStopSession() {
        Logger.d("PairingControlConnectionHelper -> SwitchProfileStopSession")
        sendEventStopSession()
        setCastingItem(data = null)
    }

    private fun updateConnectedTime() {
        MainApplication.INSTANCE.pairingScannerHelper.getListDevices().forEach { device ->
            if (MainApplication.INSTANCE.pairingScannerHelper.isInternalDeviceTheSame(device1 = currentConnection, device2 = device.deviceInfo)) {
                device.timestamp = System.currentTimeMillis()
                return
            }
        }
    }

    private fun resetConnectTime() {
        MainApplication.INSTANCE.pairingScannerHelper.getListDevices().forEach { device ->
            if (currentConnection == device.deviceInfo) { // Check
                device.timestamp = 0
                return
            }
        }
    }

    private fun triggerUpdateState(oldData: DeviceData?) {
        if (remoteDeviceData?.remotePlayer?.state != oldData?.remotePlayer?.state) {
            remotePlayerStateListeners.forEach { it.onStateChanged(state = remoteDeviceData?.remotePlayer?.state ?: RemotePlayerState.UNKNOWN) }
        }
    }

    private fun notifyActionEvent(type: String) {
        remotePlayerStateListeners.forEach { it.onActionEvent(type = type) }
    }

    private fun notifyCastSessionChanged() {
        remotePlayerStateListeners.forEach { it.onCastSessionChanged() }
    }

    private val connectionListener = object : FConnectionManager.FConnectionListener {
        override fun onConnectError(errorCode: Int, message: String) {
            Logger.d("PairingControlConnectionHelper -> onConnectError")
            notifyConnectError(errorCode = errorCode, message = message)

            //
            removeIntervalKeepConnectionForFSamsungTV()
            //
        }

        override fun onConnectSuccess(message: String) {
            Logger.d("PairingControlConnectionHelper -> onConnectSuccess")
            connectSuccessMessage = message
            when (currentConnection) {
                is FBoxDeviceInfoV2 -> {
                    initReceiverAppStartedHandler()
                    //
                }
                is FSamsungTVDeviceInfo -> {
                    initReceiverAppStartedHandler()
                    //
                    initKeepConnectionForFSamsungTVHandler()
                    //
                }
                is FSamsungTVDeviceInfoExternal -> {
                    initReceiverAppStartedHandler()
                    //
                }
                is FAndroidTVDeviceInfo -> {
                    initReceiverAppStartedHandler()
                }
                is FAndroidTVDeviceInfoExternal -> {
                    initReceiverAppStartedHandler()
                    //
                }
                else -> {
                    notifyConnectSuccess(message = message)
                }
            }
        }

        override fun onDisconnectError(message: String) {
            Logger.d("PairingControlConnectionHelper -> onDisconnectError")
            connectionListeners.forEach { it.onDisconnectError(message = message) }

            //
            removeIntervalKeepConnectionForFSamsungTV()
            //

            // Tracking
            sendCastingTracking(itemName = "DisconnectError", errorCode = "", errorMessage = message)
        }

        override fun onDisconnectSuccess(message: String) {
            Logger.d("PairingControlConnectionHelper -> onDisconnectSuccess")

            unregisterGlobalEvent()
            //
            val isNotifyDisconnect = _isConnected // Only notify 1 times. When state from connected -> disconnect
            _isConnected = false

            //region Update session
            setSessionRunning(isRunning = false)
            //endregion

            if (isNotifyDisconnect) {
                connectionListeners.forEach { it.onDisconnectSuccess(message = message) }

                // Tracking
                sendCastingTracking(itemName = "DisconnectSuccess", errorCode = "", errorMessage = message)
                sendTrackingDisconnect()
            }
            setRemoteData(data = null)

            //
            removeIntervalKeepConnectionForFSamsungTV()
            //
        }

        override fun onSessionEvent(code: Int, message: String) {
            Logger.d("PairingControlConnectionHelper -> onSessionEvent -> code: $code, message: $message")
            connectionListeners.forEach { it.onSessionEvent(code = code, message = message) }

            // Tracking
            sendCastingTracking(itemName = "SessionEvent", errorCode = code.toString(), errorMessage = message)
        }

    }

    private fun notifyConnectError(errorCode: Int, message: String) {
        //
        unregisterGlobalEvent()
        //
        _isConnected = false
        connectionListeners.forEach { it.onConnectError(errorCode = errorCode, message = message) }
        setRemoteData(data = null)

        // Tracking
        sendCastingTracking(itemName = "ConnectError", errorCode = errorCode.toString(), errorMessage = message)

        // Tracking
        if (errorCode == 2) {
            sendTrackingDisconnectRejectCast()
        }
    }

    private fun notifyConnectSuccess(isSendEventConnect : Boolean = true, message: String) {
        //
        registerGlobalEvent()
        //
        _isConnected = true

        //region Update session
        setSessionRunning(isRunning = true)
        //endregion

        if (isSendEventConnect) {
            sendEventConnectReceiver()
        }
        sendEventGetBoxInfo()

        val messageLog = connectSuccessMessage.ifBlank { message }
        connectionListeners.forEach { it.onConnectSuccess(message = messageLog) }

        // Tracking
        sendCastingTracking(itemName = "ConnectSuccess", errorCode = "", errorMessage = messageLog)

        connectSuccessMessage = ""
    }

    private val communicationListener = object: FConnectionManager.FCommunicationListener {
        override fun onMessage(message: ObjectReceiver) {
            Logger.d("PairingControlConnectionHelper -> onMessage = $message | isSessionRunning = $isSessionRunning")
            when (message) {
                is FBoxObjectReceiver -> {
                    when (message.data) {
                        is ReceiveConnectReceiver -> {
                            (message.data as? ReceiveConnectReceiver)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Connect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    // Get FBox receiver start success
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectSuccess(isSendEventConnect = false, message = "Connect Success")
                                } else if (remoteData.result == "2" && remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 2, message = "Disable cast")
                                } else if (remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 0, message = "Connect Error. Result=0")
                                }
                            }
                        }
                        is ReceiveDisconnectReceiver -> {
                            (message.data as? ReceiveDisconnectReceiver)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Disconnect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    try {
                                        Handler(Looper.getMainLooper()).post {
                                            disconnect(sendEventToReceiver = false)
                                        }
                                    } catch (e: Exception) {
                                        e.printStackTrace()
                                    }
                                }
                            }
                        }
                        is ReceiveGetReceiverInfo -> {
                            (message.data as? ReceiveGetReceiverInfo)?.run {
                                if (result.isSuccess()) {
                                    data?.let { data ->
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.apply {
                                                receiverName = data.receiverName
                                                receiverId = data.receiverId
                                                receiverType = data.receiverType
                                                standByStatus = data.standByStatus
                                                language = data.language
                                                volume = data.volume
                                                supportDrm = data.supportDrm
                                                supportVolume = data.supportVolume
                                                isMute = data.isMute
                                                screenHeight = data.screenHeight
                                                screenWidth = data.screenWidth
                                                drmLevel = data.drmLevel
                                            })
                                        } ?: kotlin.run {
                                            setRemoteData(data = data.toDeviceData())
                                        }
                                    }
                                }
                            }
                        }
                        is ReceivePlayContent -> { // result: 0: General Error | 1: Success | 2: Limit CCU | 3: BlockChannel | -1: Only for boxiptv (ref_id = -1)
                            // reset track when play new content
                            (message.data as? ReceivePlayContent)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }
                                    // Update casting item state
                                    triggerSaveCastingItem()
                                    updateCastingItemState(state = CastingItemState.CAN_CAST)
                                    //
                                }
                            }
                        }
                        is ReceiveChangeChapter -> {
                            // reset track when play new content
                            (message.data as? ReceiveChangeChapter)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }
                                }
                            }
                        }
                        is ReceiveChangeReceiverVolume -> {
                            (message.data as? ReceiveChangeReceiverVolume)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        volume = <EMAIL>?.value ?: ""
                                    })
                                }
                            }
                        }
                        is ReceiveMuteUnmuteReceiver -> {
                            (message.data as? ReceiveMuteUnmuteReceiver)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        isMute = <EMAIL>?.value ?: ""
                                    })
                                }
                            }
                        }
                        is ReceiveGetStatusWatching -> {
                            (message.data as? ReceiveGetStatusWatching)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveSetTrack -> {
                            (message.data as? ReceiveSetTrack)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveSeekTime -> {
                            (message.data as? ReceiveSeekTime)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveStopSession -> {
                            (message.data as? ReceiveStopSession)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Stop Session Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.copy(
                                                remotePlayer = RemotePlayerData(
                                                    currentDuration = it.remotePlayer.currentDuration,
                                                    bufferDuration = it.remotePlayer.bufferDuration,
                                                    totalDuration = it.remotePlayer.totalDuration,
                                                    tracks = it.remotePlayer.tracks,
                                                    state = RemotePlayerState.STOP
                                                )
                                            ))
                                        }
                                        //region Update session
                                        setSessionRunning(isRunning = false)
                                        //endregion
                                    }
                                }
                            }
                        }
                        is ReceiveActionEvent -> {
                            (message.data as? ReceiveActionEvent)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let { data ->
                                            notifyActionEvent(type = data.type)

                                            when (data.type) {
                                                ActionEventType.END_EVENT -> {
                                                    // Local message
                                                    onMessage(FBoxObjectReceiver(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveExceptionEvent -> {
                            (message.data as? ReceiveExceptionEvent)?.run {
                                if (result.isSuccess() && senderId.isMyMessage()) {
                                    //
                                    data?.let { data ->
                                        when (data.actionType) {
                                            ExceptionEventType.REQUIRE_LOGIN -> updateCastingItemState(state = CastingItemState.REQUIRE_LOGIN)
                                            ExceptionEventType.REQUIRE_PAYMENT -> updateCastingItemState(state = CastingItemState.REQUIRED_PAYMENT)
                                        }
                                    }
                                }
                            }
                        }
                        else -> {}
                    }
                }
                is FSamsungObjectReceiver,
                is FSamsungObjectReceiverExternal -> {
                    when (message.data) {
                        is ReceiveConnectReceiver -> {
                            (message.data as? ReceiveConnectReceiver)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Connect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    // Get FSamsung receiver start success
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectSuccess(isSendEventConnect = false, message = "Connect Success")
                                } else if (remoteData.result == "2" && remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 2, message = "Disable cast")
                                } else if (remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 0, message = "Connect Error. Result=0")
                                }
                            }
                        }
                        is ReceiveDisconnectReceiver -> {
                            (message.data as? ReceiveDisconnectReceiver)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Disconnect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    try {
                                        Handler(Looper.getMainLooper()).post {
                                            disconnect(sendEventToReceiver = false)
                                        }
                                    } catch (e: Exception) {
                                        e.printStackTrace()
                                    }
                                }
                            }
                        }
                        is ReceiveGetReceiverInfo -> {
                            (message.data as? ReceiveGetReceiverInfo)?.run {
                                if (result.isSuccess()) {
                                    data?.let { data ->
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.apply {
                                                receiverName = data.receiverName
                                                receiverId = data.receiverId
                                                receiverType = data.receiverType
                                                standByStatus = data.standByStatus
                                                language = data.language
                                                volume = data.volume
                                                supportDrm = data.supportDrm
                                                supportVolume = data.supportVolume
                                                isMute = data.isMute
                                                screenHeight = data.screenHeight
                                                screenWidth = data.screenWidth
                                                drmLevel = data.drmLevel
                                            })
                                        } ?: kotlin.run {
                                            setRemoteData(data = data.toDeviceData())
                                        }
                                    }
                                }
                            }
                        }
                        is ReceivePlayContent -> { // result: 0: General Error | 1: Success | 2: Limit CCU | 3: BlockChannel | -1: Only for boxiptv (ref_id = -1)
                            // reset track when play new content
                            (message.data as? ReceivePlayContent)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }
                                    // Update casting item state
                                    triggerSaveCastingItem()
                                    updateCastingItemState(state = CastingItemState.CAN_CAST)
                                    //
                                }
                            }
                        }
                        is ReceiveChangeChapter -> {
                            // reset track when play new content
                            (message.data as? ReceiveChangeChapter)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }
                                }
                            }
                        }
                        is ReceiveChangeReceiverVolume -> {
                            (message.data as? ReceiveChangeReceiverVolume)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        volume = <EMAIL>?.value ?: ""
                                    })
                                }
                            }
                        }
                        is ReceiveMuteUnmuteReceiver -> {
                            (message.data as? ReceiveMuteUnmuteReceiver)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        isMute = <EMAIL>?.value ?: ""
                                    })
                                }
                            }
                        }
                        is ReceiveGetStatusWatching -> {
                            (message.data as? ReceiveGetStatusWatching)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveSetTrack -> {
                            (message.data as? ReceiveSetTrack)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveSeekTime -> {
                            (message.data as? ReceiveSeekTime)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveStopSession -> {
                            (message.data as? ReceiveStopSession)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Stop Session Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.copy(
                                                remotePlayer = RemotePlayerData(
                                                    currentDuration = it.remotePlayer.currentDuration,
                                                    bufferDuration = it.remotePlayer.bufferDuration,
                                                    totalDuration = it.remotePlayer.totalDuration,
                                                    tracks = it.remotePlayer.tracks,
                                                    state = RemotePlayerState.STOP
                                                )
                                            ))
                                        }
                                        //region Update session
                                        setSessionRunning(isRunning = false)
                                        //endregion
                                    }
                                }
                            }
                        }
                        is ReceiveActionEvent -> {
                            (message.data as? ReceiveActionEvent)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let { data ->
                                            notifyActionEvent(type = data.type)

                                            when (data.type) {
                                                ActionEventType.END_EVENT -> {
                                                    // Local message
                                                    onMessage(FSamsungObjectReceiver(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveExceptionEvent -> {
                            (message.data as? ReceiveExceptionEvent)?.run {
                                if (result.isSuccess() && senderId.isMyMessage()) {
                                    //
                                    when (data?.actionType) {
                                        ExceptionEventType.REQUIRE_LOGIN -> updateCastingItemState(state = CastingItemState.REQUIRE_LOGIN)
                                        ExceptionEventType.REQUIRE_PAYMENT -> updateCastingItemState(state = CastingItemState.REQUIRED_PAYMENT)
                                    }
                                }
                            }
                        }
                    }
                }
                is FAndroidTVObjectReceiver -> {
                    when (message.data) {
                        is FAndroidTVCustomConnectReceiverResponse -> {
                            (message.data as? FAndroidTVCustomConnectReceiverResponse)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Connect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    // Get FBox receiver start success
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectSuccess(isSendEventConnect = false, message = "Connect Success")
                                } else if (remoteData.result == "2" && remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 2, message = "Disable cast")
                                } else if (remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 0, message = "Connect Error. Result=0")
                                }
                            }
                        }
                        is FAndroidTVGetInfoResponse -> {
                            (message.data as? FAndroidTVGetInfoResponse)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        receiverName = <EMAIL>
                                        volume = ((<EMAIL> * 100).toInt()).toString()
                                        isMute = if (<EMAIL>) "1" else "0"
                                    })
                                } ?: kotlin.run {
                                    setRemoteData(data = toDeviceData())
                                }
                            }
                        }
                        is FAndroidTVSetVolumeResponse -> {
                            (message.data as? FAndroidTVSetVolumeResponse)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        volume = ((<EMAIL>).toInt()).toString()
                                    })
                                }
                            }
                        }
                        is FAndroidTVPlayResponse -> {
                            (message.data as? FAndroidTVPlayResponse)?.run {
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.state = RemotePlayerState.PLAY
                                        })
                                    }
                                }
                            }
//                            resetRemoteData()
                        }
                        is FAndroidTVPauseResponse -> {
                            (message.data as? FAndroidTVPauseResponse)?.run {
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.state = RemotePlayerState.PAUSE
                                        })
                                    }
                                }
                            }
                        }
                        is FAndroidTVErrorResponse -> {
                            (message.data as? FAndroidTVErrorResponse)?.run {
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.state = RemotePlayerState.UNKNOWN
                                        })
                                    }
                                }
                            }
                        }
                        is FAndroidTVFinishResponse -> {
                            (message.data as? FAndroidTVFinishResponse)?.run {
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.state = RemotePlayerState.STOP
                                        })
                                    }
                                }
                            }
                        }
                        is FAndroidTVUpdateProgressResponse -> {
                            (message.data as? FAndroidTVUpdateProgressResponse)?.run {
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.currentDuration = <EMAIL> ?: 0L
                                            remotePlayer.totalDuration = <EMAIL> ?: 0L
                                        })
                                    }
                                }
                            }
                        }
                        is FAndroidTVChangeTrackResponse -> {
                            (message.data as? FAndroidTVChangeTrackResponse)?.let { remoteData ->
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.tracks = remotePlayer.tracks?.map { item ->
                                                PlayerControlView.Data.Track(
                                                    id = item.id,
                                                    name = item.name,
                                                    type = item.type,
                                                    isSelected = kotlin.run {
                                                        if (item.type == remoteData.type) {
                                                            item.id.toLongOrNull() == remoteData.trackId
                                                        } else {
                                                            item.isSelected
                                                        }
                                                    })
                                            } ?: listOf()
                                        })
                                    }
                                }
                            }
                        }
                        is FAndroidTVMetadataUpdated -> {
                            (message.data as? FAndroidTVMetadataUpdated)?.let { remoteData ->
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    getRemoteData()?.let {
                                        setRemoteData(data = it.apply {
                                            remotePlayer.tracks = kotlin.run {
                                                val tracks = mutableListOf<PlayerControlView.Data.Track>()

                                                // Filter subtitles
                                                val remoteTrackSub = remoteData.tracks?.filter { item -> kotlin.run {
                                                    val splitData = (item.name?:"").split("/")
                                                    val name = if (splitData.size == 2) { splitData[0] } else { item.name ?: "" }
                                                    // result
                                                    (item.type == MediaTrack.TYPE_TEXT) || (name == "Tắt phụ đề" && item.type == MediaTrack.TYPE_UNKNOWN)
                                                    //
                                                }} ?: listOf()

                                                // Filter audios
                                                val remoteTrackAudio = remoteData.tracks?.filter { item -> item.type == MediaTrack.TYPE_AUDIO } ?: listOf()

                                                // Map subtitles
                                                val trackSub = if (remoteTrackSub.isNotEmpty()) remoteTrackSub.map { item ->
                                                    val splitData = (item.name?:"").split("/")
                                                    if (splitData.size == 2) {
                                                        val name = splitData[0]
                                                        val type = if (name == "Tắt phụ đề") 10002 else TrackType.TEXT.ordinal

                                                        PlayerControlView.Data.Track(id = item.id.toString(), name = name, type = type, isSelected = Utils.convertStringToBoolean(splitData[1], false))
                                                    } else {
                                                        val name = item.name ?: ""
                                                        val type = if (name == "Tắt phụ đề") 10002 else TrackType.TEXT.ordinal

                                                        PlayerControlView.Data.Track(id = item.id.toString(), name = name, type = type, isSelected = false)
                                                    }
                                                } else listOf()

                                                // Map audios
                                                val trackAudio = if (remoteTrackAudio.isNotEmpty()) remoteTrackAudio.map { item ->
                                                    val splitData = (item.name?:"").split("/")
                                                    if (splitData.size == 2) {
                                                        PlayerControlView.Data.Track(id = item.id.toString(), name = splitData[0], type = TrackType.AUDIO.ordinal, isSelected = Utils.convertStringToBoolean(splitData[1], false))
                                                    } else {
                                                        PlayerControlView.Data.Track(id = item.id.toString(), name = item.name ?: "", type = TrackType.AUDIO.ordinal, isSelected = false)
                                                    }
                                                } else listOf()

                                                // Done
                                                tracks.clear()
                                                tracks.addAll(trackSub)
                                                if (trackAudio.size > 1) tracks.addAll(trackAudio)
                                                Logger.d("PairingControl tracks=${tracks}")
                                                return@run tracks
                                            }
                                        })
                                    }
                                }
                            }
                        }
                        is FAndroidTVCustomMessageResponse -> {
                            (message.data as? FAndroidTVCustomMessageResponse)?.let { remoteData ->
                                when (remoteData.message) {
                                    "stop" -> {
                                        // Tracking
                                        sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Stop Session Event | SenderId: | MySenderId: ${getMySenderId()}")
                                        //
                                        if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                            getRemoteData()?.let {
                                                setRemoteData(data = it.copy(
                                                    remotePlayer = RemotePlayerData(
                                                        currentDuration = it.remotePlayer.currentDuration,
                                                        bufferDuration = it.remotePlayer.bufferDuration,
                                                        totalDuration = it.remotePlayer.totalDuration,
                                                        tracks = it.remotePlayer.tracks,
                                                        state = RemotePlayerState.STOP
                                                    )
                                                ))
                                            }
                                            //region Update session
                                            setSessionRunning(isRunning = false)
                                            //endregion
                                        } else {
                                            return
                                        }
                                    }
                                    "disconnected" -> {
                                        // Tracking
                                        sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Disconnect Receiver Event | SenderId: | MySenderId: ${getMySenderId()}")
                                        //
                                        try {
                                            Handler(Looper.getMainLooper()).post {
                                                disconnect(sendEventToReceiver = false)
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }
                                    else -> {
                                        val splitData = remoteData.message.split("/")
                                        if (splitData.size == 2) {
                                            if (splitData.getOrNull(0) == "disconnected" && (splitData.getOrNull(1) ?: "").isMyMessage()) {
                                                try {
                                                    Handler(Looper.getMainLooper()).post {
                                                        disconnect(sendEventToReceiver = false)
                                                    }
                                                } catch (e: Exception) {
                                                    e.printStackTrace()
                                                }
                                            } else {}
                                        } else {}
                                    }
                                }
                            }
                        }
                        is FAndroidTVCustomPlayContentResponse -> {
                            (message.data as? FAndroidTVCustomPlayContentResponse)?.let { remoteData ->
                                if (remoteData.result.isSuccess()) {
                                    // Update casting item state
                                    triggerSaveCastingItem()
                                    updateCastingItemState(state = CastingItemState.CAN_CAST)
                                    //
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }
                                }
                            }
                        }
                        is FAndroidTVCustomChangeChapterResponse -> {
                            (message.data as? FAndroidTVCustomChangeChapterResponse)?.let { remoteData ->
                                if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                    if (remoteData.result.isSuccess()) {
                                        if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                            resetRemoteData()
                                        }
                                    }
                                }
                            }
                        }
                        is FAndroidTVCustomStopSessionResponse -> {
                            (message.data as? FAndroidTVCustomStopSessionResponse)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Stop Session Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.copy(
                                                remotePlayer = RemotePlayerData(
                                                    currentDuration = it.remotePlayer.currentDuration,
                                                    bufferDuration = it.remotePlayer.bufferDuration,
                                                    totalDuration = it.remotePlayer.totalDuration,
                                                    tracks = it.remotePlayer.tracks,
                                                    state = RemotePlayerState.STOP
                                                )
                                            ))
                                        }
                                        //region Update session
                                        setSessionRunning(isRunning = false)
                                        //endregion
                                    } else {
                                        return
                                    }
                                }
                            }
                        }

                        is FAndroidTVCustomActionEventResponse -> {
                            (message.data as? FAndroidTVCustomActionEventResponse)?.let { remoteData ->
                                if (remoteData.result.isSuccess()) {
                                    when (remoteData.data?.type) {
                                        ActionEventType.END_EVENT -> {
                                            // Local message
                                            onMessage(FAndroidTVObjectReceiver(FAndroidTVCustomStopSessionResponse(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                                        }
                                    }
                                }
                            }
                        }
                        is FAndroidTVCustomExceptionMessageResponse -> {
                            (message.data as? FAndroidTVCustomExceptionMessageResponse)?.let { remoteData ->
                                if (remoteData.result.isSuccess()) {
                                    //
                                    when (remoteData.data?.actionType) {
                                        ExceptionEventType.REQUIRE_LOGIN -> updateCastingItemState(state = CastingItemState.REQUIRE_LOGIN)
                                        ExceptionEventType.REQUIRE_PAYMENT -> updateCastingItemState(state = CastingItemState.REQUIRED_PAYMENT)
                                    }
                                }
                            }
                        }
                    }
                }
                is FAndroidTVObjectReceiverExternal -> {
                    when (message.data) {
                        is ReceiveConnectReceiver -> {
                            (message.data as? ReceiveConnectReceiver)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Connect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    // Get FAndroid External receiver start success
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectSuccess(isSendEventConnect = false, message = "Connect Success")
                                } else if (remoteData.result == "2" && remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 2, message = "Disable cast")
                                } else if (remoteData.senderId.isMyMessage()) {
                                    //
                                    disconnect()
                                    //
                                    removeIntervalReceiverAppStarted()
                                    notifyConnectError(errorCode = 0, message = "Connect Error. Result=0")
                                }
                            }
                        }
                        is ReceiveDisconnectReceiver -> {
                            (message.data as? ReceiveDisconnectReceiver)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Disconnect Receiver Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    try {
                                        Handler(Looper.getMainLooper()).post {
                                            disconnect(sendEventToReceiver = false)
                                        }
                                    } catch (e: Exception) {
                                        e.printStackTrace()
                                    }
                                }
                            }
                        }
                        is ReceiveGetReceiverInfo -> {
                            (message.data as? ReceiveGetReceiverInfo)?.run {
                                if (result.isSuccess()) {
                                    data?.let { data ->
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.apply {
                                                receiverName = data.receiverName
                                                receiverId = data.receiverId
                                                receiverType = data.receiverType
                                                standByStatus = data.standByStatus
                                                language = data.language
                                                volume = data.volume
                                                supportDrm = data.supportDrm
                                                supportVolume = data.supportVolume
                                                isMute = data.isMute
                                                screenHeight = data.screenHeight
                                                screenWidth = data.screenWidth
                                                drmLevel = data.drmLevel
                                            })
                                        } ?: kotlin.run {
                                            setRemoteData(data = data.toDeviceData())
                                        }
                                    }
                                }
                            }
                        }
                        is ReceivePlayContent -> { // result: 0: General Error | 1: Success | 2: Limit CCU | 3: BlockChannel | -1: Only for boxiptv (ref_id = -1)
                            // reset track when play new content
                            (message.data as? ReceivePlayContent)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }

                                    // Update casting item state
                                    triggerSaveCastingItem()
                                    updateCastingItemState(state = CastingItemState.CAN_CAST)
                                    //
                                }
                            }
                        }
                        is ReceiveChangeChapter -> {
                            // reset track when play new content
                            (message.data as? ReceiveChangeChapter)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        resetRemoteData()
                                    }
                                }
                            }
                        }
                        is ReceiveChangeReceiverVolume -> {
                            (message.data as? ReceiveChangeReceiverVolume)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        volume = <EMAIL>?.value ?: ""
                                    })
                                }
                            }
                        }
                        is ReceiveMuteUnmuteReceiver -> {
                            (message.data as? ReceiveMuteUnmuteReceiver)?.run {
                                getRemoteData()?.let {
                                    setRemoteData(data = it.apply {
                                        isMute = <EMAIL>?.value ?: ""
                                    })
                                }
                            }
                        }
                        is ReceiveGetStatusWatching -> {
                            (message.data as? ReceiveGetStatusWatching)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveSetTrack -> {
                            (message.data as? ReceiveSetTrack)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveSeekTime -> {
                            (message.data as? ReceiveSeekTime)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let {
                                            updateAllData(data = it)
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveStopSession -> {
                            (message.data as? ReceiveStopSession)?.let { remoteData ->
                                // Tracking
                                sendCastingTracking(itemName ="MessageEvent", errorCode = "", errorMessage = "Receive Stop Session Event | SenderId: ${remoteData.senderId} | MySenderId: ${getMySenderId()}")
                                //
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        getRemoteData()?.let {
                                            setRemoteData(data = it.copy(
                                                remotePlayer = RemotePlayerData(
                                                    currentDuration = it.remotePlayer.currentDuration,
                                                    bufferDuration = it.remotePlayer.bufferDuration,
                                                    totalDuration = it.remotePlayer.totalDuration,
                                                    tracks = it.remotePlayer.tracks,
                                                    state = RemotePlayerState.STOP
                                                )
                                            ))
                                        }
                                        //region Update session
                                        setSessionRunning(isRunning = false)
                                        //endregion
                                    }
                                }
                            }
                        }
                        is ReceiveActionEvent -> {
                            (message.data as? ReceiveActionEvent)?.let { remoteData ->
                                if (remoteData.result.isSuccess() && remoteData.senderId.isMyMessage()) {
                                    if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                                        remoteData.data?.let { data ->
                                            notifyActionEvent(type = data.type)

                                            when (data.type) {
                                                ActionEventType.END_EVENT -> {
                                                    // Local message
                                                    onMessage(FAndroidTVObjectReceiverExternal(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        is ReceiveExceptionEvent -> {
                            (message.data as? ReceiveExceptionEvent)?.run {
                                if (result.isSuccess() && senderId.isMyMessage()) {
                                    //
                                    when (data?.actionType) {
                                        ExceptionEventType.REQUIRE_LOGIN -> updateCastingItemState(state = CastingItemState.REQUIRE_LOGIN)
                                        ExceptionEventType.REQUIRE_PAYMENT -> updateCastingItemState(state = CastingItemState.REQUIRED_PAYMENT)
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {}
            }

            communicationListeners.forEach { it.onMessage(message = message) }
        }

        override fun onSendMessageCallback(
            messageType: Int,
            isSuccess: Boolean,
            errorCode: Int,
            errorMessage: String
        ) {
            Logger.d("PairingControlConnectionHelper -> onSendMessageCallback = $messageType | isSuccess = $isSuccess | errorMessage = $errorMessage")
            communicationListeners.forEach { it.onSendMessageCallback(messageType, isSuccess, errorCode, errorMessage) }
            //
            if (messageType == 207) {
                sendCastingTracking(itemName = if (isSuccess) "CastSendPlayContentSuccess" else "CastSendPlayContentError", errorCode = errorCode.toString(), errorMessage = errorMessage)
            }
        }


    }

    fun resetRemoteData() {
        remoteDeviceData?.remotePlayer?.currentDuration = 0L
        remoteDeviceData?.remotePlayer?.totalDuration = 0L
        remoteDeviceData?.remotePlayer?.tracks = null
    }

    private fun updateAllData(data: GetStatusWatchingResponse) {
        getRemoteData()?.let {
            setRemoteData(data = it.copy(
                id = data.id ?: "",
                refId = data.refId ?: "",
                type = data.type ?: "",
                hasChangeEpisode = it.episodeIndex != data.indexChapter,
                episodeIndex = data.indexChapter ?: "",
                remotePlayer = RemotePlayerData(
                    state = if (data.isPlay == "1") RemotePlayerState.PLAY else RemotePlayerState.PAUSE,
                    currentDuration = Utils.convertStringToLong(data.timeWatched, 0L) * 1000L,
                    bufferDuration = 0,
                    totalDuration = Utils.convertStringToLong(data.duration, 0L) * 1000L,
                    tracks = kotlin.run {
                        val tracks = mutableListOf<PlayerControlView.Data.Track>()
                        val trackSub = data.sub?.item?.map { item -> PlayerControlView.Data.Track(id = item.id ?:"", name = item.name ?:"", type = TrackType.TEXT.ordinal, isSelected = item.id == data.sub?.selectId) } ?: listOf()
                        val trackAudio = data.audio?.item?.map { item -> PlayerControlView.Data.Track(id = item.id ?:"", name = item.name ?:"", type = TrackType.AUDIO.ordinal, isSelected = item.id == data.audio?.selectId) } ?: listOf()
                        tracks.clear()
                        tracks.addAll(trackSub)
                        if (trackAudio.size > 1) tracks.addAll(trackAudio)
                        return@run tracks
                    }
                )
            ))
        }
        //
        updateCastingItemEpisodeIndex(episodeIndex = data.indexChapter ?: "0")
    }
    // endregion



    //region Fire event to remote device
    fun sendEventGetBoxInfo() {
        if (isConnected) {
            val data = GetReceiverInfo(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = GetReceiverInfoRequest()
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {
                    sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVGetInfoRequest()))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventGetBoxInfo -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }
    fun sendEventGetStatusWatching(id: String, refId: String) {
        if (isConnected) {
            if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                val data = GetStatusWatching(
                    senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                    senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                    senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                    senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                    senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                    data = GetStatusWatchingRequest(id = id, refId = refId)
                )
                when (getCurrentConnection()) {
                    is FBoxDeviceInfoV2 -> {
                        sendMessage(data = FBoxObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfo -> {
                        sendMessage(data = FSamsungObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfoExternal -> {
                        sendMessage(data = FSamsungObjectSenderExternal(data = data))
                    }
                    is FAndroidTVDeviceInfo -> {

                    }
                    is FAndroidTVDeviceInfoExternal -> {
                        sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                    }
                    else -> { }
                }
                Logger.d("PairingControlConnectionHelper -> SendEventGetStatusWatching -> Data: $data")
            }
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }


    fun sendEventPlayContent(
        id: String = "",
        highlightId: String = "",
        refId: String = "",
        appId: String = "",
        title: String = "",
        image: String = "",
        indexChapter: String = "",
        isPlay: String = "",
        isLive: String = "",
        timeWatched: String = "",
        type: String = "",
        deepLink: String = "",
        token: String = "",
        userId: String = "",
        userPhone: String = "",
        username: String = "",
        isFollow: String = "",
        currentAutoProfile: String = "",
        beginTime: String = "",
        endTime: String = "",
        autoProfiles: List<String> = listOf(),
        bookmarkInfo: BookmarkInfo = BookmarkInfo()
    ) {
        Logger.d("PairingControlConnectionHelper -> SendEventPlayContent -> Id: $id | RefId: $refId")
        if (isConnected) {
            val senderName = MainApplication.INSTANCE.sharedPreferences.displayName()
            val senderUserId = MainApplication.INSTANCE.sharedPreferences.userId()
            val profileId = MainApplication.INSTANCE.sharedPreferences.profileId()
            val profileType = MainApplication.INSTANCE.sharedPreferences.profileType()
            // save temp casting item
            val iType = when (type) {
                "vod" -> ItemType.VOD
                "livetv" -> ItemType.LiveTV
                "eventtv" -> ItemType.EventTV
                "event" -> ItemType.Event
                else -> ItemType.Unknown
            }
            val mapId = when (type) {
                "vod" -> id
                "livetv" -> id
                "eventtv" -> highlightId
                "event" -> highlightId
                else -> id
            }
            com.fptplay.mobile.features.pairing_control.Utils.saveTempCastingData(
                data = CastingData(
                    id = mapId,
                    refId = refId,
                    episodeId = indexChapter,
                    title = title,
                    image = image,
                    iType = iType,
                    autoProfiles = autoProfiles
                )
            )
            //

            //region Update session
            setSessionRunning(isRunning = true)
            //endregion

            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    if (refId != "-1") {
                        sendMessage(FBoxObjectSender(data = PlayContent(senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), senderName = senderName, senderUserId = senderUserId, senderProfileId = profileId, senderProfileType = profileType, data = PlayContentRequest(id = id, highlightId = highlightId, refId = refId, appId = appId, title = title, indexChapter = indexChapter, isPlay = isPlay, isLive = isLive, timeWatched = timeWatched, type = type, deeplink = deepLink, token = token, userId = userId, userPhone = userPhone, userName = username, isFollow = isFollow, xAgent = getXAgent(), bitrate = currentAutoProfile, beginTime = beginTime, endTime = endTime, bookmarkInfo = bookmarkInfo))))
                    } else {
                        // Local message
                        communicationListener.onMessage(FBoxObjectReceiver(ReceivePlayContent(type = "207", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), senderName = senderName, senderUserId = senderUserId, senderProfileId = profileId, senderProfileType = profileType, result = "-1")))
                    }
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(FSamsungObjectSender(data = PlayContent(senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), senderName = senderName, senderUserId = senderUserId, senderProfileId = profileId, senderProfileType = profileType, data = PlayContentRequest(id = id, highlightId = highlightId, refId = refId, appId = appId, title = title, indexChapter = indexChapter, isPlay = isPlay, isLive = isLive, timeWatched = timeWatched, type = type, deeplink = deepLink, token = token, userId = userId, userPhone = userPhone, userName = username, isFollow = isFollow, xAgent = getXAgent(), bitrate = currentAutoProfile))))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(FSamsungObjectSenderExternal(data = PlayContent(senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), senderName = senderName, senderUserId = senderUserId, senderProfileId = profileId, senderProfileType = profileType, data = PlayContentRequest(id = id, highlightId = highlightId, refId = refId, appId = appId, title = title, indexChapter = indexChapter, isPlay = isPlay, isLive = isLive, timeWatched = timeWatched, type = type, deeplink = deepLink, token = token, userId = userId, userPhone = userPhone, userName = username, isFollow = isFollow, xAgent = getXAgent(), bitrate = currentAutoProfile))))
                }
                is FAndroidTVDeviceInfo -> {
                    val builder = if (type.contains("event")) {
                        Uri.Builder()
                            .scheme("https")
                            .authority("fptplay.vn")
                            .appendPath("event")
                            .appendPath(mapId)
                            .appendQueryParameter("fromCast", "true")
                            .appendQueryParameter("type", type)
                            .appendQueryParameter("blockType", "")
                            .appendQueryParameter("userInfor", buildUserInfo(
                                profileId = profileId,
                                profileType = profileType,
                                userId = userId,
                                userName = username,
                                userPhone = userPhone,
                                token = token.safeDashSymbol()
                            ))
                            .apply {
                                if (isPlayDirect(type = type)) {
                                    appendQueryParameter("playDirect", "true")
                                }
                            }
                            .build()
                    } else {
                        val deeplink = deepLink.replace(".html","")
                        Uri.parse(deeplink)
                            .buildUpon()
                            .scheme("https")
                            .authority("fptplay.vn")
                            .appendQueryParameter("fromCast", "true")
                            .appendQueryParameter("type", type)
                            .appendQueryParameter("episodeId", indexChapter)
                            .appendQueryParameter("currentDuration", "${Utils.convertStringToLong(timeWatched, 0L)}")
                            .appendQueryParameter("userInfor", buildUserInfo(
                                profileId = profileId,
                                profileType = profileType,
                                userId = userId,
                                userName = username,
                                userPhone = userPhone,
                                token = token.safeDashSymbol()
                            ))
                            .apply {
                                if (isPlayDirect(type = type)) {
                                    appendQueryParameter("playDirect", "true")
                                }
                            }
                            .build()
                    }
                    sendMessage(FAndroidTVObjectSender(data = FAndroidTVPlayContent(title = title, deeplink = builder.toString())))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    val builder = if (type.contains("event")) {
                        Uri.Builder()
                            .scheme("https")
                            .authority("fptplay.vn")
                            .appendPath("event")
                            .appendPath(mapId)
                            .appendQueryParameter("fromCast", "true")
                            .appendQueryParameter("type", type)
                            .appendQueryParameter("blockType", "")
                            .appendQueryParameter("isPairingByCode", "true")
                            .appendQueryParameter("userInfor", buildUserInfo(
                                profileId = profileId,
                                profileType = profileType,
                                userId = userId,
                                userName = username,
                                userPhone = userPhone,
                                token = token.safeDashSymbol()
                            ))
                            .apply {
                                if (isPlayDirect(type = type)) {
                                    appendQueryParameter("playDirect", "true")
                                }
                            }
                            .build()
                    } else {
                        val deeplink = deepLink.replace(".html","")
                        Uri.parse(deeplink)
                            .buildUpon()
                            .scheme("https")
                            .authority("fptplay.vn")
                            .appendQueryParameter("fromCast", "true")
                            .appendQueryParameter("type", type)
                            .appendQueryParameter("episodeId", indexChapter)
                            .appendQueryParameter("currentDuration", "${Utils.convertStringToLong(timeWatched, 0L)}")
                            .appendQueryParameter("isPairingByCode", "true")
                            .appendQueryParameter("userInfor", buildUserInfo(
                                profileId = profileId,
                                profileType = profileType,
                                userId = userId,
                                userName = username,
                                userPhone = userPhone,
                                token = token.safeDashSymbol()
                            ))
                            .apply {
                                if (isPlayDirect(type = type)) {
                                    appendQueryParameter("playDirect", "true")
                                }
                            }
                            .build()
                    }
                    sendMessage(FAndroidTVObjectSenderExternal(data = PlayContent(senderId = MainApplication.INSTANCE.sharedPreferences.androidId().safeDashSymbol(), senderName = senderName.safeDashSymbol(), senderUserId = senderUserId.safeDashSymbol(), senderProfileId = profileId.safeDashSymbol(), senderProfileType = profileType, data = PlayContentRequest(id = id, highlightId = highlightId, refId = refId, appId = appId, title = title, indexChapter = indexChapter, isPlay = isPlay, isLive = isLive, timeWatched = timeWatched, type = type, deeplink = builder.toString(), token = token, userId = userId, userPhone = userPhone, userName = username, isFollow = isFollow, xAgent = getXAgent(), bitrate = currentAutoProfile))))
                }
                else -> { }
            }
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventKey(key: String) {
        if (isConnected) {
            if (!com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning() && (key == KeyCode.PLAY || key == KeyCode.PAUSE))
            {
                // Do nothing
            }
            else {
                val data = PressKey(
                    senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                    senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                    senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                    senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                    senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                    data = PressKeyRequest(key = key)
                )
                when (getCurrentConnection()) {
                    is FBoxDeviceInfoV2 -> {
                        sendMessage(data = FBoxObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfo -> {
                        sendMessage(data = FSamsungObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfoExternal -> {
                        sendMessage(data = FSamsungObjectSenderExternal(data = data))
                    }
                    is FAndroidTVDeviceInfo -> {

                    }
                    is FAndroidTVDeviceInfoExternal -> {
                        sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                    }
                    else -> { }
                }
                Logger.d("PairingControlConnectionHelper -> SendKey -> Data: $data")

            }
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventTogglePlayer(isPlay: Boolean) {
        if (isConnected) {
            if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                when (getCurrentConnection()) {
                    is FAndroidTVDeviceInfo -> {
                        if (isPlay) {
                            sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVPlayRequest()))
                        } else {
                            sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVPauseRequest()))
                        }
                    }
                    else -> {}
                }
            }
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventSeek(id: String, refId: String, indexChapter: String, timeWatched: String) {
        if (isConnected) {
            if (id.isBlank()) return
            if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                val data = SeekTime(
                    senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                    senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                    senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                    senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                    senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                    data = SeekTimeRequest(id = id, refId = refId, indexChapter = indexChapter, timeWatched = timeWatched)
                )
                when (getCurrentConnection()) {
                    is FBoxDeviceInfoV2 -> {
                        sendMessage(data = FBoxObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfo -> {
                        sendMessage(data = FSamsungObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfoExternal -> {
                        sendMessage(data = FSamsungObjectSenderExternal(data = data))
                    }
                    is FAndroidTVDeviceInfo -> {
                        sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVSeekRequest(time = Utils.convertStringToLong(timeWatched, 0L) * 1000L)))
                    }
                    is FAndroidTVDeviceInfoExternal -> {
                        sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                    }
                    else -> { }
                }
                Logger.d("PairingControlConnectionHelper -> SendEventSeek -> Data: $data")
            }
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventChangeTrack(id: String, refId: String, selectId: String, type: String) {
        if (isConnected) {
            if (com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning()) {
                val data = SetTrack(
                    senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                    senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                    senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                    senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                    senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                    data = SetTrackRequest(id = id, refId = refId, type = type, selectId = selectId)
                )
                when (getCurrentConnection()) {
                    is FBoxDeviceInfoV2 -> {
                        sendMessage(data = FBoxObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfo -> {
                        sendMessage(data = FSamsungObjectSender(data = data))
                    }
                    is FSamsungTVDeviceInfoExternal -> {
                        sendMessage(data = FSamsungObjectSenderExternal(data = data))
                    }
                    is FAndroidTVDeviceInfo -> {
                        sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVChangeTrackRequest(trackId = selectId.toLongOrNull() ?: 0, type = if (type == "audio") TrackType.AUDIO.ordinal else TrackType.TEXT.ordinal)))
                    }
                    is FAndroidTVDeviceInfoExternal -> {
                        sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                    }
                    else -> { }
                }
                Logger.d("PairingControlConnectionHelper -> SendEventChangeTrack -> Data: $data")
            }
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventChangeVolume(volume: String) {
        if (isConnected) {
            val data = ChangeReceiverVolume(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = ChangeReceiverVolumeRequest(value = volume)
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {
                    sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVSetVolumeRequest(volume = Utils.convertStringToInt(volume, 0))))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventChangeVolume -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventChangeMuteAudio(isMute: Boolean) {
        if (isConnected) {
            val data = MuteUnmuteReceiver(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = MuteUnmuteReceiverRequest(value = if (isMute) "1" else "0")
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {
                    sendMessage(data = FAndroidTVObjectSender(data = FAndroidTVMuteUnmuteRequest(if (isMute) 1 else 0)))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventChangeMuteAudio -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventChangeReceiverName(receiverName: String) {
        if (isConnected) {
            val data = ChangeReceiverName(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = ChangeReceiverNameRequest(value = receiverName)
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {

                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventChangeReceiverName -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventConnectReceiver(skipCheckConnected: Boolean = false) {
        val condition = if (skipCheckConnected) true else isConnected
        if (condition) {
            val data = ConnectReceiver(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = ConnectReceiverRequest(value = Util.getDeviceName())
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {
                    val androidData = FAndroidTVConnectReceiverRequest(
                        senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                        senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                        senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                        data = ConnectReceiverRequest(value = Util.getDeviceName())
                    )
                    sendMessage(data = FAndroidTVObjectSender(data = androidData))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventConnectReceiver -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }


    fun sendEventDisconnectReceiver() {
        if (isConnected) {
            val data = DisconnectReceiver(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = DisconnectReceiverRequest(value = Util.getDeviceName())
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {

                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventDisconnectReceiver -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventChangeChapter(
        id: String = "",
        refId: String = "",
        indexChapter: String = "",
        timeWatched: String = "",
        indexAction: String = "1"
    ) {
        if (isConnected) {
            val data = ChangeChapter(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = ChangeChapterRequest(id = id, refId = refId, indexChapter = indexChapter, timeWatched = timeWatched, indexAction = indexAction)
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {
                    sendMessage(FAndroidTVObjectSender(
                        data = FAndroidTVChangeChapter(
                            episodeId = Utils.convertStringToInt(indexChapter, 0),
                            currentDuration = Utils.convertStringToLong(timeWatched, 0L)
                        )
                    ))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventChangeChapter -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventActionEvent(type: String = "") {
        if (isConnected) {
            val data = ActionEvent(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = ActionEventRequest(id = castingData?.id?: "", refId = castingData?.refId?: "", type = type)
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))
                }
                is FAndroidTVDeviceInfo -> {
                    val androidData = FAndroidTVActionEventRequest(senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), data = ActionEventRequest(id = castingData?.id?: "", refId = castingData?.refId?: "", type = type))
                    sendMessage(data = FAndroidTVObjectSender(data = androidData))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventActionEvent -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }

    fun sendEventStopSession() {
        if (isConnected && isSessionRunning) {
            val data = StopSession(
                senderId = MainApplication.INSTANCE.sharedPreferences.androidId(),
                senderName = MainApplication.INSTANCE.sharedPreferences.displayName(),
                senderUserId = MainApplication.INSTANCE.sharedPreferences.userId(),
                senderProfileId = MainApplication.INSTANCE.sharedPreferences.profileId(),
                senderProfileType = MainApplication.INSTANCE.sharedPreferences.profileType(),
                data = StopSessionRequest(id = castingData?.id?: "", refId = castingData?.refId?: "")
            )
            when (getCurrentConnection()) {
                is FBoxDeviceInfoV2 -> {
                    sendMessage(data = FBoxObjectSender(data = data))

                    // Local message
                    communicationListener.onMessage(FBoxObjectReceiver(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                }
                is FSamsungTVDeviceInfo -> {
                    sendMessage(data = FSamsungObjectSender(data = data))

                    // Local message
                    communicationListener.onMessage(FSamsungObjectReceiver(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                }
                is FSamsungTVDeviceInfoExternal -> {
                    sendMessage(data = FSamsungObjectSenderExternal(data = data))

                    // Local message
                    communicationListener.onMessage(FSamsungObjectReceiverExternal(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                }
                is FAndroidTVDeviceInfo -> {
                    sendMessage(data = FAndroidTVObjectSender(FAndroidTVStopSessionRequest(senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), data = StopSessionRequest(id = castingData?.id?: "", refId = castingData?.refId?: ""))))

                    // Local message
                    communicationListener.onMessage(FAndroidTVObjectReceiver(FAndroidTVCustomStopSessionResponse(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                }
                is FAndroidTVDeviceInfoExternal -> {
                    sendMessage(data = FAndroidTVObjectSenderExternal(data = data))

                    // Local message
                    communicationListener.onMessage(FAndroidTVObjectReceiverExternal(ReceiveStopSession(type = "212", departure = "0", senderId = MainApplication.INSTANCE.sharedPreferences.androidId(), result = "1")))
                }
                else -> { }
            }
            Logger.d("PairingControlConnectionHelper -> SendEventStopSession -> Data: $data")
        }
        else {
            Timber.tag("PairingControlConnectionHelper").w("Pairing Control not connected!")
        }
    }
    //endregion

    //region Common
    private fun isPlayDirect(type: String) : Boolean {
        return type == "vod"
    }

    private fun buildUserInfo(
        userId: String,
        userName: String,
        userPhone: String,
        token: String,
        profileId: String,
        profileType: String
    ) : String {
        val result: StringBuilder = StringBuilder()
        result.append("{")
        result.append("\"senderId\": \"${MainApplication.INSTANCE.sharedPreferences.androidId().safeDashSymbol()}\",")
        result.append("\"senderType\": \"android_mobile\",")
        result.append("\"userId\": \"${userId}\",")
        result.append("\"userName\": \"${userName}\",")
        result.append("\"userPhone\": \"${userPhone}\",")
        result.append("\"profileId\": \"${profileId.safeDashSymbol()}\",")
        result.append("\"profileType\": \"${profileType}\",")
        result.append("\"token\": \"${token}\"")
        result.append("}")
        return result.toString()
    }

    private fun getXAgent() = "android"

    private fun triggerSaveCastingItem() {
        com.fptplay.mobile.features.pairing_control.Utils.getTempCastingData()?.let {
            setCastingItem(data = it)
        }
        //
        com.fptplay.mobile.features.pairing_control.Utils.clearTempCastingData()
    }
    //endregion

    //region Interval get receiver app info to determine app running
    private var retryGetAppStarted = 0
    private var handlerReceiverAppStarted: Handler? = null
    private var runnableReceiverAppStarted = Runnable {
        intervalReceiverAppStarted()
    }

    private fun initReceiverAppStartedHandler() {
        retryGetAppStarted = 0
        removeIntervalReceiverAppStarted()
        Looper.getMainLooper()?.let { handlerReceiverAppStarted = Handler(it) }
        handlerReceiverAppStarted?.post(runnableReceiverAppStarted)
    }

    private fun intervalReceiverAppStarted() {
        retryGetAppStarted += 1
        if (retryGetAppStarted > MAX_RETRY_RECEIVER_APP_START) {
            disconnect(sendEventToReceiver = false)
            notifyConnectError(errorCode = 0, message = "Connect Error. Receiver device not responding (Request connect to device (type=200))")
            return
        } else {
            sendEventConnectReceiver(skipCheckConnected = true)
            if (currentConnection is FSamsungTVDeviceInfo) {
                sendEventGetStatusWatching(id = getCastingItem()?.id ?: "", refId = getCastingItem()?.refId ?: "")
            }
            handlerReceiverAppStarted?.postDelayed(runnableReceiverAppStarted, TIME_RETRY_RECEIVER_APP_START)
        }
    }

    fun removeIntervalReceiverAppStarted() {
        if (handlerReceiverAppStarted != null) {
            handlerReceiverAppStarted?.removeCallbacks(runnableReceiverAppStarted)
            handlerReceiverAppStarted = null
        }
    }
    //endregion


    //region Interval keep connect for samsung tv
    private var handlerKeepConnectionForFSamsungTV: Handler? = null
    private var runnableKeepConnectionForFSamsungTV = Runnable {
        intervalKeepConnectionForFSamsungTV()
    }

    private fun initKeepConnectionForFSamsungTVHandler() {
        removeIntervalKeepConnectionForFSamsungTV()
        Looper.getMainLooper()?.let { handlerKeepConnectionForFSamsungTV = Handler(it) }
        handlerKeepConnectionForFSamsungTV?.post(runnableKeepConnectionForFSamsungTV)
    }

    private fun intervalKeepConnectionForFSamsungTV() {
        Logger.d("PairingControlConnectionHelper => FSamsungTV => Interval keep connect")
        sendEventGetBoxInfo()
        handlerKeepConnectionForFSamsungTV?.postDelayed(runnableKeepConnectionForFSamsungTV, INTERVAL_TIME_KEEP_CONNECTION_FOR_FSAMSUNG_TV)
    }

    private fun removeIntervalKeepConnectionForFSamsungTV() {
        if (handlerKeepConnectionForFSamsungTV != null) {
            handlerKeepConnectionForFSamsungTV?.removeCallbacks(runnableKeepConnectionForFSamsungTV)
            handlerKeepConnectionForFSamsungTV = null
        }
    }
    //endregion

    //region Tracking
    private fun sendCastingTracking(
        itemName: String,
        errorCode: String,
        errorMessage: String,
    ) {
        Logger.d("PairingControlConnectionHelper -> sendCastingTracking -> itemName = $itemName | errorCode = $errorCode | errorMessage = $errorMessage")
        MainApplication.INSTANCE.trackingProxy.sendEvent(
            InforMobile(
                infor = MainApplication.INSTANCE.trackingInfo,
                logId = "17",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Casting",
                event = "Casting",
                errorCode = errorCode,
                errorMessage = errorMessage,
                itemId = getRemoteData()?.id ?:"",
                itemName = itemName,
                url = currentConnection.getSocketChannel(),
                appSource = currentConnection.getName(),
                status = getRemoteData()?.remotePlayer?.state?.toString() ?:"",
                chapterId = getRemoteData()?.episodeIndex ?: "",
                type = getRemoteData()?.type ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                elapsedTimePlaying = getRemoteData()?.remotePlayer?.currentDuration?.toString() ?:"",
                duration = getRemoteData()?.remotePlayer?.totalDuration?.toString() ?:"",
                refItemId = getRemoteData()?.refId ?: "",
                issueId = TrackingUtil.createIssueId(),
            )
        )
    }


    private fun sendTrackingDisconnect() {
        Logger.d("PairingControlConnectionHelper -> sendTrackingDisconnect")
        MainApplication.INSTANCE.trackingProxy.sendEvent(
            InforMobile(
                infor = MainApplication.INSTANCE.trackingInfo,
                logId = "516",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "DisconnectToDevice",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun sendTrackingDisconnectRejectCast() {
        Logger.d("PairingControlConnectionHelper -> sendTrackingDisconnect")
        MainApplication.INSTANCE.trackingProxy.sendEvent(
            InforMobile(
                infor = MainApplication.INSTANCE.trackingInfo,
                logId = "516",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "DisconnectToDevice",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                errorMessage = "RejectCast",
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun DeviceInfo?.getName(): String {
        return when (this) {
            is FBoxDeviceInfoV2 -> "Box IPTV"
            is FAndroidTVDeviceInfo -> "Smart TV Android/Box OTT (Internal)"
            is FAndroidTVDeviceInfoExternal -> "Smart TV Android/Box OTT (External)"
            is FSamsungTVDeviceInfo -> "Samsung TV (Internal)"
            is FSamsungTVDeviceInfoExternal -> "Samsung TV (External)"
            else -> ""
        }
    }

    private fun DeviceInfo?.getSocketChannel(): String {
        return when (this) {
            is FBoxDeviceInfoV2 -> this.response.additionalData.channelws ?: ""
            is FAndroidTVDeviceInfo -> ""
            is FAndroidTVDeviceInfoExternal -> this.channelWs ?: ""
            is FSamsungTVDeviceInfo -> ""
            is FSamsungTVDeviceInfoExternal -> this.channelWs ?: ""
            else -> ""
        }
    }

    //endregion


    interface RemotePlayerStateListener {
        fun onStateChanged(state: RemotePlayerState)
        fun onActionEvent(type: String)
        fun onCastSessionChanged()
    }

    companion object {
        private const val TIME_RETRY_RECEIVER_APP_START = 5000L
        private const val MAX_RETRY_RECEIVER_APP_START = 6
        //
        private const val INTERVAL_TIME_KEEP_CONNECTION_FOR_FSAMSUNG_TV = 30000L
    }
}