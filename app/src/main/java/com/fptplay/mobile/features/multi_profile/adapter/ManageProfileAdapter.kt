package com.fptplay.mobile.features.multi_profile.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.MultiProfileManageProfileItemBinding
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils.isProfileKid
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils.isProfilePrivate
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.domain.entity.fplay.user.Profile
import com.xhbadxx.projects.module.domain.entity.fplay.user.ProfileItem
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class ManageProfileAdapter(context: Context, val sharedPreferences: SharedPreferences) :
    BaseAdapter<ProfileItem, ManageProfileAdapter.ManageProfileViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ManageProfileViewHolder {
        return ManageProfileViewHolder(
            MultiProfileManageProfileItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun onBindViewHolder(holder: ManageProfileViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }


    inner class ManageProfileViewHolder(private val binding: MultiProfileManageProfileItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        private val avatarSize by lazy {
            Utils.getSizeInPixel(
                context = binding.root.context,
                resId = R.dimen.mega_account_item_icon_size
            )
        }

        init {
            binding.root.setOnClickListener {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }

        fun bind(profile: ProfileItem) {
            when (profile) {
                ProfileItem.AddProfile -> bindAddProfile(profile)
                is Profile -> bindProfile(profile)
            }
        }

        private fun bindProfile(data: Profile) {
            binding.apply {
                avatarView.bindProfile(
                    url  = data.avatarUrl,
                    width = avatarSize,
                    height = avatarSize,
                    kidProfile = data.isProfileKid(),
                    privateProfile = false // not show icon lock on avatar here
                )
                avatarView.setEditMode(
                    inEditMode = false,
                    canEdit = data.allowEdit
                )
                tvName.text = data.name
                tvName.setTextColor(ContextCompat.getColor(binding.root.context, R.color.white_87))

                if (data.selected) {
                    tvProfileSelected.visible()
                    ivLockProfileItem.gone()    // force gone, only one at a time, priority tvProfileSelected
                } else {
                    tvProfileSelected.gone()
                    if (data.isProfilePrivate()) {
                        ivLockProfileItem.show()
                    } else {
                        ivLockProfileItem.gone()
                    }
                }
                ivAction.show()


            }
        }

        private fun bindAddProfile(data: ProfileItem) {
            binding.apply {
                avatarView.bindProfileItem(data, avatarSize, avatarSize)
                tvName.setText(R.string.multi_profile_manage_add_profile_item_title)
                tvName.setTextColor(ContextCompat.getColor(binding.root.context, R.color.white_38))
                tvProfileSelected.gone()
                ivLockProfileItem.gone()
                ivAction.gone()
            }
        }
    }

}