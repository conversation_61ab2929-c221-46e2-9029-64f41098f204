package com.fptplay.mobile.features.sport_interactive

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.SportInteractiveFragmentBinding
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.TabLayoutItemBinding
import com.fptplay.mobile.features.sport_interactive.adapter.SportInteractiveTabAdapter
import com.fptplay.mobile.features.sport_interactive.model.*
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils.convertToTabItems
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
@AndroidEntryPoint
class SportInteractiveFragment :
    BaseFragment<SportInteractiveViewModel.SportInteractiveState, SportInteractiveViewModel.SportInteractiveIntent>() {
    override val hasEdgeToEdge = false
    private var _binding: SportInteractiveFragmentBinding? = null
    private val binding get() = _binding!!
    override val viewModel: SportInteractiveViewModel by activityViewModels()
    private val safeArgs: SportInteractiveFragmentArgs by navArgs()

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    private val sportInteractiveFirebaseServices by lazy { SportInteractiveFirebaseServices() }

    private var sportInteractiveTbAdapter: SportInteractiveTabAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    override fun setUpEdgeToEdge() {
        //this fragment should do nothing to current edge to edge state
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SportInteractiveFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onDestroyView() {
        super.onDestroyView()
        sportInteractiveTbAdapter = null
        _binding = null
        sportInteractiveFirebaseServices.stopListenEvent()
    }

    override fun bindComponent() {
    }
    private fun updateSportInteractiveTabLayout(data:List<SportInteractiveTabItem>) {
        if(sportInteractiveTbAdapter == null){
            sportInteractiveTbAdapter = SportInteractiveTabAdapter(binding.root.context, childFragmentManager, lifecycle, data)
            binding.vpItem.apply {
                adapter = sportInteractiveTbAdapter
            }
            TabLayoutMediator(binding.tlMenuSportInteractive, binding.vpItem) { tab, position ->
                if (context.isTablet()) {
                    tab.customView = TabLayoutItemBinding.inflate(layoutInflater).root
                }
                tab.text = sportInteractiveTbAdapter?.tabName?.get(position)
                tab.view.setOnLongClickListener { true }
            }.attach()
        }
        else{
            val count = binding.tlMenuSportInteractive.tabCount
            for (i in 0 until count){
                binding.tlMenuSportInteractive.getTabAt(i)?.text = data[i].title
            }
        }

    }

    override fun bindEvent() {
        log601(0) //log kibana select first tab when init
        binding.tlMenuSportInteractive.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val position = tab?.position ?: 0
                binding.vpItem.currentItem = position
                log601(position) //log when select other tab
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
        binding.ivClose.setOnClickListener {
            findNavController().popBackStack()
        }
    }
    private fun log601(position: Int){
        //log 601
        val bundle:Bundle = bundleOf()
        bundle.putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, TrackingUtil.tabIdSportInteractive[position])
        bundle.putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "AccessFunction")
        parentFragment?.parentFragment?.setFragmentResult(Constants.SPORT_INTERACTIVE_LOG_KIBANA, bundle)
    }
    override fun onResume() {
        super.onResume()
        //check force update
    }

    override fun bindData() {
        //testData()
       bindFirestore(eventId = safeArgs.id)
    }

    private fun bindFirestore(eventId: String) {
        sportInteractiveFirebaseServices.listenSportEventData(
            lifecycleOwner = viewLifecycleOwner,
            eventId = eventId
        ) {
            Timber.tag("tam-sport").e("SportInteractiveData $it")
            updateSportInteractiveTabLayout(it.convertToTabItems())
            viewModel.triggerSportMatchDetailUpdate(it?.commonData)
            viewModel.triggerSportMatchProcessUpdate(it?.matchProcess)
            viewModel.triggerSportTeamSquadUpdate(it?.squad)
            viewModel.triggerSportMatchLiveScoreUpdate(it?.liveScore)
            viewModel.triggerSportMatchStatisticUpdate(it?.statistic)
        }
    }
    override fun SportInteractiveViewModel.SportInteractiveState.toUI() {
        Timber.d("toUI $this")
        when (this) {
            is SportInteractiveViewModel.SportInteractiveState.Loading -> {
            }

            is SportInteractiveViewModel.SportInteractiveState.Error -> {

            }

            else -> {
                Timber.d("Else with $this")
            }
        }
    }

    // region Commons


    // endregion Commons
}