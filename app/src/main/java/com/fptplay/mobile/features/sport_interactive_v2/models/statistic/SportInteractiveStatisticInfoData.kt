package com.fptplay.mobile.features.sport_interactive_v2.models.statistic

import com.fptplay.mobile.features.sport_interactive.model.SportMatchStatistic
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import timber.log.Timber

data class SportInteractiveStatisticInfoData(
    override val id: String = "",
    val actionType: SportMatchStatistic.Statistic.StatisticActionType = SportMatchStatistic.Statistic.StatisticActionType.Unknown(
        ""
    ),
    val actionName: String = "",
    val homeTeamScore: String = "",
    val awayTeamScore: String = ""
) : UIData() {
//    override fun areItemTheSame(newItem: UIData): Boolean {
//        val dataToReturn =
//            (this.actionName == (newItem as SportInteractiveStatisticInfoData).actionName &&
//                    this.actionType == newItem.actionType)
//        Timber.d("******are item the same: $dataToReturn")
//        return dataToReturn
//    }
//
//    override fun areContentTheSame(newItem: UIData): Boolean {
//        val dataToReturn =
//            this == newItem
//        Timber.d("******are content the same: $dataToReturn")
//        Timber.d("********are content the same: ${this} vs ${newItem}")
//        return this == newItem
//    }
}