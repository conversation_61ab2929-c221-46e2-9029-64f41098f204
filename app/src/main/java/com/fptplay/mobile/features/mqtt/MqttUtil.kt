package com.fptplay.mobile.features.mqtt

import androidx.lifecycle.MutableLiveData
import com.fpl.plugin.mqtt.model.ReceivedMessage
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.features.mqtt.model.MqttAutomaticRetry
import com.fptplay.mobile.features.mqtt.model.MqttConfig
import com.fptplay.mobile.features.mqtt.model.MqttNotificationConfig
import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail
import com.fptplay.mobile.features.mqtt.model.MqttNotificationType
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.playback.PlaybackPlayerRemote.playbackService
import com.fptplay.mobile.features.pladio.service.PlaybackService
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig
import com.xhbadxx.projects.module.util.fplay.SharedPreferences

object MqttUtil {
    const val REMOTE_TOPIC = "remote"
    const val PING_CCU_TOPIC = "pingccu"
    const val SEPARATOR = "/"
    const val LWT_TOPIC = "will/disconect"
    const val LWT_MESSAGE = "{}"
    const val ACTION_START = "start"
    const val ACTION_END = "stop"
    const val LOG_ID_ERROR = "114"
    const val EVENT_ERROR = "Error"
    const val ACTION_LIMIT_CCU = "limit_ccu"
    const val PUBLISHER = "_Publisher"
    const val SUBSCRIBER = "_Subcriber"
    var mqttConfig: MqttNotificationConfig? = null

    fun MQTTConfig.AutomaticReconnect.isAutoReconnect(): Boolean {
        return this.enable && this.minRetryInterval > 0 && this.maxRetryInterval > 0 && this.minRetryInterval < this.maxRetryInterval
    }
    fun initMqttConfig(data: MQTTConfig.Data){
//        mqttConfig = MqttNotificationConfig(
//            enable = data.option.automaticReconnect.enable,
//            rooms = data.rooms,
//            automaticRetry = MqttAutomaticRetry(
//                enable = data.option.automaticReconnect.enable,
//                minRetryInterval = data.option.automaticReconnect.minRetryInterval,
//                maxRetryInterval = data.option.automaticReconnect.maxRetryInterval,
//                random = data.option.automaticReconnect.random
//            ),
//            options = data.option.options,
//            config = MqttConfig(
//                expires = data.expires,
//                jwtFrom = data.jwtFrom,
//                option = data.option,
//                token = data.token,
//                url = data.url
//            )
//        )
        mqttConfig = MqttNotificationConfig(
            enable = data.option.automaticReconnect.enable,
            rooms = emptyList(),
            automaticRetry = MqttAutomaticRetry(
                enable = data.option.automaticReconnect.enable,
                minRetryInterval = data.option.automaticReconnect.minRetryInterval,
                maxRetryInterval = data.option.automaticReconnect.maxRetryInterval,
                random = 0
            ),
            options = emptyList(),
            config = MqttConfig(
                expires = data.expires,
                jwtFrom = data.jwtFrom,
                option = data.option,
                token = data.token,
                url = data.url
            )
        )
    }

    private fun restoreMqttConfig(data: MqttNotificationDetail){
        mqttConfig = mqttConfig?.copy(
            enable = data.mqtt?.enable ?: false,
            rooms = data.mqtt?.rooms ?: emptyList(),
            automaticRetry = MqttAutomaticRetry(
                enable = data.mqtt?.automaticRetry?.enable ?: false,
                minRetryInterval = data.mqtt?.automaticRetry?.minRetryInterval ?: 0,
                maxRetryInterval = data.mqtt?.automaticRetry?.maxRetryInterval ?: 0,
                random = data.mqtt?.automaticRetry?.random ?: 0
            ),
            options = data.mqtt?.options ?: emptyList(),
        )
    }

    var getCurrentMqttConfig: MqttNotificationConfig?
        get() = if (mqttConfig != null) {
            mqttConfig
        } else MqttNotificationConfig.emptyMqttNotificationConfig
        set(value) {
            mqttConfig = value
        }

    fun convertToData(details: MqttNotificationDetail?,callback: (Boolean) -> Unit = {}){
        if(details == null) return
        when(details.notificationType){
            MqttNotificationType.ALL.value -> {
                restoreMqttConfig(details)
                callback.invoke(true)
            }
            MqttNotificationType.ENABLE.value -> {
                mqttConfig?.enable = details.mqtt?.enable ?: false
                callback.invoke(true)
            }
            MqttNotificationType.AUTOMATIC_RETRY.value -> {
              mqttConfig?.automaticRetry = details.mqtt?.automaticRetry
            }
            MqttNotificationType.OPTIONS.value -> {
                mqttConfig?.options = details.mqtt?.options ?: emptyList()
            }
            MqttNotificationType.ROOMS.value -> {
                mqttConfig?.rooms = details.mqtt?.rooms ?: emptyList()
            }
            else -> {}
        }
    }

    fun MQTTConfig.AutomaticReconnect.getReconnectInterval(): Pair<Long, Long> {
        return Pair(this.minRetryInterval.toLong(), this.maxRetryInterval.toLong())
    }

    fun Publisher.toJsonString(): String {
        return Gson().toJson(this)
    }

    fun String.fromJsonString(): Publisher? {
        return try {
            Gson().fromJson(this, Publisher::class.java)
        } catch (ex: Exception) {
            null
        }
    }

    fun Int.toMillisecond(): Long {
        return (this * 1000).toLong()
    }

    fun ReceivedMessage.mapToPublisher(): Publisher? {
        val payload = this.message.payload.decodeToString()
        return payload.fromJsonString()
    }
}