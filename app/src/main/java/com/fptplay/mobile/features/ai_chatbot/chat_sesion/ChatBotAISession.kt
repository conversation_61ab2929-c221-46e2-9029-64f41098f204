package com.fptplay.mobile.features.ai_chatbot.chat_sesion
import com.fptplay.mobile.common.utils.DateTimeUtils
import timber.log.Timber
import javax.inject.Inject
class ChatBotAISession @Inject constructor(){
    private var chatSession:Long = 0L
    private var chatListener: ChatSessionRealtimeTrackerListener? = null

    fun getChatSession():String{
        return if (chatSession>0){
            Timber.d("AIChatBotFragment chatSession : $chatSession ---- chatSessionString:${DateTimeUtils.getLogSessionAtCurrentTime(chatSession)}")
            DateTimeUtils.getLogSessionAtCurrentTime(chatSession)
        } else ""
    }

    fun addChatBotAITrackingCallback(listener: ChatSessionRealtimeTrackerListener) {
        chatListener = listener
    }

    fun removeChatBotAITrackingCallback() {
        chatSession = 0L
        chatListener = null
    }

    fun startChatSessionTracker(){
        chatSession = System.currentTimeMillis()
        Timber.d("AIChatBotFragment startChatSessionTracker : $chatSession ---- chatSessionString:${DateTimeUtils.getLogSessionAtCurrentTime(chatSession)}")
        chatListener?.onStartChatSessionTracker(chatSession)
    }

    fun resumeChatSessionTracker(){
        chatSession = 0L // reset
        chatSession= System.currentTimeMillis()
        Timber.d("AIChatBotFragment resumeChatSessionTracker : $chatSession ---- chatSessionString:${DateTimeUtils.getLogSessionAtCurrentTime(chatSession)}")
        chatListener?.onResumeChatSessionTracker(chatSession)
    }

    fun stopChatSessionTracker(){
        chatSession = 0L
        Timber.d("AIChatBotFragment stopChatSessionTracker")
        chatListener?.onStopChatSessionTracker(chatSession)
    }

    interface  ChatSessionRealtimeTrackerListener{
        fun onStartChatSessionTracker(logSession: Long = 0L) {}
        fun onResumeChatSessionTracker(logSession: Long = 0L) {}
        fun onStopChatSessionTracker(logSession: Long = 0L) {}
    }
}