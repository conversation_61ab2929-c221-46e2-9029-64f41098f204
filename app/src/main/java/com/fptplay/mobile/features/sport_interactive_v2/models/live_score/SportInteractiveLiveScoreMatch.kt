package com.fptplay.mobile.features.sport_interactive_v2.models.live_score

import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData

data class SportInteractiveLiveScoreMatch(
    //common
    val time: String = "",
    val eventId: String = "",
    val eventType: SportMatchLiveScores.LiveScore.Match.MatchEventType = SportMatchLiveScores.LiveScore.Match.MatchEventType.Unknown(
        ""
    ),
    //home team
    override val id: String = "",
    val homeTeamName: String = "",
    val homeTeamShortName: String = "",
    val homeTeamLogo: String = "",
    val homeTeamScore: String = "",

    // away team info (đội khác)
    val awayTeamName: String = "",
    val awayTeamShortName: String = "",
    val awayTeamLogo: String = "",
    val awayTeamScore: String = "",
) : UIData() {
    fun getAwayTeamName(screenContainerType: ScreenContainerType): String {
        return if(screenContainerType == ScreenContainerType.NORMAL_SCREEN)
            awayTeamName.ifBlank { awayTeamShortName }
        else
            awayTeamShortName.ifBlank { awayTeamName }
    }

    fun getHomeTeamName(screenContainerType: ScreenContainerType): String {
        return if(screenContainerType == ScreenContainerType.NORMAL_SCREEN)
            homeTeamName.ifBlank { homeTeamShortName }
        else
            homeTeamShortName.ifBlank { homeTeamName }
    }
}
