package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.SportInteractiveLiveScoreMatchBinding
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.fptplay.mobile.player.utils.invisible
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.util.Utils.checkToShowContent
import com.tear.modules.util.Utils.hide
import com.tear.modules.util.Utils.show
import com.xhbadxx.projects.module.util.common.IEventListener

class SportInteractiveLiveScoreMatchViewHolder(
    private val binding: SportInteractiveLiveScoreMatchBinding,
    private val eventListener: IEventListener<UIData>? = null,
    private val screenContainerType: ScreenContainerType
) : BaseSportInteractiveViewHolder<SportInteractiveLiveScoreMatch>(binding) {

    override fun bind(data: SportInteractiveLiveScoreMatch) {
        binding.apply {
            root.onClickDelay {
                eventListener?.onClickView(absoluteAdapterPosition, binding.root, data)
            }
            tvTime.checkToShowContent(data.time, goneViewWhenNoText = false)
            vTeamInfoHome.bindMatchInfo(data, TeamType.HomeTeam, screenContainerType)
            vTeamInfoAway.bindMatchInfo(data, TeamType.AwayTeam, screenContainerType)

            if (data.eventType is SportMatchLiveScores.LiveScore.Match.MatchEventType.Unknown || data.eventId.isBlank()) {
                ivPlay.invisible()
            } else {
                ivPlay.visible()
            }

        }
    }
}