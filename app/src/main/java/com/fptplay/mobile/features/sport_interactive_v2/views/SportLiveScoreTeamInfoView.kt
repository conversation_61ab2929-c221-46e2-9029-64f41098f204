package com.fptplay.mobile.features.sport_interactive_v2.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import coil.load
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.SportInteractiveLivescoreMatchTeamInfoViewBinding
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.tear.modules.util.Utils.checkToShowContent
import com.tear.modules.util.Utils.hide
import com.xhbadxx.projects.module.util.image.ImageProxy

class SportLiveScoreTeamInfoView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
): ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding = SportInteractiveLivescoreMatchTeamInfoViewBinding.inflate(LayoutInflater.from(context), this)

    fun bindMatchInfo(matchInfo: SportInteractiveLiveScoreMatch, teamType: TeamType, screenContainerType: ScreenContainerType) {
        when(teamType) {
            TeamType.AwayTeam -> bindTeamInfo(
                matchInfo.awayTeamLogo,
                matchInfo.getAwayTeamName(screenContainerType),
                matchInfo.awayTeamScore
            )
            TeamType.HomeTeam -> bindTeamInfo(
                matchInfo.homeTeamLogo,
                matchInfo.getHomeTeamName(screenContainerType),
                matchInfo.homeTeamScore
            )
            is TeamType.Unknown -> clearData()
        }

    }

    private fun bindTeamInfo(logo: String, name: String, score: String) {
        binding.apply {
            if(logo.isNotBlank()) {
                ImageProxy.load(
                    context =binding.root.context,
                    width =   Utils.getSizeInPixel(context = binding.root.context, resId = R.dimen.sport_interactive_live_score_match_item_team_info_logo_size),
                    height =   Utils.getSizeInPixel(context = binding.root.context, resId = R.dimen.sport_interactive_live_score_match_item_team_info_logo_size),
                    target = ivLogo,
                    url = logo,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team
                )
            } else {
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    width = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                    ),
                    height = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                    ),
                    target = binding.ivLogo,
                    data = R.drawable.ic_default_logo_team,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team,
                )
            }
            tvName.checkToShowContent(name, goneViewWhenNoText = true)
            tvScore.checkToShowContent(score, goneViewWhenNoText = false)
        }
    }

    private fun clearData() {
        binding.apply{
            tvName.text = ""
            tvName.hide()
            tvScore.text = ""
            tvScore.hide()
            ivLogo.load(R.drawable.ic_default_logo_team)
        }
    }
}