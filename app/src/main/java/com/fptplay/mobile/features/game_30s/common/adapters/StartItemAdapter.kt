package com.fptplay.mobile.features.game_30s.common.adapters

import android.content.Context
import android.text.Html
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.formatNumberVote
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.StringUtils
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.game.game30s.Game30sStructure
import com.xhbadxx.projects.module.util.image.ImageProxy
import java.util.*

class StartItemAdapter(
    private val context: Context,
    private val type: Type
) : BaseAdapter<BaseObject, StartItemAdapter.StartItemViewHolder>(), DefaultLifecycleObserver {

    private val thumbWidth by lazy { bindThumbWidth() }
    private val thumbHeight by lazy { bindThumbHeight() }
    private val placeholder by lazy { bindPlaceHolder() }

    sealed interface Type {
        object HighLight : Type
        object VodLive : Type
        object AutoExpandSmall : Type
        object AutoExpandLarge : Type
        object HorizontalSliderSmall : Type
        object HorizontalSliderLarge : Type
        object HorizontalLarge : Type
    }

    override fun onBindViewHolder(holder: StartItemAdapter.StartItemViewHolder, position: Int) {
        val currentData = differ.currentList[position]
        holder.bind(currentData)
    }
    private fun bindThumbWidth(): Int {
        return when (type) {
            Type.HorizontalLarge -> if(context.isTablet()) getDimen(R.dimen._113sdp) else getDimen(R.dimen._267sdp)
            Type.AutoExpandLarge -> if(context.isTablet()) getDimen(R.dimen._85sdp) else getDimen(R.dimen._113sdp)
            Type.AutoExpandSmall -> if(context.isTablet()) getDimen(R.dimen._85sdp) else getDimen(R.dimen._113sdp)
            Type.HorizontalSliderLarge -> if(context.isTablet()) getDimen(R.dimen._113sdp) else getDimen(R.dimen._130sdp)
            Type.HorizontalSliderSmall -> if(context.isTablet()) getDimen(R.dimen._113sdp) else getDimen(R.dimen._130sdp)
            else ->if(context.isTablet()) getDimen(R.dimen._310sdp) else  getDimen(R.dimen._267sdp)
        }
    }
    private fun bindThumbHeight(): Int {
        return when (type) {
            Type.HorizontalLarge -> if(context.isTablet()) getDimen(R.dimen._88sdp) else getDimen(R.dimen._150sdp)
            Type.AutoExpandLarge -> if(context.isTablet()) getDimen(R.dimen._127sdp) else getDimen(R.dimen._170sdp)
            Type.AutoExpandSmall -> if(context.isTablet()) getDimen(R.dimen._127sdp) else getDimen(R.dimen._170sdp)
            Type.HorizontalSliderLarge -> if(context.isTablet()) getDimen(R.dimen._64sdp) else  getDimen(R.dimen._78sdp)
            Type.HorizontalSliderSmall -> if(context.isTablet()) getDimen(R.dimen._64sdp) else  getDimen(R.dimen._78sdp)
            Type.VodLive,
            Type.HighLight-> if(context.isTablet())getDimen(R.dimen._174sdp) else getDimen(R.dimen._444sdp)
            else -> getDimen(R.dimen._150sdp)
        }
    }
    private fun getDimen(id: Int): Int {
        return context.resources.getDimensionPixelSize(id)
    }
    inner class StartItemViewHolder(private val viewBinding: StartItemViewBinding) :
        RecyclerView.ViewHolder(viewBinding.root) {
        var data: BaseObject? = null
        init {
            viewBinding.root.apply {
                onClickDelay {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        data = item(absoluteAdapterPosition) ?: object : BaseObject() {})
                }
            }
            viewBinding.ivViewNow?.onClickDelay {
                eventListener?.onClickView(
                    position = absoluteAdapterPosition,
                    view = viewBinding.ivViewNow,
                    data = item(absoluteAdapterPosition) ?: object : BaseObject() {})
            }
            viewBinding.ivShare?.onClickDelay {
                eventListener?.onClickView(
                    position = absoluteAdapterPosition,
                    view = viewBinding.ivShare,
                    data = item(absoluteAdapterPosition) ?: object : BaseObject() {})
            }
        }
        fun bind(data: BaseObject) {
            this.data = data
            if (data is Game30sStructure.Game30sStructureItem) {
                when (type) {
                    Type.VodLive -> {
                        when (data.code) {
                            "live" -> {
                                viewBinding.ivShare?.visibility = VISIBLE
                                viewBinding.tvSubTitle?.setCompoundDrawablesWithIntrinsicBounds(
                                    R.drawable.ic_live_play_start,
                                    0,
                                    0,
                                    0
                                )
                            }
                            "audition" -> {
                                viewBinding.ivShare?.visibility = VISIBLE
                            }
                            else -> viewBinding.ivShare?.visibility = GONE
                        }
                        //val strSpan = SpannableString(data.subName)
                        var strSpan: SpannableString
                        strSpan = SpannableString(
                            "Mới" + context.getString(R.string.dot) + Calendar.getInstance()
                                .get(Calendar.YEAR) + context.getString(R.string.dot) + "Trò chơi tương tác"
                        )
                        strSpan.setSpan(
                            ForegroundColorSpan(context.getColor(R.color.accent)),
                            0,
                            3,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        viewBinding.tvSubTitle?.text = strSpan
                        viewBinding.tvTitle?.text = data.titleVietnam
                        if (context.isTablet()) {
                            ImageProxy.load(
                                context = viewBinding.root.context,
                                url = data.horizontalImage ?: "",
                                width = thumbWidth,
                                height = thumbHeight,
                                target = viewBinding.ivThumb,
                                placeHolderId = placeholder,
                                errorDrawableId = placeholder
                            )
                        } else {
                            if (data.thumbnailType == Game30sStructure.Game30sStructureItem.ThumbnailType.PORTRAIT) {
                                ImageProxy.load(
                                    context = viewBinding.root.context,
                                    url = data.verticalImage ?: "",
                                    width = itemView.context.resources.displayMetrics.widthPixels,
                                    height = thumbHeight,
                                    target = viewBinding.ivThumb,
                                    placeHolderId = placeholder,
                                    errorDrawableId = placeholder
                                )
                            }else {
                                ImageProxy.load(
                                    context = viewBinding.root.context,
                                    url = data.horizontalImage ?: "",
                                    width = itemView.context.resources.displayMetrics.widthPixels,
                                    height = thumbHeight,
                                    target = viewBinding.ivThumb,
                                    placeHolderId = placeholder,
                                    errorDrawableId = placeholder
                                )
                            }
                        }
                    }
                    Type.HighLight -> {
                        viewBinding.tvTitle?.text = data.titleVietnam
                        viewBinding.tvSubTitle?.text = context.getString(
                            R.string.vote_number,
                            data.votes?.toLong()?.formatNumberVote()
                        )
                        if(context.isTablet()){
                            ImageProxy.load(
                                context = viewBinding.root.context,
                                url = data.horizontalImage ?: "",
                                width = thumbWidth,
                                height = thumbHeight,
                                target = viewBinding.ivThumb,
                                placeHolderId = placeholder,
                                errorDrawableId = placeholder
                            )
                        }else {
                            if (data.thumbnailType == Game30sStructure.Game30sStructureItem.ThumbnailType.PORTRAIT) {
                                ImageProxy.load(
                                    context = viewBinding.root.context,
                                    url = data.verticalImage ?: "",
                                    width = itemView.context.resources.displayMetrics.widthPixels,
                                    height = thumbHeight,
                                    target = viewBinding.ivThumb,
                                    placeHolderId = placeholder,
                                    errorDrawableId = placeholder
                                )
                            } else {
                                ImageProxy.load(
                                    context = viewBinding.root.context,
                                    url = data.horizontalImage ?: "",
                                    width = thumbWidth,
                                    height = thumbHeight,
                                    target = viewBinding.ivThumb,
                                    placeHolderId = placeholder,
                                    errorDrawableId = placeholder
                                )
                            }
                        }
                    }
                    Type.AutoExpandSmall -> {
                        if(data.descriptionVi.isNotBlank()){
                            viewBinding.tvName?.visibility =VISIBLE
                            val textName : String = Html.fromHtml(data.descriptionVi).toString()
                            viewBinding.tvName?.text = textName
                        }
                        if (data.thumbnailType == Game30sStructure.Game30sStructureItem.ThumbnailType.PORTRAIT) {
                            ImageProxy.load(
                                context = viewBinding.root.context,
                                url = data.verticalImage ?: "",
                                width = thumbWidth,
                                height = thumbHeight,
                                target = viewBinding.ivThumb,
                                placeHolderId = placeholder,
                                errorDrawableId = placeholder
                            )
                        } else {
                            ImageProxy.load(
                                context = viewBinding.root.context,
                                url = data.horizontalImage ?: "",
                                width = thumbWidth,
                                height = thumbHeight,
                                target = viewBinding.ivThumb,
                                placeHolderId = placeholder,
                                errorDrawableId = placeholder
                            )
                        }
                    }
                    else -> {
                        if(type == Type.HorizontalSliderSmall || type == Type.HorizontalLarge) {
                            viewBinding.tvName?.text = data.season
                        } else {
                            viewBinding.tvName?.text = data.titleVietnam
                        }
                        if (data.thumbnailType == Game30sStructure.Game30sStructureItem.ThumbnailType.PORTRAIT) {
                            ImageProxy.load(
                                context = viewBinding.root.context,
                                url = data.verticalImage ?: "",
                                width = thumbWidth,
                                height = thumbHeight,
                                target = viewBinding.ivThumb,
                                placeHolderId = placeholder,
                                errorDrawableId = placeholder
                            )
                        } else {
                            ImageProxy.load(
                                context = viewBinding.root.context,
                                url = data.horizontalImage ?: "",
                                width = thumbWidth,
                                height = thumbHeight,
                                target = viewBinding.ivThumb,
                                placeHolderId = placeholder,
                                errorDrawableId = placeholder
                            )
                        }
                    }
                }
                when(type){
                    Type.HighLight,
                    Type.HorizontalSliderLarge -> {
                        if(data.isEnded) {
                            viewBinding.ivRankingTeam?.visibility = VISIBLE
                            viewBinding.ivRankingTeam?.setImageResource(checkRank(data.rank))
                        }
                    } else -> 0
                }
                if (data.avatar.isNotBlank()) {
                    if(context.isTablet()){
                        ImageProxy.load(
                            context = viewBinding.root.context,
                            url = data.avatar,
                            width = itemView.context.resources.getDimensionPixelSize(R.dimen._5sdp),
                            height = itemView.context.resources.getDimensionPixelSize(R.dimen._5sdp),
                            target = viewBinding.ivAvatarTeam,
                            placeHolderId = placeholder,
                            errorDrawableId = placeholder
                        )
                    }else{
                        ImageProxy.load(
                            context = viewBinding.root.context,
                            url = data.avatar,
                            width = itemView.context.resources.getDimensionPixelSize(R.dimen._7sdp),
                            height = itemView.context.resources.getDimensionPixelSize(R.dimen._7sdp),
                            target = viewBinding.ivAvatarTeam,
                            placeHolderId = placeholder,
                            errorDrawableId = placeholder
                        )
                    }

                }
                viewBinding.ivVote?.visibility = VISIBLE
                viewBinding.tvNumberVote?.visibility = VISIBLE
                if (type == Type.HorizontalSliderLarge || type == Type.HighLight) {
                    viewBinding.tvNumberVote?.text = context.getString(
                        R.string.vote_number,
                        data.votes?.toLong()?.formatNumberVote()

                    )
                } else {
                    viewBinding.tvNumberVote?.text =
                        data.votes?.toLong()?.formatNumberVote()
                }
                viewBinding.tvNameMember?.text = data.titleVietnam
            }

        }
    }
    private fun checkRank(rank : Int): Int{
        return when(rank) {
                1 -> R.drawable.ic_rank_1
                2 -> R.drawable.ic_rank_2
                3 -> R.drawable.ic_rank_3
                4 -> R.drawable.ic_rank_4
                else -> 0
            }
        }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StartItemViewHolder {
        return when (type) {
            Type.HighLight -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_horizontal_highlight_item, parent, false)
                    )
                )
            }
            Type.VodLive -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_vod_live_item, parent, false)
                    )
                )
            }
            Type.AutoExpandLarge -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_auto_expand_large_item, parent, false)
                    )
                )
            }
            Type.AutoExpandSmall -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_auto_expand_small_item, parent, false)
                    )
                )
            }
            Type.HorizontalSliderLarge -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_horizontal_slider_large_item, parent, false)
                    )
                )
            }
            Type.HorizontalSliderSmall -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_horizontal_slider_small_item, parent, false)
                    )
                )
            }
            Type.HorizontalLarge -> {
                StartItemViewHolder(
                    StartItemViewBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.start_horizontal_large_item, parent, false)
                    )
                )
            }
        }
    }

    private fun bindPlaceHolder(): Int {
        return when (type) {
            Type.HighLight,
            Type.VodLive -> {
                R.drawable.placeholder_highlight
            }
            Type.HorizontalSliderSmall,
            Type.HorizontalSliderLarge -> {
                R.drawable.placeholder_horizontal_slider
            }
            Type.AutoExpandSmall,
            Type.AutoExpandLarge-> {
                R.drawable.placeholder_vertical_medium
            }
            Type.HorizontalLarge -> {
                R.drawable.placeholder_horizontal_highlight
            }
        }
    }
}