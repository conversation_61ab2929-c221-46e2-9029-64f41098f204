package com.fptplay.mobile.features.sport_interactive_v2.models.match_process

import com.fptplay.mobile.features.sport_interactive.model.SportMatchProcess
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.squareup.moshi.Json

data class SportInteractiveMatchProcessItem(
    val playerName: String = "",
    val transferPlayerName: String = "",
    val description: String = "",
    val teamType: TeamType = TeamType.Unknown(""),
    val action: SportMatchProcess.Round.Process.ProcessActionType = SportMatchProcess.Round.Process.ProcessActionType.Unknown(""),
    val time: String = ""
): UIData()
