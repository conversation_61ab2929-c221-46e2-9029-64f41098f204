package com.fptplay.mobile.features.ai_chatbot.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.appcompat.widget.Toolbar
import androidx.core.widget.TextViewCompat
import com.fptplay.mobile.R

class AIChatBoxToolbarView (context: Context, attrs: AttributeSet?) : Toolbar(context, attrs) {
    private var centeredTitleTextView: TextView? = null
    private var startIconView: ImageView? = null
    private var closeButton: ImageButton? = null
    val startIcon  get() = startIconView

    override fun setTitle(title: CharSequence?) {
        ensureStartIconAndTitle()
        centeredTitleTextView?.text = title
    }

    override fun getTitle(): CharSequence {
        return centeredTitleTextView?.text?.toString() ?: ""
    }

    override fun setTitle(@StringRes resId: Int) {
        setTitle(context.getString(resId))
    }

    override fun setTitleTextColor(color: Int) {
        centeredTitleTextView?.setTextColor(color)
    }


    private fun ensureStartIconAndTitle() {
        if (startIconView == null) {
            startIconView = ImageView(context).apply {
                val paddingStart = context.resources?.getDimensionPixelSize(R.dimen.ai_chat_box_toolbar_padding_start) ?:0
                val size = context.resources?.getDimensionPixelSize(R.dimen.ic_ai_chat_box_toolbar_size) ?:0
                layoutParams = LayoutParams(size, size).apply {
                    gravity = Gravity.START or Gravity.CENTER_VERTICAL
                    marginStart = paddingStart
                }
                scaleType = ImageView.ScaleType.FIT_XY
            }
            addView(startIconView)
        }

        if (centeredTitleTextView == null) {
            centeredTitleTextView = TextView(context).apply {
                val paddingEnd = context.resources?.getDimensionPixelSize(R.dimen.ai_chat_box_toolbar_text_padding_start) ?:0
                setSingleLine()
                ellipsize = TextUtils.TruncateAt.END
                gravity = Gravity.CENTER_VERTICAL
                TextViewCompat.setTextAppearance(this, R.style.ChatBoToolbarTextViewTitle)
                layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT).apply {
                    gravity = Gravity.START or Gravity.CENTER_VERTICAL
                    marginStart = paddingEnd
                }
            }
            addView(centeredTitleTextView)
        }
    }

    fun setCloseButtonOnClick(drawableRes: Int = R.drawable.ic_close_chatbox_ai, onClick: (() -> Unit)? = null) {
        if (closeButton == null) {
            closeButton = ImageButton(context).apply {
                val size = context.resources?.getDimensionPixelSize(R.dimen.ai_chat_box_toolbar_close_size) ?:0
                val paddingEnd = context.resources?.getDimensionPixelSize(R.dimen.ai_chat_box_toolbar_padding_start) ?:0
                layoutParams = LayoutParams(size, size).apply {
                    gravity = Gravity.END or Gravity.CENTER_VERTICAL
                    marginEnd = paddingEnd
                }
                background = null
                setOnClickListener { onClick?.invoke() }
            }
            addView(closeButton)
        }
        closeButton?.setImageResource(drawableRes)
    }

    init {
        ensureStartIconAndTitle()
        startIconView?.apply {
            setImageResource(R.drawable.ic_ai_chat_box)
        }
        centeredTitleTextView?.apply {
            text = context.getString(R.string.ai_chat_bot_title)
        }
    }
}