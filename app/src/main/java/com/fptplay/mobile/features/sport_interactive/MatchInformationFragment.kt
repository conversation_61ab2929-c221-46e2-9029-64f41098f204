package com.fptplay.mobile.features.sport_interactive

import android.os.Bundle
import android.text.Html
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.text.HtmlCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet

import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.FragmentMatchInformationBinding
import com.fptplay.mobile.databinding.SportGoalItemBinding
import com.fptplay.mobile.databinding.SportInteractiveHeaderCommonV1Binding
import com.fptplay.mobile.features.sport_interactive.adapter.SportMatchInfoAdapter
import com.fptplay.mobile.features.sport_interactive.model.SportMatchDetail
import com.fptplay.mobile.features.sport_interactive.model.SportMatchStatistic
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.player.utils.invisible
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.util.Utils.hide
import com.xhbadxx.projects.module.util.common.Util.isValid
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.AndroidEntryPoint
@AndroidEntryPoint
class MatchInformationFragment :
    BaseFragment<SportInteractiveViewModel.SportInteractiveState, SportInteractiveViewModel.SportInteractiveIntent>() {
    private var _binding: FragmentMatchInformationBinding? = null
    private val binding get() = _binding!!
    private var _headerBinding: SportInteractiveHeaderCommonV1Binding? = null
    private val headerBinding get() = _headerBinding!!
    private val matchStatisticAdapter : SportMatchInfoAdapter by lazy { SportMatchInfoAdapter(viewLifecycleOwner) }
    override val viewModel: SportInteractiveViewModel by activityViewModels()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMatchInformationBinding.inflate(inflater, container, false)
        _headerBinding = SportInteractiveHeaderCommonV1Binding.bind(binding.root)
        return binding.root
    }
    override fun setUpEdgeToEdge() {
        //this fragment should do nothing to current edge to edge state
    }
    override fun observeState() {

    }
    override fun bindComponent() {
        binding.rcvMatchStatistic.apply {
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            adapter = matchStatisticAdapter
        }
    }
    private fun updateLayoutHeaderCommon(detail :SportMatchDetail?) {
        if(detail!=null) {
            if (detail.info.awayTeamName.isNotBlank()) {
                headerBinding.txtNameTeamAway.visible()
                headerBinding.txtNameTeamAway.text = detail.info.awayTeamName
            }
            if (detail.info.homeTeamName.isNotBlank()) {
                headerBinding.txtNameTeamHome.visible()
                headerBinding.txtNameTeamHome.text = detail.info.homeTeamName
            }
            headerBinding.txtMatchScore.text = detail.info.awayScore.ifBlank { "0" } + " - " + detail.info.homeScore.ifBlank { "0" }
            if (detail.info.time.isNotBlank()) {
                headerBinding.txtMatchTime.visible()
                headerBinding.txtMatchTime.text = detail.info.time
            }
            ImageProxy.load(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = headerBinding.imgAway,
                url = detail.info.awayLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team
            )
            ImageProxy.load(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = headerBinding.imgHome,
                url = detail.info.homeLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team
            )
            if (detail.listScores.isNotEmpty()) {
                headerBinding.llHeaderGoalTeam.visible()
                headerBinding.flGoalAway.removeAllViews()
                headerBinding.flGoalHome.removeAllViews()
                detail.listScores.forEachIndexed { _, score ->
                    val viewGoalItemAway =
                        SportGoalItemBinding.inflate(LayoutInflater.from(context))
                    val data = "${score.playerName}  ${score.time}"
                    if (score.teamType == TeamType.AwayTeam) {
                        viewGoalItemAway.tvDataAway.visible()
                        viewGoalItemAway.tvDataHome.invisible()
                        viewGoalItemAway.tvDataAway.text = data
                        headerBinding.flGoalAway.addView(viewGoalItemAway.root)
                    } else {
                        viewGoalItemAway.tvDataAway.invisible()
                        viewGoalItemAway.tvDataHome.visible()
                        viewGoalItemAway.tvDataHome.text = data
                        headerBinding.flGoalHome.addView(viewGoalItemAway.root)
                    }
                }
            }
        }
        else{
            headerBinding.txtNameTeamAway.hide()
            headerBinding.txtNameTeamHome.hide()
            headerBinding.txtMatchScore.text = "0 - 0"
            headerBinding.txtMatchTime.hide()
            headerBinding.llHeaderGoalTeam.hide()
            headerBinding.imgAway.setImageResource(R.drawable.ic_default_logo_team)
            headerBinding.imgHome.setImageResource(R.drawable.ic_default_logo_team)
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _headerBinding = null
    }
    override fun bindData() {
    }
    override fun bindEvent() {
        viewModel.sportMatchDetail.observe(viewLifecycleOwner) {
            updateLayoutHeaderCommon(it)
        }
        viewModel.sportMatchStatistic.observe(viewLifecycleOwner) {
            /**
             * case :In case the UI currently has data, firebase will update it
             * **/
            updateContentSportMatchStatistic(it)
        }
    }
    private fun updateContentSportMatchStatistic(statistic: SportMatchStatistic?){
        if(statistic!=null && statistic.listStats.isNotEmpty()){
            binding.txtNoDataStatistic.hide()
            binding.rcvMatchStatistic.visible()
            matchStatisticAdapter.bind(statistic.listStats)
        }
        else{
            binding.rcvMatchStatistic.hide()
            binding.txtNoDataStatistic.visible()
        }
    }
    override fun SportInteractiveViewModel.SportInteractiveState.toUI() {
        when (this) {
            is SportInteractiveViewModel.SportInteractiveState.Loading -> showLoading()
            is SportInteractiveViewModel.SportInteractiveState.Done -> hideLoading()
            else -> {

            }
        }
    }
}