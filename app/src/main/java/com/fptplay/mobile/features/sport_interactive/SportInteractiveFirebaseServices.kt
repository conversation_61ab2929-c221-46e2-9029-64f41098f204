package com.fptplay.mobile.features.sport_interactive

import androidx.lifecycle.LifecycleOwner
import com.fptplay.mobile.common.utils.firestore.LifecycleEventDispatcher
import com.fptplay.mobile.features.sport_interactive.model.SportInteractiveData
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.ListenerRegistration
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Types
import timber.log.Timber

class SportInteractiveFirebaseServices {

    val db = Firebase.firestore
    private var registration: ListenerRegistration? = null
    var listener: SportEventListener? = null

    private var eventId: String = ""
    private var sportInteractiveData: SportInteractiveData? = null
    private var fromBackground = false

    private fun setListener(listener: SportEventListener?, eventId: String?) {
        Timber.tag("tam-sport").d("*****Set listener: eventId: $eventId")
        this.listener = listener
        this.eventId = eventId ?: ""
    }

    private fun invokeListener() {
        Timber.tag("tam-sport").d("*****listener: $listener")
        listener?.onDataChange(sportInteractiveData ?: SportInteractiveData())
    }

    fun stopListenEvent() {
        registration?.remove()
        registration = null
        sportInteractiveData = null
        eventId = ""
    }

    fun listenSportEventData(lifecycleOwner: LifecycleOwner, eventId: String, listener: (data: SportInteractiveData?) -> Unit) {

        if(this.eventId != eventId || registration == null) {

            val sportEventListener = object: SportEventListener {
                override fun onDataChange(data: SportInteractiveData?) {
                    listener(data)
                }
            }

            LifecycleEventDispatcher(
                lifecycleOwner,
                onStart = {
                    Timber.tag("tam-sport").d("*****onstart listenSportEventData")
                    setListener(sportEventListener, eventId)
                    if(fromBackground) {
                        invokeListener()
                    }
                    fromBackground = false

                },
                onStop = {
                    Timber.tag("tam-sport").d("*****onstop")
                    setListener(null, null)
                    fromBackground = true
                }
            )
            val docRef = db.collection(SPORT_INTERACTION_COLLECTION).document(eventId)
            this.eventId = eventId
            sportInteractiveData = null
            registration?.remove()

            registration = docRef.addSnapshotListener { snapshot, e ->
                if (e != null) {
                    Timber.tag("tam-sport").e("*****Listen failed. $e")
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    Timber.tag("tam-sport").d("*****Current data: ${snapshot.data}")
                    try {
                        sportInteractiveData = getDataFromFirestore(snapshot)
                    } catch (e:Exception){
                        Timber.tag("tam-sport").e(e, "getDataFromFirestore")

                    } finally {
                        invokeListener()
                    }
                } else {
                    Timber.tag("tam-sport").e("*****Current data: null")
                    invokeListener()
                }
            }

        }

    }

    private fun getDataFromFirestore(snapshot: DocumentSnapshot): SportInteractiveData? {
        return try {
            val moshi = SportInteractiveUtils.moshi()
            val jsonAdapterSportInteractiveData: JsonAdapter<SportInteractiveData> =
                moshi.adapter(SportInteractiveData::class.java)

            val jsonAdapter: JsonAdapter<Map<String, Any>> = moshi.adapter(
                Types.newParameterizedType(
                    MutableMap::class.java,
                    String::class.java,
                    Any::class.java
                )
            )
            val jsonString: String = jsonAdapter.toJson(snapshot.data)
            Timber.tag("tam-sport").i("getDataFromFirestore jsonString : $jsonString")
            jsonAdapterSportInteractiveData.fromJson(jsonString)
        } catch (ex: Exception) {
            Timber.tag("tam-sport").e(ex, "getDataFromFirestore")
            ex.printStackTrace()
            null
        }
    }
    companion object {
        const val SPORT_INTERACTION_COLLECTION = "interactive_sports"
    }

    interface SportEventListener {
        fun onDataChange(data: SportInteractiveData?)
    }
}