package com.fptplay.mobile.features.sport_interactive_v2.adpters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.SportInteractiveMenuItemBinding
import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

class SportInteractiveMenuAdapter :
    BaseAdapter<SportInteractiveMenuData, SportInteractiveMenuAdapter.SportInteractiveMenuViewHolder>() {

    private var currentSelectedPos = 0

    override fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return (oldItem as SportInteractiveMenuData).title == (newItem as SportInteractiveMenuData).title
    }

    override fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return oldItem == newItem
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): SportInteractiveMenuViewHolder {
        return SportInteractiveMenuViewHolder(
            SportInteractiveMenuItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: SportInteractiveMenuViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    fun updateSelectedPosition(position: Int) {
        if (currentSelectedPos != position) {
            item(position)?.let { updatedItem ->
                notifyItemChanged(currentSelectedPos)
                currentSelectedPos = position
                notifyItemChanged(position)
                eventListener?.onSelectedItem(position, updatedItem)
            }

        }
    }

    fun getCurrentSelectedItem(): SportInteractiveMenuData? {
        return try {
            return item(currentSelectedPos)
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

    fun getCurrentSelectedPosition() = currentSelectedPos

    inner class SportInteractiveMenuViewHolder(private val binding: SportInteractiveMenuItemBinding) :
        BaseViewHolder(binding) {
        fun bind(data: SportInteractiveMenuData) {
            binding.root.onClickDelay {
                eventListener?.onClickView(absoluteAdapterPosition, binding.root, data)
                updateSelectedPosition(absoluteAdapterPosition)
            }
            binding.tvTitle.run {
                text = data.title
                isSelected = absoluteAdapterPosition == currentSelectedPos
            }
            binding.vIndicator.isVisible = absoluteAdapterPosition == currentSelectedPos
        }
    }
}