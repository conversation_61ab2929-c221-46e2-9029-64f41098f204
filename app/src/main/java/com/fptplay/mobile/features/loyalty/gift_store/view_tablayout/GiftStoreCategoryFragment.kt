package com.fptplay.mobile.features.loyalty.gift_store.view_tablayout

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.hilt.navigation.fragment.hiltNavGraphViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.NavLoyaltyDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.databinding.GiftStoreCategoryFragmentBinding
import com.fptplay.mobile.features.loyalty.gift_store.GiftStoreFragmentDirections
import com.fptplay.mobile.features.loyalty.gift_store.adapter.GiftStoreCategoryAdapter
import com.fptplay.mobile.features.loyalty.gift_store.callback.CallBack
import com.fptplay.mobile.features.loyalty.gift_store.decorations.SpacesItemDecoration
import com.fptplay.mobile.features.loyalty.voucher_detail.viewmodel.VoucherDetailViewModel
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.MenuGiftId
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.VoucherType
import com.xhbadxx.projects.module.util.common.LoadMoreHandler
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class GiftStoreCategoryFragment : BaseFragment<VoucherDetailViewModel.VoucherDetailViewState, VoucherDetailViewModel.VoucherDetailViewIntent>() {

    companion object {
        private const val MENU_ID = "menuID"
        fun newInstance(menuID: String): GiftStoreCategoryFragment {
            val fragment = GiftStoreCategoryFragment()
            val bundle = Bundle()
            bundle.putString(MENU_ID, menuID)
            fragment.arguments = bundle
            return fragment
        }
    }

    override val viewModel: VoucherDetailViewModel by hiltNavGraphViewModels(R.id.nav_loyalty_gift_store)
    override val hasEdgeToEdge: Boolean = true
    private var _binding: GiftStoreCategoryFragmentBinding? = null
    private val binding get() = _binding!!
    private val giftDetailAdapter: GiftStoreCategoryAdapter by lazy { GiftStoreCategoryAdapter() }

    private var menuTabID = ""

    private val itemInRow: Int by lazy { 1 }
    private val totalItemInPage: Int by lazy { 20 }

    private val loadMoreGiftHandler: LoadMoreHandler by lazy {
        LoadMoreHandler(
            totalItem = giftDetailAdapter.size() ?: 0,
            totalItemInPage = totalItemInPage,
            totalItemInRow = itemInRow,
            onScroll = { page ->
                viewModel.dispatchIntent(
                    VoucherDetailViewModel.VoucherDetailViewIntent.GetListGift
                        (menuId = menuTabID,
                        page = page.toString(),
                        size = totalItemInPage.toString())
                )
                when (menuTabID) {
                    MenuGiftId.EXCHANGED.id -> {
                        viewModel.savePageLoadGiftExchange(page.toString())
                    }
                    MenuGiftId.REDEEMED.id -> {
                        viewModel.savePageLoadGiftRedeem(page.toString())
                    }
                    MenuGiftId.EXPIRED.id -> {
                        viewModel.savePageLoadGiftExpire(page.toString())
                    }
                    else -> {
                        viewModel.savePageLoadGiftTransport(page.toString())
                    }
                }
            })
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = GiftStoreCategoryFragmentBinding.inflate(inflater, container, false)
            arguments?.let {
                menuTabID = it.getString(MENU_ID, "")
            }
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindComponent() {
        initRecyclerView()
    }

    override fun bindData() {
        getGiftFromAPI()
    }

    private fun getGiftFromAPI() {
        when (menuTabID) {
            MenuGiftId.EXCHANGED.id -> {
                viewModel.dispatchIntent(
                    VoucherDetailViewModel.VoucherDetailViewIntent.GetListGift(menuId = menuTabID, page = "1", (viewModel.pageLoadGiftExchange.toInt() * 20).toString()))
            }
            MenuGiftId.REDEEMED.id -> {
                viewModel.dispatchIntent(
                    VoucherDetailViewModel.VoucherDetailViewIntent.GetListGift(menuId = menuTabID, page = "1", (viewModel.pageLoadGiftRedeem.toInt() * 20).toString()))
            }
            MenuGiftId.EXPIRED.id -> {
                viewModel.dispatchIntent(
                    VoucherDetailViewModel.VoucherDetailViewIntent.GetListGift(menuId = menuTabID, page = "1", (viewModel.pageLoadGiftExpire.toInt() * 20).toString()))
            }
            else -> {
                viewModel.dispatchIntent(
                    VoucherDetailViewModel.VoucherDetailViewIntent.GetListGift(menuId = menuTabID, page = "1", (viewModel.pageLoadGiftTransport.toInt() * 20).toString()))
            }
        }
    }

    override fun retryLoadPage() {
        bindData()
    }

//    val navigateMap = mutableMapOf<String, Int>().apply {
//       this[Constants.GIFT_GOT_IT] = R.id.action_gift_store_fragment_to_voucher_got_it_exchange_fragment
//       this[Constants.GIFT_FILM] = R.id.action_gift_store_fragment_to_voucher_film_and_invoice_payment_exchange_fragment
//       this[Constants.GIFT_INVOICEPAYMENT] = R.id.action_gift_store_fragment_to_voucher_film_and_invoice_payment_exchange_fragment
//    }

    override fun bindEvent() {

        giftDetailAdapter.onItemClickListener(object : CallBack.ItemGiftCallback {
            override fun onItemGiftClickListener(voucherExchangeId: String,
                voucherType: VoucherType, voucherId: String,
                position: Int, voucherStatus: String
            ) {
//                navigateMap[voucherType].run {
//                    parentFragment?.parentFragment?.findNavController()?.navigate(
//                        this ?: R.id.action_gift_store_fragment_to_voucher_other_exchange_fragment,
//                        bundleOf(
//                            "menuTabID" to menuTabID,
//                            "voucherExchangeId" to voucherExchangeId,
//                            "voucherType" to voucherType,
//                            "voucherId" to voucherId,
//                        )
//                    )
//                }

                when (voucherType) {
                    VoucherType.MOBILE_CARD -> {
                        parentFragment?.parentFragment?.findNavController()?.navigate(NavLoyaltyDirections.evoucherFragmentToCardDetailFragment(
                            voucherExchangedId = voucherExchangeId,
                            voucherType = voucherType.id,
                        ))
                    }
                    VoucherType.GOT_IT -> {
                        parentFragment?.parentFragment?.findNavController()?.navigate(
                            GiftStoreFragmentDirections.actionGiftStoreFragmentToVoucherGotItExchangeFragment(
                                voucherExchangeId = voucherExchangeId,
                                voucherType = voucherType.id
                            ))
                    }
                    VoucherType.FILM -> {
                        parentFragment?.parentFragment?.findNavController()?.navigate(
                            GiftStoreFragmentDirections.actionGiftStoreFragmentToVoucherFilmAndInvoicePaymentExchangeFragment(
                                voucherExchangeId = voucherExchangeId,
                                voucherType = voucherType.id,
                                voucherId = voucherId,
                                voucherStatus = voucherStatus
                            ))
                    }
                    VoucherType.INVOICE_PAYMENT -> {
                        parentFragment?.parentFragment?.findNavController()?.navigate(
                            GiftStoreFragmentDirections.actionGiftStoreFragmentToVoucherFilmAndInvoicePaymentExchangeFragment(
                                voucherExchangeId = voucherExchangeId,
                                voucherType = voucherType.id,
                                voucherId = voucherId,
                                voucherStatus = voucherStatus
                            ))
                    }
                    is VoucherType.OTHERS -> {
                        parentFragment?.parentFragment?.findNavController()?.navigate(
                            GiftStoreFragmentDirections.actionGiftStoreFragmentToVoucherOtherExchangeFragment(
                                voucherExchangeId = voucherExchangeId,
                                voucherType = voucherType.id
                            )
                        )
                    }
                }
            }
        })

        binding.recyclerView.apply {
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    giftDetailAdapter.let {
                        if (!recyclerView.canScrollVertically(1)) {
                            loadMoreGiftHandler.canScroll(position = it.size() - 1)
                        }
                    }
                }
            })
        }
    }

    private fun initRecyclerView() {

        if (context.isTablet()) {
            binding.recyclerView.apply {
                layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
                adapter = giftDetailAdapter
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    outRect.top = resources.getDimensionPixelSize(R.dimen._8sdp)
//                    outRect.left =resources.getDimensionPixelSize(R.dimen._37sdp)
//                    outRect.right =resources.getDimensionPixelSize(R.dimen._37sdp)
                }
            })
            }
        } else {
            binding.recyclerView.apply {
                layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
                adapter = giftDetailAdapter
                val spacingInPixels = resources.getDimensionPixelSize(R.dimen._14sdp)
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        val position = parent.getChildAdapterPosition(view)
                        outRect.left = spacingInPixels
                        outRect.right = spacingInPixels
                        when(position) {
                            0 -> {
                                outRect.top = spacingInPixels
                                outRect.bottom = spacingInPixels / 2
                            }
                            else -> {
                                outRect.bottom = spacingInPixels / 2
                            }
                        }
                    }
                })
            }
        }

    }

    override fun VoucherDetailViewModel.VoucherDetailViewState.toUI() {
        when(this) {
            is VoucherDetailViewModel.VoucherDetailViewState.Loading -> {
//                showLoading()
            }
            is VoucherDetailViewModel.VoucherDetailViewState.Error -> {
                hideLoading()
//                showLoyaltyErrorDialog(title = getString(R.string.notification), this.message)
            }
            is VoucherDetailViewModel.VoucherDetailViewState.ErrorNoInternet -> {
                hideLoading()
//                showLoyaltyErrorDialog(title = getString(R.string.notification), this.message)
            }
            is VoucherDetailViewModel.VoucherDetailViewState.Done -> {
                hideLoading()
            }
            is VoucherDetailViewModel.VoucherDetailViewState.ResultGetListGift -> {
                hideLoading()
                if (data.code == Constants.API_SUCCESS) {
                    if (this.menuId == menuTabID) {
                        if (isBind && data.data.isEmpty()) {
                            binding.tvNotData.show()
                            binding.tvNotData.text = getString(R.string.all_not_have_data)
                            loadMoreGiftHandler.refresh(totalItem = giftDetailAdapter.size() ?: 0, endPage = true)
                        } else {
                            binding.tvNotData.hide()
                            giftDetailAdapter.add(data = this.data.data.toMutableList(), isBind) {
                                viewModel.saveListGiftFor(menuId, giftDetailAdapter.data())
                                loadMoreGiftHandler.refresh(
                                    totalItem = giftDetailAdapter.size() ?: 0,
                                    endPage = this.data.data.size < totalItemInPage
                                )
                            }
                        }
                    }
                }
                else {
                    showLoyaltyErrorDialog(title = getString(R.string.notification), message = if(this.data.errors.isNotBlank()) this.data.errors else requireContext().getString(R.string.text_loyalty_error))
                }
            }
            else -> {
                hideLoading()
            }
        }
    }
}