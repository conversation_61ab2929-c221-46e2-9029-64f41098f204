package com.fptplay.mobile.features.loyalty.eKYC.eKYCFragment

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.Surface
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.camera.core.*
import androidx.camera.core.AspectRatio.RATIO_4_3
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.*
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.runOnUiThread
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.LoyaltyCameraDialogFragmentBinding
import com.fptplay.mobile.features.loyalty.eKYC.eKYCUtils.ExifUtil
import com.fptplay.mobile.features.loyalty.eKYC.eKYCUtils.SelectImageUtils
import com.fptplay.mobile.features.loyalty.eKYC.eKYCViewmodel.EKYCViewModel
import com.google.common.util.concurrent.ListenableFuture
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


@AndroidEntryPoint
class LoyaltyCameraDialog : BaseFullDialogFragment<EKYCViewModel.EKYCState, EKYCViewModel.EKYCIntent>(){

    override val hasEdgeToEdge: Boolean = true
    override val viewModel: EKYCViewModel by activityViewModels()
    private var _binding : LoyaltyCameraDialogFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs : LoyaltyCameraDialogArgs by navArgs()
    private val cameraProviderFuture: ListenableFuture<ProcessCameraProvider> by lazy { ProcessCameraProvider.getInstance(requireContext()) }
    private val cameraSelector: CameraSelector by lazy {CameraSelector.DEFAULT_BACK_CAMERA}
    private var alertDialog: AlertDialog? = null
    private val cameraProviderResult = registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()){permissions ->
        val permissionsNotGranted = permissions.entries.filter { !it.value }
        if (permissionsNotGranted.isEmpty()) {
            checkPermissionCamera()
        } else {
            showAlertDialog(getString(R.string.qr_code_scan_no_permission), getString(R.string.login_retry))
        }
    }
    private var imageCapture: ImageCapture? = null
    private lateinit var  imgCaptureExecutor: ExecutorService
    private var typeTakeImage = ""
    private var isTakeFrontImage : Boolean = false
    private var isTakeBackImage : Boolean = false
    private var imageWidth : Int = 0
    private var imageHeight : Int = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenCameraDialogDark)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = LayoutInflater.from(MainApplication.INSTANCE.applicationContext).inflate(R.layout.loyalty_camera_dialog_fragment, container, false)
        _binding = LoyaltyCameraDialogFragmentBinding.bind(view)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindData() {
        super.bindData()
    }

    override fun bindComponent() {
        checkPermissionCamera()
        typeTakeImage = safeArgs.type
        changeImagePreview(safeArgs.type)
        imgCaptureExecutor = Executors.newSingleThreadExecutor()
        binding.apply {
            imageView5.measure(0,0)
            imageWidth = imageView5.measuredWidth
            imageHeight = imageView5.measuredHeight
            Log.i("LogSize", "width : ${imageView5.measuredWidth} || height : ${imageView5.measuredHeight}")
        }
    }

    fun changeImagePreview(type: String){
        when(type) {
            Utils.CHOOSE_IMAGE_OPTION_DIALOG_FRONT_TYPE -> {
                binding.ivThumb.setImageResource(R.drawable.ic_cccd_front)
                binding.tvNotiImage.text = resources.getString(R.string.text_noti_front_image)
            }
            Utils.CHOOSE_IMAGE_OPTION_DIALOG_BACK_TYPE -> {
                binding.ivThumb.setImageResource(R.drawable.ic_cccd_back)
                binding.tvNotiImage.text = resources.getString(R.string.text_noti_back_image)
            }
        }
    }

    override fun bindEvent() {
        binding.apply {
            ivClose.onClickDelay {
                setFragmentResultListener(isClose = true)
                dismissAllowingStateLoss()
            }
            ivTapToCapture.onClickDelay {
                takePhoto()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    animateFlash()
                }
            }
        }
    }

    override fun backHandler() {
        super.backHandler()
        setFragmentResultListener(isClose = true)
        dismissAllowingStateLoss()
    }

    private fun checkPermissionCamera() {
        val permissions = arrayOf(
            Manifest.permission.CAMERA
        )

        val listPermissionsNeeded: MutableList<String> = ArrayList()
        for (permission in permissions) {
            if (ContextCompat.checkSelfPermission(requireActivity(), permission) != PackageManager.PERMISSION_GRANTED) {
                listPermissionsNeeded.add(permission)
            }
        }
        if (listPermissionsNeeded.isEmpty()) {
            startCamera()
        } else {
            cameraProviderResult.launch(permissions)
        }
    }

    private fun startCamera() {
        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()
            imageCapture = ImageCapture.Builder().setTargetAspectRatio(RATIO_4_3).build()
            imageCapture?.targetRotation = Surface.ROTATION_0
            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(binding.cameraPreview.surfaceProvider)
            }
            try {
                cameraProvider.unbindAll()
                cameraProvider.bindToLifecycle(this,cameraSelector,preview,imageCapture)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        },ContextCompat.getMainExecutor(requireContext()))
    }

    private fun takePhoto() {
        imageCapture?.let {
            val fileName = "JPEG_${System.currentTimeMillis()}" + SelectImageUtils.EXTENSION
            val file = File(requireContext().cacheDir,fileName)
            val outputFileOptions = ImageCapture.OutputFileOptions.Builder(file).build()

            it.takePicture(
                imgCaptureExecutor,
                object : ImageCapture.OnImageCapturedCallback() {
                    override fun onCaptureSuccess(image: ImageProxy) {
                        super.onCaptureSuccess(image)
                        lifecycleScope.launch (Dispatchers.Main) {
                            var bitmap = image.convertImageProxyToBitmap().rotate(image.imageInfo.rotationDegrees.toFloat())
//                            bitmap = ExifUtil.rotateBitmap(file.absolutePath,bitmap)
                            var byteArray = cropImage(bitmap,binding.cameraPreview,binding.imageView5)
                            bitmap = BitmapFactory.decodeByteArray(byteArray,0,byteArray.size)
                            resultSaveImage(bitmap)
                        }
                    }

                    override fun onError(exception: ImageCaptureException) {
                        Toast.makeText(
                            binding.root.context,
                            "Error taking photo",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                })
//            it.takePicture(
//                outputFileOptions,
//                imgCaptureExecutor,
//                object: ImageCapture.OnImageSavedCallback {
//                    override fun onImageSaved(outputFileResults: ImageCapture.OutputFileResults) {
////                        var bitmap = BitmapFactory.decodeFile(file.absolutePath)
////                        bitmap = ExifUtil.rotateBitmap(file.absolutePath,bitmap)
////                        var byteArray = cropImage(bitmap,binding.cameraPreview,binding.imageView5)
////                        bitmap = BitmapFactory.decodeByteArray(byteArray,0,byteArray.size)
////                        saveBitmap(file,bitmap)
//                        lifecycleScope.launch (Dispatchers.Main) {
//                            resultSaveImage(file.absolutePath)
//                        }
//                    }
//
//                    override fun onError(exception: ImageCaptureException) {
//                        Toast.makeText(
//                            binding.root.context,
//                            "Error taking photo",
//                            Toast.LENGTH_LONG
//                        ).show()
//                    }
//
//                }
//            )
        }
    }

    private fun saveBitmap(file : File,bitmap : Bitmap)  {
        try {
            if(file.exists()) {
                file.delete()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        val fileOutputStream = FileOutputStream(file)
        bitmap.compress(Bitmap.CompressFormat.PNG,100,fileOutputStream)
        try {
            fileOutputStream.flush()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        try {
            fileOutputStream.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun animateFlash() {
        binding.root.postDelayed({
            binding.root.foreground = ColorDrawable(resources.getColor(R.color.take_image_color))
            binding.ivTapToCapture.hide()
            binding.root.postDelayed({
                binding.root.foreground = null
            },50)
        },100)
    }

    private fun resultSaveImage(bitmap: Bitmap) {
        binding.ivTapToCapture.show()
        when(typeTakeImage) {
            Utils.CHOOSE_IMAGE_OPTION_DIALOG_FRONT_TYPE -> {
                viewModel.saveBitmapFrontImage(bitmap)
                isTakeFrontImage = true
                typeTakeImage = Utils.CHOOSE_IMAGE_OPTION_DIALOG_BACK_TYPE
            }
            Utils.CHOOSE_IMAGE_OPTION_DIALOG_BACK_TYPE -> {
                viewModel.saveBitmapBackImage(bitmap)
                isTakeBackImage = true
                typeTakeImage = Utils.CHOOSE_IMAGE_OPTION_DIALOG_FRONT_TYPE
            }
        }
        changeImagePreview(typeTakeImage)
        setFragmentResultListener(isClose = false)
    }

    private fun setFragmentResultListener(isClose : Boolean) {
        if(isTakeFrontImage && isTakeBackImage || isClose) {
            typeTakeImage = ""
            findNavController().navigateUp()
            setFragmentResult(
                Utils.EKYC_TAKE_IMAGE_KEY,
                bundleOf(
//                    Utils.EKYC_TAKE_IMAGE_FRONT_URI to viewModel.getUrlFrontImage(),
//                    Utils.EKYC_TAKE_IMAGE_BACK_URI to viewModel.getUrlBackImage(),
                    Utils.EKYC_TAKE_IMAGE_TYPE to safeArgs.type
                )
            )
        }
    }

    private fun showAlertDialog(message: String, textConfirm: String? = null) {
        alertDialog?.dismiss()
        alertDialog = AlertDialog().apply {
            setMessage(message)
            textConfirm?.let {
                setTextConfirm(it)
            }
            setListener(object : AlertDialogListener {
                override fun onExit() {
                    findNavController().navigateUp()
                }

                override fun onConfirm() {
                    checkPermissionCamera()
                    findNavController().navigateUp()
                }
            })
        }
        alertDialog?.show(childFragmentManager, "CameraAlertDialog")
    }

    fun ImageProxy.convertImageProxyToBitmap(): Bitmap {
        val buffer = planes[0].buffer
        buffer.rewind()
        val bytes = ByteArray(buffer.capacity())
        buffer.get(bytes)
        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
    }

    fun Bitmap.rotate(degrees: Float): Bitmap {
        val matrix = Matrix().apply { postRotate(degrees) }
        return Bitmap.createBitmap(this, 0, 0, width, height, matrix, true)
    }

    private fun cropImage(bitmap: Bitmap, frame: View, reference: View): ByteArray {

        val heightOriginal = frame.height
        val widthOriginal = frame.width
        val heightFrame = reference.height
        val widthFrame = reference.width
        val leftFrame = reference.left
        val topFrame = reference.top
        val heightReal = bitmap.height
        val widthReal = bitmap.width
        val widthFinal = widthFrame * widthReal / widthOriginal
        val heightFinal = heightFrame * heightReal / heightOriginal
        val leftFinal = leftFrame * widthReal / widthOriginal
        val topFinal = topFrame * heightReal / heightOriginal
        val bitmapFinal = Bitmap.createBitmap(
            bitmap,
            leftFinal, topFinal, widthFinal, heightFinal
        )
        val stream = ByteArrayOutputStream()
        bitmapFinal.compress(
            Bitmap.CompressFormat.JPEG,
            100,
            stream
        ) //100 is the best quality possibe
        return stream.toByteArray()
    }

    override fun EKYCViewModel.EKYCState.toUI() {
        when(this) {
            is EKYCViewModel.EKYCState.Loading -> {

            }
            is EKYCViewModel.EKYCState.Error -> {

            }
            is EKYCViewModel.EKYCState.Done -> {

            }
            else -> {}
        }
    }

}