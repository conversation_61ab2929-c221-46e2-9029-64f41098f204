package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.databinding.SportInteractiveMatchProcessTitleBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.match_process.SportInteractiveMatchProcessTitle

class SportInteractiveProcessTitleViewHolder(private val binding: SportInteractiveMatchProcessTitleBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveMatchProcessTitle>(binding) {
    override fun bind(data: SportInteractiveMatchProcessTitle) {
        binding.tvHeader.text = data.title
    }
}