package com.fptplay.mobile.features.pladio
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.PageProvider
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.helper.PladioDataPreprocessor
import com.fptplay.mobile.features.pladio.playback.PlaybackPlayerRemote
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.SHUFFLE_MODE_SHUFFLE
import com.fptplay.mobile.features.pladio.util.PladioUtil
import com.fptplay.mobile.features.pladio.util.PladioUtil.mapToSong
import com.fptplay.mobile.features.pladio.search.StatusSearch
import com.fptplay.mobile.features.pladio.util.PladioUtil.getEventContentType
import com.fptplay.mobile.features.pladio.util.PladioUtil.toSongType
import com.fptplay.mobile.features.premiere.PremiereViewModel.EventType
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.AlarmTimeConfig
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioPlaylist
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioPlaylistEntity
import com.xhbadxx.projects.module.domain.repository.fplay.PladioRepository
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemContentType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureMeta
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.TabMenu
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioDetailEntity
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioEpisodeType
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioEventDetailEntity
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioRelatedEntity
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioStreamEntity
import com.xhbadxx.projects.module.domain.entity.fplay.search.Search
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchAppId
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchSuggest
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.HomeOs4Repository
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.zip
import timber.log.Timber
import javax.inject.Inject
import kotlin.system.measureTimeMillis

@HiltViewModel
class PladioViewModel @Inject constructor(
    private val homeOs4Repository: HomeOs4Repository,
    private val searchRepository: CommonRepository,
    private val savedState: SavedStateHandle,
    private val pladioRepository: PladioRepository,
) : BaseViewModel<PladioViewModel.PladioIntent, PladioViewModel.PladioState>(){

    private val pladioDataPreprocessor by lazy { PladioDataPreprocessor() }
    private var pladioGetDetailJob: Job? = null

    private var clusterItemJob: Job? = null
    private var watchingStructure: Structure? = null
    private var followStructure: Structure? = null
    private var structMeta: StructureMeta? = null

    private var _pladioPlaylistDetail : PladioPlaylist? = null

    var pladioPlaylistDetail: PladioPlaylist?
        get() = _pladioPlaylistDetail
        set(value) {
            _pladioPlaylistDetail = value
        }

    private var isFirstTimePlay = true


    fun savePremiereId(premiereId: String) { savedState.set("premiere_id", premiereId) }
    fun getPremiereId() = savedState.get<String>("premiere_id") ?: ""

    fun saveClickTimeToPlay(clickTime: Long) { savedState.set("clickTimeToPlay", clickTime) }
    fun getClickTimeToPlay(): Long = savedState.get<Long>("clickTimeToPlay") ?: 0L

    private var isVipRequired : Pair<Boolean, RequiredVip?>? = null
    fun saveVipRequired(isRequired: Boolean, requiredVip: RequiredVip? = null) { isVipRequired = Pair(isRequired, requiredVip)}
    fun getVipRequired() = isVipRequired

    override fun dispatchIntent(intent: PladioIntent) {
        safeLaunch {
            when (intent) {
                is PladioIntent.GetSomething -> {
            
                }
                is PladioIntent.GetStructure -> {
                    homeOs4Repository.getStructure(pageId = intent.pageProvider.pageId.id).collect {
                        viewModelScope.launch(Dispatchers.Main) {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                structMeta = data.meta
                                PladioState.ResultStructureMetaData(
                                    isCached = isCached,
                                    data = data.meta,
                                    shouldProcess = true)
                            }
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                PladioState.StructureResult(isCached = isCached, data = data.structure)
                            }
                        }
                    }
                }


                is PladioIntent.GetStructureLocal -> _state.value = PladioState.StructureResult(isCached = true, data = intent.dataLocal)

                is PladioIntent.GetClusterItem -> {
                    clusterItemJob?.cancel()
                    clusterItemJob = launch {
                        getStructureItems(intent) {
                            Timber.d("***** Done cluster job with ${it.data[0].second}")
                            saveWatchingAndFollowPosition(it.data.map { item -> item.second })
                            _state.value = PladioState.ResultClusterStructureItem(isCached = false, data = it.data)
                        }
                    }
                }
                is PladioIntent.GetClusterItemLocal -> {
                    getStructureItemsLocal(intent) {
                        saveWatchingAndFollowPosition(it.data.map { item -> item.second })
                        _state.value = PladioState.ResultClusterStructureItem(isCached = true, data = it.data)
                    }
                }

                is PladioIntent.GetStructureItem -> {
                    homeOs4Repository.getStructureItem(
                        type = intent.type,
                        blockId = intent.structureId,
                        blockType = intent.blockType,
                        pageIndex = intent.page,
                        pageSize = intent.perPage,
                        watchingVersion = intent.watchingVersion,
                        customData = intent.customData,
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            when (intent.type) {
                                Constants.FOLLOW_TYPE -> PladioState.ResultFollowStructureItem(
                                    isCached = isCached,
                                    data = data
                                )
                                Constants.COMING_SOON -> PladioState.ResultComingSoonStructureItem(
                                    isCached = isCached,
                                    data = data
                                )
                                else -> PladioState.ResultHistoryVodStructureItem(isCached = isCached, data = data)
                            }
                        }
                    }
                }

                is PladioIntent.GetHistoryVod -> {
                    homeOs4Repository.getHistoryVod(
                        userId = intent.userId,
                        page = intent.page,
                        perPage = intent.perPage
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            PladioState.ResultHistoryVodStructureItem(
                                isCached = isCached,
                                data = data
                            )
                        }
                    }
                }
                is PladioIntent.GetCategoryStructure -> {
                    _state.value = PladioState.ResultCategoryStructure(isCached = false, data = intent.structure)
                }
                is PladioIntent.GetSearchSuggest -> {
                    clusterItemJob?.cancel()
                    clusterItemJob = launch {
                        getSearchSuggest(intent)
                    }
                }
                is PladioIntent.GetSearchResult -> {
                    clusterItemJob?.cancel()
                    clusterItemJob = launch {
                        getStructureResultItem(intent).collect {
                            clusterItemJob = null
                            _state.value = it
                        }
                    }
                }
                is PladioIntent.GetTrendItems -> {
                    clusterItemJob?.cancel()
                    clusterItemJob = launch {
                        searchRepository.getSearchTrendingMobileV2(
                            appIds = listOf(
                                SearchAppId.Music,
                                SearchAppId.Pladio,
                                SearchAppId.Playlist
                            )
                        ).collect{
                            _state.value = it.reduce(intent) { isCached, data ->
                                PladioState.ResultGetTrendItems(data, intent.status)
                            }
                        }
                    }
                }
                is PladioIntent.GetPlaylistDetail -> {
                    pladioRepository.getPladioPlaylistDetail(intent.playlistId).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            if (intent.callFromRestoreSong) {
                                handleLogicRestorePlaylist(intent = intent, data = data)
                            }
                            PladioState.ResultPlaylistDetail(isCached, data, intent)
                        }
                    }
                }

                // region Playback
                is PladioIntent.GetPlaybackAlarmTimeConfig -> {
                    pladioRepository.getPladioAlarmTimeConfig().collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            PladioState.ResultPlaybackAlarmTimeConfig(isCached, data)
                        }
                    }
                }
                // endregion

                // region Bottom Navigation
                is PladioIntent.GetBottomNavigation -> {
                    homeOs4Repository.getMenuWithType(menuType = "podcast").collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            PladioState.ResultBottomNavigation(isCached, data, data.isNotEmpty())
                        }
                    }
                }
                // endregion

                is PladioIntent.GetPladioDetail -> {
                    pladioGetDetailJob?.cancel()
                    pladioGetDetailJob = launch {
                        //
                        pladioDataPreprocessor.clearAllData()
                        pladioDataPreprocessor.setupProcessingData(id = intent.song.id)
                        //
                        pladioRepository.getPladioDetail(pladioId = intent.song.id).collect {
                            _state.value = it.reduce(intent) { isCached, data ->
                                if (intent.isHomeCalled) {
                                    processGetDetailDone(intent = intent, data = data)
                                }
                                if (intent.callFromRestoreSong) {
                                    handleLogicRestoreSeries(intent = intent, data = data)
                                }
                                PladioState.ResultPladioDetail(isCached, data, intent)
                            }
                        }
                    }
                }

                is PladioIntent.GetPladioEventDetail -> {
                    pladioRepository.getPladioEventDetail(pladioId = intent.song.id).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            if (intent.isHomeCalled) {
                                processGetDetailEventDone(intent = intent, data = data)
                            }
                            if (intent.callFromRestoreSong) {
                                handleLogicRestoreEvent(intent = intent, data = data)
                            }
                            PladioState.ResultPladioEventDetail(isCached, data, intent)
                        }
                    }
                }

                is PladioIntent.GetPladioRecommendation -> {
                    pladioRepository.getPladioRelated(pladioId = intent.song.id).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            processGetRecommendDone(intent = intent, data = data)
                            PladioState.ResultPladioRecommendation(isCached, data, intent)
                        }
                    }
                }

                is PladioIntent.GetPladioStream -> {
                    pladioRepository.getPladioStream(pladioId = intent.id, episodeId = intent.episodeId, streamType = intent.streamType).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            PladioState.ResultPladioStream(isCached, data, intent)
                        }
                    }
                }
                else -> {}
            }
        }
    }

    private suspend fun getSearchSuggest(intent: PladioIntent.GetSearchSuggest) {
        searchRepository.getSearchSuggest(
            action = "suggest",
            query = intent.query,
            appIds = listOf(SearchAppId.Music,SearchAppId.Pladio,SearchAppId.Playlist)
        ).zip(
            searchRepository.getSearchResult(
                action = "all",
                query = intent.query,
                page = intent.page,
                perPage = 10,
                appIds = listOf(SearchAppId.Music,SearchAppId.Pladio,SearchAppId.Playlist)
            )
        ) { suggest, result ->

            if(suggest is Result.Success || result is Result.Success) {
                var resultSearchSuggest = PladioState.ResultGetSearchSuggest(
                    suggestItem = emptyList(),
                    trendingItem = emptyList(),
                    status = intent.status
                )
                if(suggest is Result.Success) {
                    resultSearchSuggest = resultSearchSuggest.copy(suggestItem = suggest.successData)
                }

                if(result is Result.Success) {
                    resultSearchSuggest = resultSearchSuggest.copy(trendingItem = result.successData)
                }

                resultSearchSuggest
            } else {
                suggest.reduce(intent) { isCached, data ->
                    PladioState.ResultGetSearchSuggest(
                        suggestItem = emptyList(),
                        trendingItem = emptyList(),
                        status = intent.status
                    )
                }
            }

        }.collect {
            viewModelScope.launch(Dispatchers.Main) {
                _state.value = it
            }
        }
    }

    private suspend fun getStructureSuggestItem(intent:PladioIntent.GetSearchSuggest) = flow {
        var error:  PladioState.Error ? = null
        var suggestItem: List<SearchSuggest> = emptyList()
        var trendingItem: List<Search> = emptyList()
        val measureTime = measureTimeMillis {
            emit(PladioState.Loading(intent))
            withTimeoutOrNull(10_000L) {
                try {
                    val deffereds: ArrayList<Deferred<Unit>> = arrayListOf()
                    deffereds.add(async(Dispatchers.IO) {
                        searchRepository.getSearchSuggest(
                            action = "suggest",
                            query = intent.query,
                            appIds = listOf(SearchAppId.Music,SearchAppId.Pladio,SearchAppId.Playlist)
                        ).process (successCallback = {
                            suggestItem = it
                        }, errorCallback = {
                            error = PladioState.Error(message = it)
                        })
                    })
                    deffereds.add(async(Dispatchers.IO) {
                        searchRepository.getSearchResult(
                            action = "all",
                            query = intent.query,
                            page = intent.page,
                            perPage = 10,
                            appIds = listOf(SearchAppId.Music,SearchAppId.Pladio,SearchAppId.Playlist)
                        ).process (successCallback = {
                            trendingItem = it
                        }, errorCallback = {
                            error = PladioState.Error(message = it)
                        })
                    })
                    deffereds.forEach { it.await() }
                    if(error != null) {
                        emit(PladioState.Error(error!!.message, intent))
                    } else {
                        emit(
                            PladioState.ResultGetSearchSuggest(
                                suggestItem = suggestItem,
                                trendingItem = trendingItem,
                                status = intent.status
                            )
                        )
                    }
                }catch (ex:Exception){
                    ex.printStackTrace()
                }
            } ?: run {
                emit(PladioState.Error("Lỗi kết nối hệ thống (#SD002). Vui lòng thử lại", intent))
            }
            withContext(Dispatchers.Main) {
                clusterItemJob = null
                _state.value = PladioState.Done()
            }
        }
        emit(PladioState.Done(intent))
    }
    private suspend fun getStructureResultItem(intent: PladioIntent.GetSearchResult)= flow{
        var error: PladioState.Error? = null
        var resultItem: List<Search>? = null
        val measureTime = measureTimeMillis {
            emit(PladioState.Loading(intent))
            withTimeoutOrNull(10_000L) {
                try {
                    val deffereds: ArrayList<Deferred<Unit>> = arrayListOf()
                    deffereds.add(async {
                        searchRepository.getSearchResult(
                            action = intent.action,
                            query = intent.query,
                            page = intent.page,
                            perPage = intent.perPage,
                            // warning  : send parameter to 79,86,10 if not save history
                            appIds = listOf(
                                SearchAppId.Music,
                                SearchAppId.Pladio,
                                SearchAppId.Playlist
                            )
                        ).process(successCallback = {
                            resultItem = it
                        }, errorCallback = {
                            error = PladioState.Error(message = it)
                        })
                    })
                    deffereds.forEach { it.await() }
                    if (error != null) {
                        emit(PladioState.Error(error!!.message, intent))
                    } else {
                        emit(PladioState.ResultGetSearchResult(
                            data = resultItem,
                            isBind = intent.page == 1,
                            status = intent.status
                        ))
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            } ?: run {
                emit(
                    PladioState.Error(
                        "Lỗi kết nối hệ thống (#SD002). Vui lòng thử lại",
                        intent
                    ))
            }
            // Process timeout or return null
            withContext(Dispatchers.Main) {
                clusterItemJob = null
                _state.value =  PladioState.Done(intent)
            }
        }
    }


    /**
     * Before adding Ads, these [watchingPos] and [followPos] were set (by calling [saveWatchingAndFollowPosition]).
     * This function is used for updated these 2 variables
     */

    private suspend fun getStructureItemsLocal(
        intent: PladioIntent.GetClusterItemLocal,
        block: (intent: PladioIntent.GetClusterItemLocal) -> Unit
    ) {
        val measureTime = measureTimeMillis {
            withContext(Dispatchers.IO) {
                try {
                    val pairs = intent.data // Pair[Index, Structure]
                    pairs.forEach {
                        val index = it.first
                        val structure = it.second
                        processLocal(index = index, data = structure)
                    }
                    pairs.map { item ->
                        if (item.second.items.isNotEmpty()) {
                            if (item.second.items[0].itype == ItemType.Fake) {
                                item.second.items = emptyList()
                            }
                        }
                    }
                    withContext(Dispatchers.Main) {
                        block(intent)
                    }
                } catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
            } // Process timeout or return null
            withContext(Dispatchers.Main) {
                _state.value = PladioState.Done()
            }
        }
        Logger.d("Measure time of GetStructureItem: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }

    private fun saveWatchingAndFollowPosition(data: List<Structure>) {
        saveWatchingPos(-1)
        saveFollowPos(-1)
        saveWatchingStructure(null)
        saveFollowStructure(null)

        var followPosTemp = 0
        var watchingPosTemp = 0

        run loop@{
            data.forEach { item ->
                when {
                    item.itype == Constants.WATCHING_TYPE -> {
//                    Timber.d("***Before update ${item.itype}: $watchingPosTemp")
                        watchingPosTemp += 1
                        saveWatchingPos(watchingPosTemp)
                        saveWatchingStructure(item)
                        if (followPos == -1) {
                            followPosTemp += 1
                        } else {
                            return@loop
                        }
                    }
                    item.itype == Constants.FOLLOW_TYPE -> {
//                    Timber.d("***Before update ${item.itype}: $followPosTemp")
                        followPosTemp += 1
                        saveFollowPos(followPosTemp)
                        saveFollowStructure(item)
                        if (watchingPos == -1) {
                            watchingPosTemp += 1
                        } else {
                            return@loop
                        }
                    }
                    (item.items.isNotEmpty() || item.itype.lowercase() == "ads") -> {
//                        Timber.d("***Before update plus 1 ${item.itype}: $watchingPosTemp $followPosTemp")
                        followPosTemp += 1
                        watchingPosTemp += 1
                    }
                    (item.items.isEmpty() && item.itype.lowercase() != "ads") -> {
//                        Timber.d("***Before update minus 1 ${item.itype}: $watchingPosTemp $followPosTemp")
                        followPosTemp -= 1
                        watchingPosTemp -= 1
                    }
                }
            }
        }
        Timber.d("***Before update watching: $watchingPos follow: $followPos")
    }

    private suspend fun getStructureItems(
        intent: PladioIntent.GetClusterItem,
        block: (intent: PladioIntent.GetClusterItem) -> Unit
    ) {
        val measureTime = measureTimeMillis {
            withContext(Dispatchers.IO) {
                try {
                    val arrDeferred: ArrayList<Deferred<Unit>> = arrayListOf()
                    val pairs = intent.data // Pair[Index, Structure]
                    withTimeoutOrNull(10_000L) {
                        pairs.forEach {
                            val index = it.first
                            val structure = it.second
                            when (structure.style) {
                                BlockStyle.HighLight -> arrDeferred.add(async {
                                    homeOs4Repository.getStructureItem(
                                        blockId = structure.id,
                                        type = structure.itype ?: "",
                                        blockType = structure.style.id,
                                        pageIndex = intent.page,
                                        pageSize = intent.perPage - 1,
                                        customData = structure.customData
                                    ).process(index = index, data = structure)
                                })
                                BlockStyle.SquareSliderMedium -> arrDeferred.add(async {
                                    homeOs4Repository.getStructureItem(
                                        blockId = structure.id,
                                        type = structure.itype ?: "",
                                        blockType = structure.style.id,
                                        pageIndex = intent.page,
                                        pageSize = 10,
                                        customData = structure.customData
                                    ).process(index = index, data = structure)
                                })
                                BlockStyle.SquareSliderSmallOneColumnItems -> arrDeferred.add(async {
                                    homeOs4Repository.getStructureItem(
                                        blockId = structure.id,
                                        type = structure.itype ?: "",
                                        blockType = structure.style.id,
                                        pageIndex = intent.page,
                                        pageSize = 12,
                                        customData = structure.customData
                                    ).process(index = index, data = structure)
                                })
                                BlockStyle.NumericRank -> arrDeferred.add(async {
                                    homeOs4Repository.getStructureItem(
                                        blockId = structure.id,
                                        type = structure.itype ?: "",
                                        blockType = structure.style.id,
                                        pageIndex = intent.page,
                                        pageSize = 10,
                                        customData = structure.customData
                                    ).process(index = index, data = structure)
                                })
                                else -> {
                                    if(structure.itype == Constants.WATCHING_TYPE) {
                                        arrDeferred.add(async {
                                            homeOs4Repository.getStructureItem(
                                                blockId = structure.id,
                                                type = structure.itype ?: "",
                                                blockType = structure.style.id,
                                                pageIndex = intent.page,
                                                pageSize = intent.perPage,
                                                watchingVersion = "v1",
                                                customData = structure.customData
                                            ).process(index = index, data = structure)
                                        })
                                    } else {
                                        arrDeferred.add(async {
                                            homeOs4Repository.getStructureItem(
                                                blockId = structure.id,
                                                type = structure.itype ?: "",
                                                blockType = structure.style.id,
                                                pageIndex = intent.page,
                                                pageSize = intent.perPage,
                                                customData = structure.customData
                                            ).process(index = index, data = structure)
                                        })
                                    }
                                }
                            }
                        }
                        // Parallel the all requests
                        arrDeferred.forEach { it.await() }
                    }
                    pairs.map { item ->
                        if (item.second.items.isNotEmpty()) {
                            if (item.second.items[0].itype == ItemType.Fake) {
                                item.second.items = emptyList()
                            }
                        }
                    }
                    withContext(Dispatchers.Main) {
                        block(intent)
                    }
                }
                catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
            } // Process timeout or return null
            withContext(Dispatchers.Main) {
                clusterItemJob = null
                _state.value = PladioState.Done()
            }
        }
        Logger.d("Measure time of GetStructureItem: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }
    private suspend fun Flow<Result<List<StructureItem>>>.process(index: Int, data: Structure) {
        this.filter { it is Result.Success || it is Result.Error }
            .collect {
                val structureItem = it.data
                if (structureItem != null) {
                    data.items = structureItem.filterEndedItem()
                    if (data.itype.equals("category")) {
                        saveStructureItemType(data.itype)
                        saveBlockId(data.id)
                        saveBlockType(data.style.id)
                        saveCustomData(data.customData)
                    }
                } else {
                    data.items = emptyList()
                }
                safeLaunch {
//                    Timber.d("*** Item result: $index - $data")
                    _state.value = PladioState.ResultStructureItem(isCached = false, data = Pair(index, data))
                }
            }
    }
    val watchingPos get() = savedState.get("watchingPos") ?: -1
    val followPos get() = savedState.get("followPos") ?: -1

    val structureType get() = savedState.get("structureType") ?: ""
    val blockType get() = savedState.get("blockType") ?: ""
    val blockId get() = savedState.get("blockId") ?: ""
    val customData get() = savedState.get("customData") ?: ""

    val watchingStruct get() = watchingStructure
    val followStruct get() = followStructure

    val structureMeta get() = structMeta
    private fun saveWatchingStructure(data: Structure?) {
        watchingStructure = data
    }

    private fun saveFollowStructure(data: Structure?) {
        followStructure = data
    }

    private fun saveWatchingPos(pos: Int) {
        savedState.set("watchingPos", pos)
    }

    private fun saveFollowPos(pos: Int) {
        savedState.set("followPos", pos)
    }

    fun clearWatchingAndFollowPos() {
        savedState.set("followPos", -1)
        savedState.set("followPos", -1)
        followStructure = null
        watchingStructure = null
    }

    fun saveStructureItemType(type:String){
        savedState.set("structureType",type)
    }
    fun saveBlockId(id:String){
        savedState.set("blockId",id)
    }
    fun saveBlockType(type:String){
        savedState.set("blockType",type)
    }
    fun saveCustomData(customData:String){
        savedState.set("customData",customData)
    }
    /**
     * Before adding Ads, these [watchingPos] and [followPos] were set (by calling [saveWatchingAndFollowPosition]).
     * This function is used for updated these 2 variables
     */
    private suspend fun <T> Flow<Result<T>>.process(successCallback: (T) -> Unit, errorCallback: ((String) -> Unit)? = null) {
        this.collect {
            if(it is Result.Success) {
                successCallback(it.successData)
            } else if(it is Result.Error) {
                errorCallback?.invoke(it.message)
            }
        }
    }
    fun updateWatchingAndFollowPos(structures: List<Structure>) {
        val differBetweenWatchingAndFollow = watchingPos - followPos
        run loop@{
            if (watchingPos > -1 && followPos > -1) {
                structures.forEachIndexed { index, item ->
                    if (item.itype == Constants.WATCHING_TYPE) {
                        saveWatchingPos(index)
                        saveFollowPos(index - differBetweenWatchingAndFollow)
                        return@loop
                    } else if (item.itype == Constants.FOLLOW_TYPE) {
                        saveFollowPos(index)
                        saveWatchingPos(index + differBetweenWatchingAndFollow)
                        return@loop
                    }
                }
            } else {
                if (watchingPos > -1) {
                    var needContinueProcess = true
                    structures.forEachIndexed { index, item ->
                        if (item.itype == Constants.WATCHING_TYPE) {
                            saveWatchingPos(index)
                            needContinueProcess = false
                            return@loop
                        }
                    }
                    if (needContinueProcess) {
                        try {
                            structures.filter { it.contentType != Structure.ContentType.Ads }[watchingPos].run {
                                saveWatchingPos(structures.indexOf(this) + 1)
                            }
                        } catch (ex: IndexOutOfBoundsException) {
                            ex.printStackTrace()
                        }
                    }
                }

                if (followPos > -1) {
                    Timber.d("***follow: $watchingPos - $differBetweenWatchingAndFollow")
                    saveFollowPos(watchingPos - differBetweenWatchingAndFollow)
                }
            }
        }

//        Timber.d("***After update watching: $watchingPos follow: $followPos")
    }
    //endregion process watching and fol
    private fun List<StructureItem>.filterEndedItem(): List<StructureItem> {
        return filter { Util.statusBetweenStartAndEndTime(it.beginTime, it.endTime) != 3 }
    }

    private fun processLocal(index: Int, data: Structure) {
        if (data.itype != Constants.WATCHING_TYPE
            && data.itype != Constants.FOLLOW_TYPE
            && data.itype != Constants.FOLLOW_CHANNEL_TYPE
            && data.itype != Constants.COMING_SOON) {
            data.items = data.items.filterEndedItem()
        }
    }

    // region Logic Process Get Detail
    fun checkNavigateToContent(
        data: BaseObject,
        onNotSupport: (message: String, textTitle: String, textConfirm: String) -> Unit = {_,_,_ -> },
        onPairingCastState: (shouldPlay: (Boolean) -> Unit) -> Unit = {},
        navigateToCategory: (StructureItem) -> Unit = {},
        navigateToPlaylist: (playlistId: String, fromDeeplink: Boolean?, isAutoOpenPlayer: Boolean?, startPlayPositionMs: Long?, isAutoPlay: Boolean?, targetIdPlay: String?, episodeId: String?) -> Unit = { _, _, _, _, _, _, _ -> },
        navigateToSeries: (vodId: String, fromDeeplink: Boolean?, isAutoOpenPlayer: Boolean?, startPlayPositionMs: Long?, isAutoPlay: Boolean?, targetIdPlay: String?, episodeId: String?) -> Unit = { _, _, _, _, _, _, _ -> },

        // Deeplink
        fromDeeplink: Boolean? = null,
        isAutoOpenPlayer: Boolean? = null,
        startPlayPositionMs: Long? = null,
        isAutoPlay: Boolean? = null,
        targetIdPlay: String? = null,
        episodeId: String? = null,
        //
    ) {
        if (data is StructureItem && (data.itype == ItemType.CATEGORY_HIGHLIGHT || data.itype == ItemType.CATEGORY_VOD )) {
            navigateToCategory.invoke(data)
        } else {
            if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected) {
                onPairingCastState.invoke { shouldPlay ->
                    if (shouldPlay) {
                        processNavigateToContent(
                            data = data,
                            onNotSupport = onNotSupport,
                            navigateToCategory = navigateToCategory,
                            navigateToPlaylist = navigateToPlaylist,
                            navigateToSeries = navigateToSeries,
                            // deeplink
                            fromDeeplink = fromDeeplink,
                            isAutoOpenPlayer = isAutoOpenPlayer,
                            startPlayPositionMs = startPlayPositionMs,
                            isAutoPlay = isAutoPlay,
                            targetIdPlay = targetIdPlay,
                            episodeId = episodeId
                        )
                        return@invoke
                    }
                }
            } else {
                processNavigateToContent(
                    data = data,
                    onNotSupport = onNotSupport,
                    navigateToCategory = navigateToCategory,
                    navigateToPlaylist = navigateToPlaylist,
                    navigateToSeries = navigateToSeries,
                    // deeplink
                    fromDeeplink = fromDeeplink,
                    isAutoOpenPlayer = isAutoOpenPlayer,
                    startPlayPositionMs = startPlayPositionMs,
                    isAutoPlay = isAutoPlay,
                    targetIdPlay = targetIdPlay,
                    episodeId = episodeId
                )
            }
        }
    }

    private fun processNavigateToContent(
        data: BaseObject,
        onNotSupport: (message: String, textTitle: String, textConfirm: String) -> Unit = {_,_,_ -> },
        navigateToCategory: (StructureItem) -> Unit = {},
        navigateToPlaylist: (playlistId: String, fromDeeplink: Boolean?, isAutoOpenPlayer: Boolean?, startPlayPositionMs: Long?, isAutoPlay: Boolean?, targetIdPlay: String?, episodeId: String?) -> Unit = { _, _, _, _, _, _, _ -> },
        navigateToSeries: (vodId: String, fromDeeplink: Boolean?, isAutoOpenPlayer: Boolean?, startPlayPositionMs: Long?, isAutoPlay: Boolean?, targetIdPlay: String?, episodeId: String?) -> Unit = { _, _, _, _, _, _, _ -> },

        // Deeplink
        fromDeeplink: Boolean? = null,
        isAutoOpenPlayer: Boolean? = null,
        startPlayPositionMs: Long? = null,
        isAutoPlay: Boolean? = null,
        targetIdPlay: String? = null,
        episodeId: String? = null
        //
    ) {
        when (data) {
            is StructureItem -> {
                if (data.itype == ItemType.CATEGORY_HIGHLIGHT || data.itype == ItemType.CATEGORY_VOD ) {
                    navigateToCategory.invoke(data)
                }
                else if (data.contentType == ItemContentType.Podcast) {
                    when (data.itype.id) {
                        ItemType.VOD.id -> {
                            preprocessCheckTypeVod(
                                song = Song(id = data.id, title = data.title),
                                extraData = PladioExtraData(
                                    startPosition = 0,
                                    isShuffleMode = false,
                                    // Deeplink
                                    fromDeeplink = fromDeeplink,
                                    isAutoOpenPlayer = isAutoOpenPlayer,
                                    startPlayPositionMs = startPlayPositionMs,
                                    isAutoPlay = isAutoPlay,
                                    targetIdPlay = targetIdPlay,
                                    episodeId = episodeId
                                ),
                                navigateToSeries = navigateToSeries
                            )
                        }
                        ItemType.PLAYLIST.id -> {
                            navigateToPlaylist.invoke(data.id, fromDeeplink, isAutoOpenPlayer, startPlayPositionMs, isAutoPlay, targetIdPlay, episodeId)
                        }
                        ItemType.Event.id,
                        ItemType.EventTV.id -> {
                            preprocessCheckTypeEvent(
                                song = Song(id = data.highlightId, title = data.title),
                                extraData = PladioExtraData(
                                    startPosition = 0,
                                    isShuffleMode = false,
                                    fromDeeplink = fromDeeplink,
                                    isAutoOpenPlayer = isAutoOpenPlayer,
                                    startPlayPositionMs = startPlayPositionMs,
                                    isAutoPlay = isAutoPlay,
                                    targetIdPlay = targetIdPlay,
                                    episodeId = episodeId
                                ),
                                onNotSupport = onNotSupport
                            )
                        }
                        else -> {}
                    }
                } else {
                    onNotSupport.invoke(
                        MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_description),
                        MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_title),
                        MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_text_close)
                    )
                }
            }
            is Search ->{
                if (data.contentType == ItemContentType.Podcast) {
                    if(data.searchItemType == Search.SearchItemType.VODPlaylist) {
                        navigateToPlaylist.invoke(data.id, fromDeeplink, isAutoOpenPlayer, startPlayPositionMs, isAutoPlay, targetIdPlay, episodeId)
                    }
                    else {
                        if (data.id != PlaybackPlayerRemote.currentSong.id) {
                            preprocessCheckTypeVod(
                                song = Song(id = data.id, title = data.title),
                                extraData = PladioExtraData(
                                    startPosition = 0,
                                    isShuffleMode = false
                                ),
                                navigateToSeries = navigateToSeries
                            )
                        }
                    }
                } else {
                    onNotSupport.invoke(
                        MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_description),
                        MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_title),
                        MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_text_close)
                    )
                }
            }
        }
    }

    private fun preprocessCheckTypeVod(song: Song, extraData: PladioExtraData, navigateToSeries: (vodId: String, fromDeeplink: Boolean?, isAutoOpenPlayer: Boolean?, startPlayPositionMs: Long?, isAutoPlay: Boolean?, targetIdPlay: String?, episodeId: String?) -> Unit = { _, _, _, _, _, _, _ -> }) {
        dispatchIntent(PladioIntent.GetPladioDetail(song, extraData, navigateToSeries, isHomeCalled = true))
    }

    fun triggerPlaySongPlaylist(songs: List<Song>, extraData: PladioExtraData) {
        pladioDataPreprocessor.processPladioSeries(data = songs, extraData = extraData)
    }

    fun triggerPlaySongPlaylistFromRestore(songs: List<Song>, extraData: PladioExtraData, startPlayPositionMs: Long, isAutoPlay: Boolean) {
        pladioDataPreprocessor.processPladioSeriesFromRestore(data = songs, extraData = extraData, startPlayPosition = startPlayPositionMs, isAutoPlay = isAutoPlay)
    }

    private fun processGetDetailDone(intent: PladioIntent.GetPladioDetail, data: PladioDetailEntity) {
        pladioGetDetailJob = null

        // Check Type
        when (data.data.episodeType) {
            PladioEpisodeType.Series -> {
                intent.navigateToSeries(
                    intent.song.id,
                    intent.extraData.fromDeeplink,
                    intent.extraData.isAutoOpenPlayer,
                    intent.extraData.startPlayPositionMs,
                    intent.extraData.isAutoPlay,
                    intent.extraData.targetIdPlay,
                    intent.extraData.episodeId
                )
            }
            PladioEpisodeType.Single -> {
                triggerPlaySingleSong(intent = intent, data = data)
            }
            else -> {}
        }

    }

    private fun triggerPlaySingleSong(intent: PladioIntent.GetPladioDetail, data: PladioDetailEntity) {
        pladioDataPreprocessor.processPladioDetail(id = intent.song.id, data = data, extraData = intent.extraData)
        dispatchIntent(PladioIntent.GetPladioRecommendation(intent.song, songType = data.data.pladioType.toSongType(), intent.extraData))
    }

    private fun processGetRecommendDone(intent: PladioIntent.GetPladioRecommendation, data: PladioRelatedEntity) {
        pladioDataPreprocessor.processPladioRecommendation(id = intent.song.id, songType = intent.songType, data = data, force = intent.callFromRestoreSong)
    }

    // Event
    private fun preprocessCheckTypeEvent(song: Song, extraData: PladioExtraData, onNotSupport: (message: String, textTitle: String, textConfirm: String) -> Unit = {_,_,_ -> }) {
        dispatchIntent(PladioIntent.GetPladioEventDetail(song, extraData, onNotSupport = onNotSupport, isHomeCalled = true))
    }

    private fun processGetDetailEventDone(intent: PladioIntent.GetPladioEventDetail, data: PladioEventDetailEntity) {
        // Check Type
        val eventType = data.data.getEventContentType(isPremiere = data.data.isPremier == "1")
        when (eventType) {
            Song.PladioEventType.Event -> {
                val listSong = listOf(
                    data.data.mapToSong(id = intent.song.id, songType = Song.PladioType.Event, contentType = Song.PladioContentType.Single)
                )
                triggerPlayEvent(data = listSong, extraData = intent.extraData)
            }
            Song.PladioEventType.EventTV,
            Song.PladioEventType.Premiere -> {
                intent.onNotSupport.invoke(
                    MainApplication.INSTANCE.resources.getString(R.string.pladio_event_not_supported_description),
                    MainApplication.INSTANCE.resources.getString(R.string.pladio_event_not_supported_title),
                    MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_text_close)
                )
            }
            else -> {
                intent.onNotSupport.invoke(
                    MainApplication.INSTANCE.resources.getString(R.string.pladio_event_not_supported_description),
                    MainApplication.INSTANCE.resources.getString(R.string.pladio_event_not_supported_title),
                    MainApplication.INSTANCE.resources.getString(R.string.pladio_playlist_not_supported_text_close)
                )
            }
        }
    }

    fun triggerPlayEvent(data: List<Song>, extraData: PladioExtraData) {
        pladioDataPreprocessor.processPladioEventDetail(data = data, extraData = extraData)
    }


    private fun handleLogicRestorePlaylist(intent: PladioIntent, data: PladioPlaylistEntity) {
        // Update detail data
        if (intent is PladioViewModel.PladioIntent.GetPlaylistDetail) {
            if (intent.callFromRestoreSong) {
                if (data.status == "1") {
                    if (data.data.listChapter.isNotEmpty()) {
                        val tempRestoreSong = PlaybackPlayerRemote.tempRestoreSong
                        var positionInPlaylist = -1
                        data.data.listChapter.forEachIndexed { index, item ->
                            if (tempRestoreSong?.song?.id == item.id && tempRestoreSong.song.playlistId == data.data.id && tempRestoreSong.song.streamRequestDataExtra?.episodeId == item.episodes?.firstOrNull()?.id) {
                                positionInPlaylist = index
                            }
                        }
                        if (positionInPlaylist != -1) {
                            triggerPlaySongPlaylistFromRestore(
                                songs = data.data.listChapter.map {
                                    val newDetail = if (it.playlistId.isNotBlank()) { it } else { it.copy(playlistId = data.data.id) }
                                    newDetail.mapToSong(
                                        playlistTitle = data.data.title,
                                        songType = data.data.pladioType.toSongType(),
                                        playState = Song.PlayState.NonPlay,
                                        contentType = Song.PladioContentType.Playlist,
                                    )
                                },
                                extraData = PladioExtraData(
                                    startPosition = positionInPlaylist,
                                    isShuffleMode = PlaybackPlayerRemote.shuffleMode == SHUFFLE_MODE_SHUFFLE
                                ),
                                startPlayPositionMs = tempRestoreSong?.startPlayPositionMs ?: 0L,
                                isAutoPlay = tempRestoreSong?.isAutoPlay ?: false
                            )
                        }
                    }

                } else {
                    // Do nothing, restore fail
                }
            }
        }
    }


    private fun handleLogicRestoreSeries(intent: PladioIntent, data: PladioDetailEntity) {
        if (intent is PladioViewModel.PladioIntent.GetPladioDetail) {
            if (intent.callFromRestoreSong) {
                val pladioPlaylist = PladioUtil.convertPladioDetailToPladioPlaylist(data.data)
                pladioPlaylist?.let { playlist ->
                    if (playlist.listChapter.isNotEmpty()) {
                        val tempRestoreSong = PlaybackPlayerRemote.tempRestoreSong
                        var positionInPlaylist = -1
                        playlist.listChapter.forEachIndexed { index, item ->
                            if (tempRestoreSong?.song?.id == item.id && tempRestoreSong.song.playlistId == data.data.id && tempRestoreSong.song.streamRequestDataExtra?.episodeId == item.episodes?.firstOrNull()?.id) {
                                positionInPlaylist = index
                            }
                        }
                        if (positionInPlaylist != -1) {
                            triggerPlaySongPlaylistFromRestore(
                                songs = playlist.listChapter.map {
                                    val newDetail = if (it.playlistId.isNotBlank()) { it } else { it.copy(playlistId = data.data.id) }
                                    newDetail.mapToSong(
                                        playlistTitle = playlist.title,
                                        songType = data.data.pladioType.toSongType(),
                                        playState = Song.PlayState.NonPlay,
                                        contentType = Song.PladioContentType.Series,
                                    )
                                },
                                extraData = PladioExtraData(
                                    startPosition = positionInPlaylist,
                                    isShuffleMode = PlaybackPlayerRemote.shuffleMode == SHUFFLE_MODE_SHUFFLE
                                ),
                                startPlayPositionMs = tempRestoreSong?.startPlayPositionMs ?: 0L,
                                isAutoPlay = tempRestoreSong?.isAutoPlay ?: false
                            )
                        }
                    }
                }
            }
        }
    }


    private fun handleLogicRestoreEvent(intent: PladioIntent.GetPladioEventDetail, data: PladioEventDetailEntity) {
        // TODO: Restore state event, ask product should be??? => Final, don't restore
        if (intent.callFromRestoreSong) {

        }
    }
    // endregion


    override fun <T> Result<T>.reduce(
        intent: PladioIntent?,
        successFun: (Boolean, T) -> PladioState
    ): PladioState {
        return when (this) {
            is Result.Init -> PladioState.Loading(intent = intent)
            is Result.Success -> { successFun(this.isCached, this.successData) }
            is Result.UserError.RequiredLogin -> PladioState.ErrorRequiredLogin(
                message = this.message,
                intent = intent,
                requiredLogin = this.requiredLogin
            )
            is Result.UserError.RequiredVip -> PladioState.ErrorRequiredVip(
                message = this.message,
                intent = intent,
                requiredVip = this.requiredVip
            )
            is Result.Error.Intenet -> PladioState.ErrorByInternet(
                message = this.message,
                intent = intent
            )
            is Result.ServerError.ItemNotFound -> PladioState.ErrorItemNotFound(
                title = this.detail.title,
                message = this.detail.message,
                intent = intent
            )
            is Result.Error -> PladioState.Error(message = this.message, intent = intent)
            Result.Done -> PladioState.Done(intent = intent)
        }
    }

    sealed class PladioIntent : ViewIntent {
        object GetSomething : PladioIntent()
        // Playlist
        data class GetPlaylistDetail(val playlistId: String, val callFromRestoreSong: Boolean = false) : PladioIntent()
        // Home
        data class GetStructure(val pageProvider: PageProvider, val userLogin: Boolean, val revision: String) : PladioIntent()
        data class GetStructureLocal(val pageProvider: PageProvider, val dataLocal: List<Structure>) : PladioIntent()
        data class GetClusterItem(
            val pageProvider: PageProvider,
            val data: List<Pair<Int, Structure>>,
            val userId: String,
            val page: Int,
            val perPage: Int) : PladioIntent()
        data class GetClusterItemLocal(
            val pageProvider: PageProvider,
            val data: List<Pair<Int, Structure>>,
        ) : PladioIntent()
        data class GetStructureItem(
            val structureId: String,
            val type: String,
            val blockType: String,
            val userId: String,
            val page: Int,
            val perPage: Int,
            val watchingVersion: String? = null,
            val customData: String,
        ) : PladioIntent()
        data class GetHistoryVod(val userId: String, val page: Int, val perPage: Int) : PladioIntent()
        data class GetCategoryStructure(val structure : Structure) : PladioIntent()
        // Search
        data class GetTrendItems(val action: String, val query: String,val status: StatusSearch) : PladioIntent()
        data class GetSearchSuggest(val query: String, val page: Int, val perPage: Int,val status: StatusSearch) : PladioIntent()
        data class GetSearchResult(val action: String, val query: String, val page: Int, val perPage: Int,val status: StatusSearch) : PladioIntent()
        // Playback speed
        object GetPlaybackAlarmTimeConfig: PladioIntent()
        // Bottom Navigation
        object GetBottomNavigation: PladioIntent()
        // Detail
        data class GetPladioDetail(val song: Song, val extraData: PladioExtraData, val navigateToSeries: (vodId: String, fromDeeplink: Boolean?, isAutoOpenPlayer: Boolean?, startPlayPositionMs: Long?, isAutoPlay: Boolean?, targetIdPlay: String?, episodeId: String?) -> Unit = { _, _, _, _, _, _, _ -> }, val isHomeCalled: Boolean = false, val callFromRestoreSong: Boolean = false) : PladioIntent()
        // Detail Event
        data class GetPladioEventDetail(val song: Song, val extraData: PladioExtraData, val onNotSupport: (message: String, textTitle: String, textConfirm: String) -> Unit = {_,_,_ -> }, val isHomeCalled: Boolean = false, val callFromRestoreSong: Boolean = false) : PladioIntent()
        // Recommend
        data class GetPladioRecommendation(val song: Song, val songType: Song.PladioType?, val extraData: PladioExtraData, val callFromRestoreSong: Boolean = false) : PladioIntent()
        // Stream
        data class GetPladioStream(val id: String, val episodeId: String, val streamType: String) : PladioIntent()
    }
    sealed class PladioState : ViewState {
        object Init : PladioState()
        data class Loading(val intent: PladioIntent? = null) : PladioState()
        data class StructureResult(val isCached: Boolean, val data: List<Structure>) : PladioState()
        data class ResultStructureItem(val isCached: Boolean, val data: Pair<Int, Structure>) : PladioState()
        data class ResultClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) : PladioState()
        data class ResultComingSoonStructureItem(val isCached: Boolean, val data: List<StructureItem>) : PladioState()
        data class ResultFollowStructureItem(val isCached: Boolean, val data: List<StructureItem>) : PladioState()
        data class ResultHistoryVodStructureItem(val isCached: Boolean, val data: List<StructureItem>) : PladioState()
        data class ResultCategoryStructure(val isCached: Boolean, val data: Structure) : PladioState()
        data class ResultStructureMetaData(val isCached: Boolean, val shouldProcess: Boolean, val data: StructureMeta) : PladioState()
        data class Error(val message: String, val intent: PladioIntent? = null) : PladioState()
        data class ErrorRequiredLogin(val message: String, val intent: PladioIntent? = null, val requiredLogin: RequiredLogin?) : PladioState()
        data class ErrorRequiredVip(val message: String, val intent: PladioIntent? = null, val requiredVip: RequiredVip?) : PladioState()
        data class ErrorByInternet(val message: String, val intent: PladioIntent? = null) : PladioState()
        data class ErrorItemNotFound(val title: String, val message: String, val intent: PladioIntent? = null) : PladioState()
        data class Done(val intent: PladioIntent? = null) : PladioState()
        // Search
        data class ResultGetSearchResult(val data: List<Search>?=null, val isBind: Boolean,val status: StatusSearch) : PladioState()
        data class ResultGetSearchSuggest(val suggestItem: List<SearchSuggest>?,val trendingItem: List<Search>?,val status: StatusSearch) : PladioState()
        data class ResultGetTrendItems(val data: List<String>?=null,val status: StatusSearch) : PladioState()
        // Playlist
        data class ResultPlaylistDetail(val isCache : Boolean, val data: PladioPlaylistEntity, val intent: PladioIntent? = null) : PladioState()
        // Playback Speed
        data class ResultPlaybackAlarmTimeConfig(val isCached: Boolean, val data: List<AlarmTimeConfig>) : PladioState()

        // Bottom Navigation
        data class ResultBottomNavigation(val isCached: Boolean, val data: List<TabMenu>, val isBind: Boolean): PladioState()

        // Detail
        data class ResultPladioDetail(val isCached: Boolean, val data: PladioDetailEntity, val intent: PladioIntent? = null) : PladioState()

        // Detail Event
        data class ResultPladioEventDetail(val isCached: Boolean, val data: PladioEventDetailEntity, val intent: PladioIntent? = null) : PladioState()

        // Recommendation
        data class ResultPladioRecommendation(val isCached: Boolean, val data: PladioRelatedEntity, val intent: PladioIntent? = null) : PladioState()

        // Stream
        data class ResultPladioStream(val isCached: Boolean, val data: PladioStreamEntity, val intent: PladioIntent? = null) : PladioState()
    }

    data class PladioExtraData(
        val startPosition: Int,
        val isShuffleMode: Boolean,

        // Deeplink
        val fromDeeplink: Boolean? = null,
        val isAutoOpenPlayer: Boolean? = null,
        val startPlayPositionMs: Long? = null,
        val isAutoPlay: Boolean? = null,
        val targetIdPlay: String? = null,
        val episodeId: String? = null
    )

}