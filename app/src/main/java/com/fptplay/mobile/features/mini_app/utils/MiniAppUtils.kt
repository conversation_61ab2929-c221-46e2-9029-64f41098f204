package com.fptplay.mobile.features.mini_app.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import com.fptplay.mobile.features.mini_app.model.MiniAppResponse
import com.squareup.moshi.FromJson
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.JsonReader
import com.squareup.moshi.JsonWriter
import com.squareup.moshi.Moshi
import com.squareup.moshi.ToJson
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import okio.Buffer
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import timber.log.Timber

object MiniAppUtils {
    object JSONObjectAdapter {
        @FromJson
        fun fromJson(reader: JsonReader): JSONObject? {
            // Here we're expecting the JSON object, it is processed as Map<String, Any> by <PERSON>shi
            return (reader.readJsonValue() as? Map<String, Any>)?.let { data ->
                try {
                    JSONObject(data)
                } catch (e: JSONException) {
                    // Handle error if arises
                    null
                } catch(e: Exception) {
                    null
                }
            }
        }

        @ToJson
        fun toJson(writer: <PERSON>sonWriter, value: JSONObject?) {
            value?.let { writer.value(Buffer().writeUtf8(value.toString())) }
        }
    }

    class JSONArrayAdapter {

        @ToJson
        fun toJson(array: JSONArray): List<Any> {
            // Convert JSONArray to List<Any> for Moshi to serialize
            try {
                val list = mutableListOf<Any>()
                for (i in 0 until array.length()) {
                    list.add(array.get(i))
                }
                return list
            } catch (ex: JSONException) {
                return emptyList()
            } catch (e: Exception) {
                return emptyList()
            }
        }

        @FromJson
        fun fromJson(list: List<Any>): JSONArray {
            // Convert List<Any> back to JSONArray
            val jsonArray = JSONArray()
            for (item in list) {
                jsonArray.put(item)
            }
            return jsonArray
        }
    }

    inline fun <reified T> T.toJsonString(): String {
        return try {
            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .add(JSONObjectAdapter)
                .add(JSONArrayAdapter())
                .build()
            val jsonAdapter: JsonAdapter<T> = moshi.adapter(T::class.java)
            jsonAdapter.toJson(this)
        } catch(e: Exception) {
            Timber.e("toJsonString error $e")
            ""
        }
    }
    fun getAppSettingsIntent(context: Context): Intent {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.data = Uri.parse("package:" + context.packageName)
        return intent
    }


    fun callback(miniAppResponseString: String): String {
        // callback method receive params jsonString, not json object,
        // so response string have to be put in ''
        return "${MiniAppConstants.callBackMethodName}('${miniAppResponseString}')"
    }
    fun callback(miniAppResponse: MiniAppResponse): String {
        // callback method receive params jsonString, not json object,
        // so response string have to be put in ''

        return "${MiniAppConstants.callBackMethodName}('${miniAppResponse.toJsonString()}')"

    }

}