package com.fptplay.mobile.features.choihaychia.model

import com.google.gson.annotations.SerializedName
import com.xhbadxx.projects.module.domain.entity.fplay.gameplayorshare.GamePlayOrShareCustomerInfo

data class ChoiHayChiaPushQuestion(
    @SerializedName("game_id")
    val gameId: String? = null,

    @SerializedName("round_id")
    val roundId: String? = null,

    @SerializedName("round_name")
    val roundName: String? = null,

    @SerializedName("step")
    val step: String? = null,

    @SerializedName("max_dice")
    val maxDice: String? = null,

    @SerializedName("time")
    val time: String? = null,

    @SerializedName("question_name")
    val questionName: String? = null,

    @SerializedName("time_hide")
    val timeHide: String? = null,

    @SerializedName("customerinfo")
    val customerInformation: ChoiHayChiaUserInformationResponse? = null,

    @SerializedName("list_score")
    val listScore: List<Score>? = null,

    @SerializedName("list_answer")
    val listAnswer: List<Answer>? = null,

    @SerializedName("list_answerv2")
    val listAnswerV2: List<Answer>? = null,

    @SerializedName("ranking")
    val ranking: List<Ranking>? = null,

    @SerializedName("ccu")
    val ccu: String? = null
) {
    fun getCustomerInfo() : GamePlayOrShareCustomerInfo {
        return GamePlayOrShareCustomerInfo(
            mac = customerInformation?.mac ?:"",
            contract = customerInformation?.contract ?:"",
            userName = customerInformation?.userName ?:"",
            score = customerInformation?.score ?:"",
            totalPlayer = customerInformation?.totalPlayer ?:"",
            rank = customerInformation?.rank ?:"",
            top_score = customerInformation?.topScore ?:"",
            result = customerInformation?.result ?:"",
            round_id = customerInformation?.roundId ?:"",
            step = customerInformation?.step ?:"",
            status = customerInformation?.status ?:"",
            round_name = customerInformation?.roundName ?:"",
            title = customerInformation?.title ?:"",
            msg = customerInformation?.msg ?:"",
            msg1 = customerInformation?.msg1 ?:"",
            msg2 = customerInformation?.msg2 ?:"",
            msg3 = customerInformation?.msg3 ?:"",
        )
    }
}