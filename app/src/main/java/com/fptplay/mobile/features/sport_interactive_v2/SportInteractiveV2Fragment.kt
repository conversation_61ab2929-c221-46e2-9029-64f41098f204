package com.fptplay.mobile.features.sport_interactive_v2

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.hilt.navigation.fragment.hiltNavGraphViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.SportInteractiveV2FragmentBinding
import com.fptplay.mobile.features.sport_interactive.SportInteractiveViewModel
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveMenuAdapter
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveOuterAdapter
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.fptplay.mobile.features.sport_interactive_v2.models.live_score.SportInteractiveLiveScoreMatch
import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.util.common.IEventListener
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class SportInteractiveV2Fragment :
    BaseFragment<SportInteractiveViewModel.SportInteractiveState, SportInteractiveViewModel.SportInteractiveIntent>() {
    override val viewModel: SportInteractiveViewModel by hiltNavGraphViewModels(R.id.nav_sport_interactive)

    private var _binding: SportInteractiveV2FragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: SportInteractiveV2FragmentArgs by navArgs()

    private val fireStoreService by lazy { SportInteractiveFirebaseProxy() }
    private val menuAdapter: SportInteractiveMenuAdapter by lazy { SportInteractiveMenuAdapter() }
    private val mainAdapter: SportInteractiveOuterAdapter by lazy {
        SportInteractiveOuterAdapter(ScreenContainerType.NORMAL_SCREEN).apply { setInnerEvenListener(innerAdapterItemClickEvent) }
    }
    private val innerAdapterItemClickEvent by lazy {
        object : IEventListener<UIData> {
            override fun onClickView(position: Int, view: View?, data: UIData) {
                Timber.tag("tam-sport").i("onClickedItem: $data")
                Timber.tag("tam-sport")
                    .i("onClickedItem: ${parentFragment} - ${parentFragment?.parentFragment}")
                if (data is SportInteractiveLiveScoreMatch) {
                    if(data.eventId.isNotBlank()) {
                        when (data.eventType) {
                            SportMatchLiveScores.LiveScore.Match.MatchEventType.Event -> {
                                logChangeLiveShow(data, menuAdapter.getCurrentSelectedItem())
                                navigateToContent(id = data.eventId, type = ItemType.Event)
                            }

                            SportMatchLiveScores.LiveScore.Match.MatchEventType.EventTv -> {
                                logChangeLiveShow(data, menuAdapter.getCurrentSelectedItem())
                                navigateToContent(id = data.eventId, type = ItemType.EventTV)

                            }

                            is SportMatchLiveScores.LiveScore.Match.MatchEventType.Unknown -> {
                                Timber.tag("tam-sport").i("onClickedItem: Unknown type. Do Nothing")
                            }
                        }
                    }
                }
            }
        }
    }

    override fun setUpEdgeToEdge() {

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = SportInteractiveV2FragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        _binding?.rvMain?.scrollToPosition(menuAdapter.getCurrentSelectedPosition())
    }

    override fun bindData() {
        bindFireStore(safeArgs.idToPlay)
    }

    override fun bindComponent() {
        menuAdapter.eventListener = object : IEventListener<SportInteractiveMenuData> {
            override fun onClickView(position: Int, view: View?, data: SportInteractiveMenuData) {
                if ((binding.rvMain.layoutManager as? LinearLayoutManager)?.findFirstVisibleItemPosition() != position) {
                    binding.rvMain.smoothScrollToPosition(position)
                }
            }

            override fun onSelectedItem(position: Int, data: SportInteractiveMenuData) {
                logAccessModule(data)
            }
        }

        binding.rvMain.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                val currentPosition =
                    (binding.rvMain.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    menuAdapter.updateSelectedPosition(currentPosition)
                    binding.rvMenu.smoothScrollToPosition(currentPosition)
                }
            }
        })

        binding.ivClose.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.rvMenu.apply {
            adapter = menuAdapter
        }

        binding.rvMain.apply {
            adapter = mainAdapter
            val snapHelper = PagerSnapHelper()
            onFlingListener = null
            snapHelper.attachToRecyclerView(this)
        }
    }

    private var isFireStoreFirstInit = true
    private fun bindFireStore(eventId: String) {
        fireStoreService.listenSportEventData(
            lifecycleOwner = viewLifecycleOwner,
            eventId = eventId
        ) {
            Timber.tag("tam-sport").e("SportInteractiveData $it")
            menuAdapter.bind(it?.titlesData) {
                if (isFireStoreFirstInit) {
                    logAccessModule(it?.titlesData?.firstOrNull())
                    isFireStoreFirstInit = false
                }
            }
            mainAdapter.bind(it?.toList()) {
                val curPosition = getCurrentContentDisplayPosition()
                Timber.d("*****are change tab: curtab:$curPosition - toPosition: ${menuAdapter.getCurrentSelectedPosition()}")
                if (curPosition != RecyclerView.NO_POSITION) {
                    binding.rvMain.scrollToPosition(menuAdapter.getCurrentSelectedPosition())
                }
            }
        }
    }

    private fun getCurrentContentDisplayPosition(): Int {
        return if (_binding != null) (binding.rvMain.layoutManager as? LinearLayoutManager)?.findFirstVisibleItemPosition()
            ?: RecyclerView.NO_POSITION else RecyclerView.NO_POSITION
    }

    private fun logChangeLiveShow(
        data: SportInteractiveLiveScoreMatch? = null,
        currentMenu: SportInteractiveMenuData?
    ) {
        data?.let { matchData ->
            val bundle: Bundle = bundleOf().apply {
                putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, "ChangeLiveshow")
                putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "ChangeLiveshow")
                putString(Constants.SPORT_INTERACTIVE_LOG_ITEM_NAME, matchData.eventId)
            }
            TrackingUtil.screen = TrackingUtil.screenRelated
            parentFragment?.parentFragment?.setFragmentResult(
                Constants.SPORT_INTERACTIVE_LOG_KIBANA,
                bundle
            )
        }
    }

    private fun logAccessModule(currentMenu: SportInteractiveMenuData?) {
        val bundle: Bundle = bundleOf().apply {
            putString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, currentMenu?.logName ?: "")
            putString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "AccessFunction")
        }
        parentFragment?.parentFragment?.setFragmentResult(
            Constants.SPORT_INTERACTIVE_LOG_KIBANA,
            bundle
        )
    }

    private fun navigateToContent(id: String, type: ItemType) {
        val bundle = bundleOf(
            Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_CONTENT_ID to id
        )
        bundle.putString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_ITEM_TYPE, type.name)
        parentFragment?.parentFragment?.setFragmentResult(
            Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_KEY,
            bundle
        )
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun SportInteractiveViewModel.SportInteractiveState.toUI() {

    }

}