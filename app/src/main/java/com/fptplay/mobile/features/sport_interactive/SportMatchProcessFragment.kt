package com.fptplay.mobile.features.sport_interactive

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.text.HtmlCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.SportGoalItemBinding
import com.fptplay.mobile.databinding.SportInteractiveFragmentBinding
import com.fptplay.mobile.databinding.SportInteractiveHeaderCommonV1Binding
import com.fptplay.mobile.databinding.SportInteractiveHeaderCommonV2Binding
import com.fptplay.mobile.databinding.SportInteractiveMatchProcessFragmentBinding
import com.fptplay.mobile.features.sport_interactive.adapter.MatchProcessRoundAdapter
import com.fptplay.mobile.features.sport_interactive.model.SportMatchDetail
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive.model.SportMatchStatistic
import com.fptplay.mobile.features.sport_interactive.model.SportMatchProcess
import com.fptplay.mobile.features.sport_interactive.model.SportTeamSquad
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils
import com.fptplay.mobile.player.utils.invisible
import com.fptplay.mobile.player.utils.visible
import com.squareup.moshi.JsonAdapter
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.util.Utils.hide
import com.tear.modules.util.Utils.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import timber.log.Timber
import javax.inject.Inject

class SportMatchProcessFragment :
    BaseFragment<SportInteractiveViewModel.SportInteractiveState, SportInteractiveViewModel.SportInteractiveIntent>() {

    override val viewModel: SportInteractiveViewModel by activityViewModels()

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var _binding: SportInteractiveMatchProcessFragmentBinding? = null
    private val binding get() = _binding!!
    private var _headerBinding: SportInteractiveHeaderCommonV1Binding? = null
    private val headerBinding get() = _headerBinding!!

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    private val processRoundAdapter by lazy { MatchProcessRoundAdapter() }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SportInteractiveMatchProcessFragmentBinding.inflate(inflater, container, false)
        _headerBinding = SportInteractiveHeaderCommonV1Binding.bind(binding.root)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        //check force update
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    override fun setUpEdgeToEdge() {
        // override to prevent setup edgeToEdge
    }

    override fun bindComponent() {
        binding.rvRounds.apply {
            adapter = processRoundAdapter
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(false)

        }
    }

    override fun bindData() {

    }


    override fun bindEvent() {
        viewModel.sportMatchDetail.observe(viewLifecycleOwner) {
            updateLayoutHeaderCommon(it)
        }

        viewModel.sportMatchProcess.observe(viewLifecycleOwner) {
            updateLayoutMatchProcess(it)
        }

    }

    private fun updateLayoutHeaderCommon(detail :SportMatchDetail?) {
        if(detail!=null) {
            if (detail.info.awayTeamName.isNotBlank()) {
                headerBinding.txtNameTeamAway.visible()
                headerBinding.txtNameTeamAway.text = detail.info.awayTeamName
            }
            if (detail.info.homeTeamName.isNotBlank()) {
                headerBinding.txtNameTeamHome.visible()
                headerBinding.txtNameTeamHome.text = detail.info.homeTeamName
            }
            headerBinding.txtMatchScore.text = detail.info.awayScore.ifBlank { "0" } + " - " + detail.info.homeScore.ifBlank { "0" }
            if (detail.info.time.isNotBlank()) {
                headerBinding.txtMatchTime.visible()
                headerBinding.txtMatchTime.text = detail.info.time
            }
            headerBinding.vSeparateLine.hide()
            ImageProxy.load(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = headerBinding.imgAway,
                url = detail.info.awayLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team
            )
            ImageProxy.load(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = headerBinding.imgHome,
                url = detail.info.homeLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team
            )
            if (detail.listScores.isNotEmpty()) {
                headerBinding.llHeaderGoalTeam.visible()
                headerBinding.flGoalAway.removeAllViews()
                headerBinding.flGoalHome.removeAllViews()
                detail.listScores.forEachIndexed { _, score ->
                    val viewGoalItemAway =
                        SportGoalItemBinding.inflate(LayoutInflater.from(context))
                    val data =
                        "${score.playerName} <span style=\"color:#999999\">${score.time}</span>"
                    if (score.teamType == TeamType.AwayTeam) {
                        viewGoalItemAway.tvDataAway.visible()
                        viewGoalItemAway.tvDataHome.invisible()
                        viewGoalItemAway.tvDataAway.text =
                            HtmlCompat.fromHtml(data, HtmlCompat.FROM_HTML_MODE_LEGACY)
                        headerBinding.flGoalAway.addView(viewGoalItemAway.root)
                    } else {
                        viewGoalItemAway.tvDataAway.invisible()
                        viewGoalItemAway.tvDataHome.visible()
                        viewGoalItemAway.tvDataHome.text =
                            HtmlCompat.fromHtml(data, HtmlCompat.FROM_HTML_MODE_LEGACY)
                        headerBinding.flGoalHome.addView(viewGoalItemAway.root)
                    }
                }
            }
        }
        else{
            headerBinding.txtNameTeamAway.hide()
            headerBinding.txtNameTeamHome.hide()
            headerBinding.txtMatchScore.text = "0 - 0"
            headerBinding.txtMatchTime.hide()
            headerBinding.vSeparateLine.hide()
            headerBinding.llHeaderGoalTeam.hide()
            headerBinding.imgAway.setImageResource(R.drawable.ic_default_logo_team)
            headerBinding.imgHome.setImageResource(R.drawable.ic_default_logo_team)
        }
    }

    private fun updateLayoutMatchProcess(matchProcess: SportMatchProcess?) {
        if(matchProcess != null && matchProcess.listRounds.isNotEmpty()) {
//            Timber.tag("tam-sport").i("MatchProcess: $matchProcess")
            processRoundAdapter.bind(matchProcess.listRounds)
            binding.rvRounds.show()
            binding.txtNoData.hide()

        } else {
            binding.rvRounds.hide()
            binding.txtNoData.show()

        }
    }

    override fun SportInteractiveViewModel.SportInteractiveState.toUI() {
        Timber.d("toUI $this")
        when (this) {
            is SportInteractiveViewModel.SportInteractiveState.Loading -> {
            }

            is SportInteractiveViewModel.SportInteractiveState.Error -> {

            }

            else -> {
                Timber.d("Else with $this")
            }
        }
    }

    // region Commons


    // endregion Commons
}