package com.fptplay.mobile.features.library

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.PageProvider
import com.fptplay.mobile.homebase.data.BlockHighlightRecommendation
import com.fptplay.mobile.homebase.helpers.HighlightRecommendHandler
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemContainer
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureMeta
import com.xhbadxx.projects.module.domain.entity.fplay.user.Profile
import com.xhbadxx.projects.module.domain.repository.fplay.HomeOs4Repository
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject
import kotlin.system.measureTimeMillis

@HiltViewModel
class LibraryViewModel @Inject constructor(
    private val sharedPreferences: SharedPreferences,
    private val userRepository: UserRepository,
    private val homeOs4Repository: HomeOs4Repository,
    private val savedState: SavedStateHandle
) : BaseViewModel<LibraryViewModel.LibraryIntent, LibraryViewModel.LibraryState>() {

    var newestProfileData: Profile? = null
    private val highlightRecommendHandler = HighlightRecommendHandler(sharedPreferences)
    private var clusterItemJob: Job? = null


    fun saveStructureItemType(type:String){
        savedState.set("structureType",type)
    }
    fun saveBlockId(id:String){
        savedState.set("blockId",id)
    }
    fun saveBlockType(type:String){
        savedState.set("blockType",type)
    }
    fun saveCustomData(customData:String){
        savedState.set("customData",customData)
    }

    override fun dispatchIntent(intent: LibraryIntent) {
        safeLaunch {
            when (intent) {

                is LibraryIntent.GetLibraryDetailPage -> {
                    homeOs4Repository.getStructureItemWithMeta(
                        profileId = intent.profileId,
                        blockId = intent.blockId,
                        type = intent.type,
                        blockType = intent.blockType,
                        pageIndex = intent.page,
                        pageSize = intent.perPage,
                        watchingVersion = intent.watchingVersion,
                        customData = intent.customData,
                        pageId = intent.pageId
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LibraryState.ResultViewingHistoryStructureItem(
                                isCached = isCached,
                                type = intent.type,
                                data = data,
                                page = intent.page
                            )
                        }
                    }
                }

                is LibraryIntent.DeleteAllViewingHistory -> {
                    userRepository.removeUserHistory(
                        profileId = intent.profileId,
                        type = intent.type
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LibraryState.ResultDeleteAllViewingHistory(
                                isCached = isCached,
                                intent = intent,
                                status = data.status,
                                message = data.message.content
                            )
                        }
                    }
                }

                is LibraryIntent.GetLibraryStructure -> {
                    homeOs4Repository.getStructure(pageId = intent.pageProvider.pageId.id).collect {
                        viewModelScope.launch(Dispatchers.Main) {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                LibraryState.ResultStructureMetaData(
                                    isCached = isCached,
                                    data = data.meta,
                                    shouldProcess = true
                                )
                            }
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                LibraryState.StructureResult(
                                    isCached = isCached,
                                    data = data.structure
                                )
                            }
                        }
                    }
                }


                is LibraryIntent.GetClusterItem -> {
                    clusterItemJob?.cancel()
                    clusterItemJob = launch {
                        getStructureItems(intent) {
                            Timber.d("***** Done cluster job with ${it.data[0].second}")
                            _state.value = LibraryState.ResultClusterStructureItem(
                                isCached = false,
                                data = it.data
                            )
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private suspend fun getStructureItems(
        intent: LibraryIntent.GetClusterItem,
        block: (intent: LibraryIntent.GetClusterItem) -> Unit
    ) {
        val measureTime = measureTimeMillis {
            withContext(Dispatchers.IO) {
                try {
                    val arrDeferred: ArrayList<Deferred<Unit>> = arrayListOf()
                    val pairs = intent.data // Pair[Index, Structure]
                    withTimeoutOrNull(10_000L) {
                        pairs.forEach {
                            val index = it.first
                            val structure = it.second

                            val pageSize = when(structure.style) {
                                BlockStyle.HighLight -> intent.perPage - 1
                                BlockStyle.NumericRank -> 10
                                else -> intent.perPage
                            }
                            val watchingVersion = if(structure.itype == Constants.WATCHING_TYPE) "v1" else null

                            arrDeferred.add(async {
                                getBlockDataWithRecommend(
                                    structure,
                                    intent,
                                    pageSize,
                                    watchingVersion
                                ).process(index, structure, pageSize)
                            })
                        }
                        // Parallel the all requests
                        arrDeferred.forEach { it.await() }
                    }
                    pairs.map { item ->
                        if (item.second.items.isNotEmpty()) {
                            if (item.second.items[0].itype == ItemType.Fake) {
                                item.second.items = emptyList()
                            }
                        }
                    }
                    withContext(Dispatchers.Main) {
                        block(intent)
                    }
                }
                catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
            } // Process timeout or return null
            withContext(Dispatchers.Main) {
                clusterItemJob = null
                _state.value = LibraryState.Done()
            }
        }
        Logger.d("Measure time of GetStructureItem: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }


    private fun getBlockDataWithRecommend(
        structure: Structure,
        intent: LibraryIntent.GetClusterItem,
        pageSize: Int = 10,
        watchingVersion: String? = null
    ): Flow<Result<List<StructureItem>>> {

        if(structure.needRecommend) {
            var cacheItems = highlightRecommendHandler.getHighlightRecommendBlock(structure.id)?.blockRecommendationData?.blockRecommendationValue
            if (!cacheItems.isNullOrEmpty()) {
                return homeOs4Repository.getStructureItem(
                    blockId = structure.id,
                    type = structure.itype ?: "",
                    blockType = structure.style.id,
                    pageIndex = intent.page,
                    pageSize = pageSize,
                    customData = structure.customData,
                    watchingVersion = watchingVersion,
                )
            } else {
                return homeOs4Repository.getStructureItem(
                    blockId = structure.id,
                    type = structure.itype ?: "",
                    blockType = structure.style.id,
                    pageIndex = intent.page,
                    pageSize = pageSize,
                    customData = structure.customData,
                    watchingVersion = watchingVersion,
                    pageId = intent.pageProvider.pageId.id
                ).zip(
                    homeOs4Repository.getRecommendHighlightBlockItems(
                        blockId = structure.id,
                        blockDataType = structure.itype ?: "",
                        blockStyle = structure.style.id,
                    )
                ) { structureItem, recommend ->
                    if (recommend is Result.Success) {
                        if (!recommend.data.isNullOrEmpty()) {
                            cacheItems = recommend.data
                            highlightRecommendHandler.updateHighlightRecommendBlock(
                                blockId = structure.id,
                                blockRecommendationData = BlockHighlightRecommendation.BlockHighlightRecommendationData(
                                    blockRecommendationTimestamp = System.currentTimeMillis(),
                                    blockRecommendationValue = recommend.data
                                )
                            )
                        }
                    }
                    structureItem
                }
            }
        } else {
            return homeOs4Repository.getStructureItem(
                blockId = structure.id,
                type = structure.itype ?: "",
                blockType = structure.style.id,
                pageIndex = intent.page,
                pageSize = pageSize,
                customData = structure.customData,
                watchingVersion = watchingVersion,
                pageId = intent.pageProvider.pageId.id,
            )
        }
    }

    override fun <T> Result<T>.reduce(
        intent: LibraryIntent?,
        successFun: (Boolean, T) -> LibraryState
    ): LibraryState {
        return when (this) {
            is Result.Init -> LibraryState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }

            is Result.UserError.RequiredLogin -> LibraryState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )

            is Result.Error.Intenet -> LibraryState.ErrorNoInternet(
                message = this.message,
                intent = intent
            )

            is Result.Error -> LibraryState.Error(message = this.message, intent = intent)
            Result.Done -> LibraryState.Done(intent = intent)
        }

    }

    private fun List<StructureItem>.filterEndedItem(): List<StructureItem> {
        return filter { Util.statusBetweenStartAndEndTime(it.beginTime, it.endTime) != 3 }
    }

    private suspend fun Flow<Result<List<StructureItem>>>.process(index: Int, data: Structure, dataItemSize: Int) {
        this.filter { it is Result.Success || it is Result.Error }
            .collect {
                val structureItem = it.data ?: emptyList()
                val cacheItems =
                    highlightRecommendHandler.getHighlightRecommendBlock(data.id)?.blockRecommendationData?.blockRecommendationValue
                val metaName = structureItem.firstOrNull()?.metaName ?: ""
                data.name = metaName.ifBlank { data.name }

                data.items = if (cacheItems?.isNotEmpty() == true) {
                    highlightRecommendHandler.mergeOriginalDataWithRecommend(
                        originalData = structureItem,
                        recommendData = cacheItems,
                        requiredDataSize = dataItemSize
                    ).filterEndedItem()
                } else {
                    structureItem.filterEndedItem()
                }

                if (data.itype.equals("category")) {
                    saveStructureItemType(data.itype)
                    saveBlockId(data.id)
                    saveBlockType(data.style.id)
                    saveCustomData(data.customData)
                }

                safeLaunch {
//                    Timber.d("*** Item result: $index - $data")
                    _state.value = LibraryState.ResultStructureItem(
                        isCached = false,
                        data = Pair(index, data)
                    )
                }
            }
    }

    private suspend fun <T> Flow<Result<T>>.process(
        successFun: (Boolean, T) -> Unit,
        errorFun: (Result.Error) -> Unit
    ) {

        this.filter { it is Result.Success || it is Result.Error }
//            .filter { it is Result.Success }
            .collect {
                if (it is Result.Success) {
                    it.let { res ->
                        successFun(res.isCached, res.successData)
                    }
                } else if (it is Result.Error) {
                    errorFun(it)
                }
            }
    }


    private fun Result.Error.processError(intent: LibraryIntent): LibraryState {
        return if (this is Result.UserError.RequiredLogin) {
            LibraryState.ErrorRequiredLogin(message = this.message, intent = intent)
        } else {
            LibraryState.Error(message = this.message, intent = intent)
        }
    }

    sealed class LibraryState : ViewState {
        object Idle : LibraryState()
        data class Loading(val intent: LibraryIntent? = null) : LibraryState()

        data class Error(val message: String, val intent: LibraryIntent? = null) :
            LibraryState()

        data class ErrorRequiredLogin(val message: String, val intent: LibraryIntent? = null) :
            LibraryState()

        data class Done(val intent: LibraryIntent? = null) : LibraryState()
        data class ErrorNoInternet(val intent: LibraryIntent? = null, val message: String) :
            LibraryState()

        data class ResultViewingHistoryStructureItem(
            val isCached: Boolean,
            val type: String,
            val data: StructureItemContainer,
            val page: Int
        ) : LibraryState()

        data class ResultDeleteAllViewingHistory(
            val isCached: Boolean,
            val intent: LibraryIntent? = null,
            val status: String,
            val message: String
        ) : LibraryState()

        data class ResultStructureMetaData(
            val isCached: Boolean,
            val shouldProcess: Boolean,
            val data: StructureMeta
        ) : LibraryState()

        data class StructureResult(val isCached: Boolean, val data: List<Structure>) :
            LibraryState()

        data class ResultStructureItem(val isCached: Boolean, val data: Pair<Int, Structure>) : LibraryState()

        data class ResultClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) :
            LibraryState()

    }

    sealed class LibraryIntent : ViewIntent {
        // viewing history
        data class GetLibraryStructure(
            val profileId: String,
            val pageProvider: PageProvider
        ) : LibraryIntent()

        data class GetLibraryDetailPage(
            val profileId: String,
            val type: String,
            val blockId: String,
            val blockType: String,
            val page: Int,
            val perPage: Int,
            val watchingVersion: String,
            val customData: String,
            val pageId: String
        ) : LibraryIntent()

        data class DeleteAllViewingHistory(val profileId: String, val type: String) :
            LibraryIntent()

        data class GetClusterItem(
            val pageProvider: PageProvider,
            val data: List<Pair<Int, Structure>>,
            val userId: String,
            val page: Int,
            val perPage: Int,
        ) : LibraryIntent()

        data class GetClusterItemLocal(
            val pageProvider: PageProvider,
            val data: List<Pair<Int, Structure>>,
        ) : LibraryIntent()

    }
}