package com.fptplay.mobile.features.mega.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.MegaNavigationMenuItemBinding
import com.jakewharton.rxbinding2.view.clicks
import com.tear.modules.util.Utils.checkToShowContent
import com.xhbadxx.projects.module.domain.entity.fplay.common.AutoScrollConfig
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class NavigationMenuAdapter(context: Context, val isolated: Boolean = false, val sharedPreferences: SharedPreferences) :
    BaseAdapter<MegaMenuItem, RecyclerView.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when(viewType) {
            1 -> NavigationMenuSwitchCompatViewHolder(
                MegaNavigationMenuItemBinding.inflate(
                    LayoutInflater.from(
                        parent.context
                    ), parent, false
                )
            )
            else -> NavigationMenuViewHolder(
                MegaNavigationMenuItemBinding.inflate(
                    LayoutInflater.from(
                        parent.context
                    ), parent, false
                )
            )
        }
    }

    override fun getItemViewType(position: Int): Int {
        val item = differ.currentList[position]

        return when(item.actionType) {
            MegaMenuItem.ActionType.AudioVideoAutoScrollConfigure -> 1
            else -> 0
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if(holder is NavigationMenuViewHolder)
            holder.bind(differ.currentList[position], !isolated && position < differ.currentList.size - 1)
        else if(holder is NavigationMenuSwitchCompatViewHolder)
            holder.bind(differ.currentList[position], !isolated && position < differ.currentList.size - 1)

    }


    inner class NavigationMenuViewHolder(private val binding: MegaNavigationMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.ctlMenu.setOnClickListener {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }

            if(isolated) {
                binding.ctlMenu.background = ContextCompat.getDrawable(binding.root.context, R.drawable.mega_menu_background)

            }
        }

        fun bind(menu: MegaMenuItem, showDivider: Boolean = true) {
            Timber.tag("tam-mega").w("NavigationMenuViewHolder bind $menu")
            binding.apply {
                if(menu.img.isNotBlank()) {
                    ivIcon.load(menu.img)
                } else if(menu.imgResId != null) {
                    ivIcon.setImageResource(menu.imgResId!!)
                } else {
                    ivIcon.setImageResource(0)
                }
                tvTitle.text = menu.title
                switchCompatAction.hide()
                ivAction.show()
                ivAction.setImageResource(R.drawable.ic_arrow_right)
                if (showDivider) {
                    vDivider.show()
                } else {
                    vDivider.hide()
                }
            }
        }

    }
    inner class NavigationMenuSwitchCompatViewHolder(private val binding: MegaNavigationMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.ctlMenu.setOnClickListener {
                Timber.tag("tam-mega").d("NavigationMenuSwitchCompatViewHolder click root")
                binding.switchCompatAction.toggle()

                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
            binding.switchCompatAction.setOnCheckedChangeListener { compoundButton, isChecked ->
                Timber.tag("tam-mega").i("NavigationMenuSwitchCompatViewHolder click switchCompatAction")
                if(compoundButton.isPressed) {
                    Timber.tag("tam-mega").e("NavigationMenuSwitchCompatViewHolder click switchCompatAction manually")
                    item(absoluteAdapterPosition)?.let {
                        eventListener?.onClickedItem(
                            absoluteAdapterPosition,
                            it
                        )
                    }
                }
            }

            if(isolated) {
                binding.ctlMenu.background = ContextCompat.getDrawable(binding.root.context, R.drawable.mega_menu_background)

            }
        }

        fun bind(menu: MegaMenuItem, showDivider: Boolean = true) {
            Timber.tag("tam-mega").w("NavigationMenuSwitchCompatViewHolder bind $menu")
            binding.apply {
                if(menu.img.isNotBlank()) {
                    ivIcon.load(menu.img)
                } else if(menu.imgResId != null) {
                    ivIcon.setImageResource(menu.imgResId!!)
                } else {
                    ivIcon.setImageResource(0)
                }
                tvTitle.text = menu.title
                tvDescription.checkToShowContent(menu.description, goneViewWhenNoText = true)
                ivAction.hide()
                switchCompatAction.show()
                switchCompatAction.isChecked = Utils.getCurrentAutoScrollStatus(sharedPreferences) == AutoScrollConfig.On

                if (showDivider) {
                    vDivider.show()
                } else {
                    vDivider.hide()
                }
            }
        }

    }
}