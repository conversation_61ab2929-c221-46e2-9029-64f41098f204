package com.fptplay.mobile.features.mega.apps.airline.vod.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.RecycledViewPool
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.databinding.AirlineMovieGroupItemBinding
import com.fptplay.mobile.databinding.AllItemUnknownBinding
import com.fptplay.mobile.features.mega.apps.airline.util.AirlineUtils.getLocalName
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.home.StructureItem
import com.xhbadxx.projects.module.util.common.IEventListener

class AirlineMovieGroupAdapter : BaseAdapter<Structure, RecyclerView.ViewHolder>() {

    var groupEventListener: IEventListener<BaseObject>? = null
    private val verticalSharedPool by lazy { RecycledViewPool() }
    private val horizontalSharedPool by lazy { RecycledViewPool() }
    private val limitPerStructure by lazy { MainApplication.INSTANCE.appConfig.numItemOfPage }

    override fun getItemViewType(position: Int): Int {
        val structure = differ.currentList[position]
        return when (structure.style) {
            Structure.Style.VodVertical -> 1
            Structure.Style.VodHorizontal -> 2
            else -> 3
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            1 -> AirlineMovieGroupVerticalViewHolder(
                AirlineMovieGroupItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
            2 -> AirlineMovieGroupHorizontalViewHolder(
                AirlineMovieGroupItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
            else -> UnknownViewHolder(
                AllItemUnknownBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val data = differ.currentList[position]
        when (holder) {
            is AirlineMovieGroupVerticalViewHolder -> holder.bind(data)
            is AirlineMovieGroupHorizontalViewHolder -> holder.bind(data)
            is UnknownViewHolder -> holder.bind(data)
        }
    }

    inner class AirlineMovieGroupVerticalViewHolder(private val binding: AirlineMovieGroupItemBinding) : RecyclerView.ViewHolder(binding.root) {

        private val verticalAdapter by lazy { AirlineVerticalMovieAdapter() }

        init {
            binding.tvViewMore.text = MainApplication.INSTANCE.appConfig.textViewMore.ifBlank { binding.root.context.getString(R.string.view_more) }
            binding.rvMovieGroup.apply {
                setHasFixedSize(true)
                setRecycledViewPool(horizontalSharedPool)
                layoutManager = LinearLayoutManager(itemView.context, RecyclerView.HORIZONTAL, false).apply {
                    initialPrefetchItemCount = 4
                }
                adapter = verticalAdapter
            }

            verticalAdapter.eventListener = object : IEventListener<StructureItem> {
                override fun onClickedItem(position: Int, data: StructureItem) {
                    groupEventListener?.onClickedItem(position, data)
                }
            }
        }

        fun bind(data: Structure) {
            binding.tvGroupTitle.text = data.getLocalName(itemView.context)
            binding.tvViewMore.isVisible = data.items.size >(limitPerStructure) // view more items
            verticalAdapter.bind(data.items.removeStructureItemIfOverSizeLimit()) // if item is too large than limit and needs to removed item
            binding.tvViewMore.setOnClickListener {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    groupEventListener?.onClickView(absoluteAdapterPosition, null, data)
                }
            }
        }
    }
    private fun List<StructureItem>.removeStructureItemIfOverSizeLimit():List<StructureItem>?{
        if (this.isNotEmpty()) {
            return try {
                if(this.size > (limitPerStructure)){
                    this.slice(0 until limitPerStructure)
                }else{
                    this
                }
            }catch (e:Exception){
                this
            }
        }
        return null
    }
    inner class AirlineMovieGroupHorizontalViewHolder(private val binding: AirlineMovieGroupItemBinding) : RecyclerView.ViewHolder(binding.root) {

        private val horizontalAdapter by lazy { AirlineHorizontalMovieAdapter() }

        init {
            binding.tvViewMore.text = MainApplication.INSTANCE.appConfig.textViewMore.ifBlank { binding.root.context.getString(R.string.view_more) }
            binding.rvMovieGroup.apply {
                setHasFixedSize(true)
                setRecycledViewPool(verticalSharedPool)
                layoutManager = LinearLayoutManager(itemView.context, RecyclerView.HORIZONTAL, false).apply {
                    initialPrefetchItemCount = 4
                }
                adapter = horizontalAdapter
            }

            horizontalAdapter.eventListener = object : IEventListener<StructureItem> {
                override fun onClickedItem(position: Int, data: StructureItem) {
                    groupEventListener?.onClickedItem(position, data)
                }
            }
        }

        fun bind(data: Structure) {
            binding.tvGroupTitle.text = data.getLocalName(itemView.context)
            binding.tvViewMore.isVisible = data.items.size > (limitPerStructure) // view more items
            horizontalAdapter.bind(data.items.removeStructureItemIfOverSizeLimit()) // if item is too large than limit and needs to removed item

            binding.tvViewMore.setOnClickListener {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    groupEventListener?.onClickView(absoluteAdapterPosition, null, data)
                }
            }
        }
    }

    class UnknownViewHolder(binding: AllItemUnknownBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(data: Structure) {}
    }
}