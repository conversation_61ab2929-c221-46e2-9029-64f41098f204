package com.fptplay.mobile.features.ai_chatbot.utils
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.createBitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.fptplay.mobile.R
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.DelayHandler
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.ai_chatbot.model.AiChatTypeSendLog
import com.fptplay.mobile.features.ai_chatbot.model.MiniAppResponseMetaDataSendLog
import com.fptplay.mobile.features.ai_chatbot.model.MiniAppResponseMetaDataValueSendLog
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_IS_COMMENT
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_METADATA_KEY_ITEM_NAME
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_METADATA_KEY_STATUS
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_METADATA_KEY_TYPE
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_METADATA_KEY_VALUE
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_SCREEN
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants.AI_CHAT_BOX_SUB_MENU_ID
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import org.json.JSONObject
import timber.log.Timber
import java.util.concurrent.TimeUnit
import androidx.core.graphics.drawable.toDrawable

class AIChatBotUtils(
    var trackingProxy: TrackingProxy? =null,
    var trackingInfo: Infor?= null,
) {
    companion object {
        const val  SESSION_LOAD_TIME_OUT_MS  = 2_000L
    }

    fun loadDrawableAIChatbotIconAsFlow(
        context: Context,
        url: String?
    ): Flow<Drawable?> = callbackFlow {
        loadDrawableIcon(
            context = context,
            url = url,
            onDone = { drawable ->
                trySend(drawable).isSuccess
            },
            onFail = {
                val defaultDrawable = ContextCompat.getDrawable(context, R.drawable.ic_ai_chat_box)
                trySend(defaultDrawable?.getScaledDrawable(context)?:defaultDrawable).isSuccess
            }
        )
        awaitClose {

        }
    }.flowOn(Dispatchers.IO)

    private  fun loadDrawableIcon(context: Context,url:String?, onDone: (Drawable?) -> Unit,onFail: () -> Unit){
        if (!url.isNullOrBlank()){
            val size = context.resources?.getDimensionPixelSize(R.dimen.ic_ai_chat_box_size) ?: 0
            GlideApp.with(context)
                .load(ImageProxy.optimizeImageUrl(url, size, size))
                .error(R.drawable.ic_ai_chat_box)
                .centerCrop()
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>?, isFirstResource: Boolean): Boolean {
                        onFail.invoke()
                        return false
                    }
                    override fun onResourceReady(resource: Drawable?, model: Any?, target: Target<Drawable>?, dataSource: DataSource?, isFirstResource: Boolean): Boolean {
                        return false
                    }

                })
                .into(object : CustomTarget<Drawable>() {
                    override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                        onDone.invoke(resource.getScaledDrawable(context))
                    }
                    override fun onLoadCleared(placeholder: Drawable?) {

                    }
                })
        }
        else {
            onFail.invoke()
        }
    }



    fun loadDrawableAIChatBoxUrl(context: Context, ivBg: ImageView, iconUrl: String?, size: Int) {
        if (!AIChatBotProvider.currentAiChatBotConfig.iconUrl.isNullOrEmpty()) {
            ImageProxy.load(
                context = context,
                width = size,
                height = size,
                url = iconUrl,
                target = ivBg,
                placeHolderId = R.drawable.ic_ai_chat_box,
                errorDrawableId = R.drawable.ic_ai_chat_box
            )
        } else {
            ImageProxy.loadLocal(
                context = context,
                data = R.drawable.ic_ai_chat_box,
                width = size,
                height = size,
                target = ivBg,
            )
        }
    }

    fun errorMessage(context: Context, message: String?): String {
        return if(message.isNullOrBlank()) {
            context.getString(R.string.multi_profile_login_profile_error)
        } else{
            message
        }
    }

    fun sendLogEnterChatBot(chatSession:String="",status:Boolean) {
        trackingInfo?.let {
            trackingProxy?.sendEvent(
                InforMobile(
                    infor = it,
                    logId = "530",
                    appId = AIChatBotConstants.AI_CHAT_BOX,
                    appName = AIChatBotConstants.AI_CHAT_BOX,
                    event = AIChatBotConstants.AI_CHAT_BOX_LOG_EVENT_ENTER_CHAT_BOT,
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    status = if (status) "Success" else "Fail",
                    chatSession = chatSession
                )
            )
        }
    }

    private fun sendLogRequestChatBot(
        chatSession:String="",
        value:MiniAppResponseMetaDataValueSendLog?
    ) {
        trackingInfo?.let {
            trackingProxy?.sendEvent(
                InforMobile(
                    infor = it,
                    logId = "531",
                    appId = AIChatBotConstants.AI_CHAT_BOX,
                    appName = AIChatBotConstants.AI_CHAT_BOX,
                    event = AIChatBotConstants.AI_CHAT_BOX_LOG_EVENT_REQUEST_CHAT_BOT,
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    itemName = value?.itemName?:"",
                    status = value?.status?:"",
                    chatSession = chatSession
                )
            )
        }
    }

    private fun sendLogFeedbackChatBot(
        chatSession:String="",
        value:MiniAppResponseMetaDataValueSendLog?
    ) {
        trackingInfo?.let {
            trackingProxy?.sendEvent(
                InforMobile(
                    infor = it,
                    logId = "532",
                    appId = AIChatBotConstants.AI_CHAT_BOX,
                    appName = AIChatBotConstants.AI_CHAT_BOX,
                    event = AIChatBotConstants.AI_CHAT_BOX_LOG_EVENT_FEEDBACK_CHAT_BOT,
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    itemName = value?.itemName?:"",
                    status = value?.status?:"",
                    chatSession = chatSession
                )
            )
        }
    }

    sealed class AiChatResult {
        object Success : AiChatResult()
        object NotSupported : AiChatResult()
        object NoInternet : AiChatResult()
        object KidProfileNotSupported : AiChatResult()
        object RequiredLogin : AiChatResult()
    }

    fun evaluateChatBotAIAvailability(
        sharedPreferences: SharedPreferences,
        onResult: (AiChatResult) -> Unit
    ) {
        when {
            !sharedPreferences.userLogin() -> onResult(AiChatResult.RequiredLogin)
            MultiProfileUtils.isProfileKid(sharedPreferences.profileType()) -> onResult(AiChatResult.KidProfileNotSupported)
            !AIChatBotProvider.isEnabledAIChatBox -> onResult(AiChatResult.NotSupported)
            !NetworkUtils.isNetworkAvailable() -> onResult(AiChatResult.NoInternet)
            else -> onResult(AiChatResult.Success)
        }
    }

    private fun String.toAiChatTypeSendLog(): AiChatTypeSendLog = try {
        when (this) {
            AIChatBotConstants.AI_CHAT_BOX_METADATA_TYPE_ENTER_CHAT -> AiChatTypeSendLog.EnterChatBotType
            AIChatBotConstants.AI_CHAT_BOX_METADATA_TYPE_REQUEST -> AiChatTypeSendLog.RequestType
            AIChatBotConstants.AI_CHAT_BOX_METADATA_TYPE_FEEDBACK -> AiChatTypeSendLog.FeedbackType
            else -> AiChatTypeSendLog.Other
        }
    } catch (e: Exception) {
        AiChatTypeSendLog.Other
    }

    private fun JSONObject?.toMiniAppResponseMetaDataSendLog(): MiniAppResponseMetaDataSendLog? {
        try {
            this?.let { metadata ->
                val value = metadata.optJSONObject(AI_CHAT_BOX_METADATA_KEY_VALUE)
                if (value != null) {
                    return MiniAppResponseMetaDataSendLog(
                        typeLog = metadata.optString(AI_CHAT_BOX_METADATA_KEY_TYPE, "").toAiChatTypeSendLog(),
                        valueLog = MiniAppResponseMetaDataValueSendLog(
                            status = value.optString(AI_CHAT_BOX_METADATA_KEY_STATUS, ""),
                            itemName = value.optString(AI_CHAT_BOX_METADATA_KEY_ITEM_NAME, "")
                        )
                    )
                }
            }
            return null
        } catch (e: Exception) {
            return null
        }
    }

    fun sendLogAIChatBot(
        requestId: String,
        type: String,
        message: String,
        timestamp: Long,
        metadata: JSONObject?,
        chatSession: String,
    ) {
        metadata.toMiniAppResponseMetaDataSendLog()?.let { metadataSendLog ->
            Timber.d("AIChatBotFragment --  toMiniAppResponseMetaDataSendLog :$metadata  --  metadataSendLog : $metadataSendLog")
            when (metadataSendLog.typeLog) {
                AiChatTypeSendLog.EnterChatBotType -> sendLogEnterChatBot(chatSession = chatSession, status = true)
                AiChatTypeSendLog.FeedbackType -> sendLogFeedbackChatBot(chatSession = chatSession, value = metadataSendLog.valueLog)
                AiChatTypeSendLog.RequestType -> sendLogRequestChatBot(chatSession = chatSession, value = metadataSendLog.valueLog)
                else -> Unit
            }
        }
    }

    fun startDelaySendLogEnterChatSession(lifecycleOwner: LifecycleOwner, chatSession: String) {
        DelayHandler(TimeUnit.SECONDS.toMillis(SESSION_LOAD_TIME_OUT_MS)).apply {
            runnable {
                sendLogEnterChatBot(chatSession = chatSession, status = true)
            }
            lifecycleOwner.lifecycle.addObserver(this)
            start()
        }
    }

    fun openDeepLinkFromChatBot(url:String){
        TrackingUtil.setDataTrackingChatBot(
            screenValue = AI_CHAT_BOX_SCREEN,
            subMenuId = AI_CHAT_BOX_SUB_MENU_ID,
            isRecommend = AI_CHAT_BOX_IS_COMMENT
        )
        DeeplinkUtils.parseDeepLinkAndExecute(
            deeplink = url,
            useWebViewInApp = false,
            trackingInfo = trackingInfo,
            isDeeplinkCalledInApp = true
        )
    }
}
fun Drawable.getScaledDrawable(context: Context): Drawable {
    if (intrinsicWidth > 0 && intrinsicHeight > 0) {
        Timber.d("intrinsicWidth $intrinsicWidth && intrinsicHeight $intrinsicHeight")
        val size = context.resources?.getDimensionPixelSize(R.dimen.ic_ai_chat_box_size) ?: 0
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        setBounds(0, 0, size, size)
        draw(canvas)
        return bitmap.toDrawable(context.resources)
    }
    return this
}