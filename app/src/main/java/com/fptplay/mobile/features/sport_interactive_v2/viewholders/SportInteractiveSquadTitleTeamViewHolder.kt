package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.databinding.SportInteractiveSquadTitleTeamBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTitleTeam
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.util.Utils.checkToShowContent

class SportInteractiveSquadTitleTeamViewHolder(private val binding: SportInteractiveSquadTitleTeamBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveSquadTitleTeam>(binding) {
    override fun bind(data: SportInteractiveSquadTitleTeam) {
        if (data.fullTitle.isEmpty() && data.coachName.isEmpty()) {
            binding.tvTeamFullName.gone()
            binding.tvCoachName.gone()
            binding.root.gone()
        } else {
            binding.root.visible()
            binding.tvTeamFullName.checkToShowContent(data.fullTitle, goneViewWhenNoText = true)
            binding.tvCoachName.checkToShowContent(data.coachName, goneViewWhenNoText = true)
        }
    }
}