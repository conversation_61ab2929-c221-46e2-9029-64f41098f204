package com.fptplay.mobile.features.mini_app.model

import java.io.Serializable
//Base class
open class MiniAppRequest(
    open val requestId: String? = ""
):Serializable

// contact
data class MiniAppContactRequest(
    override val requestId: String? = "",
) : MiniAppRequest()

//Pick Media
data class MiniAppPickMediaRequest(
    override val requestId: String? = "",
) : MiniAppRequest()

// download
data class MiniAppDownloadRequest(
    override val requestId: String? = "",
    val url: String,
    val fileName: String? = "",
    val mineType: MiniAppDownloadRequestType? = MiniAppDownloadRequestType.NONE,
    val isShared: Boolean = false
): MiniAppRequest()

enum class MiniAppDownloadRequestType {
    NONE, IMAGE, VIDEO,
}