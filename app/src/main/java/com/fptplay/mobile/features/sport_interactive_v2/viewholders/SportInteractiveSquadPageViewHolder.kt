package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import android.view.MotionEvent
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.SportInteractiveSquadPageBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.adpters.SportInteractiveInnerAdapter
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadBlock
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadPage
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTabHeader
import com.tear.modules.util.Utils.checkToShowContent
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import kotlin.math.abs

class SportInteractiveSquadPageViewHolder(private val binding: SportInteractiveSquadPageBinding, private val screenContainerType: ScreenContainerType) :
    BaseSportInteractiveViewHolder<SportInteractiveSquadPage>(binding) {

    private val squadBlockAdapter by lazy { SportInteractiveInnerAdapter(screenContainerType) }

    private var storedTabs : SportInteractiveSquadTabHeader ?= null
    private var storedBlocks : SportInteractiveSquadBlock ?= null


    private val rvScrollListener = object : RecyclerView.OnItemTouchListener {
        var downX = 0f
        var downY = 0f
        var isHorizontalScroll = false
        override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
            return try {
                when (e.action) {
                    MotionEvent.ACTION_DOWN -> {
                        downX = e.x
                        downY = e.y
                        isHorizontalScroll = false
                        rv.parent?.requestDisallowInterceptTouchEvent(true)
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val dx = abs(e.x - downX)
                        val dy = abs(e.y - downY)
                        if (dx > dy) {
                            isHorizontalScroll = true
                            rv.parent?.requestDisallowInterceptTouchEvent(false)
                        } else {
                            rv.parent?.requestDisallowInterceptTouchEvent(true)
                        }
                    }
                }
                false
            } catch (e : Exception) {
                e.printStackTrace()
                false
            }

        }
        override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {}
        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}
    }


    init {
        binding.llHomeTab.onClickDelay {
            storedTabs?.positionSelected = 0
            updateTabSelected()
            //
            storedBlocks?.let { bindBlockDataToAdapter(blocks = it) }
        }

        binding.llAwayTab.onClickDelay {
            storedTabs?.positionSelected = 1
            updateTabSelected()
            //
            storedBlocks?.let { bindBlockDataToAdapter(blocks = it) }
        }


        binding.rvSquadBlock.apply {
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
            adapter = squadBlockAdapter
            addOnItemTouchListener(rvScrollListener)
        }
    }



    override fun bind(data: SportInteractiveSquadPage) {
        // Tabs Data
        if (data.data.size == 2) { // Include: SportInteractiveHeaderData + SportInteractiveSquadBlock

            val tabs = data.data.firstOrNull()
            val blocks = data.data[1]

            if (tabs is SportInteractiveSquadTabHeader) {
                storedTabs = tabs
                bindTabsUI(data = tabs)
            }

            if (blocks is SportInteractiveSquadBlock) {
                storedBlocks = blocks
                bindBlockUI(blocks = blocks)
            }
        }

    }

    fun bindPayloads(newData: SportInteractiveSquadPage?) {
        newData?.let { newData ->
            if (storedTabs == null || storedBlocks == null) {
                bind(data = newData)
            } else {
                // Old Data
                val oldTabs = storedTabs
                val oldBlocks = storedBlocks
                // New Data
                val newTabs = newData.data.firstOrNull()
                val newBlocks = newData.data[1]

                if (newTabs is SportInteractiveSquadTabHeader
                    && newBlocks is SportInteractiveSquadBlock) {

                    newTabs.positionSelected = oldTabs?.positionSelected ?: 0

                    updateTabSelected()
                    //
                    storedTabs = newTabs
                    bindTabsUI(data = newTabs)
                    //
                    storedBlocks = newBlocks
                    bindBlockUI(blocks = newBlocks)
                }
            }
        }
    }

    private fun bindTabsUI(data: SportInteractiveSquadTabHeader) {
        binding.apply {
            updateTabSelected()
            val tabs = data.data
            //
            tvHomeShortName.checkToShowContent(tabs.info.homeShortName, goneViewWhenNoText = true)
            tvAwayShortName.checkToShowContent(tabs.info.awayShortName, goneViewWhenNoText = true)

            if (tabs.info.homeLogo.isNotBlank()) {
                ImageProxy.load(
                    context = binding.root.context,
                    width = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._13sdp else R.dimen._23sdp
                    ),
                    height = 0,
                    target = binding.imgHome,
                    url = tabs.info.homeLogo,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team,
                )
            } else {
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    width = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._13sdp else R.dimen._23sdp
                    ),
                    height = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._13sdp else R.dimen._23sdp
                    ),
                    target = binding.imgHome,
                    data = R.drawable.ic_default_logo_team,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team,
                )
            }


            if (tabs.info.awayLogo.isNotBlank()) {
                ImageProxy.load(
                    context = binding.root.context,
                    width = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._13sdp else R.dimen._23sdp
                    ),
                    height = 0,
                    target = imgAway,
                    url = tabs.info.awayLogo,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team,
                )

            } else {
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    width = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._13sdp else R.dimen._23sdp
                    ),
                    height = Utils.getSizeInPixel(
                        context = binding.root.context,
                        resId = if (binding.root.context.isTablet()) R.dimen._13sdp else R.dimen._23sdp
                    ),
                    target = binding.imgAway,
                    data = R.drawable.ic_default_logo_team,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team,
                )
            }
        }
    }

    private fun bindBlockUI(blocks: SportInteractiveSquadBlock) {
        bindBlockDataToAdapter(blocks)
    }

    private fun bindBlockDataToAdapter(blocks: SportInteractiveSquadBlock) {
        storedTabs?.let {
            if (it.positionSelected in 0 until blocks.blocks.size){
                squadBlockAdapter.bind(data = blocks.blocks[it.positionSelected])
            }
        }
    }

    private fun updateTabSelected() {
        storedTabs?.let {
            binding.llHomeTab.isSelected = it.positionSelected == 0
            binding.llAwayTab.isSelected = it.positionSelected == 1
        }
    }
}