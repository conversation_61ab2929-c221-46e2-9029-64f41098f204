package com.fptplay.mobile.features.sport_interactive_v2.models

import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class SportInteractiveGeneralData (
    val title: String = "",
    val data: List<UIData> = emptyList()
): BaseObject() {
    enum class UIType {

        //COMMON
        SPORT_INTERACTIVE_HEADER_WITH_SCORED_AUTHORS,
        SPORT_INTERACTIVE_HEADER_WITH_COACH,
        SPORT_INTERACTIVE_SEPARATOR,

        //STATIC TAB
        SPORT_INTERACTIVE_STATIC_INFO,

        //MATCH PROCESS
        SPORT_INTERACTIVE_PROCESS_TITLE,
        SPORT_INTERACTIVE_PROCESS_ITEM,

        //SQUAD
        SPORT_INTERACTIVE_SQUAD_ITEM,
        SPORT_INTERACTIVE_SQUAD_RESERVE_TITLE,

        //LIVESCORE
        SPORT_INTERACTIVE_LIVE_SCORE_LEAGUE,
        SPORT_INTERACTIVE_LIVE_SCORE_MATCH,
    }
}
abstract class UIData: BaseObject() {
    open fun areItemTheSame(newItem: UIData): Boolean = this.id == newItem.id
    open fun areContentTheSame(newItem: UIData): Boolean = this == newItem
}