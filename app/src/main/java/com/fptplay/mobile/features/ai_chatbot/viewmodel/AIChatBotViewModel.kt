package com.fptplay.mobile.features.ai_chatbot.viewmodel

import androidx.lifecycle.SavedStateHandle
import com.fpl.plugin.mini_app_sdk.android_view.ResponseMetadata
import com.fpl.plugin.mini_app_sdk.entity.MiniAppManifest
import com.fpl.plugin.mini_app_sdk.model.MiniAppResponseError
import com.fpl.plugin.mini_app_sdk.model.MiniAppUserInfo
import com.fpl.plugin.mini_app_sdk.android_view.Token
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.IntegratedMiniAppManifest
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMiniAppManifest
import com.xhbadxx.projects.module.domain.entity.fplay.partner.PartnerToken
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class AIChatBotViewModel @Inject constructor(
    private val commonRepository: CommonRepository,
    private val userRepository: UserRepository,
    private val sharedPreferences: SharedPreferences,
    private val savedState: SavedStateHandle,
) : BaseViewModel<AIChatBotViewModel.AIChatBotIntent, AIChatBotViewModel.AIChatBotState>() {

    fun getUserId(): String {
        return sharedPreferences.userId()
    }

    override fun dispatchIntent(intent: AIChatBotIntent) {
        safeLaunch {
            when (intent) {
                is AIChatBotIntent.GetManifest -> {
                    commonRepository.getIntegratedMiniAppManifest(
                        miniAppId = AIChatBotConstants.AI_CHAT_BOX_ID_APP
                    ).collect {
                        _state.value = it.reduce { _, aiChatBotManifest ->
                            AIChatBotState.ResultManifest(
                                data = aiChatBotManifest.toMiniAppManifest()
                            )
                        }
                    }
                }
            }
        }
    }

    fun getPartnerToken(
        requestId: String,
        responseCallback: (Token, ResponseMetadata) -> Unit
    ) {
        safeLaunch {
            commonRepository.getPartnerToken(AIChatBotConstants.AI_CHAT_BOX_ID_APP).collect {
                when (it) {
                    is Result.UserError.RequiredLogin -> {
                        responseCallback(
                            "",
                            ResponseMetadata(
                                isSuccess = false,
                                jsonError = MiniAppResponseError(
                                    code = AIChatBotConstants.AI_CHAT_BOX_REQUIRED_LOGIN_ERROR_CODE,
                                    message = it.message,
                                )
                            )
                        )
                    }
                    is Result.Error -> {
                        responseCallback(
                            "",
                            ResponseMetadata(
                                isSuccess = false,
                                jsonError = MiniAppResponseError(
                                    code = AIChatBotConstants.DEFAULT_ERROR_CODE,
                                    message = it.message,
                                )
                            )
                        )
                    }

                    is Result.Success -> {
                        responseCallback(
                            it.data?.token ?: "",
                            ResponseMetadata(
                                isSuccess = true,
                                jsonError = null
                            )
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    fun getUserInfo(
        requestId: String,
        responseCallback: (MiniAppUserInfo?, ResponseMetadata) -> Unit
    ) {
        safeLaunch {
            userRepository.getUserInfo().collect {
                when (it) {
                    is Result.UserError.RequiredLogin -> {
                        responseCallback(
                            null,
                            ResponseMetadata(
                                isSuccess = false,
                                jsonError = MiniAppResponseError(
                                    code = AIChatBotConstants.AI_CHAT_BOX_REQUIRED_LOGIN_ERROR_CODE,
                                    message = it.message,
                                )
                            )
                        )
                    }
                    is Result.Error -> {
                        responseCallback(
                            null,
                            ResponseMetadata(
                                isSuccess = false,
                                jsonError = MiniAppResponseError(
                                    code = AIChatBotConstants.DEFAULT_ERROR_CODE,
                                    message = it.message,
                                )
                            )
                        )
                    }


                    is Result.Success -> {
                        responseCallback(
                            it.data?.toMiniAppUserInfo(),
                            ResponseMetadata(
                                isSuccess = true,
                                jsonError = null
                            )
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    private fun UserInfo.toMiniAppUserInfo(): MiniAppUserInfo {
        return this.let {
            MiniAppUserInfo(
                id = it.id,
                profileId = it.profile.id,
                email = it.email,
                phone = it.phone,
                avatar = it.avatar,
                responseStatus = MiniAppUserInfo.ResponseStatus(
                    isSuccess = true,
                    message = ""
                )
            )
        }
    }

    private fun IntegratedMiniAppManifest?.toMiniAppManifest(): MiniAppManifest {
        return this?.let {
            MiniAppManifest(
                id = data.id,
                version = data.version,
                compatibleVersions = data.compatibleVersions.toMiniAppCompatibleVersions(),
                title = data.title,
                description = data.description,
                iconUrl = data.iconUrl,
                scope = data.scope.toMiniAppScopes(),
            )
        } ?: MiniAppManifest()
    }

    private fun MegaMiniAppManifest.CompatibleVersions?.toMiniAppCompatibleVersions(): MiniAppManifest.CompatibleVersions {
        return this?.let {
            MiniAppManifest.CompatibleVersions(
                min = it.min,
                max = it.max,
            )
        } ?: MiniAppManifest.CompatibleVersions()
    }

    private fun MegaMiniAppManifest.MiniAppScopesResponse?.toMiniAppScopes(): MiniAppManifest.MiniAppScopesResponse {
        return this?.let {
            MiniAppManifest.MiniAppScopesResponse(
                megaApp = MiniAppManifest.MiniAppScopesResponse.MegaAppScope(
                    methods = it.megaApp.methods,
                    events = it.megaApp.events,
                    sourceUrl = it.megaApp.sourceUrl
                )
            )
        } ?: MiniAppManifest.MiniAppScopesResponse()
    }

    private fun String.addQueryParams(params: Map<String, String>): String {
        val query = params.entries.joinToString("&") { "${it.key}=${it.value}" }
        return if (this.contains("?")) {
            "$this&$query"
        } else {
            "$this?$query"
        }
    }


    override fun <T> Result<T>.reduce(
        intent: AIChatBotIntent?,
        successFun: (Boolean, T) -> AIChatBotState
    ): AIChatBotState {
        return when (this) {
            is Result.Init -> AIChatBotState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.Error.Intenet -> AIChatBotState.ErrorNotInternet(
                this.message,
                intent = intent
            )
            is Result.UserError.RequiredLogin -> AIChatBotState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )
            is Result.Error -> AIChatBotState.Error(message = this.message, intent = intent)
            is Result.Done -> AIChatBotState.Done(intent = intent)
        }
    }

    sealed class AIChatBotState : ViewState {
        data class Loading(val intent: AIChatBotIntent? = null) : AIChatBotState()

        data class Error(val message: String, val intent: AIChatBotIntent? = null) : AIChatBotState()
        data class ErrorRequiredLogin(val message: String, val intent: AIChatBotIntent? = null) : AIChatBotState()
        data class ErrorNotInternet(val message: String, val intent: AIChatBotIntent? = null) : AIChatBotState()

        data class Done(val intent: AIChatBotIntent? = null) : AIChatBotState()

        data class ResultManifest(
            val data: MiniAppManifest,
        ) : AIChatBotState()

        data class ResultGetPartnerToken(
            val data: PartnerToken,
        ) : AIChatBotState()
    }

    sealed class AIChatBotIntent : ViewIntent {
        object GetManifest : AIChatBotIntent()
    }
}