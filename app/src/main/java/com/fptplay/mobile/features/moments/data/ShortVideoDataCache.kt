package com.fptplay.mobile.features.moments.data

import com.fptplay.mobile.features.moments.MomentsViewModel
import com.fptplay.mobile.features.short_video.DataCacheObject
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentDetail
import com.xhbadxx.projects.module.domain.entity.fplay.moment.ShortVideoTabMenuEntity
import com.xhbadxx.projects.module.util.logger.Logger


data class MomentData(
    var listMoment: List<MomentDetail> = arrayListOf(),
    var tabId: String = "",
    var curItemId: String = "",
    var curIndex: Int = 0,
    var curTimePlaying: Long = 0L
)

data class ShortVideoDataCache(
    private var listTabData: List<ShortVideoTabMenuEntity> = arrayListOf(),
    private var curIndexTabFocus: Int = 0,
    private var listMomentData: ArrayList<MomentData> = arrayListOf(),
    private var isNeedToReload: Boolean = false
) {
    fun saveListTabData(data: List<ShortVideoTabMenuEntity>) {
        listTabData = data
    }

    fun getListTabData(): List<ShortVideoTabMenuEntity> = listTabData
    fun getTabWithId(id: String): ShortVideoTabMenuEntity? = listTabData.find { it.id == id }
    fun getTabWithIndex(index: Int): ShortVideoTabMenuEntity? = listTabData.getOrNull(index)
    fun getCurFocusTabIndex(): Int = curIndexTabFocus
    fun getCurTabId(): String? = listTabData.getOrNull(curIndexTabFocus)?.id
    fun saveFocusTabWithIndex(index: Int) {
        curIndexTabFocus = index
        Logger.d("trangtest ==== Set CurFocusTabIndex() = ${DataCacheObject.dataCache.getCurFocusTabIndex()}")
    }

    fun saveListMoment(data: List<MomentDetail>, tabId: String, curIndex: Int, curItemId: String, curTimePlaying: Long) {
        val item = listMomentData.find { it.tabId == tabId }
        val momentData: MomentData = MomentData(
            tabId = tabId,
            listMoment = data,
            curIndex = curIndex,
            curItemId = curItemId,
            curTimePlaying = curTimePlaying
        )
        listMomentData.remove(item)
        listMomentData.add(momentData)
    }

    fun getMomentWithTab(tabId: String): MomentData? = listMomentData.find { it.tabId == tabId }
    fun setNeedToReload() {
        isNeedToReload = true
    }

    fun needReload(): Boolean = isNeedToReload

    fun checkNeedSeek(momentId:String):Boolean{
        listMomentData.forEach {
            if(it.curItemId == momentId && it.curTimePlaying > 0) return true
        }
        return false
    }
    fun saveCurTimePlayingWithTabId(tabId: String, currentTimePlaying:Long){
        val item = listMomentData.find { it.tabId == tabId }
        item?.curTimePlaying = currentTimePlaying
    }
}