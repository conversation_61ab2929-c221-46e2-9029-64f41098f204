package com.fptplay.mobile.features.ai_chatbot.utils


import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.features.ai_chatbot.model.AIChatBotConfig
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils

object AIChatBotProvider {
    private var aiChatBotConfig: AIChatBotConfig? = null
    private val sharePre get() = MainApplication.INSTANCE.sharedPreferences

    private fun initAiChatBotConfig(): AIChatBotConfig {
        return AIChatBotConfig(
            iconUrl = sharePre.aiChatBotIconUrl(),
            enable = sharePre.isAIChatBotEnableForUser() && sharePre.isAIChatBotSearchEnable(),
            url= "",
            title = sharePre.aiChatBotTitle()
        )
    }

    val isKid get() = MultiProfileUtils.isProfileKid(sharePre.profileType())

    val currentAiChatBotConfig: AIChatBotConfig
        get() {
            if (aiChatBotConfig == null) {
                aiChatBotConfig = initAiChatBotConfig()
            }
            return aiChatBotConfig ?: AIChatBotConfig.emptyAIChatBotConfig
        }

    val isEnabledAIChatBox get() = currentAiChatBotConfig.enable && sharePre.userLogin()

    fun clearDataRemote() {
        aiChatBotConfig = null
    }
}

