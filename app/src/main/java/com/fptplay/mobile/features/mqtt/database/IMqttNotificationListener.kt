package com.fptplay.mobile.features.mqtt.database

import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail
import com.fptplay.mobile.features.mqtt.model.MqttRoom

interface IMqttNotificationListener {
    fun onNotificationsReceived(mqttNotificationDetail: MqttNotificationDetail?)
    fun onNotificationError(error: String)

    /**
     * Called when emergency room data is updated
     * @param room The emergency room that was updated
     * @param notification The notification data for this room
     */
    fun onEmergencyRoomUpdate(room: MqttRoom, notification: MqttNotificationDetail) {
        // Default implementation - can be overridden
    }
}