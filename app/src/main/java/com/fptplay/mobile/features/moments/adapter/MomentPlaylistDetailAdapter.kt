package com.fptplay.mobile.features.moments.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.MomentPlaylistItemBinding
import com.tear.modules.util.Utils.checkToShowContent
import com.xhbadxx.projects.module.domain.entity.fplay.moment.PlaylistMomentDetail
import com.xhbadxx.projects.module.util.image.ImageProxy

class MomentPlaylistDetailAdapter(): BaseAdapter<PlaylistMomentDetail.PlaylistEpisode, MomentPlaylistDetailAdapter.MomentPlaylistViewHolder>() {

    var curPlaylistEpisodeId: String? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MomentPlaylistViewHolder {
        return MomentPlaylistViewHolder(MomentPlaylistItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: MomentPlaylistViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    fun findIndexByEpisode(episodeId: String?): Int {
        if(episodeId == null) return -1
        for (i in 0 until differ.currentList.size) {
            if (differ.currentList[i].id == episodeId) { return i }
        }
        return -1
    }

    fun addWithLocation(data: List<PlaylistMomentDetail.PlaylistEpisode>, isBind: Boolean = false, addToFirst: Boolean = false, callback: Runnable? = null) {
        if (isBind) {
            bind(data, callback)
        } else {
            val mutableData = differ.currentList.toMutableList()
            if (addToFirst) {
                mutableData.addAll(0, data)
            } else {
                mutableData.addAll(data)
            }
            differ.submitList(mutableData, callback)
        }
    }

    inner class MomentPlaylistViewHolder(val binding: MomentPlaylistItemBinding): RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.onClickDelay {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onSelectedItem(absoluteAdapterPosition, it)
                }
            }
        }

        fun bind(data: PlaylistMomentDetail.PlaylistEpisode) {
            ImageProxy.load(
                context = binding.root.context,
                url = data.thumb,
                width = binding.root.context.resources.getDimensionPixelOffset(R.dimen.moment_playlist_item_thumbnail_width),
                height = binding.root.context.resources.getDimensionPixelOffset(R.dimen.moment_playlist_item_thumbnail_height),
                target = binding.ivThumbnail,
                placeHolderId = R.drawable.place_holder_moment,
                errorDrawableId = R.drawable.place_holder_moment
            )
            binding.tvTitle.text = "${data.index}. ${data.caption}"
            binding.tvDuration.checkToShowContent(data.time, goneViewWhenNoText = true)
            binding.vPlaying.isVisible = curPlaylistEpisodeId != null && curPlaylistEpisodeId == data.id
        }

    }

}