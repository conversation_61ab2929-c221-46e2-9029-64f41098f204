package com.fptplay.mobile.features.pladio.playback.panel

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.SeekBar
import androidx.fragment.app.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_DRAGGING
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.NavPladioOuterDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.PladioPlaybackPanelFragmentBinding
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.mapToPublisher
import com.fptplay.mobile.features.pladio.data.PladioActionSeekType
import com.fptplay.mobile.features.pladio.data.PladioPlaybackUniversalSettingsData
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.helper.PlaybackProgressViewUpdateHelper
import com.fptplay.mobile.features.pladio.helper.PlaybackStateHandler
import com.fptplay.mobile.features.pladio.playback.CustomLinearLayoutManager
import com.fptplay.mobile.features.pladio.playback.OpenPanel
import com.fptplay.mobile.features.pladio.playback.PladioPlaybackViewModel
import com.fptplay.mobile.features.pladio.playback.PlaybackPlayerRemote
import com.fptplay.mobile.features.pladio.playback.adapter.PladioPlaybackPanelAdapter
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.REPEAT_MODE_ALL
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.REPEAT_MODE_NONE
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.REPEAT_MODE_THIS
import com.fptplay.mobile.features.pladio.util.PladioConstants
import com.fptplay.mobile.features.pladio.util.PladioUtil
import com.fptplay.mobile.features.pladio.util.PladioUtil.createTopToBottomGradient
import com.fptplay.mobile.features.pladio.util.context
import com.tear.modules.player.exo.ExoPlayerView
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class PladioPlaybackPanelFragment : BaseFragment<PladioPlaybackViewModel.PladioPlaybackState, PladioPlaybackViewModel.PladioPlaybackIntent>(), PlaybackProgressViewUpdateHelper.Callback {

    override val viewModel: PladioPlaybackViewModel by viewModels()

    private var _binding: PladioPlaybackPanelFragmentBinding? = null
    private val binding get() = _binding!!

    // ------ Player Progress --------
    private lateinit var progressViewUpdateHelper: PlaybackProgressViewUpdateHelper
    private var progressAnimator: ObjectAnimator? = null
    private var isSeeking = false

    // ------ Recycler View --------
    private val mAdapter: PladioPlaybackPanelAdapter by lazy { PladioPlaybackPanelAdapter() }
    private var mExoPlayerView: ExoPlayerView? = null
    private var invalidateBindPlayerViewCount = 0

    // ------ Playback Button --------
    private var playbackPanelControlHelper: PladioPlaybackPanelControlHelper? = null

    private var playbackUiState = OpenPanel.PLAYBACK

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        progressViewUpdateHelper = PlaybackProgressViewUpdateHelper(this)
    }

    override fun setUpEdgeToEdge() {}

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PladioPlaybackPanelFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        progressViewUpdateHelper.start()
    }

    override fun onPause() {
        super.onPause()
        progressViewUpdateHelper.stop()
    }

    @SuppressLint("SetTextI18n")
    override fun bindComponent() {
        Logger.d("$TAG -> bindComponent")
        playbackPanelControlHelper = PladioPlaybackPanelControlHelper(binding.pladioPlaybackControl)
        // --- UI Setup ---
        binding.pladioPlaybackControlPanelLayout.tvSongName.isSelected = true
        binding.pladioPlaybackControlPanelLayout.btnSpeed.text = "1X"

        binding.rvSongs.apply {
            val snapHelper = PagerSnapHelper()
            val linearLayoutManager = CustomLinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
            layoutManager = linearLayoutManager
            adapter = mAdapter
            snapHelper.attachToRecyclerView(this)
            setItemViewCacheSize(1)
        }
        setUpProgressSlider()
        // Restore list when switch panel type
        playbackStateListener.onQueueChanged()
        playbackStateListener.onRepeatModeChanged()
        playbackStateListener.onShuffleModeChanged()
        playbackStateListener.onPlaybackSpeedChanged()
        playbackStateListener.onTimerChanged()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun bindEvent() {
        PlaybackStateHandler.addPlaybackServiceEventListener(playbackStateListener)
        playbackPanelControlHelper?.setOnPlaybackButtonListener(listener = onPlaybackButtonListener)
        //
        binding.ibBack.onClickDelay {
            LocalBroadcastManager.getInstance(MainApplication.INSTANCE).sendBroadcast(Intent(
                PladioConstants.PLADIO_MAIN_OPEN_PANEL).apply {
                putExtra(PladioConstants.PLADIO_MAIN_OPEN_PANEL_DATA, OpenPanel.MAIN.rawValue)
            })
        }

        binding.ibPlaybackMore.onClickDelay {
            val networkAvailable = NetworkUtils.isNetworkAvailable()
            if (networkAvailable) {
                parentFragment?.findNavController()?.navigateSafe(NavPladioOuterDirections.actionGlobalToPladioPlaybackMenuDialogFragment(
                    contentType = PlaybackPlayerRemote.currentSong.contentType?.rawValue ?: 0,
                    type = PlaybackPlayerRemote.currentSong.songType?.rawValue ?: 0
                ))
            }
        }

        mAdapter.eventListener = object : IEventListener<Song> {
            override fun onClickedItem(position: Int, data: Song) {}
        }

        binding.rvSongs.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            private var scrolledByUser = false

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    SCROLL_STATE_DRAGGING -> {
                        scrolledByUser = true
                    }
                    SCROLL_STATE_IDLE -> {
                        if (scrolledByUser) {
                            if(PlaybackPlayerRemote.currentSong.contentType == Song.PladioContentType.Single) {
                                TrackingUtil.setDataTracking(
                                    screenValue = TrackingUtil.screenRelated,
                                    idRelated = PlaybackPlayerRemote.currentSong.id,
                                    position = ""
                                )
                            } else {
                                TrackingUtil.setDataTracking(
                                    screenValue = TrackingUtil.screen,
                                    idRelated = TrackingUtil.idRelated,
                                    position = TrackingUtil.position
                                )
                            }
                            triggerPlayItem(recyclerView.currentRealPosition(), recyclerView.currentPosition())
                        } else {
                            // Do nothing
                        }
                        scrolledByUser = false
                    }
                }
            }
        })

        MqttConnectManager.INSTANCE.messageArrived()?.let {
            try {
                it.observe(viewLifecycleOwner) { messages ->
                    if (messages.isNotEmpty()) {
                        val latestMessage = messages[0]
                        val publisher = latestMessage.mapToPublisher()
                        publisher?.let { pub ->
                            if (pub.action == MqttUtil.ACTION_LIMIT_CCU) {
                                MqttConnectManager.INSTANCE.sendLogLimitCCU(pub, latestMessage.topic)
                            }
                        }
                    }
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        PlaybackStateHandler.removePlaybackServiceEventListener(playbackStateListener)
        _binding = null
    }

    override fun PladioPlaybackViewModel.PladioPlaybackState.toUI() {

    }

    // region Setup UI
    private fun setUpProgressSlider() {
        binding.pladioPlaybackControlPanelLayout.playbackSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                onProgressChange(progress, fromUser)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                onStartTrackingTouch()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                onStopTrackingTouch(seekBar?.progress ?: 0)
            }
        })
        binding.pladioPlaybackControlPanelLayout.tvCurrentDuration.text = PladioUtil.getReadableDurationString(0)
        binding.pladioPlaybackControlPanelLayout.tvTotalDuration.text = PladioUtil.getReadableDurationString(0)
    }
    // endregion

    // region Playback State Listener
    private val playbackStateListener = object : PlaybackStateHandler.IPlaybackServiceEventListener {
        override fun onServiceConnected() {
            Logger.d("$TAG -> onServiceConnected")

        }

        override fun onServiceDisconnected() {
        }

        override fun onQueueChanged() {
            playbackPanelControlHelper?.onQueueChanged()
            updateListPlayingSong()
        }

        override fun onPlaybackReady() {
            invalidatePlayerView()
        }

        override fun onPlayingMetaChanged() {
            playbackPanelControlHelper?.onPlayingMetaChanged()
            bindSongToUI()
        }

        override fun onPlayStateChanged() {
            playbackPanelControlHelper?.onPlayStateChanged()
        }

        override fun onRepeatModeChanged() {
            playbackPanelControlHelper?.onRepeatModeChanged()
            val repeatMode = PlaybackPlayerRemote.repeatMode
            when (repeatMode) {
                REPEAT_MODE_NONE,
                REPEAT_MODE_THIS -> {
                    updateAdapterLoop(enable = false)
                }
                REPEAT_MODE_ALL -> {
                    updateAdapterLoop(enable = true)
                }
            }
        }

        override fun onShuffleModeChanged() {
            playbackPanelControlHelper?.onShuffleModeChanged()
        }

        override fun onPlaybackSpeedChanged() {
            playbackPanelControlHelper?.onPlaybackSpeedChanged()
        }

        override fun onTimerChanged() {
            playbackPanelControlHelper?.onTimerChanged()
        }

        override fun onPlaybackSheetPanelChanged(isExpanded: Boolean, exoPlayerView: ExoPlayerView?) {
            Logger.d("$TAG -> onPlaybackSheetPanelChanged -> isExpanded: $isExpanded")
            playbackUiState = if (isExpanded) OpenPanel.PLAYBACK else OpenPanel.MAIN
            if (isExpanded) {
                mExoPlayerView = exoPlayerView
                exoPlayerView?.let {
                    it.updateShowProgressWhenBuffering(true)
                    moveToCurrentSongWhenRemoteChange(false)
                }
            } else {
                mExoPlayerView = null
            }
        }

    }
    // endregion

    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(message: Message) {
            when (message.what) {
                MSG_INVALIDATE_BIND_PLAYER_VIEW -> {
                    mExoPlayerView?.let {
                        val position = message.obj as? Int ?: -1
                        if (position >= 0) {
                            bindExoPlayerView(it, position)
                        }
                    }
                }
            }
        }
    }

    private fun bindExoPlayerView(exoPlayerView: ExoPlayerView, position: Int) {
        try {
            val viewHolder = binding.rvSongs.findViewHolderForAdapterPosition(position)
            if (viewHolder != null) {
                invalidateBindPlayerViewCount = 0
                if (viewHolder is PladioPlaybackPanelAdapter.ViewHolder) {
                    viewHolder.bindExoPlayerView(exoPlayerView)
                }
            } else {
                if (invalidateBindPlayerViewCount < INVALIDATE_BIND_PLAYER_VIEW_MAX) {
                    handleInvalidatePlayerView(position = position)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handleInvalidatePlayerView(position: Int) {
        invalidateBindPlayerViewCount++
        handler.removeMessages(MSG_INVALIDATE_BIND_PLAYER_VIEW)
        handler.sendMessageAtTime(handler.obtainMessage(MSG_INVALIDATE_BIND_PLAYER_VIEW, position), SystemClock.uptimeMillis() + INVALIDATE_BIND_PLAYER_VIEW_DELAY_MS)
    }

    // region Update UI
    @SuppressLint("NotifyDataSetChanged")
    private fun updateListPlayingSong() {
        if (_binding == null) return
        if (PlaybackPlayerRemote.playingQueue.isEmpty()) return
        val layoutManager = (_binding?.rvSongs?.layoutManager as? CustomLinearLayoutManager)
        layoutManager?.setListSize(PlaybackPlayerRemote.playingQueue.size)
        mAdapter.bind(PlaybackPlayerRemote.playingQueue) {
            mAdapter.notifyDataSetChanged()
            when (mAdapter.adapterType) {
                PladioPlaybackPanelAdapter.PladioAdapterType.NORMAL -> {
                    val position = PlaybackPlayerRemote.position
                    _binding?.rvSongs?.scrollToPosition(position)
                    layoutManager?.initPosition(position = position, realPosition = PlaybackPlayerRemote.position)
                    mExoPlayerView?.let { bindExoPlayerView(it, position) }
                }
                PladioPlaybackPanelAdapter.PladioAdapterType.LOOP -> {
                    val position = Int.MAX_VALUE / 2 - Int.MAX_VALUE / 2 % PlaybackPlayerRemote.playingQueue.size + PlaybackPlayerRemote.position
                    _binding?.rvSongs?.scrollToPosition(position)
                    layoutManager?.initPosition(position = position, realPosition = PlaybackPlayerRemote.position)
                    mExoPlayerView?.let { bindExoPlayerView(it, position) }
                }

            }
        }
    }

    private fun bindSongToUI() {
        if (_binding == null) return
        val currentSong = PlaybackPlayerRemote.currentSong
        if (currentSong.id.isNotEmpty()) {
            binding.pladioPlaybackControlPanelLayout.tvSongName.text = currentSong.title
            val description = if (currentSong.artist.isNullOrBlank()) {
                currentSong.playlistTitle
            } else currentSong.artist
            binding.pladioPlaybackControlPanelLayout.tvSongDescription.text = description ?: ""

            val defaultColor = binding.context.getColor(R.color.pladio_detail_default_background_color)
            val color = try {
                if (currentSong.dominantColor.isNullOrBlank()) {
                    defaultColor
                } else {
                    android.graphics.Color.parseColor(currentSong.dominantColor)
                }
            } catch (e: Exception) {
                defaultColor
            }
            val background = createTopToBottomGradient(
                startColor = color,
                endColor = android.graphics.Color.parseColor("#00000000")
            )
            binding.vBackground.background = background

            // TODO: Place Image poster and show video after that

            moveToCurrentSongWhenRemoteChange()

        }
    }
    // endregion


    private fun onProgressChange(value: Int, fromUser: Boolean) {
        if (fromUser) {
            onUpdateProgressViews(value.toLong(), PlaybackPlayerRemote.songBufferMillis, PlaybackPlayerRemote.songDurationMillis)
        }
    }

    private fun onStartTrackingTouch() {
        isSeeking = true
        progressViewUpdateHelper.stop()
        progressAnimator?.cancel()
    }

    private fun onStopTrackingTouch(value: Int) {
        isSeeking = false
        PlaybackPlayerRemote.seekTo(value.toLong(), PladioActionSeekType.Seek)
        progressViewUpdateHelper.start()
    }

    override fun onUpdateProgressViews(progress: Long, buffer: Long, total: Long) {
        binding.pladioPlaybackControlPanelLayout.playbackSeekBar.max = total.toInt()
        binding.pladioPlaybackControlPanelLayout.playbackSeekBar.secondaryProgress = buffer.toInt()

        if (isSeeking) {
            binding.pladioPlaybackControlPanelLayout.playbackSeekBar.progress = progress.toInt()
        } else {
            progressAnimator =
                ObjectAnimator.ofInt(binding.pladioPlaybackControlPanelLayout.playbackSeekBar, "progress", progress.toInt()).apply {
                    duration = 400L
                    interpolator = LinearInterpolator()
                    start()
                }

        }
        binding.pladioPlaybackControlPanelLayout.tvTotalDuration.text = PladioUtil.getReadableDurationString(total)
        binding.pladioPlaybackControlPanelLayout.tvCurrentDuration.text = PladioUtil.getReadableDurationString(progress)
    }

    // region Recycler View Song
    private fun updateAdapterLoop(enable: Boolean) {
        if (_binding == null) return
        mAdapter.updateType(type = if (enable) PladioPlaybackPanelAdapter.PladioAdapterType.LOOP else PladioPlaybackPanelAdapter.PladioAdapterType.NORMAL)
        updateListPlayingSong()
    }

    private fun triggerPlayItem(realPosition: Int, position: Int) {
        Logger.d("$TAG triggerPlayItem : $realPosition")
        PlaybackPlayerRemote.playSongAt(realPosition, false)
        //
        mExoPlayerView?.let { exoPlayerView ->
//            bindExoPlayerView(exoPlayerView, position = position)
        }
    }

    private fun moveToCurrentSongWhenRemoteChange(isSmooth: Boolean = true) {
        if (playbackUiState == OpenPanel.PLAYBACK) {
            if (_binding == null) return
            if (mAdapter.size() <= 0) return
            val playbackPosition = PlaybackPlayerRemote.position
            val currentRealPosition = binding.rvSongs.currentRealPosition()

            val positionToScroll = when (mAdapter.adapterType) {
                PladioPlaybackPanelAdapter.PladioAdapterType.NORMAL -> playbackPosition
                PladioPlaybackPanelAdapter.PladioAdapterType.LOOP -> calculateTargetRecyclerPosition(
                    currentPosition = binding.rvSongs.currentPosition(),
                    realPosition = currentRealPosition,
                    targetRealPosition = playbackPosition,
                    listSize = mAdapter.data().size
                )
            }
            Logger.d("$TAG -> moveToCurrentSongWhenRemoteChange -> positionToScroll: $positionToScroll | playbackPosition=$playbackPosition | binding.rvSongs.currentPosition()=${binding.rvSongs.currentPosition()} | currentRealPosition=${currentRealPosition}")
            mExoPlayerView?.let { bindExoPlayerView(it, position = positionToScroll) }
            if (isSmooth) {
                binding.rvSongs.smoothScrollToPosition(positionToScroll)
            } else {
                binding.rvSongs.scrollToPosition(positionToScroll)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun invalidatePlayerView() {
        if (_binding == null) return
        if (mAdapter.size() <= 0) return
        val playbackPosition = PlaybackPlayerRemote.position
        val currentRealPosition = binding.rvSongs.currentRealPosition()
        val positionToScroll = when (mAdapter.adapterType) {
            PladioPlaybackPanelAdapter.PladioAdapterType.NORMAL -> playbackPosition
            PladioPlaybackPanelAdapter.PladioAdapterType.LOOP -> calculateTargetRecyclerPosition(
                currentPosition = binding.rvSongs.currentPosition(),
                realPosition = currentRealPosition,
                targetRealPosition = playbackPosition,
                listSize = mAdapter.data().size
            )
        }
        Logger.d("$TAG -> invalidatePlayerView -> positionToScroll: $positionToScroll | playbackPosition=$playbackPosition | binding.rvSongs.currentPosition()=${binding.rvSongs.currentPosition()} | currentRealPosition=${currentRealPosition}")
//        mExoPlayerView?.let { bindExoPlayerView(it, position = positionToScroll) }
        //
        // TODO: Optimize notify data changed
        mAdapter.updateItem(PlaybackPlayerRemote.currentSong)
        mAdapter.notifyDataSetChanged()
    }

    private fun calculateTargetRecyclerPosition(
        currentPosition: Int,
        realPosition: Int,
        targetRealPosition: Int,
        listSize: Int
    ): Int {
        val delta = (targetRealPosition - realPosition) % listSize
        return currentPosition + if (delta < 0) delta + listSize else delta
    }

    private fun RecyclerView.currentRealPosition(): Int {
        val currentRealPosition = (layoutManager as? CustomLinearLayoutManager)?.currentRealPosition ?: 0
        Logger.d("$TAG -> GetPosition -> currentRealPosition: $currentRealPosition")
        return currentRealPosition
    }
    private fun RecyclerView.currentPosition(): Int {
        val currentPosition = (layoutManager as? CustomLinearLayoutManager)?.currentPosition ?: 0
        Logger.d("$TAG -> GetPosition -> currentPosition: $currentPosition")
        return currentPosition
    }
    // endregion


    // region Playback Control
    private val onPlaybackButtonListener = object :
        PladioPlaybackPanelControlHelper.OnPlaybackButtonListener {
        override fun onClickShuffle() {
            PlaybackPlayerRemote.toggleShuffleMode()
        }
        override fun onClickSpeed() {
            parentFragment?.findNavController()?.navigateSafe(NavPladioOuterDirections.actionGlobalToPladioPlaybackUniversalSettingsDialogFragment(
                settingType = PladioPlaybackUniversalSettingsData.UniversalSettingType.Speed.rawValue
            ))
        }
        override fun onClickPrevious() {
            PlaybackPlayerRemote.playPreviousSong()
        }
        override fun onClickSeekPrevious() {
            PlaybackPlayerRemote.seekTo(PlaybackPlayerRemote.songProgressMillis - 15_000L, PladioActionSeekType.SkipBack)
        }
        override fun onClickPlayPause() {
            if (PlaybackPlayerRemote.currentSong.stream == null) {
                PlaybackPlayerRemote.playSongAt(PlaybackPlayerRemote.position)
            } else {
                if (PlaybackPlayerRemote.isPlaying) {
                    PlaybackPlayerRemote.pauseSong()
                } else {
                    PlaybackPlayerRemote.resumePlaying()
                }
            }
        }
        override fun onClickSeekNext() {
            PlaybackPlayerRemote.seekTo(PlaybackPlayerRemote.songProgressMillis + 15_000L, PladioActionSeekType.SkipForward)
        }
        override fun onClickNext() {
            if (PlaybackPlayerRemote.currentSong.contentType == Song.PladioContentType.Single) {
                TrackingUtil.screen = TrackingUtil.screenRelated
            }
            PlaybackPlayerRemote.playNextSong()
        }
        override fun onClickTimer() {
            val networkAvailable = NetworkUtils.isNetworkAvailable()
            if (networkAvailable) {
                parentFragment?.findNavController()?.navigateSafe(NavPladioOuterDirections.actionGlobalToPladioPlaybackUniversalSettingsDialogFragment(
                    settingType = PladioPlaybackUniversalSettingsData.UniversalSettingType.Timer.rawValue
                ))
            }
        }
        override fun onClickRepeat() {
            PlaybackPlayerRemote.cycleRepeatMode()
        }
    }

    // endregion

    companion object {
        val TAG = PladioPlaybackPanelFragment::class.java.simpleName
        private const val INVALIDATE_BIND_PLAYER_VIEW_MAX = 3
        private const val MSG_INVALIDATE_BIND_PLAYER_VIEW = 1
        private const val INVALIDATE_BIND_PLAYER_VIEW_DELAY_MS = 300L
    }

}