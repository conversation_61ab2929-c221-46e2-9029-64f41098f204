package com.fptplay.mobile.features.multi_profile.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.common.adapter.block.BlockItemAdapter.ContentType
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.ViewHistoryHeaderItemBinding
import com.fptplay.mobile.databinding.ViewingHistoryItemBinding
import com.fptplay.mobile.features.poster_overlay.PosterOverlayView
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
class MultiProfileViewingHistoryAdapter() : BaseAdapter<BaseObject, RecyclerView.ViewHolder>() {

    private var typeContent: ContentType = ContentType.Main

    override fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return when {
            oldItem is ViewingHistoryHeader && newItem is ViewingHistoryHeader ->
                oldItem.title == newItem.title
            oldItem is StructureItem && newItem is StructureItem ->
                oldItem.id == newItem.id
            else -> false
        }
    }

    override fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return oldItem == newItem
    }

    override fun getItemViewType(position: Int): Int {
        return when (differ.currentList[position]) {
            is ViewingHistoryHeader -> ItemTypeViewingHistory.HEADER.ordinal
            is StructureItem -> ItemTypeViewingHistory.STRUCTURE_ITEM.ordinal
            else -> throw IllegalArgumentException("Unknown type")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ItemTypeViewingHistory.HEADER.ordinal -> {
                 MultiProfileViewingHistoryHeaderViewHolder(
                    ViewHistoryHeaderItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
            ItemTypeViewingHistory.STRUCTURE_ITEM.ordinal -> {
                MultiProfileViewingHistoryViewHolder(
                    ViewingHistoryItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
            else -> throw IllegalArgumentException("Unknown view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when(holder){
            is MultiProfileViewingHistoryHeaderViewHolder -> {
                holder.bind(differ.currentList[position])
            }
            is MultiProfileViewingHistoryViewHolder ->{
                holder.bind(differ.currentList[position])
            }
        }
    }
    fun setContentType(type: ContentType) {
        typeContent = type
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        super.onBindViewHolder(holder, position, payloads)
        if (holder is MultiProfileViewingHistoryViewHolder) {
            holder.bind(differ.currentList[position], payloads)
        }
    }

    data class UpdateViewHistoryStatusProcess(val isSelected: Boolean)

    private inner class MultiProfileViewingHistoryHeaderViewHolder(private val binding: ViewBinding): BaseViewHolder(binding){
        private val tvTitle: TextView? = binding.root.findViewById(R.id.tv_title)

        fun bind(data: BaseObject) {
            if(data is ViewingHistoryHeader){
                tvTitle?.text = data.title
            }
        }
    }
    private inner class MultiProfileViewingHistoryViewHolder(private val binding: ViewBinding) : BaseViewHolder(binding) {
        private val ivThumb: ImageView? = binding.root.findViewById(R.id.iv_thumb)
        private val tvTitle: TextView? = binding.root.findViewById(R.id.tv_title)
        private val pbTimeWatched: ProgressBar? = binding.root.findViewById(R.id.pb_time_watched)
        private val ivTL1: ImageView? by lazy { binding.root.findViewById(R.id.iv_tl_1) }
        private val ivTL2: ImageView? by lazy { binding.root.findViewById(R.id.iv_tl_2) }
        private val ivBL1: ImageView? by lazy { binding.root.findViewById(R.id.iv_bl_1) }
        private val ivBL2: ImageView? by lazy { binding.root.findViewById(R.id.iv_bl_2) }
        private val ivDes: ImageView? by lazy { binding.root.findViewById(R.id.iv_ribbon_payment) }
        private val ivBR1: ImageView? by lazy { binding.root.findViewById(R.id.iv_br_1) }
        private val tvBL1: TextView? by lazy { binding.root.findViewById(R.id.tv_bl_1) }
        private val tvBR1: TextView? by lazy { binding.root.findViewById(R.id.tv_br_1) }
        private val posterOverlay: PosterOverlayView? by lazy { binding.root.findViewById(R.id.poster_overlay) }
        private val thumbWidth by lazy { Utils.getSizeInPixel(context = binding.root.context, resId = R.dimen.poster_track_width) }
        private val thumbHeight by lazy { Utils.getSizeInPixel(context = binding.root.context, resId = R.dimen.poster_track_height) }
        private val ribbonHeight by lazy { Utils.getSizeInPixel(context = binding.root.context, resId = R.dimen.block_ribbon_bottom_height) }
        init {
            binding.root.onClickDelay {
                if (absoluteAdapterPosition >= 0 && absoluteAdapterPosition < size()) {
                    eventListener?.onClickedItem(
                        position = absoluteAdapterPosition,
                        data = differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }
        private fun showRibbon(ribbonView1: ImageView?, ribbonView2: ImageView?, listRibbon: List<String>) {
            if (listRibbon.isNotEmpty()) {
                ribbonView1?.isVisible = true
                ImageProxy.load(
                    context = binding.root.context,
                    url = listRibbon[0],
                    width = 0,
                    height = ribbonHeight,
                    target = ribbonView1
                )

                if (listRibbon.size > 1) {
                    ribbonView2?.isVisible = true
                    ImageProxy.load(
                        context = binding.root.context,
                        url = listRibbon[1],
                        width = 0,
                        height = ribbonHeight,
                        target = ribbonView2
                    )
                } else {
                    ribbonView2?.isVisible = false
                }
            } else {
                ribbonView1?.isVisible = false
            }
        }

        fun bind(data:BaseObject ,payloads: MutableList<Any>) {
            for (obj in payloads) {
                when (obj) {
                    is UpdateViewHistoryStatusProcess -> {

                    }
                }
            }
        }
        fun bind(data: BaseObject) {
            if (data is StructureItem) {
                data.apply {
                    ImageProxy.loadLocal(
                        context = binding.root.context,
                        data = horizontalImage,
                        width = thumbWidth,
                        height = thumbHeight,
                        target = ivThumb,
                        placeHolderId = R.drawable.placeholder_horizontal_slider,
                        errorDrawableId = R.drawable.placeholder_horizontal_slider
                    )
                    tvTitle?.text = title
                    //region show ribbon
                    showRibbon(ivTL1, ivTL2, data.ribbon.tlRibbons)
                    showRibbon(ivBL1, ivBL2, data.ribbon.blRibbons)
                    showRibbon(ivBR1, null, data.ribbon.brRibbons)
                    if (data.ribbon.descriptionRibbons.isNotEmpty()) {
                        ivDes?.apply {
                            visible()
                            ImageProxy.load(
                                context = binding.root.context,
                                url = data.ribbon.descriptionRibbons[0],
                                width = 0,
                                height = ribbonHeight,
                                target = this,
                            )
                        }
                    } else {
                        ivDes?.hide()
                    }
                    //endregion
                    if (typeContent is ContentType.History) {
                        pbTimeWatched?.isVisible = true
                        try {
                            pbTimeWatched?.progress =
                                ((data.timeWatched.toDouble() / data.detail.durationI.toDouble()) * 100).toInt()
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                            pbTimeWatched?.progress = 0
                        }
                    } else {
                        pbTimeWatched?.isVisible = false
                    }
                    posterOverlay?.let {
                        val isBindBL = ivBL1?.isVisible != true && tvBL1?.isVisible != true
                        val isBindBR = ivBR1?.isVisible != true && tvBR1?.isVisible != true && data.itype != ItemType.PLAYLIST
                        Logger.d("Bind item: ${data.id} Bind BL: $isBindBL, BR: $isBindBR")
                        it.bindData(
                            itemData = data.posterOverlayGroup,
                            blockStyle = data.blockStyle,
                            isBindBL = isBindBL,
                            isBindBR = isBindBR,
                        )
                    }
                }
            }
        }
    }
}


enum class ItemTypeViewingHistory{
    HEADER,
    STRUCTURE_ITEM
}
class ViewingHistoryHeader(
    val title: String?
):BaseObject()

