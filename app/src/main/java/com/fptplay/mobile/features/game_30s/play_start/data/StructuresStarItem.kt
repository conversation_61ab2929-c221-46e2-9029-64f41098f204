package com.fptplay.mobile.features.game_30s.play_start.data

import com.fptplay.mobile.features.game_30s.common.adapters.StartStyle
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class StructuresStarItem(
    val bitrateId: String? = null,
    val blockStyle: StartStyle? = StartStyle.grid,
    val name: String? = null,
    val title: String? = null,
    val subName: String? = null,
    val numberVote: Double? = null,
    val background: String? = null,
    val endTime: String? = null,
    val beginTime: String? = null,
    val videoUrl: String = "",
    val hashtag: List<String> = listOf(),
    val username: String = "",
    val avatar: String = "",
    val urlRanking:String?=null,
    val nameTeam:String?=null,
) : BaseObject()