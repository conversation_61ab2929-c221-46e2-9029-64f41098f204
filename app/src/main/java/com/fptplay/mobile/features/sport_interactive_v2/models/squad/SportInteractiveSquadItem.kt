package com.fptplay.mobile.features.sport_interactive_v2.models.squad

import com.fptplay.mobile.features.sport_interactive.model.SportTeamSquad
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData

data class SportInteractiveSquadItem(
    val member: SportInteractiveSquadMember = SportInteractiveSquadMember(),
) : UIData() {
    data class SportInteractiveSquadMember(
        val playerName: String = "",
        val playerNumber: String = "",
        val playerTime: String = "",
        val actions: SportTeamSquad.Squad.SquadMember.SquadActionType = SportTeamSquad.Squad.SquadMember.SquadActionType.Unknown(
            ""
        )
    )
}
