package com.fptplay.mobile.features.sport

import androidx.lifecycle.SavedStateHandle
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.features.sport.tournament.schedule_and_result.SportTournamentScheduleAndResultFragment
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemLeagueMatch
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureMeta
import com.xhbadxx.projects.module.domain.entity.fplay.sport.*
import com.xhbadxx.projects.module.domain.repository.fplay.HomeOs4Repository
import com.xhbadxx.projects.module.domain.repository.fplay.SportRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collect
import javax.inject.Inject

@HiltViewModel
class SportViewModel @Inject constructor(
    private val savedState: SavedStateHandle,
    private val sportRepository: SportRepository,
    private val homeOs4Repository: HomeOs4Repository
): BaseViewModel<SportViewModel.SportIntent, SportViewModel.SportState>() {

    var sportScheduleData = listOf<SportScheduleAndResultItemV2>()
        private set

    var sportResultData = listOf<SportScheduleAndResultItemV2>()
        private set

    //region Overrides
    override fun dispatchIntent(intent: SportIntent) {
        safeLaunch {
            when (intent) {
                SportIntent.GetSportTournament -> {
                    sportRepository.getSportTournament().collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            SportState.ResultSportTournament(isCached = isCached, data = data)
                        }
                    }
                }
                is SportIntent.GetDates -> {
                    sportRepository.getDates("menu_table_highlight").collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            SportState.ResultDates(isCached = isCached, data = data)
                        }
                    }
                }
                is SportIntent.GetSportSchedule -> {
                    homeOs4Repository.getStructureItemWithDate(
                        type = intent.type,
                        blockId = intent.blockId,
                        blockType = intent.blockType,
                        pageIndex = 1,
                        pageSize = MainApplication.INSTANCE.appConfig.numItemOfPage,
                        date = intent.date,
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            val leagues = data.mapNotNull {
                                val matches = it.league?.matchs?.toMutableList() ?: return@mapNotNull null
                                var roundName = ""
                                var index = 0
                                while (index < matches.size) {
                                    val match = matches[index]
                                    if (match.roundName != roundName) {
                                        roundName = match.roundName
                                        matches.add(index, StructureItemLeagueMatch.createHeaderItem(roundName))
                                        index++
                                    }
                                    index++
                                }

                                SportScheduleAndResultItemV2(it.league?.name ?: "", matches, it.league?.image ?: "")
                            }
                            SportState.ResultSportSchedule(isCached = isCached, date = intent.date, data = leagues)
                        }
                    }
                }
                is SportIntent.GetSportTournamentSchedule -> {
                    sportRepository.getSportScheduleAndResultV2(intent.seasonId).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            data.data.firstOrNull() {
                                it.dataType == SportScheduleAndResultV2.SportDataType.SCHEDULE
                            }?.scheduleAndResult?.let {
                                sportScheduleData = it
                                SportState.ResultSportTournamentSchedule(isCached = isCached, data = sportScheduleData,metaData =  data.meta)
                            } ?: SportState.Error("No data")
                        }
                    }
                }
                is SportIntent.GetSportTournamentResult -> {
                    sportRepository.getSportScheduleAndResultV2(intent.seasonId).collect { _result ->
                        _state.value = _result.reduce(intent = intent) { isCached, data ->
                            data.data.firstOrNull {
                                it.dataType == SportScheduleAndResultV2.SportDataType.RESULT
                            }?.scheduleAndResult?.let {
                                sportResultData = it
                                SportState.ResultSportTournamentResult(isCached = isCached, data = sportResultData, metaData =  data.meta)
                            } ?: SportState.Error("No data")
                        }
                    }
                }
                is SportIntent.GetSportTournamentTeamRank -> {
                    sportRepository.getSportScheduleAndResultV2(intent.seasonId).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            data.data.firstOrNull {
                                it.dataType == SportScheduleAndResultV2.SportDataType.RANKING
                            }?.ranking?.let {
                                SportState.ResultSportTournamentTeamRank(isCached = isCached, data = it,metaData =  data.meta)
                            } ?: SportState.Error("No data")
                        }
                    }
                }
            }
        }
    }
    override fun <T> Result<T>.reduce(
        intent: SportIntent?,
        successFun: (Boolean, T) -> SportState
    ): SportState {
        return when (this) {
            is Result.Init -> SportState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.UserError.RequiredLogin -> SportState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )
            is Result.Error -> SportState.Error(this.message, intent = intent)
            Result.Done -> SportState.Done(intent = intent)
        }
    }
    //endregion

    //region Commons
    fun saveSeasonId(seasonId: String) {
        savedState.set("seasonId", seasonId)
    }

    fun saveBlockScheduleId(blockScheduleId: String) {
        savedState.set("blockScheduleId", blockScheduleId)
    }

    fun saveScreenType(screenType: Int) {
        savedState.set("screenType", screenType)
    }

    fun seasonId() = savedState.get<String>("seasonId") ?: ""
    fun screenType() = savedState.get<Int>("screenType") ?: SportTournamentScheduleAndResultFragment.SCHEDULE_SCREEN
    fun blockScheduleId() = savedState.get<String>("blockScheduleId") ?: ""
    //endregion

    //region Intent, State
    sealed class SportState: ViewState {
        data class Loading(val intent: SportIntent? = null) : SportState()
        data class ResultDates(val isCached: Boolean, val data: List<SportDate>) : SportState()
        data class ResultSportSchedule(val isCached: Boolean, val date: String, val data: List<SportScheduleAndResultItemV2>) : SportState()
        data class ResultSportTournament(val isCached: Boolean, val data: List<SportTournament>) : SportState()
        data class ResultSportTournamentSchedule(val isCached: Boolean, val data: List<SportScheduleAndResultItemV2>,val metaData: SportScheduleAndResultV2Container.Meta) : SportState()
        data class ResultSportTournamentResult(val isCached: Boolean, val data: List<SportScheduleAndResultItemV2>,val metaData: SportScheduleAndResultV2Container.Meta) : SportState()
        data class ResultSportTournamentTeamRank(val isCached: Boolean, val data: List<SportTeamInRankGroupV2>, val metaData: SportScheduleAndResultV2Container.Meta) : SportState()
        data class Error(val message: String, val intent: SportIntent? = null) : SportState()
        data class ErrorRequiredLogin(val message: String, val intent: SportIntent? = null) : SportState()
        data class Done(val intent: SportIntent? = null) : SportState()
    }

    sealed class SportIntent: ViewIntent {
        object GetSportTournament : SportIntent()
        object GetDates : SportIntent()
        data class GetSportSchedule(val type: String, val blockId: String, val blockType: String, val date: String) : SportIntent()
        data class GetSportTournamentSchedule(val seasonId: String) : SportIntent()
        data class GetSportTournamentResult(val seasonId: String) : SportIntent()
        data class GetSportTournamentTeamRank(val seasonId: String) : SportIntent()
    }
    //endregion
}