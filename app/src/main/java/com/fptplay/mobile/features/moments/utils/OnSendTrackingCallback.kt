package com.fptplay.mobile.features.moments.utils

interface OnSendTrackingCallback {
    fun updatePlayingSession(playingSession: Long)
    fun sendTrackingMoments(logId: String,
                            screen: String = "Moments",
                            event: String,
                            momentId: String= "",
                            episodeId: String = "",
                            url: String = "",
                            title: String,
                            totalDuration: String = "",
                            currentDuration: String = "",
                            realTimePlaying: String = "",
                            errorCode: String = "",
                            errorMessage: String = "",
                            isRepeat: String = "0",
                            chapterId: String = "",
    )
    fun sendTrackingMomentsHeartbeat(logId: String,
                            screen: String = "Moments",
                            event: String,
                            momentId: String= "",
                            episodeId: String = "",
                            url: String = "",
                            title: String,
                            totalDuration: String = "",
                            currentDuration: String = "",
                            realTimePlaying: String = "",
                            errorCode: String = "",
                            errorMessage: String = "",
                            isRepeat: String = "0",
                            chapterId: String = "",
    )
}