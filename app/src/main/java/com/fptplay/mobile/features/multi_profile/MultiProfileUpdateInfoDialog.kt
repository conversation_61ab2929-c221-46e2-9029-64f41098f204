package com.fptplay.mobile.features.multi_profile

import android.graphics.Rect
import android.os.Bundle
import android.text.InputFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.EditText
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.ViewCompat
import androidx.core.view.marginBottom
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.getDisplayHeight
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.MultiProfileUpdateInfoDialogBinding
import com.fptplay.mobile.features.multi_profile.utils.UpdateProfileInfoScreen
import com.fptplay.mobile.player.utils.gone
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject


@AndroidEntryPoint
class MultiProfileUpdateInfoDialog :
    BaseFullDialogFragment<MultiProfileViewModel.MultiProfileState, MultiProfileViewModel.MultiProfileIntent>() {

    override val hasEdgeToEdge = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor
    private val safeArgs: MultiProfileUpdateInfoDialogArgs by navArgs()

    override val viewModel: MultiProfileViewModel by activityViewModels()

    private var _binding: MultiProfileUpdateInfoDialogBinding? = null
    private val binding get() = _binding!!

    private var globalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null

    private var edtLayout: EditText? = null

    private var nameRegEx = "^[A-Za-z0-9\\s\\u00C0-\\u1EF9]+".toRegex()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.MultiProfilePinProfileBottomSheetDialogTheme)
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MultiProfileUpdateInfoDialogBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onStop() {
        super.onStop()
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)

    }

    override fun onDestroyView() {
        super.onDestroyView()
        edtLayout?.hideKeyboard()
        _binding = null
        edtLayout = null
    }

    override fun bindComponent() {
        binding.apply {
            ivClear.hide()
            setError(null)

            when(safeArgs.targetScreen) {
                UpdateProfileInfoScreen.UpdateProfileName -> {
                    vAvatar.gone()
                    edtValue.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(15))
                    edtValue.hint = getString(R.string.multi_profile_edit_profile_name_hint)

                    tvHeader.setText(R.string.multi_profile_edit_profile_name_header)
                    tvDes.setText(R.string.multi_profile_edit_profile_name_description)

                }
            }

        }


        edtLayout = binding.edtValue

    }


    override fun bindData() {
        binding.edtValue.setText(safeArgs.currentValue)
    }

    override fun bindEvent() {
        binding.vOutside.onClickDelay {
            handleBack()
        }
        binding.ivClose.onClickDelay {
            handleBack()
        }

        binding.flContain.onClickDelay {
            binding.edtValue.hideKeyboard()
        }

        binding.ivClear.setOnClickListener {
            binding.edtValue.setText("")
        }

        binding.edtValue.doAfterTextChanged {
            val newText = it?.toString() ?: ""
            if (newText.isNotEmpty()) {
                setError(null)
                binding.ivClear.show()

            } else {
                binding.ivClear.hide()

            }
            updateConfirmButtonBackground()
        }


//        bindEventKeyBoardChange()
    }

    private fun setError(error: String?) {
        if (error.isNullOrBlank()) {
            binding.inputLayout.setBackgroundResource(R.drawable.multi_profile_password_view_bg)
            binding.tvError.hide()
            binding.tvError.text = ""
        } else {
            binding.inputLayout.setBackgroundResource(R.drawable.multi_profile_password_view_error_bg)
            binding.tvError.show()
            binding.tvError.text = error
        }
    }
    private fun bindEventKeyBoardChange() {
        binding.apply {
            if (globalLayoutListener != null) {
                root.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
            }
            globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
                val displayRect = Rect().apply { root.getWindowVisibleDisplayFrame(this) }
                Timber.tag("tam-multiProfile")
                    .d("globalLayoutListener ${activity?.getDisplayHeight() ?: 0} - ${displayRect.bottom}")
                val insets = activity?.let {
                    ViewCompat.getRootWindowInsets(it.window.decorView)
                }
                val keypadHeight = (activity?.getDisplayHeight() ?: 0) - displayRect.bottom

                if (keypadHeight > 200) {
                    Timber.tag("tam-multiProfile")
                        .d("open keyboard $keypadHeight - ${flContain.marginBottom}")
                    if (flContain.marginBottom != keypadHeight) {
                        Timber.tag("tam-multiProfile").i("open keyboard change margin bottom")
                        val param = flContain.layoutParams as ViewGroup.MarginLayoutParams
                        param.setMargins(0, 0, 0, keypadHeight)
                        flContain.layoutParams = param
//                        val param = layoutInput.layoutParams as ViewGroup.MarginLayoutParams
//                        param.setMargins(0, 0, 0, keypadHeight)
//                        layoutInput.layoutParams = param

                    } else return@OnGlobalLayoutListener
                } else {
                    Timber.tag("tam-multiProfile")
                        .d("close keyboard  $keypadHeight - ${flContain.marginBottom}")
                    val marginBottom = 0
                    if (flContain.marginBottom != marginBottom) {
                        Timber.tag("tam-multiProfile").i("close keyboard change margin bottom")
                        val param = flContain.layoutParams as ViewGroup.MarginLayoutParams
                        param.setMargins(0, 0, 0, marginBottom)
                        flContain.layoutParams = param
//                        val param = layoutInput.layoutParams as ViewGroup.MarginLayoutParams
//                        param.setMargins(0, 0, 0, marginBottom)
//                        layoutInput.layoutParams = param
                    } else return@OnGlobalLayoutListener

                }
            }

            root.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)

        }

    }

    private fun moveLayout(layout: View, keypadHeight: Int, marginWithoutKeypad: Int = 0) {
        Timber.tag("tam-multiProfile")
            .d("moveLayout $layout : $keypadHeight - ${layout.marginBottom}")
        if (keypadHeight > 200) {
            val displayRect = Rect().apply { layout.getWindowVisibleDisplayFrame(this) }
            Timber.tag("tam-multiProfile").d("open keyboard $keypadHeight - ${layout.marginBottom}")
            if (layout.marginBottom != keypadHeight) {
                Timber.tag("tam-multiProfile").i("open keyboard change margin bottom")
                val param = layout.layoutParams as ViewGroup.MarginLayoutParams
                param.setMargins(0, 0, 0, keypadHeight)
                layout.layoutParams = param

            } else return
        } else {
            Timber.tag("tam-multiProfile")
                .d("close keyboard  $keypadHeight - ${layout.marginBottom}")
            if (layout.marginBottom != marginWithoutKeypad) {
                Timber.tag("tam-multiProfile").i("close keyboard change margin bottom")
                val param = layout.layoutParams as ViewGroup.MarginLayoutParams
                param.setMargins(0, 0, 0, marginWithoutKeypad)
                layout.layoutParams = param
            } else return

        }
    }


    private fun updateConfirmButtonBackground() {
        val value  = binding.edtValue.text

        val isEnable = when (safeArgs.targetScreen) {

            UpdateProfileInfoScreen.UpdateProfileName ->  (value?.length ?: 0) > 0
        }
        if (isEnable) {
            binding.btnConfirm.isEnabled = true
            binding.btnConfirm.background = AppCompatResources.getDrawable(
                requireContext(),
                R.drawable.account_rounded_btn_background_enable
            )
            binding.btnConfirm.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.app_content_text_color
                )
            )
            binding.btnConfirm.onClickDelay {
                when (safeArgs.targetScreen) {

                    UpdateProfileInfoScreen.UpdateProfileName -> {
                        checkAndSendResult(
                            safeArgs.targetScreen,
                            binding.edtValue.text.toString().trim()
                        )

                    }
                }

            }
        } else {
            binding.btnConfirm.isEnabled = false
            binding.btnConfirm.background = AppCompatResources.getDrawable(
                requireContext(),
                R.drawable.account_rounded_btn_background_disable
            )
            binding.btnConfirm.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.app_content_text_disable_color
                )
            )
            binding.btnConfirm.setOnClickListener(null)
        }
    }

    private fun checkAndSendResult(screen: UpdateProfileInfoScreen, updatedValue: String) {
        when (screen) {
            UpdateProfileInfoScreen.UpdateProfileName -> {
                if(updatedValue.matches(nameRegEx)) {
                    setFragmentResult(
                        Utils.PROFILE_UPDATE_NAME_EVENT,
                        bundleOf(Utils.PROFILE_UPDATE_NAME_VALUE to updatedValue)
                    )
                    handleBack()
                } else {
                    setError(getString(R.string.multi_profile_edit_profile_name_error))
                }
            }
        }


    }

    fun handleBack() {
        findNavController().navigateUp()
    }

    override fun MultiProfileViewModel.MultiProfileState.toUI() {
    }


}