package com.fptplay.mobile.features.loyalty.e_voucher_card.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.databinding.EVoucherCardSupplierItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.LoyDetailMobileCardEntity
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.LoyHistoryEntity
import com.xhbadxx.projects.module.util.image.ImageProxy
import timber.log.Timber

class EvoucherSupplierAdapter : BaseAdapter<LoyDetailMobileCardEntity.Data, RecyclerView.ViewHolder>() {

    private var currentPos = 0
    private var prevPos = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return EvoucherViewHolder(
            EVoucherCardSupplierItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is EvoucherViewHolder -> holder.bind(differ.currentList[position])
        }
    }

    inner class EvoucherViewHolder(private val binding: EVoucherCardSupplierItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                if (absoluteAdapterPosition >= 0 && absoluteAdapterPosition < size()) {
                    if(absoluteAdapterPosition != currentPos){
                        prevPos = currentPos
                        currentPos = absoluteAdapterPosition
                    }
                    notifyItemChanged(prevPos)
                    notifyItemChanged(currentPos)
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        fun bind(data: LoyDetailMobileCardEntity.Data) {
            if(absoluteAdapterPosition == currentPos)
                binding.ivSupplier.foreground = ContextCompat.getDrawable(binding.root.context, R.drawable.loyalty_evoucher_red_border)
            else binding.ivSupplier.foreground = ContextCompat.getDrawable(binding.root.context, R.drawable.loyalty_evoucher_none_border)
            ImageProxy.load(
                context = binding.root.context,
                url = data.avatarUrl,
                width = binding.root.context.resources.getDimensionPixelOffset(R.dimen.loyalty_evoucher_supplier_width),
                height = binding.root.context.resources.getDimensionPixelOffset(R.dimen.loyalty_evoucher_supplier_height),
                target = binding.ivItem,
                placeHolderId = R.drawable.image_placeholder,
                errorDrawableId = R.drawable.image_placeholder
            )
        }
    }

    fun resetCurrentPos(pos: Int){
        currentPos = pos
        prevPos = -1
    }
}