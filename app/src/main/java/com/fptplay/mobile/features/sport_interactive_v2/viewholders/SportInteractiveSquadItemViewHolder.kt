package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.SportInteractiveSquadMemberBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadItem
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.util.Utils.checkToShowContent
import com.tear.modules.util.Utils.hide
import com.xhbadxx.projects.module.util.image.ImageProxy
import timber.log.Timber

class SportInteractiveSquadItemViewHolder(private val binding: SportInteractiveSquadMemberBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveSquadItem>(binding) {
    override fun bind(data: SportInteractiveSquadItem) {
        Timber.d("*****gen bind: ${data.member.playerName} - ${data.member.actions.icon}")
        data.member.let { member ->
            binding.apply {
                tvPlayerName.text = member.playerName
                tvPlayerNumber.text = member.playerNumber
                tvPlayerTime.checkToShowContent(member.playerTime, goneViewWhenNoText = true)

                try {
                    if (member.actions.icon != -1) {
                        ImageProxy.loadLocal(
                            context = binding.root.context,
                            width = Utils.getSizeInPixel(
                                context = binding.root.context,
                                resId = if (binding.root.context.isTablet()) R.dimen._8sdp else R.dimen._13sdp
                            ),
                            height = Utils.getSizeInPixel(
                                context = binding.root.context,
                                resId = if (binding.root.context.isTablet()) R.dimen._8sdp else R.dimen._13sdp
                            ),
                            target = binding.imgAction,
                            data = member.actions.icon,
                            placeHolderId = member.actions.icon,
                            errorDrawableId = member.actions.icon,
                        )
                        imgAction.visible()
                    } else {
                        imgAction.hide()
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }

            }
        }

    }
}