package com.fptplay.mobile.features.search.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.databinding.SearchSuggestItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchSuggest
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchSuggestV2
import com.xhbadxx.projects.module.util.image.ImageProxy

class SearchSuggestAdapter : BaseAdapter<SearchSuggestV2.SearchSuggestItemV2, RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return SearchSuggestViewHolder(SearchSuggestItemBinding.inflate(LayoutInflater.from(parent.context),parent, false))
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
       if (holder is SearchSuggestViewHolder)
           holder.bind(differ.currentList[position])
    }

     inner class SearchSuggestViewHolder ( val binding: SearchSuggestItemBinding) : BaseViewHolder(binding) {

        init {
            binding.root.setOnClickListener {
                if (absoluteAdapterPosition >= 0 && absoluteAdapterPosition < size()) {
                    eventListener?.onClickedItem(
                        position = absoluteAdapterPosition,
                        data = differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

         fun bind(data:SearchSuggestV2.SearchSuggestItemV2) {
             binding.tvSuggest.text = data.keyword
             ImageProxy.load(
                 context = binding.root.context,
                 url = data.icon,
                 width = binding.root.context.resources.getDimensionPixelOffset(R.dimen.item_search_suggest_image_size) *2,
                 height = binding.root.context.resources.getDimensionPixelOffset(R.dimen.item_search_suggest_image_size) *2,
                 target = binding.ivType
             )
         }
    }
}