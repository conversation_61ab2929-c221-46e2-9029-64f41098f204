package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.SportInteractiveHeaderWithCoachBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveHeaderData
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.image.coil.CoilImage
import timber.log.Timber

class SportInteractiveHeaderWithCoachViewHolder(private val binding: SportInteractiveHeaderWithCoachBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveHeaderData>(binding) {

    override fun bind(data: SportInteractiveHeaderData) {
        binding.txtNameTeamHome.text = data.info.homeTeamName
        binding.txtNameTeamAway.text = data.info.awayTeamName
        binding.txtNameCoachHome.text = data.info.homeCoach
        binding.txtNameCoachAway.text = data.info.awayCoach

        if (data.info.awayLogo.isNotBlank()) {
            Timber.d("-----awaylogo: ${data.info.awayLogo}")
            ImageProxy.load(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = 0,
                target = binding.ivAwayTeam,
                url = data.info.awayLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )

        } else {
            ImageProxy.loadLocal(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = binding.ivAwayTeam,
                data = R.drawable.ic_default_logo_team,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )
        }

        if (data.info.homeLogo.isNotBlank()) {
            ImageProxy.load(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = 0,
                target = binding.ivHomeTeam,
                url = data.info.homeLogo,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )
        } else {
            ImageProxy.loadLocal(
                context = binding.root.context,
                width = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                height = Utils.getSizeInPixel(
                    context = binding.root.context,
                    resId = if (binding.root.context.isTablet()) R.dimen._20sdp else R.dimen._34sdp
                ),
                target = binding.ivHomeTeam,
                data = R.drawable.ic_default_logo_team,
                placeHolderId = R.drawable.ic_default_logo_team,
                errorDrawableId = R.drawable.ic_default_logo_team,
            )
        }
    }
}