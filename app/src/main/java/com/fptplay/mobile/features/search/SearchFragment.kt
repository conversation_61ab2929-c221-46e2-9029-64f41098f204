package com.fptplay.mobile.features.search

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.databinding.ErrorLayoutBinding
import com.fptplay.mobile.databinding.SearchEmptyLayoutBinding
import com.fptplay.mobile.databinding.SearchFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.search.adapters.SubCategorySearchResultAdapter
import com.fptplay.mobile.features.search.adapters.CategorySearchResultAdapter
import com.fptplay.mobile.features.search.adapters.SearchBlockAdapter
import com.fptplay.mobile.features.search.adapters.SearchRecentAdapter
import com.fptplay.mobile.features.search.adapters.SearchSuggestAdapter
import com.fptplay.mobile.features.search.adapters.SearchTrendingAdapter
import com.fptplay.mobile.features.search.utils.SearchUtils.Companion.CATEGORY_ALL
import com.fptplay.mobile.features.search.models.CategorySearchObj
import com.fptplay.mobile.features.search.models.SearchBlockObj
import com.fptplay.mobile.features.search.utils.CenterSmoothScroller
import com.fptplay.mobile.features.search.utils.SearchUtils
import com.fptplay.mobile.features.search.utils.SearchUtils.Companion.PAGE_INDEX
import com.fptplay.mobile.features.search.utils.SearchUtils.Companion.PAGE_SIZE
import com.fptplay.mobile.features.search.utils.SearchUtils.Companion.RESULT_PAGE_SIZE
import com.fptplay.mobile.features.search.utils.SearchUtils.Companion.SEARCH_VERSION
import com.fptplay.mobile.features.search.utils.SearchUtils.Companion.mapToBlockList
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchHistoryV2
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchSuggestV2
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchV2
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchV2.SearchItemV2
import com.xhbadxx.projects.module.domain.entity.fplay.vod.StructureItem
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@AndroidEntryPoint
class SearchFragment :
    BaseFragment<SearchViewModel.SearchState, SearchViewModel.SearchIntent>() {
        companion object {
            const val MAX_RETRY_COUNT = 10
        }
    override val hasEdgeToEdge = true

    override val viewModel: SearchViewModel by activityViewModels()

    private var _binding: SearchFragmentBinding? = null
    private val binding get() = _binding!!

    private var _errorBinding: ErrorLayoutBinding? = null
    private val errorBinding get() = _errorBinding!!

    private var _emptyBinding: SearchEmptyLayoutBinding? = null
    private val emptyBinding get() = _emptyBinding!!

    private val suggestAdapter: SearchSuggestAdapter by lazy { SearchSuggestAdapter() }
    private val blockAdapter: SearchBlockAdapter by lazy { SearchBlockAdapter() }
    private val recentAdapter: SearchRecentAdapter by lazy { SearchRecentAdapter() } // poster overlay
    private val trendingAdapter: SearchTrendingAdapter by lazy { SearchTrendingAdapter() } // khong co image
    private val categorySearchResultAdapter: CategorySearchResultAdapter by lazy { CategorySearchResultAdapter() }
    private val subCategorySearchResultAdapter: SubCategorySearchResultAdapter by lazy { SubCategorySearchResultAdapter() }
    private var searchResults: SearchV2? = null
    private var categorySelected: CategorySearchObj = CategorySearchObj()
    private var subCategorySelected: CategorySearchObj = CategorySearchObj()
    private var query = ""
    private var statusSearch = "0"
    private var retryCount: Int = 0
    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var hasSubCategory: Boolean = false

    private var errorType: String = ""
    private var lastOrientation = Configuration.ORIENTATION_UNDEFINED

    private val previousFragment: BaseFragment<*, *>? by lazy { parentFragmentManager.fragments[0] as? BaseFragment<*, *> }

    private var isRestore: Boolean = false

    private val filterItemDecoration = object : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val currentPos = parent.getChildAdapterPosition(view)
            val itemCount = parent.adapter?.itemCount ?: 0
            val spacingPxl = resources.getDimensionPixelSize(R.dimen.category_search_result_item_spacing)

            if (currentPos == 0) {
                outRect.left = resources.getDimensionPixelSize(R.dimen.category_search_result_item_margin_vertical)
            } else {
                outRect.left = spacingPxl
            }
            if (currentPos == (itemCount - 1)) {
                outRect.right = resources.getDimensionPixelSize(R.dimen.category_search_result_item_margin_vertical)
            }
        }
    }

    private val blockDecoration = object: ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            when(val position = parent.getChildAdapterPosition(view)) {
                0 -> {
                    outRect.top = if (hasSubCategory) {
                        binding.root.resources.getDimensionPixelSize(R.dimen.item_result_top_spacing_with_sub_category)
                    } else {
                        0
                    }
                    if (blockAdapter.size() > 1) {
                        outRect.bottom = binding.root.resources.getDimensionPixelSize(R.dimen.search_result_item_bottom_spacing)
                    }
                }
                else ->  {
                    if (position != blockAdapter.size() - 1) {
                        outRect.bottom = binding.root.resources.getDimensionPixelSize(R.dimen.search_result_item_bottom_spacing)
                    }
                }
            }
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(checkBeforePlayUtil)
        checkBeforePlayUtil.setScreenProvider("Search")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SearchFragmentBinding.inflate(inflater, container, false)
        _errorBinding = ErrorLayoutBinding.bind(binding.root)
        _emptyBinding = SearchEmptyLayoutBinding.bind(binding.root)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _errorBinding = null
        _emptyBinding = null
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkBeforePlayUtil)
    }

    override fun initData() {
        super.initData()
        if(blockAdapter.data().isNotEmpty()) {
            restoreSearchResult()
        } else {
            restoreSearchHome()
        }
    }

    private fun restoreSearchHome() {
        if (recentAdapter.data().isNotEmpty() || recentAdapter.data().isNotEmpty()) {
            isRestore = true
            hideSearchRecent(flag = false)
        }
    }

    private fun restoreSearchResult() {
        isRestore = true
        hideSearchRecent(true)
        hideSearchResult(flag = false)
        updateItemDecoration()
        categorySearchResultAdapter.data().takeIf { it.size > 1 }?.let {
            binding.rcvCategorySearchResults.show()
            binding.nsvResult.elevation = binding.root.resources.getDimension(R.dimen.search_result_elevation)
        } ?: kotlin.run {
            binding.rcvCategorySearchResults.hide()
            binding.nsvResult.elevation = binding.root.resources.getDimension(R.dimen.search_result_elevation_2dp)
        }
        if(hasSubCategory) {
            binding.rcvSubCategorySearchResults.slideDown(requireContext(), 0)
        } else {
            binding.rcvSubCategorySearchResults.slideUp(requireContext(), 0)
        }
    }

    override fun bindComponent() {
        binding.rcvSuggest.apply {
            adapter = suggestAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            addItemDecoration(
                object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        outRect.bottom =
                            resources.getDimensionPixelSize(R.dimen.search_trending_item_margin_vertical)
                        outRect.right =
                            resources.getDimensionPixelSize(R.dimen.search_trending_item_margin)
                    }
                }
            )
        }
        binding.rcvResult.apply {
            adapter = blockAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        }
        binding.rcvRecentSearch.apply {
            adapter = recentAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }
        binding.rcvCategorySearchResults.apply {
            adapter = categorySearchResultAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            addItemDecoration(filterItemDecoration)
        }
        binding.rcvSubCategorySearchResults.apply {
            adapter = subCategorySearchResultAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            addItemDecoration(filterItemDecoration)
        }

        binding.rcvSearchTrending.apply {
            adapter = trendingAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            addItemDecoration(
                object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        outRect.bottom =
                            resources.getDimensionPixelSize(R.dimen.search_trending_item_margin_vertical)
                        outRect.right =
                            resources.getDimensionPixelSize(R.dimen.search_trending_item_margin)
                    }
                }
            )
        }
        showKeyBoard()
    }
    override fun bindData() {
        if (isRestore) return
        if(NetworkUtils.isNetworkAvailable()) {
            hideErrorLayout()
            hideSearchRecent(false)
            viewModel.dispatchIntent(
                SearchViewModel.SearchIntent.GetRecentSearch(page = PAGE_INDEX, perPage = PAGE_SIZE)
            )
            viewModel.dispatchIntent(
                SearchViewModel.SearchIntent.GetSearchTrendingMobile(PAGE_INDEX, PAGE_SIZE)
            )
        } else {
            showErrorByInternetLayout(SearchUtils.NETWORK_ERROR)
            hideSearchRecent(true)
        }
    }

    private fun getSpanCount(): Int {
        val orientation = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation
        return if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            if (context.isTablet()) 3 else 2
        } else {
            if (context.isTablet()) 4 else 2
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        configurationChanged(newConfig = MainApplication.INSTANCE.applicationContext.resources.configuration)
    }
    private fun configurationChanged(newConfig: Configuration?){
        if (lastOrientation == newConfig?.orientation) return
        lastOrientation = newConfig?.orientation ?: Configuration.ORIENTATION_UNDEFINED
        val spanCount = getSpanCount()
        try {
            blockAdapter.configurationChanged(lastOrientation)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }

    }

    @SuppressLint("SimpleDateFormat")
    override fun SearchViewModel.SearchState.toUI() {
        when (this) {
            is SearchViewModel.SearchState.Loading -> {
                if(intent is SearchViewModel.SearchIntent.GetSearchResult) {
                    hideEmptyLayout()
                    hideErrorLayout()
                }
            }
            is SearchViewModel.SearchState.Done -> {
            }
            is SearchViewModel.SearchState.ErrorNoInternet ->{
                if (intent is SearchViewModel.SearchIntent.GetSearchResult) {
                    showErrorByInternetLayout(SearchUtils.API_ERROR)
                    binding.nsvResult.hide()
                }
            }
            is SearchViewModel.SearchState.Error -> {
                if (intent is SearchViewModel.SearchIntent.GetSearchResult) {
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = TrackingConstants.EVENT_APP_ERROR,
                            event = AppErrorType.ERROR,
                            appId = TrackingUtil.appSearch,
                            appName = TrackingUtil.appSearch,
                            errorCode = AppErrorConstants.SEARCH_RESULT_CODE,
                            errorMessage = this.message,
                            itemName = AppErrorConstants.SEARCH_RESULT_MESSAGE,
                            issueId = TrackingUtil.createIssueId(),
                            subMenuId = intent.category
                        )
                    )
                    showOtherErrorLayout(SearchUtils.API_ERROR)
                    binding.nsvResult.hide()
                } else if (intent is SearchViewModel.SearchIntent.GetRecentSearch) {
                    binding.tvRecent.hide()
                }  else if (intent is SearchViewModel.SearchIntent.GetSearchTrendingMobile) {
                    binding.tvSearchTrending.hide()
                }
            }
            is SearchViewModel.SearchState.SearchSuggestResult -> {
                suggestAdapter.bind((this.data.listItem)) {
                    if(_binding!=null){
                        binding.rcvSuggest.scrollToPosition(0)
                    }
                }
            }
            is SearchViewModel.SearchState.SearchResult -> {
                val itemId = if (data.listItem.isEmpty()) "0" else "1"
                retryCount = 0
                if (data.uiType != SearchV2.SearchUIType.FAKE && !isCached) {
                    AdjustAllEvent.sendSearchContentEvent(query)
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = "510",
                            appId = TrackingUtil.appSearch,
                            appName = TrackingUtil.appSearch,
                            event = "Search",
                            itemId = itemId,
                            itemName = query,
                            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                            status = statusSearch,
                            subMenuId = category.ifBlank { CATEGORY_ALL }
                        )
                    )
                }
                if (data.uiType != SearchV2.SearchUIType.FAKE) {
                    if (data.listItem.isEmpty()) {
                        hideSearchCategory(flag = true)
                        showEmptyLayout()
                        binding.nsvResult.hide()
                    } else {
                        hideEmptyLayout()
                        hideErrorLayout()
                        hideSearchResult(false)
                        searchResults = data
                        binding.nsvResult.show()
                        blockAdapter.add(
                            data.mapToBlockList(true),
                            isBind = isBind
                        )
                    }
                } else {
                    binding.nsvResult.show()
                    binding.nsvResult.elevation = binding.root.resources.getDimension(R.dimen.search_result_elevation_2dp)
                    blockAdapter.add(
                        data.mapToBlockList(category.isEmpty()),
                        isBind = isBind
                    )
                }
            }
            is SearchViewModel.SearchState.SearchRecent -> {
                retryCount = 0
                if (data.isNotEmpty()) {
                    binding.tvRecent.show()
                    binding.rcvRecentSearch.show()
                    recentAdapter.bind(this.data)
                } else {
                    binding.tvRecent.hide()
                }
            }
            is SearchViewModel.SearchState.SearchTrending -> {
                retryCount = 0
                if (this.data.listItem.isNotEmpty()) {
                    binding.tvSearchTrending.show()
                    binding.rcvSearchTrending.show()
                    trendingAdapter.bind(this.data.listItem)
                } else {
                    binding.tvSearchTrending.hide()
                }
            }
            is SearchViewModel.SearchState.ResultCategories -> {
                data.takeIf { data.size > 1 }?.let {
                    binding.rcvCategorySearchResults.show()
                    binding.nsvResult.elevation = binding.root.resources.getDimension(R.dimen.search_result_elevation)
                    categorySearchResultAdapter.bind(it) {
                        binding.rcvCategorySearchResults.onScrollToCenterAt(position)
                        categorySearchResultAdapter.updateItemSelect(position)
                    }
                } ?: kotlin.run {
                    binding.rcvCategorySearchResults.hide()
                    binding.nsvResult.elevation = binding.root.resources.getDimension(R.dimen.search_result_elevation_2dp)
                }
            }
            is SearchViewModel.SearchState.ResultSubCategories -> {
                if(data.size > 2 && position != 0) {
                    hasSubCategory = true
                    binding.rcvSubCategorySearchResults.slideDown(requireContext(), 0)
                    subCategorySearchResultAdapter.bind(data) {
                        subCategorySearchResultAdapter.updateItemSelect(0)
                        binding.rcvSubCategorySearchResults.smoothScrollToPosition(0)
                    }
                } else {
                    hasSubCategory = false
                    binding.rcvSubCategorySearchResults.slideUp(requireContext(), 0)
                }
                updateItemDecoration()
            }

        }
    }

    override fun bindEvent() {
        binding.ivBack.setOnClickListener {
            backHandler()
        }
        binding.ivDelete.setOnClickListener {
            binding.editSearch.setText("")
            hideSearchRecent(false)
            binding.nsvResult.isGone = true
            binding.rcvSuggest.isGone = true
            binding.ivDelete.isVisible = false
            hideSearchCategory()
            hideEmptyLayout()
            hideErrorLayout()
            clearSearchResult()
        }

        binding.editSearch.apply {
            query = if (!isRestore) "" else query
            addTextChangedListener(object : TextWatcher {

                override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}

                override fun afterTextChanged(s: Editable) {
                    binding.ivDelete.isVisible = s.toString().isNotEmpty()
                    if (isRestore) {
                        isRestore = false
                        return
                    }
                    hideSearchResult(true)
                    hideSearchRecent(true)
                    viewModel.dispatchIntent(
                        SearchViewModel.SearchIntent.GetSearchSuggest(
                            query = s.toString(),
                            searchVersion = SEARCH_VERSION,
                            pageSize = PAGE_SIZE,
                            pageIndex = PAGE_INDEX
                        )
                    )
                    query = s.toString()
                    hideSearchCategory()
                    hideEmptyLayout()
                    hideErrorLayout()
                    if (s.toString().isEmpty()) {
                        binding.rcvSuggest.isGone = true
                        hideSearchRecent(false)
                        clearSearchResult()
                    }
                    setSelection(s.length)
                }
            })

            setOnEditorActionListener { _, action, _ ->
                return@setOnEditorActionListener when (action) {
                    EditorInfo.IME_ACTION_SEARCH -> {
                        statusSearch = "0"
                        if (query.isNotEmpty()) {
                            hideSearchResult(false)
                            hideErrorLayout()
                            hideEmptyLayout()
                            viewModel.dispatchIntent(
                                SearchViewModel.SearchIntent.GetSearchResult(
                                    query = query,
                                    pageSize =  RESULT_PAGE_SIZE,
                                    pageIndex =  1,
                                    category = "",
                                    isTablet = <EMAIL>().isTablet(),
                                    position = 0
                                )
                            )
                            closeKeyBoard()
                        }
                        true
                    }
                    else -> false
                }
            }

            setOnClickListener {
                isCursorVisible = true
            }
        }



        suggestAdapter.eventListener = object : IEventListener<SearchSuggestV2.SearchSuggestItemV2> {
            override fun onClickedItem(position: Int, data: SearchSuggestV2.SearchSuggestItemV2) {
                binding.editSearch.setText(data.keyword)
                hideSearchResult(false)
                statusSearch = "1"
                viewModel.dispatchIntent(
                    SearchViewModel.SearchIntent.GetSearchResult(
                        query = data.keyword,
                        pageSize =  RESULT_PAGE_SIZE,
                        pageIndex =  1,
                        category = "",
                        isTablet = <EMAIL>().isTablet(),
                        position = 0
                    )
                )
            }
        }

        blockAdapter.setBlockEventListener(
            object: IEventListener<BaseObject> {
                override fun onClickView(position: Int, view: View?, data: BaseObject) {
                    when(view?.id) {
                        R.id.iv_share -> {
                            if (data is SearchBlockObj) {
                                if (data.searchList.isNotEmpty()) {
                                    val item = data.searchList[0]
                                    sendLogTrackingAction(eventName = "Share", detail = item, categorySelected.title)
                                    val link = "${getString(R.string.base_url_deep_link_moment_share)}/${item.id}/${item.chapterId}"
                                    onShareLink(link)
                                }
                            }
                        }
                        R.id.cl_play_now -> {
                            if (data is SearchBlockObj) {
                                val item = data.searchList[0]
                                handleClickSearchItem(item, categorySelected.title)
                            }
                        }
                    }
                }
            }
        )

        blockAdapter.setItemEventListener(
            object: IEventListener<BaseObject> {
                override fun onClickedItem(position: Int, data: BaseObject) {
                    if (data is SearchItemV2) {
                        handleClickSearchItem(data, categorySelected.title)
                    }
                }
            }
        )

        categorySearchResultAdapter.eventListener = object : IEventListener<CategorySearchObj> {
            override fun onClickedItem(position: Int, data: CategorySearchObj) {
                binding.rcvCategorySearchResults.onScrollToCenterAt(position)
                categorySearchResultAdapter.updateItemSelect(position)
                viewModel.dispatchIntent(
                    SearchViewModel.SearchIntent.GetSearchResult(
                        query = query,
                        pageSize =  RESULT_PAGE_SIZE,
                        pageIndex =  1,
                        category = if (data.categoryType == CategorySearchObj.Type.ALL) "" else data.title,
                        isTablet = <EMAIL>().isTablet(),
                        position = position
                    )
                )
            }

            override fun onSelectedItem(position: Int, data: CategorySearchObj) {
                categorySelected = data
            }
        }

        subCategorySearchResultAdapter.eventListener = object : IEventListener<CategorySearchObj> {
            override fun onClickedItem(position: Int, data: CategorySearchObj) {
                binding.rcvSubCategorySearchResults.onScrollToCenterAt(position)
                subCategorySearchResultAdapter.updateItemSelect(position)
            }
            override fun onSelectedItem(position: Int, data: CategorySearchObj) {
                subCategorySelected = data

                lifecycleScope.launch(Dispatchers.IO) {
                    searchResults?.let { result ->
                        val filterSearchResults = result.copy(
                            listItem = result.listItem.filter {
                                if (data.title != CATEGORY_ALL) {
                                    it.subCategory == data.title
                                } else true
                            }
                        )
                        delay(200)
                        bindDataListResult(filterSearchResults, data.title)
                    }
                }
            }
        }

        binding.nsvResult.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            if (hasSubCategory) {
                if (oldScrollY > scrollY) {
                    binding.rcvSubCategorySearchResults.slideDown(requireContext())
                } else {
                    binding.rcvSubCategorySearchResults.slideUp(requireContext())
                }
            }
        }

        trendingAdapter.eventListener = object : IEventListener<SearchHistoryV2.SearchHistoryItemV2> {
            override fun onClickedItem(position: Int, data: SearchHistoryV2.SearchHistoryItemV2) {
                binding.editSearch.setText(data.keyword)
                viewModel.dispatchIntent(
                    SearchViewModel.SearchIntent.GetSearchResult(
                        query = data.keyword,
                        pageSize =  RESULT_PAGE_SIZE,
                        pageIndex =  1,
                        category = "",
                        isTablet = <EMAIL>().isTablet(),
                        position = 0
                    )
                )
                statusSearch = "2"
                hideSearchRecent(true)
                binding.rcvSuggest.isGone = true
            }
        }

        recentAdapter.eventListener = object : IEventListener<StructureItem> {
            override fun onClickedItem(position: Int, data: StructureItem) {
                TrackingUtil.setDataTracking(screenValue = TrackingUtil.screenSearch, nameBlockVal = "trending", indexBlockVal = -1)
                TrackingUtil.keyword = ""
                TrackingUtil.currentAppId = TrackingUtil.appSearch
                TrackingUtil.currentAppName = TrackingUtil.appSearch
                TrackingUtil.resetPosition()
                findNavController().navigateUp()
                checkBeforePlayUtil.navigateToSelectedContent(data)
            }
        }

        errorBinding.btnRetry.setOnClickListener {
//            retryCount += 1
//            if (retryCount > MAX_RETRY_COUNT) {
//                errorBinding.btnRetry.alpha = 0.7f
//                errorBinding.btnRetry.isClickable = false
//            } else {
            if (NetworkUtils.isNetworkAvailable()) {
                if (errorType == SearchUtils.API_ERROR) {
                    viewModel.dispatchIntent(
                        SearchViewModel.SearchIntent.GetSearchResult(
                            query = query,
                            pageSize = RESULT_PAGE_SIZE,
                            pageIndex = 1,
                            category = "",
                            isTablet = <EMAIL>().isTablet(),
                            position = 0,
                            isForce = true
                        )
                    )
                } else {
                    bindData()
                }
            } else {
                showErrorByInternetLayout(errorType = SearchUtils.API_ERROR)
            }
//            }
        }
    }

    private fun clearSearchResult() {
        blockAdapter.clearData()
    }


    private fun handleClickSearchItem(data: SearchItemV2, category: String) {
        AdjustAllEvent.dataCur.contentId = data.id
        AdjustAllEvent.dataCur.contentName = data.titleVie
        AdjustAllEvent.dataCur.genre = ""
        AdjustAllEvent.sendSuggestClickEvent(query)
        TrackingUtil.setDataTracking(screenValue = TrackingUtil.screenSearch, nameBlockVal = category, indexBlockVal = -1)
        TrackingUtil.keyword = query
        TrackingUtil.currentAppId = TrackingUtil.appSearch
        TrackingUtil.currentAppName = TrackingUtil.appSearch
        TrackingUtil.resetPosition()
        TrackingUtil.setTrackingKey(TrackingUtil.TrackingKey.NONE)
        checkBeforePlayUtil.navigateToSelectedContent(data)
    }

    private fun updateItemDecoration() {
        binding.rcvResult.apply {
            for (itemDecorationIndex in 0 until itemDecorationCount) {
                removeItemDecorationAt(itemDecorationIndex)
            }
            addItemDecoration(blockDecoration)
        }
    }

    private fun bindDataListResult(searchResult: SearchV2, category: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            val blockList = searchResult.mapToBlockList(hasItemPin = category == CATEGORY_ALL)
            withContext(Dispatchers.Main) {
                blockAdapter.bind(
                    blockList
                )
            }
        }
    }

    private fun closeKeyBoard() {
        binding.editSearch.clearFocus()
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(requireView().windowToken, 0)
    }

    private fun showKeyBoard() {
        if (isRestore) return
        binding.editSearch.postDelayed({
            binding.editSearch.requestFocus()
            val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.showSoftInput(binding.editSearch, InputMethodManager.SHOW_IMPLICIT)
        },400)
    }

    private fun hideSearchRecent(flag: Boolean) {
        binding.rcvRecentSearch.isGone = flag
        binding.tvRecent.isGone = flag
        binding.tvSearchTrending.isGone = flag
        binding.rcvSearchTrending.isGone = flag
        binding.ncv.isGone = flag
    }

    private fun hideSearchCategory(flag: Boolean = true) {
        binding.rcvCategorySearchResults.isGone = flag
        if (flag) {
            binding.rcvSubCategorySearchResults.slideUp(requireContext(), 0)
        } else {
            binding.rcvSubCategorySearchResults.slideDown(requireContext(), 0)
        }
    }

    private fun hideSearchResult(flag: Boolean) {
        binding.rcvSuggest.isGone = flag.not()
        binding.nsvResult.isGone = flag
    }

    fun View.slideUp(context: Context, duration: Long = -1) {
        if (this.visibility == View.VISIBLE) {
            val slideUp = AnimationUtils.loadAnimation(context, R.anim.search_category_slide_up)
            if (duration > -1) {
                slideUp.duration = duration
            }
            this.startAnimation(slideUp)
            this.visibility = View.GONE
        }
    }

    fun View.slideDown(context: Context, duration: Long = -1) {
        if (this.visibility == View.GONE) {
            this.visibility = View.VISIBLE
            val slideDown = AnimationUtils.loadAnimation(context, R.anim.search_category_slide_down)
            if (duration > -1) {
                slideDown.duration = duration
            }
            this.startAnimation(slideDown)
        }
    }

    fun RecyclerView.onScrollToCenterAt(position: Int) {
        val centerSmoothScroller = CenterSmoothScroller(requireContext())
        centerSmoothScroller.targetPosition = position
        this.layoutManager?.startSmoothScroll(centerSmoothScroller)
    }

    private fun showErrorByInternetLayout(errorType: String) {
        showErrorLayout(errorType)
        if (_errorBinding != null) {
            ImageProxy.loadLocal(
                context = errorBinding.root.context,
                data = R.drawable.ic_no_internet,
                width = 0,
                height = 0,
                target = errorBinding.ivError
            )
            errorBinding.tvError.text = errorBinding.root.resources.getString(R.string.error_layout_title_no_internet)
            errorBinding.tvTroubleshoot.text = errorBinding.root.resources.getString(R.string.error_layout_troubleshoot)
        }
    }

    private fun showOtherErrorLayout(errorType: String) {
        showErrorLayout(errorType)
        if (_errorBinding != null) {
            ImageProxy.loadLocal(
                context = errorBinding.root.context,
                data = R.drawable.iv_mega_error,
                width = 0,
                height = 0,
                target = errorBinding.ivError
            )
            errorBinding.tvError.text = errorBinding.root.resources.getString(R.string.error_layout_title)
            errorBinding.tvTroubleshoot.text = errorBinding.root.resources.getString(R.string.error_layout_troubleshoot)
        }
    }

    private fun showErrorLayout(errorType: String) {
        if (_binding != null) {
            this.errorType = errorType
            binding.errorLayout.visibility = View.VISIBLE
        }
    }

    private fun hideErrorLayout() {
        if (_binding != null) {
            this.errorType = ""
            binding.errorLayout.visibility = View.GONE
        }
    }

    private fun showEmptyLayout() {
        if (_binding != null) {
            fromHtml(tv = emptyBinding.tvEmptyData, string = getString(R.string.search_empty,"${binding.editSearch.text}"))
            binding.emptyLayout.visibility = View.VISIBLE
        }
    }

    fun fromHtml(tv: TextView, string: String) {
        tv.text = if (Build.VERSION.SDK_INT >= 24)
            Html.fromHtml(string, Html.FROM_HTML_MODE_LEGACY)
        else
            Html.fromHtml(string)
    }

    private fun hideEmptyLayout() {
        if (_binding != null) {
            binding.emptyLayout.visibility = View.GONE
        }
    }

    private fun onShareLink(url: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, url)
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, getString(R.string.share))
        context?.startActivity(shareIntent)
    }
    private fun sendLogTrackingAction(eventName: String,detail: SearchItemV2, category: String){
        trackingProxy.sendEvent(
            InforMobile(infor = trackingInfo, logId = "516",
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screenSearch,
                event =eventName,
                itemId = detail.id,
                itemName = "",
                url = "",
                subMenuId = TrackingUtil.blockId,
                videoQuality = "auto_vip",
                isRecommend = TrackingUtil.isRecommend
            ))
    }

    override fun backHandler() {
        super.backHandler()
        viewModel.resetState()
    }
}