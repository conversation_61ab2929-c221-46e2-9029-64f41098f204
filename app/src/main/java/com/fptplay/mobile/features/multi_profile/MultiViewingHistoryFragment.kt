package com.fptplay.mobile.features.multi_profile

import android.os.Bundle
import android.text.format.DateUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.common.ui.bases.BaseFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.block.BlockItemAdapter
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.MegaErrorLayoutBinding
import com.fptplay.mobile.databinding.MultiViewingHistoryFragmentBinding
import com.fptplay.mobile.databinding.NoInternetViewBinding
import com.fptplay.mobile.databinding.ViewingHistoryNotFoundBinding
import com.fptplay.mobile.features.moments.utils.LoadMorePreloadHandler
import com.fptplay.mobile.features.multi_profile.adapter.MultiProfileViewingHistoryAdapter
import com.fptplay.mobile.features.multi_profile.adapter.ViewingHistoryHeader
import com.fptplay.mobile.features.multi_profile.utils.LoadMorePreloadViewingHistoryHandler
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemContainer
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.LoadMoreHandler
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject

@AndroidEntryPoint
class MultiViewingHistoryFragment :  BaseFragment<MultiProfileViewModel.MultiProfileState, MultiProfileViewModel.MultiProfileIntent>() {
    private var _binding: MultiViewingHistoryFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    private val viewingHistoryAdapter by lazy { MultiProfileViewingHistoryAdapter() }
    private var _clNotInternetPage: NoInternetViewBinding? = null
    private val clNotInternetPage get() = _clNotInternetPage!!
    private var _clErrorPage: MegaErrorLayoutBinding? = null
    private val clErrorPage get() = _clErrorPage!!
    private val numberOfPage by lazy { MainApplication.INSTANCE.appConfig.numItemOfPage}
    override val viewModel: MultiProfileViewModel by activityViewModels()
    private var metaPopupInfo : StructureItemContainer.Meta?  = null
    private val serverDateFormat by lazy { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }
    private val loadMoreHandler: LoadMorePreloadViewingHistoryHandler by lazy {
        LoadMorePreloadViewingHistoryHandler(
            totalItem = viewingHistoryAdapter.size() ,
            totalItemInPage = numberOfPage,
            totalItemInRow = 1,
            onScroll = { page ->
                viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.GetViewingHistory(
                    profileId = safeArgs.profileId,
                    type =  WATCHING_HISTORY_TYPE,
                    blockType = WATCHING_HISTORY_BLOCK_TYPE,
                    perPage = numberOfPage,
                    page = page,
                    watchingVersion = WATCHING_VERSION,
                    customData = CUSTOM_DATA
                ))
            }
        )
    }
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override val handleBackPressed = true
    private val safeArgs: MultiViewingHistoryFragmentArgs by navArgs()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MultiViewingHistoryFragmentBinding.inflate(inflater, container, false)
        _clNotInternetPage = NoInternetViewBinding.bind(binding.root)
        _clErrorPage = MegaErrorLayoutBinding.bind(binding.root)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _clNotInternetPage = null
        _clErrorPage = null
    }

    override fun bindData() {
        hidePageErrorNotFound()
        viewingHistoryAdapter.bind(listOf())
        viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.GetViewingHistory(
            profileId = safeArgs.profileId,
            type =  WATCHING_HISTORY_TYPE,
            blockType = WATCHING_HISTORY_BLOCK_TYPE,
            perPage = numberOfPage,
            page = 1,
            watchingVersion = WATCHING_VERSION,
            customData = CUSTOM_DATA
        ))
    }

    private fun setUIToolbar(isDisableRemove:Boolean = false) {
        if (!isDisableRemove){
            binding.toolbar.menu.clear()
            binding.toolbar.inflateMenu(R.menu.viewing_history_menu)
        }
        else{
            binding.toolbar.menu.clear()
            binding.toolbar.menu.removeItem(R.id.action_remove_viewing_history)
        }
        val menuItem = binding.toolbar.menu.findItem(R.id.action_remove_viewing_history)
        binding.toolbar.setNavigationIcon(
           ContextCompat.getDrawable(
                requireContext(),
                R.drawable.ic_arrow_left
            )
        )
        menuItem?.title = context?.getString(R.string.multi_profile_action_remove_history)
        binding.toolbar.title = getString(R.string.multi_profile_title_viewing_history)

    }

    private fun showPageErrorViewingHistory(typeErrorPage: TypeErrorPage) {
        setUIToolbar(isDisableRemove = true)
        binding.rvViewingHistory.isGone = true
        when (typeErrorPage){
            TypeErrorPage.NOT_FOUND_PAGE->{
                binding.clNotFound.show()
                binding.clErrorLayout.hide()
                binding.clErrorNoInternet.hide()
            }
            TypeErrorPage.ERROR_PAGE->{
                binding.clNotFound.hide()
                binding.clErrorLayout.show()
                binding.clErrorNoInternet.hide()
            }
            TypeErrorPage.NOT_INTERNET_PAGE->{
                binding.clNotFound.hide()
                binding.clErrorLayout.hide()
                clNotInternetPage.let {
                    it.btRetry.setBackgroundResource(R.drawable.no_internet_button_disabled_bg)
                    it.btRetry.setTextColor(ContextCompat.getColor(requireContext(), R.color.white_87))
                    it.tvDes.visible()
                    it.tvDes.text = ContextCompat.getString(requireContext(),R.string.error_layout_troubleshoot)
                    it.btToDownloadMega.gone()
                }
                binding.clErrorNoInternet.show()
            }
            else ->{

            }
        }

    }

    private fun hidePageErrorNotFound(){
        binding.apply {
            setUIToolbar(isDisableRemove = true)
            binding.rvViewingHistory.visible()
            clNotFound.hide()
            clErrorLayout.hide()
            clErrorNoInternet.hide()
        }
    }
    
    override fun bindComponent() {
        viewingHistoryAdapter.setContentType(BlockItemAdapter.ContentType.History)
        binding.rvViewingHistory.apply {
            adapter = viewingHistoryAdapter
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
        }
    }

    override fun bindEvent() {

        binding.toolbar.setNavigationOnClickListener {
            backHandler()
        }

        binding.toolbar.setOnMenuItemClickListener {
            showPopUpWarningRemove(
                title = metaPopupInfo?.btnInfo?.popupInfoTitle?:"",
                message = metaPopupInfo?.btnInfo?.popupInfoDescription?:""
            )
            false
        }

        viewingHistoryAdapter.eventListener = object : IEventListener<BaseObject> {
            override fun onClickedItem(position: Int, data: BaseObject) {
                if(data is StructureItem){
                    checkBeforePlayUtil.navigateToSelectedContent(data)
                }
            }
        }

        binding.rvViewingHistory.apply {
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    if (!recyclerView.canScrollVertically(1)) {
                        loadMoreHandler.canScroll(viewingHistoryAdapter.size() - 1)
                    }

                }
            })
        }
        clErrorPage.btnRetry.setOnClickListener {
            hidePageErrorNotFound()
            reloadPage()
        }
        clNotInternetPage.btRetry.setOnClickListener {
            hidePageErrorNotFound()
            reloadPage()
        }
    }

    private fun showPopUpWarningRemove(title:String = "", message:String = ""){
        showWarningDialog(
            textTitle = title.ifBlank { getString(R.string.multi_profile_remove_viewing_history_title)},
            message =  message.ifBlank { getString(R.string.multi_profile_remove_viewing_history_msg)},
            textConfirm = getString(R.string.multi_profile_action_remove_history),
            textClose = getString(R.string.all_cancel),
            isConfirmEnable = true,
            isOnlyConfirmButton = false,
            isExitEnable = true,
            isShowTitle = true,
            onConfirm = {
                val popupType = if (metaPopupInfo?.btnInfo?.popupInfoType.isNullOrBlank()) {
                    WATCHING_HISTORY_TYPE
                } else {
                    metaPopupInfo?.btnInfo?.popupInfoType
                } ?: WATCHING_HISTORY_TYPE
                viewModel.dispatchIntent(
                    MultiProfileViewModel.MultiProfileIntent.DeleteAllViewingHistory(
                        profileId = safeArgs.profileId,
                        type = popupType
                    )
                )
            })
    }
    private fun reloadPage(){
        loadMoreHandler.refresh(
            totalItem = viewingHistoryAdapter.size(),
            endPage = true
        )
        viewingHistoryAdapter.bind(listOf())
        bindData()
    }

    private fun List<StructureItem>.updateGroupByDate():List<BaseObject> {
        val currentDisplayList  = viewingHistoryAdapter.data().toMutableList()
        val existingHeaders = currentDisplayList.filterIsInstance<ViewingHistoryHeader>().map { it.title }.toSet()
        val newGrouped = this.groupBy { it.date.toTimeSpanViewingHistory() }

        newGrouped.forEach { (header, items) ->
            if (!existingHeaders.contains(header)) {
                currentDisplayList.add(ViewingHistoryHeader(title = header))
            }
            currentDisplayList.addAll(items)
        }

        return currentDisplayList
    }

    private fun String?.toTimeSpanViewingHistory():String {
        if (this.isNullOrBlank()) return ""
        val currentDate = serverDateFormat.parse(this) ?: return this
        val currentTime = currentDate.time
        for (i in -1..1) {
            if (DateUtils.isToday(currentTime  + i * DateUtils.DAY_IN_MILLIS)) {
                return when (i) {
                    1 -> getString(R.string.multi_profile_viewing_history_title_yesterday)
                    0 -> getString(R.string.multi_profile_viewing_history_title_now)
                    else -> DateTimeUtils.getDayMonthYearFromMillis(currentTime)
                }
            }
        }
        return DateTimeUtils.getDayMonthYearFromMillis(currentTime)
    }

    private fun updateUIViewingHistoryItem(data: List<StructureItem>,page:Int) {
        if (data.isNotEmpty()){
            if (page == 1){
                setUIToolbar(isDisableRemove = false)
            }
            viewingHistoryAdapter.bind(data = data.updateGroupByDate()){
                loadMoreHandler.refresh(
                    addToFirst = page == 1,
                    totalItem = viewingHistoryAdapter.size(),
                    endPage = data.size < numberOfPage
                )
            }
        }
        else{
            loadMoreHandler.refresh(
                totalItem = viewingHistoryAdapter.size(),
                endPage = true
            )
            if (page == 1){
                showPageErrorViewingHistory(typeErrorPage = TypeErrorPage.NOT_FOUND_PAGE)
            }
        }
    }

    override fun MultiProfileViewModel.MultiProfileState.toUI() {
        when (this) {

            is MultiProfileViewModel.MultiProfileState.Loading ->{
                showLoading()
            }

            is MultiProfileViewModel.MultiProfileState.ResultDeleteAllViewingHistory -> {
                if (status == "1") {
                    viewingHistoryAdapter.bind(listOf())
                    bindData()
                } else showSnackbar(text = message)
            }

            is MultiProfileViewModel.MultiProfileState.ResultViewingHistoryStructureItem -> {
                hideLoading()
                metaPopupInfo = data.meta
                updateUIViewingHistoryItem(data = data.listStructureItem, page)
            }

            is MultiProfileViewModel.MultiProfileState.ErrorNoInternet -> {
                if(this.intent is MultiProfileViewModel.MultiProfileIntent.GetViewingHistory){
                    hideLoading()
                    if(intent.page == 1) {
                        showPageErrorViewingHistory(typeErrorPage = TypeErrorPage.NOT_INTERNET_PAGE)
                    }
                }
                if(this.intent is MultiProfileViewModel.MultiProfileIntent.DeleteAllViewingHistory){
                    hideLoading()
                    showSnackbar(text = message)
                }
            }

            is MultiProfileViewModel.MultiProfileState.ErrorRequiredLogin -> {
                navigateToLoginWithParams(requestRestartApp = true)
            }

            is MultiProfileViewModel.MultiProfileState.Done ->{
                hideLoading()
            }

            is MultiProfileViewModel.MultiProfileState.Error ->{
                if(this.intent is MultiProfileViewModel.MultiProfileIntent.GetViewingHistory){
                    hideLoading()
                    if(intent.page == 1) {
                        showPageErrorViewingHistory(typeErrorPage = TypeErrorPage.ERROR_PAGE)
                    }
                }
                else{
                    showSnackbar(text = message)
                }
                loadMoreHandler.refresh(
                    totalItem = viewingHistoryAdapter.size(),
                    endPage = false
                )
            }
            else -> {

            }
        }
    }
    companion object {
        const val WATCHING_HISTORY_TYPE = "history_view"
        const val WATCHING_HISTORY_BLOCK_TYPE = "horizontal_slider"
        const val CUSTOM_DATA = ""
        const val WATCHING_VERSION = "v1"
    }
}
enum class TypeErrorPage{
    NOT_INTERNET_PAGE,
    ERROR_PAGE,
    NOT_FOUND_PAGE
}