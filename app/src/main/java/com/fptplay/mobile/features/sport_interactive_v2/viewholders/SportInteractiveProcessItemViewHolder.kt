package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import androidx.core.content.ContextCompat
import com.fptplay.mobile.databinding.SportInteractiveMatchProcessItemBinding
import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.match_process.SportInteractiveMatchProcessItem
import com.tear.modules.util.Utils.checkToShowContent
import com.tear.modules.util.Utils.hide
import com.tear.modules.util.Utils.show

class SportInteractiveProcessItemViewHolder(private val binding: SportInteractiveMatchProcessItemBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveMatchProcessItem>(binding) {
    override fun bind(data: SportInteractiveMatchProcessItem) {
        when (data.teamType) {
            TeamType.AwayTeam -> showAwayTeamDetail(data)
            TeamType.HomeTeam -> showHomeTeamDetail(data)
            else -> hideAll()
        }
    }

    private fun showHomeTeamDetail(process: SportInteractiveMatchProcessItem) {
        binding.apply {
            ctlAwayTeam.hide()

            tvHomeTime.checkToShowContent(process.time, goneViewWhenNoText = true)

            tvHomePlayerDescription.checkToShowContent(
                process.description,
                goneViewWhenNoText = true
            )
            tvHomePlayerName.checkToShowContent(process.playerName, goneViewWhenNoText = true)
            tvHomeTransferName.checkToShowContent(
                process.transferPlayerName,
                goneViewWhenNoText = true
            )


            if (process.action.icon != -1) {
                ivHomeIcon.background =
                    ContextCompat.getDrawable(root.context, process.action.icon)
                ivHomeIcon.show()
            } else {
                ivHomeIcon.hide()
            }

            ctlHomeTeam.show()
        }
    }

    private fun showAwayTeamDetail(process: SportInteractiveMatchProcessItem) {
        binding.apply {
            ctlHomeTeam.hide()

            tvAwayTime.checkToShowContent(process.time, goneViewWhenNoText = true)

            tvAwayPlayerDescription.checkToShowContent(
                process.description,
                goneViewWhenNoText = true
            )
            tvAwayPlayerName.checkToShowContent(process.playerName, goneViewWhenNoText = true)
            tvAwayTransferName.checkToShowContent(
                process.transferPlayerName,
                goneViewWhenNoText = true
            )


            if (process.action.icon != -1) {
                ivAwayIcon.background =
                    ContextCompat.getDrawable(root.context, process.action.icon)
                ivAwayIcon.show()
            } else {
                ivAwayIcon.hide()
            }

            ctlAwayTeam.show()
        }
    }

    private fun hideAll() {
        binding.apply {
            ctlHomeTeam.hide()
            ctlAwayTeam.hide()
        }
    }
}