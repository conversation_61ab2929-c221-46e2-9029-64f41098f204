package com.fptplay.mobile.features.login.fragmentV2

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.clearFragmentResultListener
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.paris.extensions.style
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.databinding.LoginListDeviceFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.login.LoginFragment
import com.fptplay.mobile.features.login.LoginViewModelV2
import com.fptplay.mobile.features.login.adapter.LoginListDeviceAdapter
import com.fptplay.mobile.features.login.utils.LoginUtilsV2
import com.fptplay.mobile.features.qrcode.QRCodeViewModel.QRCodeIntent.QRCodeLogin
import com.fptplay.mobile.features.qrcode.QRCodeViewModel.QRCodeState.ErrorRequiredLogin
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Constant
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.invisible
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.loginv2.DeviceListEntity
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class LoginListDeviceFragment :
    BaseFragment<LoginViewModelV2.LoginState, LoginViewModelV2.LoginIntent>() {
    override val viewModel: LoginViewModelV2 by activityViewModels()
    private var _binding: LoginListDeviceFragmentBinding? = null

    private val safeArgs: LoginListDeviceFragmentArgs by navArgs()
    private val binding get() = _binding!!
    override val handleBackPressed: Boolean = true

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor
    private lateinit var loginUtilsV2: LoginUtilsV2

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var loginListDeviceAdapter = LoginListDeviceAdapter()
    private var listId: ArrayList<String> = arrayListOf()
    private var verifyTokenDevice = ""

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LoginListDeviceFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun backHandler() {
        if (!findNavController().popBackStack(R.id.login_otp_fragment, true))
            findNavController().navigateUp()

        loginUtilsV2.sendLogLoginFIDAndOTP(
            logId = "145",
            phone = viewModel.phone,
            screen = "LimitDevice",
            event = "CancelLogin",
            method = viewModel.method
        )
        if (safeArgs.callFromQrCode) {
            setFragmentResult(Constants.LOGIN_DELETE_DEVICE, bundleOf(Constants.LOGIN_DELETE_DEVICE_STATUS to Constants.LOGIN_DELETE_DEVICE_STATUS_FAIL))
        }
    }
    private fun backWhenLoginSuccess(){
        if (!findNavController().popBackStack(R.id.login_otp_fragment, true))
            findNavController().navigateUp()

        loginUtilsV2.sendLogLoginFIDAndOTP(
            logId = "145",
            phone = viewModel.phone,
            screen = "LimitDevice",
            event = "CancelLogin",
            method = viewModel.method
        )
        if (safeArgs.callFromQrCode) {
            setFragmentResult(Constants.LOGIN_DELETE_DEVICE, bundleOf(Constants.LOGIN_DELETE_DEVICE_STATUS to Constants.LOGIN_DELETE_DEVICE_STATUS_ON_LOGIN))
        }
    }

    override fun setUpEdgeToEdge() {}

    override fun initData() {
        loginUtilsV2 = LoginUtilsV2(trackingProxy, trackingInfo)
        when (safeArgs.typeShow) {
            LoginViewModelV2.Status.ERROR_NEED_DELETE_DEVICE -> {
                showAllWhiteList(false)
                showErrorView(false)
                viewModel.dispatchIntent(LoginViewModelV2.LoginIntent.GetDeviceList(safeArgs.verifyToken))
            }

            LoginViewModelV2.Status.ERROR_SUB_DEBT -> {
                showSubDebt(true, safeArgs.title, safeArgs.description)
                loginUtilsV2.sendLogLoginFIDAndOTP(
                    logId = "150",
                    phone = viewModel.phone,
                    screen = "Fail",
                    event = "Other",
                    errorMes = "ERROR_SUB_DEBT: " + safeArgs.description,
                    method = viewModel.method
                )
                //loginUtilsV2.sendLogLogin(false, back = false, errorMes = safeArgs.description, phone = viewModel.phone,method = viewModel.method)
            }

            LoginViewModelV2.Status.ERROR_FULL_WHITE_LIST -> {
                showAllWhiteList(true, safeArgs.title, safeArgs.description)
                loginUtilsV2.sendLogLoginFIDAndOTP(
                    logId = "150",
                    phone = viewModel.phone,
                    screen = "Fail",
                    event = "Other",
                    errorMes = "ERROR_FULL_WHITE_LIST: " + safeArgs.description,
                    method = viewModel.method
                )
                //loginUtilsV2.sendLogLogin(false, back = false, errorMes = safeArgs.description, phone = viewModel.phone,method = viewModel.method)
            }

            else -> {
                showErrorView(true, safeArgs.title, safeArgs.description)
            }
        }

    }

    private fun bindDescription(description: String, subDescription: String) {
        if (description.isNotBlank()) {
            binding.tvDesTop.text = description
            binding.tvDesTop.visible()
        } else {
            binding.tvDesTop.gone()
        }
        if (subDescription.isNotBlank()) {
            binding.tvDesBottom.text = subDescription
            binding.tvDesBottom.visible()
        } else {
            binding.tvDesBottom.gone()
        }
    }

    private fun bindListDevice(listDevices: List<DeviceListEntity.Data.Device>, needRemove: Int) {
        listId.clear()
        changeButtonContinueStatus(listDeviceIdToRemove = listId, needRemove = needRemove)
        loginListDeviceAdapter.eventListener =
            object : IEventListener<DeviceListEntity.Data.Device> {
                override fun onClickView(
                    position: Int,
                    view: View?,
                    data: DeviceListEntity.Data.Device
                ) {
                    if (view is CheckBox) {
                        if (view.isChecked) {
                            listId.add(data.id)
                        } else {
                            listId.remove(data.id)
                        }
                        changeButtonContinueStatus(listId, needRemove)
                    }
                }

            }
        binding.rvListDevice.apply {
            adapter = loginListDeviceAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            loginListDeviceAdapter.bind(listDevices)
        }
    }

    override fun bindEvent() {
        binding.apply {
            tvExit.setOnClickListener {
                backHandler()
            }
            btnContinue.setOnClickListener {
                viewModel.dispatchIntent(
                    LoginViewModelV2.LoginIntent.RemoveDevice(
                        listIds = listId,
                        verifyToken = verifyTokenDevice,
                        ignoreToken = if (safeArgs.callFromQrCode) "1" else "0",
                        requiredLogin = if (safeArgs.callFromQrCode) "1" else "0"
                    )
                )
            }
        }
    }

    private fun changeButtonContinueStatus(listDeviceIdToRemove: List<String>, needRemove: Int) {
        binding.btnContinue.isEnabled = listDeviceIdToRemove.size >= needRemove
    }

    private fun checkIsAllWhiteList(listDevices: List<DeviceListEntity.Data.Device>): Boolean {
        var result = true
        if (safeArgs.callFromQrCode) {
            for (device in listDevices) {
                if (device.isCurrent) device.isWhitelist = true
                if (!device.isWhitelist) {
                    result = false
                }
            }
        } else {
            for (device in listDevices) {
                if (!device.isWhitelist) {
                    result = false
                }
            }
        }
        return result
    }


    override fun onDestroyView() {
        Logger.d("onDestroyView")
        _binding = null
        super.onDestroyView()
    }

    private fun showAllWhiteList(show: Boolean, title: String = "", description: String = "") {
        if (show) {
            binding.apply {
                tvTitle.invisible()
                tvExit.invisible()
                tvTitleAllWhitelist.text = title
                tvDesAllWhiteList.text = description
                ivAllWhitelist.setBackgroundResource(R.drawable.ic_all_is_whitelist)
                btnExit.text = getString(R.string.all_string_known)
                btnExit.setOnClickListener {
                    backHandler()
                }
                llAllWhitelist.visible()
            }
        } else {
            binding.apply {
                llAllWhitelist.gone()
                tvTitle.visible()
                tvExit.visible()
            }

        }
    }

    private fun showSubDebt(show: Boolean, title: String = "", description: String = "") {
        if (show) {
            binding.apply {
                tvTitle.invisible()
                tvExit.invisible()
                tvTitleAllWhitelist.text = title
                tvDesAllWhiteList.text = description
                ivAllWhitelist.setBackgroundResource(R.drawable.ic_sub_debt)
                btnExit.text = getString(R.string.all_string_known)
                btnExit.setOnClickListener {
                    backHandler()
                }
                llAllWhitelist.visible()
            }
        } else {
            binding.apply {
                llAllWhitelist.gone()
                tvTitle.visible()
                tvExit.visible()
            }

        }
    }

    private fun showErrorView(show: Boolean, title: String = "", description: String = "") {
        if (show) {
            binding.apply {
                tvTitle.visible()
                tvExit.visible()
                tvTitleAllWhitelist.text = title
                tvDesAllWhiteList.text = description
                ivAllWhitelist.setBackgroundResource(R.drawable.login_error_expire)
                btnExit.text = getString(R.string.login_button_token_expire)
                btnExit.style(R.style.LoginButtonRetryErrorView)
                btnExit.setOnClickListener {
                    viewModel.dispatchIntent(LoginViewModelV2.LoginIntent.GetDeviceList(safeArgs.verifyToken))
                    showErrorView(false)
                }
                llAllWhitelist.visible()
            }
        } else {
            binding.apply {
                llAllWhitelist.gone()
                tvTitle.visible()
                tvExit.visible()
            }

        }
    }

    private fun showPopUpTokenExpire(title: String, description: String) {
        loginUtilsV2.showNotifyDialog(
            parentFragmentManager = parentFragmentManager,
            title = title.ifBlank { getString(R.string.login_title_token_expire) },
            des = description.ifBlank { getString(R.string.login_des_token_expire) },
            buttonRight = getString(R.string.all_string_known),
            onNotifyButtonRightClickListener = {
                backHandler()
            }
        )
    }

    override fun LoginViewModelV2.LoginState.toUI() {
        when (this) {
            is LoginViewModelV2.LoginState.Loading -> {
                showLoading()
            }

            is LoginViewModelV2.LoginState.Done -> {
                when (data) {
                    is LoginViewModelV2.LoginIntent.RemoveDevice,
                    is LoginViewModelV2.LoginIntent.GetDeviceList -> {
                        hideLoading()
                    }

                    else -> {}
                }
            }

            is LoginViewModelV2.LoginState.ManyRequest -> {
                hideLoading()
                showErrorView(true, title = getString(R.string.login_notify_title), description = message)
                loginUtilsV2.showNotifyDialog(
                    parentFragmentManager = parentFragmentManager,
                    title = getString(R.string.login_notify_title),
                    des = message,
                    timeCountdown = seconds,
                    buttonRight = getString(R.string.login_close)
                )
            }

            is LoginViewModelV2.LoginState.Error -> {
                hideLoading()
                if (data is LoginViewModelV2.LoginIntent.GetDeviceList) {
                    showErrorView(true, getString(R.string.login_notify_title), message)
                } else {

                    when (data) {
                        is LoginViewModelV2.LoginIntent.GetPackageUser -> {
                            loginUtilsV2.sendLogLoginFIDAndOTP(
                                logId = "150",
                                phone = viewModel.phone,
                                screen = "Fail",
                                event = "Other",
                                errorMes = "CheckAPIGetPackageUser : $message",
                                method = viewModel.method
                            )
                            setFragmentResult(
                                LoginFragment.LOGIN_RESULT_REQUEST_KEY,
                                bundleOf(LoginFragment.LOGIN_RESULT_BUNDLE_KEY to true)
                            )
                        }

                        is LoginViewModelV2.LoginIntent.GetInfoUser -> {
                            Utils.clearUserData(sharedPreferences)
                            loginUtilsV2.showNotifyDialog(
                                parentFragmentManager = parentFragmentManager,
                                title = getString(R.string.login_title_not_success),
                                des = message,
                                buttonRight = getString(R.string.login_close),
                                onNotifyButtonRightClickListener = {
                                    try {
                                        findNavController().popBackStack(
                                            findNavController().graph.startDestinationId,
                                            false
                                        )
                                    } catch (ex: Exception) {
                                        ex.printStackTrace()
                                    }
                                }
                            )

                            loginUtilsV2.sendLogLogin(result = false, back = false, errorMes = message, phone = viewModel.phone, method = viewModel.method)
                            loginUtilsV2.sendLogLoginFIDAndOTP(
                                logId = "150",
                                phone = viewModel.phone,
                                screen = "Fail",
                                event = "CheckAPIGetUserInfo",
                                errorMes = "CheckAPIGetUserInfo : $message",
                                method = viewModel.method
                            )
                        }

                        else -> {
                            loginUtilsV2.showNotifyDialog(
                                parentFragmentManager = parentFragmentManager,
                                title = getString(R.string.login_notify_title),
                                des = message,
                                buttonRight = getString(R.string.login_close)
                            )
                        }
                    }
                }
            }

            is LoginViewModelV2.LoginState.ResultGetDeviceList -> {
                if (data.status == LoginViewModelV2.Status.IS_SUCCESS) {
                    if(!safeArgs.callFromQrCode) AdjustAllEvent.sendLimitPopupDisplayEvent(userPhone = viewModel.phone, deviceCount = data.data.devices.size)
                    if (data.data.devices.isEmpty()) {
                        showErrorView(true, data.data.title, data.data.description)
                        return
                    }
                    if (checkIsAllWhiteList(data.data.devices)) {
                        showAllWhiteList(true, data.data.title, data.data.description)
                    } else {
                        showAllWhiteList(false)
                        verifyTokenDevice = data.data.verifyToken
                        bindListDevice(data.data.devices, data.data.needRemove.toInt())
                        bindDescription(data.data.description, data.data.subDescription)
                    }
                } else if (data.errorCode == LoginViewModelV2.Status.ERROR_TOKEN_EXPIRE) {
                    showErrorView(true, data.data.title, data.message)
                    showPopUpTokenExpire(data.data.title, data.message)
                } else {
                    showErrorView(true, data.data.title, data.message)
                }
            }

            is LoginViewModelV2.LoginState.ResultRemoveDevice -> {
                if (data.status == LoginViewModelV2.Status.IS_SUCCESS) {
                    if(!safeArgs.callFromQrCode) {
                        AdjustAllEvent.sendDeviceRemovedEvent(
                            userPhone = viewModel.phone,
                            deviceDeleteId = listId.joinToString(","),
                            deviceCount = loginListDeviceAdapter.size() - listId.size
                        )
                    }
                    //save data
                    if (safeArgs.callFromQrCode) {
                        val bundle = bundleOf(Constants.LOGIN_DELETE_DEVICE_STATUS to Constants.LOGIN_DELETE_DEVICE_STATUS_SUCCESS)
                        setFragmentResult(Constants.LOGIN_DELETE_DEVICE, bundle)
                        findNavController().navigateUp()
                        return
                    }
                    sharedPreferences.apply {
                        saveUserLogin(true)
                        saveAccessToken(data.accessToken)
                        saveAccessTokenType(data.accessTokenType)
                        setLinkingToken("")
                    }
                    showLoading()
                    viewModel.dispatchIntent(LoginViewModelV2.LoginIntent.GetInfoUser)
                    triggerZendesk()
                } else {
                    when (data.errorCode) {
                        LoginViewModelV2.Status.ERROR_NEED_RELOAD_LIST_DEVICE -> {
                            loginUtilsV2.showNotifyDialog(
                                parentFragmentManager = parentFragmentManager,
                                title = data.title.ifBlank { getString(R.string.login_notify_title) },
                                des = data.message,
                                buttonRight = getString(R.string.login_retry),
                                onNotifyButtonRightClickListener = {
                                    viewModel.dispatchIntent(LoginViewModelV2.LoginIntent.GetDeviceList(safeArgs.verifyToken))
                                }
                            )
                        }

                        LoginViewModelV2.Status.ERROR_TOKEN_EXPIRE -> {
                            showPopUpTokenExpire(data.title, data.message)
                        }

                        LoginViewModelV2.Status.ERROR_LOGOUT -> {
                            loginUtilsV2.showNotifyDialog(
                                parentFragmentManager = parentFragmentManager,
                                title = data.title.ifBlank { getString(R.string.logout_error_title) },
                                des = data.message,
                                buttonRight = getString(R.string.login_retry),
                            )
                        }

                        else -> {
                            loginUtilsV2.showNotifyDialog(
                                parentFragmentManager = parentFragmentManager,
                                title = data.title.ifBlank { getString(R.string.login_notify_title) },
                                des = data.message,
                                buttonRight = getString(R.string.login_close)
                            )
                        }
                    }
                    loginUtilsV2.sendLogLoginFIDAndOTP(
                        logId = "150",
                        phone = viewModel.phone,
                        screen = "Fail",
                        event = "Other",
                        errorMes = "CheckAPIRemoveDevice : ${data.message}",
                        method = viewModel.method
                    )
                    // loginUtilsV2.sendLogLogin(result = false, back = false, errorMes = data.message, phone = viewModel.phone,method = viewModel.method)
                }
            }

            is LoginViewModelV2.LoginState.ResultUserInfo -> {
                hideLoading()
                Utils.pushUserProfileCleverTap(requireContext(), data.id)
                showLoading()
                viewModel.dispatchIntent(
                    LoginViewModelV2.LoginIntent.GetPackageUser(
                        data.id ?: "",
                        userInfo = data
                    )
                )
                loginUtilsV2.sendLogLogin(true, token = sharedPreferences.accessToken(), back = false, phone = viewModel.phone, method = viewModel.method)
                loginUtilsV2.sendLogLoginFIDAndOTP(
                    logId = "149",
                    phone = viewModel.phone,
                    screen = "Success",
                    event = "ThirdPartyRespond",
                    token = sharedPreferences.accessToken(),
                    method = viewModel.method
                )
                TrackingGA4Proxy.sendTrackingEvent(TrackingGA4Constant.EVENT_LOGIN_SUCCESS, HashMap())
                // Zendesk
                registerNotificationZendesk()
            }

            is LoginViewModelV2.LoginState.ResultPackageUser -> {
                hideLoading()
                setFragmentResult(
                    LoginFragment.LOGIN_RESULT_REQUEST_KEY,
                    bundleOf(LoginFragment.LOGIN_RESULT_BUNDLE_KEY to true)
                )
            }

            is LoginViewModelV2.LoginState.ErrorRequiredLogin -> {
                if(intent is LoginViewModelV2.LoginIntent.RemoveDevice){
                    if(safeArgs.callFromQrCode){
                        navigateToLoginWithParams(isDirect = false)
                        clearFragmentResultListener(Constants.LOGIN_SUCCESS)
                        setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
                            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
                            if (isSuccess) {
                                backWhenLoginSuccess()
                            }
                        }
                    }
                }
            }

            else -> {}
        }
    }

    //region Zendesk
    private fun registerNotificationZendesk() {
        ZendeskUtils.registerNotificationZendesk(userId = sharedPreferences.userId())
    }

    private fun triggerZendesk() {
        // Init Zendesk
        ZendeskUtils.initBaseZendeskIfNeed(context = context)
        ZendeskUtils.updateZendeskIdentity(
            userLogin = true,
            userToken = sharedPreferences.accessToken(),
            userTokenType = sharedPreferences.accessTokenType()
        )
    }
    //endregion

}