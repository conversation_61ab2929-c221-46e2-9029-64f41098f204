package com.fptplay.mobile.features.sport_interactive_v2.models.match_process

import com.fptplay.mobile.features.sport_interactive_v2.models.UIData

data class SportInteractiveMatchProcessTitle(
    val title: String = "",
): UIData() {
    override fun areContentTheSame(newItem: UIData): Bo<PERSON>an {
        return super.areContentTheSame(newItem)
    }

    override fun areItemTheSame(newItem: UIData): Boolean {
        return super.areItemTheSame(newItem)
    }
}
