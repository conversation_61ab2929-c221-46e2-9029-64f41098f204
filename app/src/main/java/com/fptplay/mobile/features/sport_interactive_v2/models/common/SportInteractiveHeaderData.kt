package com.fptplay.mobile.features.sport_interactive_v2.models.common

import com.fptplay.mobile.features.sport_interactive.model.TeamType
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class SportInteractiveHeaderData constructor(
    val headerType: HeaderType = HeaderType.WITH_SCORE,
    val info: SportInteractiveHeader = SportInteractiveHeader(),
    val listScores: List<SportInteractiveScoredAuthors> = arrayListOf()
) : UIData() {

    data class SportInteractiveHeader(
        //common
        val time: String = "",
        //home team
        val homeTeamName: String = "",
        val homeCoach: String = "",
        val homeColor: String = "",
        val homeLogo: String = "",
        val homeScore: String = "",
        val homeShortName: String = "",

        // away team info (đội khách)
        val awayTeamName: String = "",
        val awayCoach: String = "",
        val awayColor: String = "",
        val awayLogo: String = "",
        val awayScore: String = "",
        val awayShortName: String = ""
    )

    data class SportInteractiveScoredAuthors(
        val teamType: TeamType = TeamType.Unknown(""),
        val playerName: String = "",
        val time: String = ""
    ): BaseObject()

    override fun areItemTheSame(newItem: UIData): Boolean {
        return this.info.homeTeamName == (newItem as SportInteractiveHeaderData).info.homeTeamName &&
                this.info.awayTeamName == newItem.info.awayTeamName
    }

    enum class HeaderType { WITH_SCORE, WITH_COACH, WITHOUT_SCORE, TAB_STYLE }
}