package com.fptplay.mobile.features.categories.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.CategoryItemBinding
import com.fptplay.mobile.databinding.CategoryOfCategoryItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.TabMenu
import com.xhbadxx.projects.module.util.image.ImageProxy

class CategoryOfCategoriesAdapter :
    BaseAdapter<StructureItem, CategoryOfCategoriesAdapter.CategoryViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): CategoryOfCategoriesAdapter.CategoryViewHolder {
        return CategoryViewHolder(
            CategoryOfCategoryItemBinding.bind(
                LayoutInflater.from(parent.context).inflate(R.layout.category_of_category_item, parent, false)
            )
        )
    }

    override fun onBindViewHolder(holder: CategoryOfCategoriesAdapter.CategoryViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    inner class CategoryViewHolder(private val viewBinding: CategoryOfCategoryItemBinding) :
        RecyclerView.ViewHolder(viewBinding.root) {
        val width by lazy {
            Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.category_width)
        }
        val height by lazy {
            Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.category_height)
        }

        init {
            viewBinding.root.setOnClickListener {
                eventListener?.onClickedItem(
                    absoluteAdapterPosition,
                    item(absoluteAdapterPosition)!!
                )
            }
        }

        fun bind(data: StructureItem) {
            ImageProxy.load(
                context = viewBinding.root.context,
                width = width,
                height = height,
                target = viewBinding.ivThumb,
                url = data.horizontalImage,
            )
        }
    }
}