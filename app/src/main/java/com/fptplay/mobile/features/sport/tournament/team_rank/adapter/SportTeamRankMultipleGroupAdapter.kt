package com.fptplay.mobile.features.sport.tournament.team_rank.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.databinding.SportTeamRankMultipleGroupItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemLeagueRankingItem
import com.xhbadxx.projects.module.util.image.ImageProxy

class SportTeamRankMultipleGroupAdapter : BaseAdapter<StructureItemLeagueRankingItem, SportTeamRankMultipleGroupAdapter.SportTeamRankMultipleGroupViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SportTeamRankMultipleGroupViewHolder {
        return SportTeamRankMultipleGroupViewHolder(
            SportTeamRankMultipleGroupItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        )
    }

    override fun onBindViewHolder(holder: SportTeamRankMultipleGroupViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    inner class SportTeamRankMultipleGroupViewHolder(private val binding: SportTeamRankMultipleGroupItemBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(data: StructureItemLeagueRankingItem) {
            binding.apply {
                tvRank.text = data.position
                tvTeamName.text = data.team
                tvMatch.text = data.played
                tvGd.text = data.goalDifference
                tvScore.text = data.point

                ImageProxy.loadLocal(
                    context = itemView.context,
                    data = data.logo,
                    width = 0,
                    height = 0,
                    target = ivTeamLogo,
                    placeHolderId = R.drawable.ic_default_logo_team,
                    errorDrawableId = R.drawable.ic_default_logo_team

                )
            }

            if (absoluteAdapterPosition % 2 == 0) {
                itemView.setBackgroundColor(Color.parseColor("#08FFFFFF"))
            } else {
                itemView.setBackgroundColor(Color.TRANSPARENT)
            }
        }
    }
}