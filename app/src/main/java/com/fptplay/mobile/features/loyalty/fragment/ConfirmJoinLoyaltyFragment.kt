package com.fptplay.mobile.features.loyalty.fragment

import android.content.pm.ActivityInfo
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.NavLoyaltyDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClick
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.FragmentConfirmJoinLoyaltyBinding
import com.fptplay.mobile.features.loyalty.utils.CheckNavigateLoyaltyUtils
import com.fptplay.mobile.features.loyalty.utils.PackageLoyaltyValidationViewModel
import com.fptplay.mobile.features.loyalty.utils.UserInfoLoyaltyUtils
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmJoinLoyaltyFragment : BaseFragment<PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState, PackageLoyaltyValidationViewModel.PackageLoyaltyValidationIntent>() {
    private var _binding: FragmentConfirmJoinLoyaltyBinding? = null
    override val hasEdgeToEdge: Boolean = true
    private val binding get() = _binding!!
    override val viewModel: PackageLoyaltyValidationViewModel by activityViewModels()
    private var firstInit = true
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentConfirmJoinLoyaltyBinding.inflate(layoutInflater)
        return binding.root
    }
    override fun bindData() {
        if(sharedPreferences.userLogin()){
            //
            if (context.isTablet()) {
                activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
            //

            viewModel.dispatchIntent(PackageLoyaltyValidationViewModel.PackageLoyaltyValidationIntent.GetUserInfor)
        }else{
            if(firstInit) {
                navigateToLoginWithParams(isDirect = true)
            } else {
                findNavController().navigateUp()
            }
        }
        firstInit = false
    }
    private fun isEKYC() = UserInfoLoyaltyUtils.getUserInfoLoyalty()?.isEkyc == CheckNavigateLoyaltyUtils.STATUS_HAVE_LOYAL
    private fun isLoyalty() = UserInfoLoyaltyUtils.getUserInfoLoyalty()?.isLoyalty == CheckNavigateLoyaltyUtils.STATUS_HAVE_LOYAL
    override fun bindComponent() {
        binding.apply {
            btnOption1.onClick(200) {
                if(isEKYC()){
                    if(!isLoyalty()){
                        viewModel.dispatchIntent(PackageLoyaltyValidationViewModel.PackageLoyaltyValidationIntent.UserJoinLoyalty)
                    }
                }
                else{
                    navigateLoyaltyEKYS()
                }
            }
            btnOption2.onClick {
                findNavController().navigateUp()
            }
        }
    }
    override fun bindEvent() {
        setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            if (isSuccess) {
                //
                if (context.isTablet()) {
                    activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                }
                //
                viewModel.dispatchIntent(PackageLoyaltyValidationViewModel.PackageLoyaltyValidationIntent.GetUserInfor)
            }else{
                findNavController().navigateUp()
            }
        }
    }
    private fun navigateLoyaltyBusiness(title:String?,description:String?,url:String){
        findNavController()?.navigate(
            NavLoyaltyDirections.actionGlobalToPedingJoinLoyalty(
                type = Utils.TYPE_ERROR,
                title = title?:requireContext().getString(R.string.text_title_pending_join_loyal),
                description = description?:requireContext().getString(R.string.text_description_business_ekyc),
                url = url,
                type1 = Utils.TYPE_UNDERSTAND,
                isEkyc = true

            ))
    }
    private fun navigateLoyaltyErrorEKYC(title:String?,description:String?,url:String){
        findNavController()?.navigate(
            NavLoyaltyDirections.actionGlobalToPedingJoinLoyalty(
                type = Utils.TYPE_ERROR,
                title = title?: requireContext().getString(R.string.e_title_verify_wrong),
                description = description?:requireContext().getString(R.string.e_description_verify_wrong),
                url = url,
                type1 = Utils.TYPE_ACCESS,
                type2 = Utils.TYPE_RETRY,
                isEkyc = true
            ))
    }
    private fun navigateLoyaltyHome(){
        findNavController().apply {
            navigate(ConfirmJoinLoyaltyFragmentDirections.actionConfirmJoinLoyaltyFragmentToLoyaltyHomeFragment())
        }
    }
    private fun navigateLoyaltyEKYS(){
        findNavController()
            ?.navigate(
                NavLoyaltyDirections.actionGlobalToNavEkyc()
            )
    }
    private fun navigateLoyaltyPending(title:String?,description:String?){
        findNavController()?.navigate(NavLoyaltyDirections.actionGlobalToPedingJoinLoyalty(
            type = Utils.TYPE_SUCCESS,
            title = title ?: requireContext().getString(R.string.text_title_pending_join_loyal),
            description = description ?: requireContext().getString(R.string.text_description_pending_join_loyal),
            type1 =Utils.TYPE_CLOSE,
            isEkyc = true
        ))
    }
    override fun PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.toUI() {
        when (this) {
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.Loading -> {
                showLoading()
            }
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.Error -> {
                hideLoading()
                if(this.intent is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationIntent.GetUserInfor){
                    showLoyaltyErrorRequiredLoginDialog(title = getString(R.string.notification), message = message)
                }else{
                    showLoyaltyErrorDialog(title = getString(R.string.notification), message = message)
                }
            }
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.Done -> {
                hideLoading()
            }
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ErrorRequiredLogin -> {
                hideLoading()
                navigateToLoginWithParams(isDirect = true)
            }
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ResultGetUserInfo ->{
                hideLoading()
                if(data.code == "0"){
                    if (this.data != null) {
                        UserInfoLoyaltyUtils.setUserInfoLoyalty(this.data.data)
                        when (this.data.data.isEkyc) {
                            //    when (newData.isEkyc) {
                            CheckNavigateLoyaltyUtils.STATUS_IS_PENDING -> {
                                navigateLoyaltyPending(
                                    title = this.data.data.title,
                                    description = this.data.data.message
                                )
                            }
                            CheckNavigateLoyaltyUtils.STATUS_NON_LOYAL -> {
                                binding.lnRootConfirmJoin.visibility = View.VISIBLE
                                binding.tvTitle.text = this.data.data.title
                                binding.tvDescription.text =  this.data.data.message
                                // navigateLoyaltyEKYS()
                            }
                            CheckNavigateLoyaltyUtils.STATUS_HAVE_LOYAL -> {
                                if (this.data.data.isLoyalty == CheckNavigateLoyaltyUtils.STATUS_HAVE_LOYAL) {
                                    navigateLoyaltyHome()
                                }else{
                                    binding.lnRootConfirmJoin.visibility = View.VISIBLE
                                    binding.tvTitle.text = this.data.data.title
                                    binding.tvDescription.text =  this.data.data.message
                                }
                            }
                            CheckNavigateLoyaltyUtils.STATUS_IS_BUSINESS ->{
                                navigateLoyaltyBusiness(
                                    title = this.data.data.title,
                                    description = this.data.data.message,
                                    url = this.data.data.url
                                )
                            }
                            CheckNavigateLoyaltyUtils.STATUS_IS_ERROR_EKYS ->{
                                navigateLoyaltyErrorEKYC(
                                    title = this.data.data.title,
                                    description = this.data.data.message,
                                    url = this.data.data.url
                                )
                            }
                            else ->{
                                /**
                                 * Note : case otherwise is_ekyc  = 0,1,2,3,4
                                 * **/
                                Timber.tag("tulog-CheckNavigateLoyaltyUtils").d("isEKYC: ${this.data.data.isEkyc} && isLoyalty: ${this.data.data.isLoyalty}")
                                navigateLoyaltyEKYS()
                            }
                        }
                    }
                }
                else{
                    showLoyaltyErrorRequiredLoginDialog(title = getString(R.string.notification), message = data.errors)
                }
            }
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ResultGetUserJoinLoyalty -> {
                hideLoading()
                if(data !=null && data.code == "0"){
                    UserInfoLoyaltyUtils.setUserInfoLoyalty(data.data)
                    navigateLoyaltyHome()
                    //pending fix core api
                }
                if(data.code != "0"){
                    showLoyaltyErrorRequiredLoginDialog(title = getString(R.string.notification), message = data.error)
                }
            }
            is PackageLoyaltyValidationViewModel.PackageLoyaltyValidationState.ErrorNoInternet ->{
                showLoyaltyErrorRequiredLoginDialog(title = getString(R.string.notification), message = this.message)
            }
            else ->{
            }
        }
    }
}