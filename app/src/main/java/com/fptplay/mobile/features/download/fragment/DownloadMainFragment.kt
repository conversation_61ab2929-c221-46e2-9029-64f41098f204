package com.fptplay.mobile.features.download.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.DownloadMainFragmentBinding
import com.fptplay.mobile.features.download.DownloadViewModel

class DownloadMainFragment: BaseFragment<DownloadViewModel.DownloadState, DownloadViewModel.DownloadIntent>() {

    // region Variables
    override val viewModel : DownloadViewModel by activityViewModels()

    private var _binding: DownloadMainFragmentBinding? = null
    private val binding get() = _binding!!

    // endregion Variables

    // region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DownloadMainFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun DownloadViewModel.DownloadState.toUI() {}
    // endregion Overrides

}