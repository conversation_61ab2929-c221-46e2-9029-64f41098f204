package com.fptplay.mobile.features.mega

import android.view.View
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.features.mega.util.CheckNavigateMegaUtils
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.*
import com.xhbadxx.projects.module.domain.entity.fplay.game.GameDetailV2
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class MegaNavigateViewModel @Inject constructor(
    private val commonRepository: CommonRepository,
) : BaseViewModel<MegaNavigateViewModel.MegaNavigateIntent, MegaNavigateViewModel.MegaNavigateState>() {

    val loadingViewId: Int = View.generateViewId()
    //region Overrides

    private var navigateMenu: MegaMenuItem? = null

    fun saveNavigateMenu(navigateMenu: MegaMenuItem?) {
        this.navigateMenu = navigateMenu
    }

    fun navigateMenu() = navigateMenu
    override fun dispatchIntent(intent: MegaNavigateIntent) {
        safeLaunch {
            when (intent) {
                is MegaNavigateIntent.GetMiniAppManifest -> {
                    commonRepository.getMegaMiniAppManifest(url = intent.url).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, megaMiniAppManifest ->
                            MegaNavigateState.ResultMiniAppManifest(
                                isCached = isCached,
                                data = megaMiniAppManifest,
                                megaMenuItem = intent.megaMenuItem,
                                deeplinkExtraData = intent.deeplinkExtraData
                            )

                        }
                    }
                }

                is MegaNavigateIntent.GetMegaMenuItem -> {
                    commonRepository.getMegaMenuItem(
                        id = intent.menuId,
                        miniAppSdkVersion = intent.miniAppSdkVersion,
                        fromDeeplink = intent.fromDeeplink
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, megaMenuItem ->
                            MegaNavigateState.ResultMegaMenuItem(
                                isCached = isCached,
                                data = megaMenuItem
                            )

                        }
                    }

                }

                is MegaNavigateIntent.GetGameDetail -> {
                    commonRepository.getGameDetailV2(gameId = intent.gameId).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, gameDetail ->
                            MegaNavigateState.ResultGameDetail(
                                isCached = isCached,
                                data = gameDetail
                            )
                        }
                    }
                }

                is MegaNavigateIntent.GetGameManifest -> {
                    commonRepository.getMegaMiniAppManifest(url = intent.url).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, megaMiniAppManifest ->
                            MegaNavigateState.ResultGameManifest(
                                isCached = isCached,
                                data = megaMiniAppManifest,
                                gameDetail = intent.gameDetail,
                                deeplinkExtraData = intent.deeplinkExtraData
                            )

                        }
                    }
                }

                else -> {}
            }
        }
    }


    override fun <T> Result<T>.reduce(
        intent: MegaNavigateIntent?,
        successFun: (Boolean, T) -> MegaNavigateState
    ): MegaNavigateState {
        return when (this) {
            is Result.Init -> MegaNavigateState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }

            is Result.UserError.RequiredLogin -> MegaNavigateState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )

            is Result.Error -> {
                if (this is Result.Error.Intenet) {
                    MegaNavigateState.ErrorNoInternet(message = this.message, intent = intent)
                } else {
                    MegaNavigateState.Error(message = this.message, intent = intent)
                }
            }

            Result.Done -> MegaNavigateState.Done(intent = intent)
        }
    }

    private suspend fun <T> Flow<Result<T>>.process(
        successFun: (Boolean, T) -> Unit
    ) {

        this
//            .filter { it is Result.Success }
            .collect {
                if (it is Result.Success) {
                    it.let { res ->
                        successFun(res.isCached, res.successData)
                    }
                }
            }
    }

    //endregion

    //region Commons


    //endregion

    //region Intent, State
    sealed class MegaNavigateState : ViewState {
        object Idle: MegaNavigateState()
        data class Loading(val intent: MegaNavigateIntent? = null) : MegaNavigateState()
        data class ResultMiniAppManifest(
            val isCached: Boolean,
            val data: MegaMiniAppManifest,
            val megaMenuItem: MegaMenuItem,
            val deeplinkExtraData: CheckNavigateMegaUtils.NavExtraData? = null
        ) : MegaNavigateState()

        data class ResultMegaMenuItem(val isCached: Boolean, val data: MegaMenuItem) :
            MegaNavigateState()

        data class ResultGameDetail(val isCached: Boolean, val data: GameDetailV2) : MegaNavigateState()

        data class ResultGameManifest(
            val isCached: Boolean,
            val data: MegaMiniAppManifest,
            val gameDetail: GameDetailV2,
            val deeplinkExtraData: CheckNavigateMegaUtils.NavExtraData? = null
        ) : MegaNavigateState()

        data class Error(val message: String, val intent: MegaNavigateIntent? = null) :
            MegaNavigateState()

        data class ErrorNoInternet(val intent: MegaNavigateIntent? = null, val message: String) :
            MegaNavigateState()

        data class ErrorRequiredLogin(val message: String, val intent: MegaNavigateIntent? = null) :
            MegaNavigateState()

        data class Done(val intent: MegaNavigateIntent? = null) : MegaNavigateState()
    }

    sealed class MegaNavigateIntent : ViewIntent {
        data class GetMiniAppManifest(val url: String, val megaMenuItem: MegaMenuItem, val deeplinkExtraData: CheckNavigateMegaUtils.NavExtraData? = null) :
            MegaNavigateIntent()

        data class GetMegaMenuItem(
            val menuId: String,
            val miniAppSdkVersion: String,
            val fromDeeplink: Boolean = false
        ) : MegaNavigateIntent()

        data class GetGameDetail(
            val gameId: String
        ) : MegaNavigateIntent()

        data class GetGameManifest(val url: String, val gameDetail: GameDetailV2, val deeplinkExtraData: CheckNavigateMegaUtils.NavExtraData? = null) :
            MegaNavigateIntent()
    }
    //endregion
}
