package com.fptplay.mobile.features.mega.account.dialog_tablet

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseDialogFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.findChildNavController
import com.fptplay.mobile.databinding.AccountActionDialogFragmentBinding
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.account.util.AccountMegaScreen
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AccountActionDialogFragment :
    BaseDialogFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {

    override val hasEdgeToEdge: Boolean = true

    private var _binding: AccountActionDialogFragmentBinding? = null
    private val binding get() = _binding!!
    override val viewModel by activityViewModels<MegaViewModel>()

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private val safeArgs: AccountActionDialogFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = AccountActionDialogFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if(safeArgs.targetScreen == AccountMegaScreen.PromotionCode) {
            setFragmentResult(Constants.REFRESH_DATA, bundleOf(Constants.HAVE_CHANGE_DATA to true))

        }
        _binding = null
    }

    override fun bindEvent() {
        binding.ivClose.setOnClickListener {
            dismiss()
        }
        binding.toolbar.setNavigationOnClickListener {
            handleBack()
        }

        setFragmentResultListener(Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_EVENT) { _, bundle ->
            val title = bundle.getString(Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_KEY_TITLE)
            binding.toolbar.title = if(title.isNullOrEmpty())  "" else title

        }
        setFragmentResultListener(Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT) { _, bundle ->
            dismiss()
        }
    }

    override fun bindComponent() {
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        val navHostFragment =
            childFragmentManager.findFragmentById(R.id.nav_host_fragment_account_action) as? NavHostFragment
        val navController = navHostFragment?.navController

        val navGraph = navController?.navInflater?.inflate(R.navigation.nav_account_action)
        navGraph?.apply {
            val bundle = Bundle()
            setStartDestination(
                when (safeArgs.targetScreen) {
                    AccountMegaScreen.DeviceManager -> R.id.account_device_manager_fragment
                    AccountMegaScreen.PackageUser -> R.id.account_package_user_fragment
                    AccountMegaScreen.PaymentHistory -> R.id.account_payment_history_fragment
                    AccountMegaScreen.PromotionCode -> {
                        bundle.putString("code", safeArgs.promotionCode)
                        R.id.account_promotion_code_fragment
                    }
                }
            )
            navController.setGraph(this, bundle)

        }

    }


    override fun MegaViewModel.MegaState.toUI() {
        Timber.d("MegaState: $this")
    }
    //endregion


    private fun handleBack() {
        val navController = findChildNavController(R.id.nav_host_fragment_account_action)
        if (navController?.navigateUp() != true) {
            dismissAllowingStateLoss()
        }
    }
}