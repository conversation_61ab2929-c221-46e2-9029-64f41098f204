package com.fptplay.mobile.features.premiere.live_chat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.databinding.ChatOptionItemBinding
import com.fptplay.mobile.features.premiere.live_chat.data.LiveChatAction
import com.fptplay.mobile.features.premiere.live_chat.data.LiveChatOption
import com.xhbadxx.projects.module.util.image.ImageProxy

class LiveChatOptionAdapter :
    BaseAdapter<LiveChatAction, LiveChatOptionAdapter.ChatOptionViewHolder>() {

    inner class ChatOptionViewHolder(val binding: ChatOptionItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                eventListener?.onClickedItem(
                    absoluteAdapterPosition,
                    differ.currentList[absoluteAdapterPosition].copy(selected = true)
                )
            }
        }

        fun bind(item: LiveChatAction) {
            binding.apply {
                tvTitle.text = item.title
                ImageProxy.load(
                    context = binding.root.context,
                    url = item.icon,
                    width = binding.root.resources.getDimensionPixelSize(R.dimen.chat_option_icon_size),
                    height = binding.root.resources.getDimensionPixelSize(R.dimen.chat_option_icon_size),
                    target = ivIcon
                )
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatOptionViewHolder {
        return ChatOptionViewHolder(
            ChatOptionItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: ChatOptionViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }
}