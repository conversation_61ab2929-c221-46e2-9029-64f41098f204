package com.fptplay.mobile.features.pladio.service

import android.content.Intent
import android.os.Bundle
import android.support.v4.media.session.MediaSessionCompat
import android.view.KeyEvent
import com.fptplay.mobile.features.pladio.auto.AutoMediaIDHelper
import com.fptplay.mobile.features.pladio.data.Song
import com.xhbadxx.projects.module.util.logger.Logger
import timber.log.Timber

class MediaSessionCallback(
    private val playbackService: PlaybackService,
) : MediaSessionCompat.Callback() {

    private val TAG = "PladioMediaSessionCallback"

//    private val songRepository by inject<SongRepository>()
//    private val albumRepository by inject<AlbumRepository>()
//    private val artistRepository by inject<ArtistRepository>()
//    private val genreRepository by inject<GenreRepository>()
//    private val playlistRepository by inject<PlaylistRepository>()
//    private val topPlayedRepository by inject<TopPlayedRepository>()

    override fun onPlayFromMediaId(mediaId: String?, extras: Bundle?) {
        super.onPlayFromMediaId(mediaId, extras)
        val musicId = AutoMediaIDHelper.extractMusicID(mediaId!!)
        Logger.d("Music Id $musicId")
        val itemId = musicId?.toLong() ?: -1
        val songs: ArrayList<Song> = ArrayList()
//        when (val category = AutoMediaIDHelper.extractCategory(mediaId)) {
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_ALBUM -> {
//                val album: Album = albumRepository.album(itemId)
//                songs.addAll(album.songs)
//                playbackService.openQueue(songs, 0, true)
//            }
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_ARTIST -> {
//                val artist: Artist = artistRepository.artist(itemId)
//                songs.addAll(artist.songs)
//                playbackService.openQueue(songs, 0, true)
//            }
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_ALBUM_ARTIST -> {
//                val artist: Artist =
//                    artistRepository.albumArtist(albumRepository.album(itemId).albumArtist!!)
//                songs.addAll(artist.songs)
//                playbackService.openQueue(songs, 0, true)
//            }
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_PLAYLIST -> {
//                val playlist: Playlist = playlistRepository.playlist(itemId)
//                songs.addAll(playlist.getSongs())
//                playbackService.openQueue(songs, 0, true)
//            }
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_GENRE -> {
//                songs.addAll(genreRepository.songs(itemId))
//                playbackService.openQueue(songs, 0, true)
//            }
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_SHUFFLE -> {
//                val allSongs = songRepository.songs().toMutableList()
//                makeShuffleList(allSongs, -1)
//                playbackService.openQueue(allSongs, 0, true)
//            }
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_HISTORY,
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_SUGGESTIONS,
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_TOP_TRACKS,
//            AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_QUEUE,
//                -> {
//                val tracks: List<Song> = when (category) {
//                    AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_HISTORY -> topPlayedRepository.recentlyPlayedTracks()
//                    AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_SUGGESTIONS -> topPlayedRepository.recentlyPlayedTracks()
//                    AutoMediaIDHelper.MEDIA_ID_MUSICS_BY_TOP_TRACKS -> topPlayedRepository.recentlyPlayedTracks()
//                    else -> playbackService.playingQueue
//                }
//                songs.addAll(tracks)
//                var songIndex = MusicUtil.indexOfSongInList(tracks, itemId)
//                if (songIndex == -1) {
//                    songIndex = 0
//                }
//                playbackService.openQueue(songs, songIndex, true)
//            }
//        }
        playbackService.play()
    }

    override fun onPlayFromSearch(query: String?, extras: Bundle?) {
//        val songs = ArrayList<Song>()
//        if (query.isNullOrEmpty()) {
//            // The user provided generic string e.g. 'Play music'
//            // Build appropriate playlist queue
//            songs.addAll(songRepository.songs())
//        } else {
//            // Build a queue based on songs that match "query" or "extras" param
//            val mediaFocus: String? = extras?.getString(MediaStore.EXTRA_MEDIA_FOCUS)
//            if (mediaFocus == MediaStore.Audio.Artists.ENTRY_CONTENT_TYPE) {
//                val artistQuery = extras.getString(MediaStore.EXTRA_MEDIA_ARTIST)
//                if (artistQuery != null) {
//                    artistRepository.artists(artistQuery).forEach {
//                        songs.addAll(it.songs)
//                    }
//                }
//            } else if (mediaFocus == MediaStore.Audio.Albums.ENTRY_CONTENT_TYPE) {
//                val albumQuery = extras.getString(MediaStore.EXTRA_MEDIA_ALBUM)
//                if (albumQuery != null) {
//                    albumRepository.albums(albumQuery).forEach {
//                        songs.addAll(it.songs)
//                    }
//                }
//            }
//        }
//
//        if (songs.isEmpty()) {
//            // No focus found, search by query for song title
//            query?.also {
//                songs.addAll(songRepository.songs(it))
//            }
//        }
//
//        playbackService.openQueue(songs, 0, true)
//
//        playbackService.play()
    }

    override fun onPrepare() {
        super.onPrepare()
//        if (playbackService.currentSong != Song.emptySong)
//            playbackService.restoreState(::onPlay)
    }

    override fun onPlay() {
        super.onPlay()
        if (playbackService.currentSong != Song.emptySong) playbackService.play()
    }

    override fun onPause() {
        super.onPause()
        playbackService.pause()
    }

    override fun onSkipToNext() {
        super.onSkipToNext()
        Timber.i("${this.javaClass.simpleName} -> onSkipToNext ")
        playbackService.playNextSong(true)
    }

    override fun onSkipToPrevious() {
        super.onSkipToPrevious()
        playbackService.playPreviousSong(true)
    }

    override fun onStop() {
        super.onStop()
        playbackService.quit()
    }

    override fun onSeekTo(pos: Long) {
        super.onSeekTo(pos)
        playbackService.seek(pos)
    }

    override fun onMediaButtonEvent(mediaButtonEvent: Intent?): Boolean {
        val keyEvent = mediaButtonEvent?.getParcelableExtra<KeyEvent>(Intent.EXTRA_KEY_EVENT)
        Logger.d("$TAG -> MediaButtonEventHandler -> KeyEvent: $keyEvent -> keycode: ${keyEvent?.keyCode}")
        if (keyEvent != null && keyEvent.action == KeyEvent.ACTION_DOWN) {
            when (keyEvent.keyCode) {
                KeyEvent.KEYCODE_MEDIA_PLAY,
                KeyEvent.KEYCODE_MEDIA_PAUSE -> {
                    return playbackService.checkHandlePlayPause()
                }
            }
        }
        return false
    }

    override fun onCustomAction(action: String, extras: Bundle?) {
//        when (action) {
//            CYCLE_REPEAT -> {
//                cycleRepeatMode()
//                playbackService.updateMediaSessionPlaybackState()
//            }
//
//            TOGGLE_SHUFFLE -> {
//                playbackService.toggleShuffle()
//                playbackService.updateMediaSessionPlaybackState()
//            }
//            TOGGLE_FAVORITE -> {
//                playbackService.toggleFavorite()
//            }
//            else -> {
//                logE("Unsupported action: $action")
//            }
//        }
    }

    private fun checkAndStartPlaying(songs: ArrayList<Song>, itemId: Long) {
//        var songIndex = MusicUtil.indexOfSongInList(songs, itemId)
//        if (songIndex == -1) {
//            songIndex = 0
//        }
//        openQueue(songs, songIndex)
    }

    private fun openQueue(songs: ArrayList<Song>, index: Int, startPlaying: Boolean = true) {
//        MusicPlayerRemote.openQueue(songs, index, startPlaying)
    }
}