package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.databinding.SportInteractiveSquadTitleBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.squad.SportInteractiveSquadTitle

class SportInteractiveSquadTitleViewHolder(private val binding: SportInteractiveSquadTitleBinding) :
    BaseSportInteractiveViewHolder<SportInteractiveSquadTitle>(binding) {
    override fun bind(data: SportInteractiveSquadTitle) {
        binding.txtTitleSquad.text = data.title
    }
}