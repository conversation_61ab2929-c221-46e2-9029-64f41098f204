package com.fptplay.mobile.features.mini_app

//import com.fptplay.mobile.features.mini_app.utils.MiniAppGateWaySdkCallBack

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Utils.isActive
import com.fptplay.mobile.common.utils.Utils.requireLogin
import com.fptplay.mobile.databinding.MiniAppStartingFragmentBinding
import com.fptplay.mobile.features.mini_app.viewmodel.MiniAppViewModel
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMiniAppManifest
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject


@AndroidEntryPoint
class StartingMiniAppFragment :
    BaseFullDialogFragment<MiniAppViewModel.MiniAppViewState, MiniAppViewModel.MiniAppViewIntent>() {
    private var _binding: MiniAppStartingFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    override val viewModel: MiniAppViewModel by activityViewModels()
    private val safeArgs: StartingMiniAppFragmentArgs by navArgs()

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MiniAppStartingFragmentBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun bindComponent() {
        binding.pbLoading.root.show()
    }


    override fun bindData() {
        val miniAppManifestUrl = safeArgs.megaAppManifestUrl
        if (miniAppManifestUrl.isNullOrBlank()) {
            val megaAppId = safeArgs.megaAppId
            if (megaAppId.isNullOrBlank()) {
                findNavController().navigateUp()
            } else {
                viewModel.dispatchIntent(
                    MiniAppViewModel.MiniAppViewIntent.GetMegaMenuItem(
                        menuId = megaAppId,
                        miniAppSdkVersion = Constants.MINI_APP_SDK_SUPPORTED
                    )
                )
            }
        } else {
            getMiniAppManifest(miniAppManifestUrl, safeArgs.title ?: "", safeArgs.requiredLogin)
        }
    }

    override fun bindEvent() {
        setFragmentResultListener(Constants.LOGIN_SUCCESS_FOR_DIALOG) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            when {
                isSuccess -> {
                    // naviagte info in  in extra data
                }
                else -> { findNavController().navigateUp() }
            }
        }
    }


    override fun MiniAppViewModel.MiniAppViewState.toUI() {
        when (this) {
            is MiniAppViewModel.MiniAppViewState.Loading -> {
            }

            is MiniAppViewModel.MiniAppViewState.Error -> {
                Timber.tag("tam-miniapp").e("MiniAppViewState.Error $intent $message")
                if (intent is MiniAppViewModel.MiniAppViewIntent.GetMegaMenuItem) {
                    showPopupError(message = getString(R.string.mini_app_api_error_message))
                }
                if (intent is MiniAppViewModel.MiniAppViewIntent.GetMiniAppManifest) {
                    showPopupError(message = getString(R.string.mini_app_api_error_message))

                }
            }

            is MiniAppViewModel.MiniAppViewState.ErrorNoInternet -> {
                Timber.tag("tam-miniapp").e("MiniAppViewState.ErrorNoInternet $intent $message")
                if (intent is MiniAppViewModel.MiniAppViewIntent.GetMegaMenuItem) {
                    showPopupError(message = getString(R.string.mini_app_api_error_message))
                }
                if (intent is MiniAppViewModel.MiniAppViewIntent.GetMiniAppManifest) {
                    showPopupError(message = getString(R.string.mini_app_api_error_message))

                }
            }

            is MiniAppViewModel.MiniAppViewState.ErrorRequiredLogin -> {
                Timber.tag("tam-miniapp").e("MiniAppViewState.ErrorRequiredLogin $intent $message")
                if (intent is MiniAppViewModel.MiniAppViewIntent.GetMegaMenuItem) {
                    showPopupError(message = getString(R.string.mini_app_api_error_message))
                }
                if (intent is MiniAppViewModel.MiniAppViewIntent.GetMiniAppManifest) {
                    showPopupError(message = getString(R.string.mini_app_api_error_message))

                }
            }

            is MiniAppViewModel.MiniAppViewState.Done -> {
            }

            is MiniAppViewModel.MiniAppViewState.ResultMiniAppManifest -> {
                Timber.tag("tam-miniapp").i("MiniAppViewState.ResultMiniAppManifest $title $data")
                navigateToMiniApp(miniAppManifest = data, requiredLogin = requiredLogin, title = title)
            }

            is MiniAppViewModel.MiniAppViewState.ResultMegaMenuItem -> {
                Timber.tag("tam-miniapp").i("MiniAppViewState.ResultMegaMenuItem $data")
                if (data.validateMiniApp()) {
                    getMiniAppManifest(data.miniAppManifestUrl, data.title, data.requireLogin())
                } else {
                    showPopupError(message = getString(R.string.mini_app_unsupported_error_message))
                }
            }

            else -> {}
        }
    }

    private fun showPopupError(message: String, textConfirm: String? = null) {
//        Toast.makeText(context, "Lấy thông tin lỗi", Toast.LENGTH_SHORT).show()
        Timber.tag("tam-miniapp").e("showPopupError $message ${<EMAIL>}")
        AlertDialog().apply {
            setShowTitle(true)
            setTextTitle(<EMAIL>(R.string.notification))
            setMessage(message)
            setTextConfirm(textConfirm ?: <EMAIL>(R.string.mini_app_alert_dialog_error_confirm_text))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    findNavController().navigateUp()
                }
            })
            isCancelable = false
            setOnlyConfirmButton(true)
        }.show(<EMAIL>, "PopupErrorApi")

    }


    private fun MegaMenuItem.validateMiniApp(): Boolean {
        return (id.isNotBlank()
                && isActive()
                && actionType == MegaMenuItem.ActionType.MegaApp
                && miniAppManifestUrl.isNotBlank()
                )
    }

    private fun getMiniAppManifest(url: String, title: String, requiredLogin: Boolean) {
        viewModel.dispatchIntent(
            MiniAppViewModel.MiniAppViewIntent.GetMiniAppManifest(
                url = url,
                title = title,
                requiredLogin = requiredLogin
            )
        )
    }

    private fun navigateToMiniApp(miniAppManifest: MegaMiniAppManifest, title: String, requiredLogin: Boolean) {

        val navigationId = R.id.action_startingMiniAppFragment_to_miniAppFragment
        val navigateFun:() -> Unit = {
            findNavController().navigate(
                StartingMiniAppFragmentDirections.actionStartingMiniAppFragmentToMiniAppFragment(
                    megaAppId = safeArgs.megaAppId?:"",
                    title = title,
                    url = miniAppManifest.scope.megaApp.sourceUrl ?: "",
                    listFunctionMiniApp = miniAppManifest.scope.megaApp.methods.toTypedArray(),
                    listEventsMiniApp = miniAppManifest.scope.megaApp.events.toTypedArray(),
                    deeplinkUrl = safeArgs.megaAppId,
                    allowWildCard = miniAppManifest.allowWildCard?.toTypedArray(),
                    closeAppInfo = miniAppManifest.closeAppInfo?.let {
                        CloseInfoAppMiniAppData(
                            title = it.title,
                            message = it.message
                        )
                    },
                    theme = miniAppManifest.theme,
                )
            )
        }

        val extendsArgs = bundleOf(
            "megaAppId" to (safeArgs.megaAppId ?: ""),
            "title" to (title ?: ""),
            "url" to (miniAppManifest.scope.megaApp.sourceUrl ?: ""),
            "listFunctionMiniApp" to miniAppManifest.scope.megaApp.methods.toTypedArray(),
            "listEventsMiniApp" to miniAppManifest.scope.megaApp.events.toTypedArray()
        )

        if (requiredLogin) {
            checkUserLoginBeforeNavigate(
                navigationId = navigationId,
                navigateFun = navigateFun,
                extendsArgs = extendsArgs
            )

        } else {
            navigateFun()
        }


    }

    private fun checkUserLoginBeforeNavigate(
        navigationId: Int? = null,
        extendsArgs: Bundle? = null,
        navigateFun: () -> Unit,
    ) {
        if (sharedPreferences.userLogin()) {
            navigateFun()
        } else {
            navigateToLogin(navigationId = navigationId, extendsArgs = extendsArgs)
        }
    }

    private fun navigateToLogin(navigationId: Int? = null, extendsArgs: Bundle? = null) {
        navigationId?.let {
            navigateToLoginWithParams(
                isDirect = true,
                navigationId = it,
                extendsArgs = extendsArgs,
                displayInDialogForMobile = true
            )
        } ?: run {
            navigateToLoginWithParams(isDirect = true, displayInDialogForMobile = true)
        }
    }


}