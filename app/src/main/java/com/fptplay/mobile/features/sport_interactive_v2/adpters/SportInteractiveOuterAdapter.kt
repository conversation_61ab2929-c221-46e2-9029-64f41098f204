package com.fptplay.mobile.features.sport_interactive_v2.adpters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.databinding.SportInteractiveV2ItemBinding
import com.fptplay.mobile.features.sport_interactive_v2.models.ScreenContainerType
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveGeneralData
import com.fptplay.mobile.features.sport_interactive_v2.models.UIData
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.util.common.IEventListener
import timber.log.Timber

class SportInteractiveOuterAdapter(private val screenContainerType: ScreenContainerType) :
    BaseAdapter<SportInteractiveGeneralData, SportInteractiveOuterAdapter.SportInteractiveOuterViewHolder>() {
    private val mapViewHolder = mutableMapOf<Int, SportInteractiveOuterViewHolder>()

    override fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return (oldItem as? SportInteractiveGeneralData)?.title == (newItem as? SportInteractiveGeneralData)?.title
    }

    override fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return (oldItem as? SportInteractiveGeneralData) == (newItem as? SportInteractiveGeneralData)
    }

    override fun getItemViewType(position: Int): Int {
        return if (item(position)?.title.isNullOrBlank()) position
        else (item(position)?.title?.hashCode() ?: 0) + position
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): SportInteractiveOuterViewHolder {
        Timber.d("*******are create view holder for $viewType")
        return mapViewHolder[viewType]?.let { oldViewHolder ->
            if (oldViewHolder.itemView.parent == null) {
                oldViewHolder
            } else {
                SportInteractiveOuterViewHolder(
                    SportInteractiveV2ItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    ),
                    screenContainerType
                ).apply { mapViewHolder[viewType] = this@apply }
            }
        } ?: SportInteractiveOuterViewHolder(
            SportInteractiveV2ItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ),
            screenContainerType
        ).apply { mapViewHolder[viewType] = this@apply }
    }

    private var innerEventListener: IEventListener<UIData>? = null
    fun setInnerEvenListener(iEventListener: IEventListener<UIData>) {
        innerEventListener = iEventListener
    }


    override fun onBindViewHolder(holder: SportInteractiveOuterViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    inner class SportInteractiveOuterViewHolder(private val binding: SportInteractiveV2ItemBinding, screenContainerType: ScreenContainerType) :
        BaseViewHolder(binding) {

        private val itemAdapter: SportInteractiveInnerAdapter by lazy { SportInteractiveInnerAdapter(screenContainerType = screenContainerType) }

        init {
            itemAdapter.eventListener = object : IEventListener<UIData> {
                override fun onClickView(position: Int, view: View?, data: UIData) {
                    innerEventListener?.onClickView(position, view, data)
                }
            }
            binding.rvMainItem.adapter = itemAdapter
        }

        fun bind(data: SportInteractiveGeneralData) {
            itemAdapter.bind(data.data)
        }
    }
}