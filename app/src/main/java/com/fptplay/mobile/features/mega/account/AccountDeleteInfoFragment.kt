package com.fptplay.mobile.features.mega.account


import android.annotation.SuppressLint
import android.content.Context
import android.content.IntentFilter
import android.os.Bundle
import android.os.CountDownTimer
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.databinding.AccountDeleteInfoFragmentBinding
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.login.LoginViewModel
import com.fptplay.mobile.features.login.gmsotp.SMSReceiver
import com.fptplay.mobile.features.login.utils.LoginUtils

import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger

import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AccountDeleteInfoFragment : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>(), SMSReceiver.OTPReceiveListener {
    sealed class ScreenMode {
        object VerifyOTP : ScreenMode()
        object DeleteAccount : ScreenMode()
        object ConfirmPassword : ScreenMode()
    }
    override val handleBackPressed = true
    override val viewModel by activityViewModels<MegaViewModel>()
    private val loginViewModel by activityViewModels<LoginViewModel>()
    private var _binding: AccountDeleteInfoFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: AccountDeleteInfoFragmentArgs by navArgs()
    private val requiredPasswordLength by lazy { 6 }
    private val loginUtils by lazy { LoginUtils(loginViewModel, parentFragmentManager) }
    private var countDownTimer: CountDownTimer? = null
    private val screenMode: ScreenMode by lazy {
        if (safeArgs.screenType == "DeleteAccount") ScreenMode.DeleteAccount else if (safeArgs.screenType == "ConfirmPassword") ScreenMode.ConfirmPassword else ScreenMode.DeleteAccount
    }

    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    //endregion
    //region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountDeleteInfoFragmentBinding.inflate(inflater, container, false)
        if(screenMode == ScreenMode.DeleteAccount) {
            startSMSListener()
        }
        return binding.root
    }
    override fun onDestroyView() {
        super.onDestroyView()
        countDownTimer?.cancel()
        smsReceiver?.apply {
            requireActivity().unregisterReceiver(smsReceiver)
            smsReceiver = null
        }
        _binding = null
    }
    override fun bindComponent() {
        binding.apply {
            tvInputOtpPhoneNumber.text = viewModel.userPhone()
            pvConfirmPassword.setHint(getString(R.string.old_password))
            pvConfirmPassword.setMaxLength(requiredPasswordLength)
            pvConfirmPassword.setTypeInputEditText(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD)
        }
        bindScreen()
    }
    override fun bindEvent() {
        when (screenMode) {
            ScreenMode.DeleteAccount -> {
                viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestDeleteAccountOtp)
            }
            else -> {

            }
        }
        binding.apply {
            toolbar?.setNavigationOnClickListener {
//                findNavController().navigate(AccountChangePasswordFragmentDirections.accountChangePasswordFragmentToAccountInfoFragment())
                when (screenMode) {
                    ScreenMode.DeleteAccount -> {
                        navigateToConfirmedPassword()
                    }
                    else -> {
                        parentFragment?.parentFragment?.setFragmentResult(Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT, bundleOf())

                    }
                }
            }
            tvResend.onClickDelay {
                startSMSListener()
                when (screenMode) {
                    ScreenMode.DeleteAccount -> {
                        viewModel.dispatchIntent(MegaViewModel.MegaIntent.ResendDeleteAccountOtp)
                    }
                    else -> {

                    }
                }
            }

            edtOtp.doAfterTextChanged {
                updateConfirmButtonBackground()
            }
            pvConfirmPassword.passwordEdittext.doAfterTextChanged {
                updateConfirmButtonBackground()
            }
            pvConfirmPassword.passwordEdittext.setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE) binding.btnConfirm.performClick()
                false
            }

            btnConfirm.setOnClickListener {
                activity?.currentFocus?.hideKeyboard()
                when (screenMode) {
                    ScreenMode.DeleteAccount -> {
                        verifyOTP()
                    }
                    ScreenMode.ConfirmPassword->{
                        clearError()
                        verifyAndConfirmPassword()
                    }
                    else ->{}
                }
            }
        }
        parentFragment?.parentFragment?.setFragmentResultListener(Constants.LOGIN_SUCCESS_FOR_DIALOG) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            when {
                isSuccess -> {}
                else -> backHandler()
            }
        }
        parentFragment?.parentFragment?.setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            if(isSuccess){
                navigateToLoginSuccessBackToMega(bundle)
            }
        }
    }
    private fun navigateToLoginSuccessBackToMega(bundle: Bundle) {
        resetUi()
        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
            NavHomeMainDirections.actionGlobalToHome())
        setFragmentResult(Constants.REFRESH_DATA, bundle)
    }
    override fun backHandler() {
//        findNavController().navigate(AccountChangePasswordFragmentDirections.accountChangePasswordFragmentToAccountInfoFragment())
        when (screenMode) {
            ScreenMode.DeleteAccount -> {
                navigateToConfirmedPassword()
            }
            else -> {
                parentFragment?.parentFragment?.setFragmentResult(Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT, bundleOf())
            }
        }
    }
    override fun observeState() {
        super.observeState()
        Timber.tag("tam-account").d("loginViewModel observeState")

        loginViewModel.state.observe(viewLifecycleOwner) { it.toUI() }

    }

    override fun MegaViewModel.MegaState.toUI() {
        Timber.tag("tam-account").d("${<EMAIL>} MegaState: $this")
        when (this) {
            is MegaViewModel.MegaState.Loading -> showLoading(true)
            is MegaViewModel.MegaState.ResultChangePassword -> {
                trackingProxy.sendEvent(
                    InforMobile(
                        infor = trackingInfo,
                        logId = "198",
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        screen = "ModifiedInformation",
                        event = "ChangePassword",
                        status = if (data.isSuccess()) "Success" else "Failed"
                    )
                )

                if (data.isSuccess()) {
                    resetUi()
                    if (data.message.isNotBlank()) {
                        showWarningDialog(data.message, onConfirm = {
//                            findNavController().navigate(AccountChangePasswordFragmentDirections.accountChangePasswordFragmentToAccountInfoFragment())
                            parentFragment?.parentFragment?.setFragmentResult(Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT, bundleOf())
                        })
                    } else {
                        showWarningDialog(getString(R.string.change_password_success), onConfirm = {
//                            findNavController().navigate(AccountChangePasswordFragmentDirections.accountChangePasswordFragmentToAccountInfoFragment())
                            parentFragment?.parentFragment?.setFragmentResult(Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT, bundleOf())
                        })
                    }
                } else {
                    if (data.messageCode == "9") {
                        binding.pvOldPassword.setError(data.message.ifBlank { getString(R.string.old_password_is_not_correct) })
                    }else{
                        if (data.message.isNotBlank()) {
                            showWarningDialog(data.message)
                        }
                    }
                }
            }
            is MegaViewModel.MegaState.Error -> {
            }
            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                Timber.tag("tam-account").d("Change password ErrorRequiredLogin: $message")
//                navigateToLogin()
                parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = true)

            }
            is MegaViewModel.MegaState.Done -> showLoading(false)

            is MegaViewModel.MegaState.ResultRequestOtp -> {
                startCountdown(if(data.seconds < 30) 30 else data.seconds)
            }
            is MegaViewModel.MegaState.ResultVerifyOtp -> {
                if(data.errorCode == 0) {
                    if(screenMode == ScreenMode.DeleteAccount){
                        viewModel.dispatchIntent(MegaViewModel.MegaIntent.DeleteAccount(verityToken = data.verifyToken))
                    }
                } else {
                    showWarningDialog(
                        message = data.message.ifBlank { data.data?.message?: "" }
                    )
                }
            }
            is MegaViewModel.MegaState.ResultDeleteAccount ->{
                if(status ==200){
                    logOut(msgSuccess = message)
                }
                else{
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = "198",
                            appId = TrackingUtil.currentAppId,
                            appName = TrackingUtil.currentAppName,
                            screen = "DeactivateAccount",
                            event = "DeactivateSuccess",
                            status ="Fail"
                        )
                    )
                    showWarningMessageDialog(title = getString(R.string.notification), textConfirm = getString(R.string.btn_accept),message = message, isCancelled = true,
                        onConfirm = {
                            resetUi()
                            navigateToAccountInfo()
                        })
                }
            }
            is MegaViewModel.MegaState.ResultConfirmPassword ->{
                if(status ==1){
                  findNavController().navigate(AccountDeleteInfoFragmentDirections.accountDeleteConfirmPasswordToDeleteAccountFragment(screenType = "DeleteAccount"))
                }
                else{
                    binding.pvConfirmPassword.setError(message)
                }
            }
            is MegaViewModel.MegaState.ErrorNoInternet->{
                showWarningDialog(message = message)
            }
            else -> {}
        }
    }
    private fun logOut(msgSuccess: String){
        AdsUtils.saveUserType(sharedPreferences, false)

        // Picture in Picture
        PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)

        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "198",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "DeactivateAccount",
                event = "DeactivateSuccess",
                status = "Success"
            )
        )

        Utils.clearUserData(sharedPreferences)
        trackingInfo.updateUserSession(0)
        trackingInfo.updateUserPhone("")
        trackingInfo.updateUserContract("")
        trackingInfo.updateUserId("")
        ZendeskUtils.updateZendeskIdentity(userLogin = false, userToken = "", userTokenType = "")
        navigateToMega(message = msgSuccess)
    }
    private fun navigateToMega(message: String){
        if(context.isTablet()){
            parentFragment?.parentFragment?.setFragmentResult(
                Constants.ACCOUNT_DELETE_HANDLE_BACK_COMPLETELY_EVENT, bundleOf(
                    Constants.ACCOUNT_DELETE_HANDLE_BACK_COMPLETELY_EVENT_MESSAGE to (message))
            )
        }
        else{
            showWarningMessageDialog(title = getString(R.string.notification), textConfirm = getString(R.string.understood), message = message, isCancelled = true,onConfirm = {
                resetUi()
                activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
                    NavHomeMainDirections.actionGlobalToHome())})
        }
    }
    private fun navigateToAccountInfo(){
        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(AccountInfoFragmentDirections.actionGlobalToAccountInfo())
    }
    private fun navigateToConfirmedPassword() {
        if (context.isTablet()) {
            parentFragment?.parentFragment?.setFragmentResult(
                Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT,
                bundleOf()
            )
        } else {
            resetUi()
            findNavController().navigate(
                AccountDeleteInfoFragmentDirections.accountDeleteConfirmPasswordToDeleteAccountFragment(
                    screenType = "ConfirmPassword"
                )
            )
        }
    }
    //endregion
    // region navigate to mega

    //region Commons
    private fun LoginViewModel.LoginState.toUI() {
        when (this) {
            is LoginViewModel.LoginState.Loading -> showLoading(true)
            is LoginViewModel.LoginState.ResultVerifyOTP -> {
                when (data.status) {
                    LoginViewModel.Status.IS_SUCCESS -> {//success
                        Timber.tag("tam-account").d("LoginViewModel findNavController")

                        findNavController().navigateSafe(AccountChangePasswordFragmentDirections.accountOtpFragmentToAccountChangePasswordFragment())
                    }
                    else -> { //error
                        loginUtils.checkErrorRequest(
                            requireContext(),
                            data.errorCode,
                            data.message,
                            data.timeToBlock
                        )
                    }
                }
            }
            is LoginViewModel.LoginState.ResultResendOTP -> {
                when (data.status) {
                    LoginViewModel.Status.IS_SUCCESS -> {
                        startCountdown(30)
                    }
                    else -> {
                        when (data.errorCode) {
                            LoginViewModel.Status.ERROR_MANY_REQUEST_21,
                            LoginViewModel.Status.ERROR_MANY_REQUEST_22,
                            LoginViewModel.Status.ERROR_MANY_REQUEST_429 -> {
                                startCountdown(data.seconds)
                            }
                            else -> {
                                showWarningDialog(data.message)
                            }
                        }
                    }
                }
            }
            is LoginViewModel.LoginState.Error -> {}
            is LoginViewModel.LoginState.Done -> showLoading(false)
            else -> {}
        }
    }

    private fun bindScreen() {
        when (screenMode) {
            ScreenMode.VerifyOTP -> {
                parentFragment?.parentFragment?.setFragmentResult(
                    Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_EVENT,
                    bundleOf(Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_KEY_TITLE to getString(R.string.account_input_otp))
                )
                binding.layoutOtp.show()
                binding.layoutChangePassword.hide()
                binding.layoutConfirmPassword.hide()
            }
            ScreenMode.DeleteAccount -> {
                parentFragment?.parentFragment?.setFragmentResult(
                    Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_EVENT,
                    bundleOf(Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_KEY_TITLE to getString(R.string.account_input_otp))
                )
                binding.layoutOtp.show()
                binding.layoutChangePassword.hide()
                binding.layoutConfirmPassword.hide()
            }
            ScreenMode.ConfirmPassword -> {
                parentFragment?.parentFragment?.setFragmentResult(
                    Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_EVENT,
                    bundleOf(Constants.ACCOUNT_TOOLBAR_UPDATE_TITLE_KEY_TITLE to getString(R.string.account_confirm_password))
                )
                binding.layoutOtp.hide()
                binding.layoutConfirmPassword.show()
                binding.layoutChangePassword.hide()
            }
        }
    }
    private var smsReceiver: SMSReceiver? = null


    private fun String.getDigits(): String {
        return this.filter { it.isDigit() }
    }

    private fun startSMSListener() {
        try {
            smsReceiver?.apply {
                requireActivity().unregisterReceiver(smsReceiver)
                smsReceiver = null
            }

            smsReceiver = SMSReceiver()
            smsReceiver?.setOTPListener(this)
            val intentFilter = IntentFilter()
            intentFilter.addAction(SmsRetriever.SMS_RETRIEVED_ACTION)
            activity?.let { requiredActivity ->
                ContextCompat.registerReceiver(requiredActivity, smsReceiver, intentFilter, ContextCompat.RECEIVER_EXPORTED)
            }
            val client = SmsRetriever.getClient(requireActivity())
            val task = client.startSmsRetriever()
            task.addOnSuccessListener {
                // API successfully started
                Logger.d("addOnSuccessListener")
            }
            task.addOnFailureListener {
                // Fail to start API
                Logger.d("addOnFailureListener")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    override fun onOTPReceived(otp: String?) {
        otp?.apply {
            Logger.d("trangtest ${getOTP(otp)}")
            binding.edtOtp.setText(getOTP(otp))
            if(binding.btnConfirm.isEnabled) {
                if (smsReceiver != null) {
                    requireActivity().unregisterReceiver(smsReceiver)
                    smsReceiver = null
                }
                verifyOTP()
            }
        }
    }
    private fun getOTP(sms: String): String{
        //11 is hashkey length
        return sms.substring(0, sms.length - 11).filter { it.isDigit() }
    }

    override fun onOTPTimeOut() {
        showToast("OTP Time out")
    }

    override fun onOTPReceivedError(error: String?) {
        error?.apply {
            showToast(this)
        }

    }

    private fun showToast(msg: String) {
        Toast.makeText(requireContext(), msg, Toast.LENGTH_SHORT).show()
    }

    private fun updateConfirmButtonBackground() {
        val otp = binding.edtOtp.text
        val oldPassword = binding.pvOldPassword.password
        val newPassword = binding.pvNewPassword.password
        val newPasswordAgain = binding.pvNewPasswordAgain.password
        val confirmPasswordAgain = binding.pvConfirmPassword.password

        val isEnable = when (screenMode) {
           ScreenMode.DeleteAccount -> !otp.isNullOrBlank() && otp.length == 4
            ScreenMode.VerifyOTP -> !otp.isNullOrBlank() && otp.length == 4
            ScreenMode.ConfirmPassword -> !confirmPasswordAgain.isNullOrBlank()
        }
        if (isEnable) {
            binding.btnConfirm.background = AppCompatResources.getDrawable(requireContext(), R.drawable.account_rounded_btn_background_enable)
            binding.btnConfirm.setTextColor(ContextCompat.getColor(requireContext(), R.color.app_content_text_color))
        } else {
            binding.btnConfirm.background = AppCompatResources.getDrawable(requireContext(), R.drawable.account_rounded_btn_background_disable)
            binding.btnConfirm.setTextColor(ContextCompat.getColor(requireContext(), R.color.app_content_text_disable_color))
        }
    }

    private fun verifyOTP() {
        val otp = binding.edtOtp.text?.toString()
        if (otp == null || otp.length != 4) return
        when(screenMode){
            ScreenMode.DeleteAccount->{
                viewModel.dispatchIntent(MegaViewModel.MegaIntent.VerifyDeleteAccountOtp(otpCode = otp))
            }
            else ->{}
        }
    }
    private fun verifyAndConfirmPassword(){
        val confirmPassword = binding.pvConfirmPassword.password
        when{
            confirmPassword.isNullOrBlank() ->{
                return
            }

//            confirmPassword.length < requiredPasswordLength -> {
//                binding.pvConfirmPassword.setError(getString(R.string.confirm_password_not_equal_required_length))
//                return
//            }
        }
        viewModel.dispatchIntent(
            MegaViewModel.MegaIntent.ConfirmPassword(
                viewModel.userPhone(),
                confirmPassword.toString(),
            )
        )
    }
    @SuppressLint("SetTextI18n")
    private fun startCountdown(seconds: Int) {
        binding.tvResend.hide()
        binding.tvResendDes.text = getString(R.string.login_resend_otp_des, "(${seconds})")
        var timeCountdown = seconds
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(timeCountdown * 1000L, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeCountdown--
                binding.tvResendDes.text = getString(R.string.login_resend_otp_des,"(${timeCountdown}s)")
            }
            override fun onFinish() {
                binding.tvResendDes.text = getString(R.string.login_not_have_otp)
                binding.tvResend.show()
            }
        }.start()
    }
    private fun showLoading(isShowed: Boolean = false) {
        binding.apply {
            if (isShowed) {
                pbLoading.root.show()
            } else {
                pbLoading.root.hide()
            }
        }
    }

    private fun clearError() {
        binding.pvConfirmPassword.setError(null)
    }
    private fun resetUi() {
        binding.apply {
            showLoading(false)
            binding.pvOldPassword.setPasswordText("")
            binding.pvNewPassword.setPasswordText("")
            binding.pvNewPasswordAgain.setPasswordText("")
        }
    }
}