package com.fptplay.mobile.features.tracking_appsflyer

import com.appsflyer.AFInAppEventParameterName
import com.appsflyer.AFInAppEventType
import com.appsflyer.AppsFlyerLib
import com.fptplay.mobile.MainApplication
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackagePlan
import timber.log.Timber

object TrackingAppsFlyerProxy {
    fun sendTrackingAppsFlyerPaymentSuccess(packagePlan: PackagePlan) {
        traceLogDebug(AFInAppEventType.SUBSCRIBE + "_" + packagePlan.planType)
        try {
            val eventValue: HashMap<String, Any> = HashMap()
            eventValue[AFInAppEventParameterName.COUPON_CODE] = ""
            eventValue[AFInAppEventParameterName.PRICE] = packagePlan.amountStr ?: ""
            eventValue[AFInAppEventParameterName.CURRENCY] = packagePlan.currency ?: ""
            eventValue["fptplay_plan_id"] = packagePlan.id
            eventValue["fptplay_plan_name"] = packagePlan.name ?: ""
            eventValue["subscription_method"] = ""
            eventValue["expiration_date"] = packagePlan.valueDate?.toString() ?: ""
            AppsFlyerLib.getInstance().logEvent(
                MainApplication.INSTANCE.applicationContext,
                AFInAppEventType.SUBSCRIBE + "_" + packagePlan.planType,
                eventValue
            )

        } catch (ex: java.lang.Exception) {
            Timber.e(ex)
        }
    }

    fun sendTrackingAppsFlyerLoginSuccess() {
        if (MainApplication.INSTANCE.sharedPreferences.appFlyerFirstLoginKey()) {
            traceLogDebug(AFInAppEventType.LOGIN)
            AppsFlyerLib.getInstance().logEvent(MainApplication.INSTANCE.applicationContext, AFInAppEventType.LOGIN, HashMap())
            MainApplication.INSTANCE.sharedPreferences.saveAppFlyerFirstLoginKey(false)
        }
    }

    fun sendAppFlyerRegisterAccountSuccess() {
        traceLogDebug(AFInAppEventType.COMPLETE_REGISTRATION)
        AppsFlyerLib.getInstance().logEvent(MainApplication.INSTANCE.applicationContext, AFInAppEventType.COMPLETE_REGISTRATION, HashMap())
    }

    private fun traceLogDebug(eventName: String) {
        Timber.d("AppsFlyer log event: $eventName")
    }
}