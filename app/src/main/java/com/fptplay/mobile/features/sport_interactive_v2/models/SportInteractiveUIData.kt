package com.fptplay.mobile.features.sport_interactive_v2.models

import com.fptplay.mobile.features.sport_interactive_v2.models.tabmenu.SportInteractiveMenuData

data class SportInteractiveUIData constructor (
    val titlesData: List<SportInteractiveMenuData> = emptyList(),
    val statisticData: SportInteractiveGeneralData = SportInteractiveGeneralData(),
    val matchProcessData: SportInteractiveGeneralData = SportInteractiveGeneralData(),
    val squadData: SportInteractiveGeneralData = SportInteractiveGeneralData(),
    val liveScoreData: SportInteractiveGeneralData = SportInteractiveGeneralData(),
) {
    fun toList(): List<SportInteractiveGeneralData> {
        return arrayListOf<SportInteractiveGeneralData>().apply {
            add(statisticData)
            add(matchProcessData)
            add(squadData)
            add(liveScoreData)
        }
    }
}