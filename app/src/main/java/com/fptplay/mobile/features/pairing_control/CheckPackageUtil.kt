package com.fptplay.mobile.features.pairing_control

import android.content.Context
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.navigation.NavOptions
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Navigation.navigateToRequiredBuyPackage
import com.fptplay.mobile.features.home.HomeMainFragmentDirections
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannel

class CheckPackageUtil(
    private val context: Context,
    private val pairingControlViewModel: PairingControlViewModel,
    private val navHostFragment: NavHostFragment?,
    private val requestFromCast: Boolean
) : LifecycleEventObserver {

    private var alertDialog: AlertDialog? = null
    private var screenProvider: String = ""

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> onCreate(source)
            Lifecycle.Event.ON_START -> onStart()
            Lifecycle.Event.ON_STOP -> onStop()
            Lifecycle.Event.ON_DESTROY -> onDestroy()
            else -> {}
        }
    }

    private fun onCreate(lifecycleOwner: LifecycleOwner) {
        observeData(lifecycleOwner)
    }

    private fun onStart() {

    }

    private fun onStop() {

    }

    private fun onDestroy() {

    }

    /**
     * Set screen provider for ads
     * @param provider: Screen provider
     */
    fun setScreenProvider(provider: String) {
        this.screenProvider = provider

    }

    fun navigateToSelectedContent(id: String, highlightId: String, type: String, episodeId: String, bitrateId: String, isPremiere: String) {
        when (type) {
            "vod" -> {
                pairingControlViewModel.dispatchIntent(PairingControlViewModel.PairingControlIntent.CheckVODRequirePackage(id = id, episodeId = episodeId, bitrateId = MainApplication.INSTANCE.pairingConnectionHelper.findCastingDataBitrateId(episodeIndex = episodeId)))
                // Update new bookmark
                MainApplication.INSTANCE.pairingConnectionHelper.updateCastingItemEpisodeIndex(episodeIndex = episodeId)
            }
            "livetv" -> {
                pairingControlViewModel.dispatchIntent(PairingControlViewModel.PairingControlIntent.CheckTVRequirePackage(id = id, bitrateId = MainApplication.INSTANCE.pairingConnectionHelper.findCastingDataBitrateId(episodeIndex = "0")))
            }
            "eventtv",
            "event" -> {
                pairingControlViewModel.dispatchIntent(PairingControlViewModel.PairingControlIntent.CheckEventRequirePackage(id = id, highlightId = highlightId, episodeId = episodeId, bitrateId = MainApplication.INSTANCE.pairingConnectionHelper.findCastingDataBitrateId(episodeIndex = "0"), type = type, isPremiere = isPremiere))
            }
            else -> {
                // test
//                pairingControlViewModel.dispatchIntent(PairingControlViewModel.PairingControlIntent.CheckVODRequirePackage(id = MainApplication.INSTANCE.pairingConnectionHelper.getCastingItem()?.id?:"", episodeId = "3", bitrateId = "auto_vip"))

            }
        }
    }


    private fun observeData(lifecycleOwner: LifecycleOwner) {
        if (pairingControlViewModel.state.hasObservers()) pairingControlViewModel.resetState()

        pairingControlViewModel.state.observe(lifecycleOwner) {
            when (it) {
                is PairingControlViewModel.PairingControlState.ResultCheckVODRequirePackage -> {
//                    navigateToVod(it.intent.id)
                }
                is PairingControlViewModel.PairingControlState.ResultCheckTVRequirePackage -> {
//                    navigateToLiveTV(it.intent.id)
                }
                is PairingControlViewModel.PairingControlState.ResultCheckEventRequirePackage -> {
//                    when (it.intent.type) {
//                        "eventtv" -> {
//                            navigateToLiveTV(it.intent.highlightId)
//                        }
//                        "event" -> {
//                            navigateToPremiere(it.intent.highlightId)
//                        }
//                    }
                }
                is PairingControlViewModel.PairingControlState.ErrorItemNotFound -> {
                    showWarningDialog(message = it.message, textClose = context.getString(R.string.understood))
                }
                is PairingControlViewModel.PairingControlState.Error -> {
                    if (it.data is PairingControlViewModel.PairingControlIntent.GetChannelWebSocket) {
                        MainApplication.INSTANCE.pairingConnectionHelper.showToast(message = it.message)
                    } else {
                        showWarningDialog(message = it.message, textClose = context.getString(R.string.understood))
                    }
                }
                is PairingControlViewModel.PairingControlState.ErrorRequiredLogin -> {
                    when (it.intent) {
                        is PairingControlViewModel.PairingControlIntent.CheckTVRequirePackage -> {
                            if (navHostFragment?.navController?.currentDestination?.id == R.id.livetv_detail_fragment) {
                                navHostFragment.navigateToLoginWithParams(
                                    title = it.message,
                                    idToPlay = it.intent.id,
                                    popupToId = R.id.livetv_detail_fragment,
                                    checkRequireVip = true,
                                    requestFromCast = requestFromCast
                                )
                            } else {
                                navHostFragment?.navigateToLoginWithParams(
                                    title = it.message,
                                    navigationId =  R.id.action_global_to_tv_detail,
                                    idToPlay = it.intent.id,
                                    checkRequireVip = true,
                                    requestFromCast = requestFromCast
                                )
                            }
                        }
                        is PairingControlViewModel.PairingControlIntent.CheckVODRequirePackage -> {
                            navHostFragment?.navigateToLoginWithParams(
                                navigationId = R.id.action_global_to_vod,
                                idToPlay = it.intent.id,
                                isPlaylist = false,
                                isDirect = false,
                                requestFromCast = requestFromCast
                            )
                        }
                        is PairingControlViewModel.PairingControlIntent.CheckEventRequirePackage -> {
                            when (it.intent.type) {
                                "eventtv" -> {
                                    navHostFragment?.navigateToLoginWithParams(
                                        title = it.message,
                                        navigationId = R.id.action_global_to_premiere,
                                        idToPlay = it.intent.highlightId,
                                        checkRequireVip = true,
                                        requestFromCast = requestFromCast
                                    )
                                }
                                "event" -> {
                                    navHostFragment?.navigateToLoginWithParams(
                                        title = it.message,
                                        navigationId = R.id.action_global_to_premiere,
                                        idToPlay = it.intent.highlightId,
                                        checkRequireVip = true,
                                        requestFromCast = requestFromCast
                                    )
                                }
                            }
                        }
                        else -> {}
                    }
                }
                is PairingControlViewModel.PairingControlState.ErrorRequiredVip -> {
                    when (it.intent) {
                        is PairingControlViewModel.PairingControlIntent.CheckTVRequirePackage -> {
                            var popupToId = R.id.livetv_detail_fragment
                            var navigateToId = R.id.action_global_to_tv_detail
                            if (navHostFragment?.navController?.currentDestination?.id == R.id.livetv_detail_fragment) {
                                navigateToId = -1
                            } else {
                                popupToId = -1
                            }
                            if (it.requiredVip?.isTvod == true) {
                                navHostFragment?.findNavController()?.navigate(HomeMainFragmentDirections.actionGlobalToPaymentInAppPurchase(
                                    title = it.requiredVip.requireVipTitle,
                                    message = it.requiredVip.requireVipDescription,
                                    negativeText = it.requiredVip.btnSkip,
                                    positiveText = it.requiredVip.btnActive,
                                    packageId = it.requiredVip.requireVipPlan,
                                    popupToId = popupToId,
                                    navigationId = navigateToId,
                                    idToPlay = it.intent.id,
                                    continueWatch = true,
                                    isDirect = false,
                                    requestFromCast = requestFromCast
                                ))
                            } else {
                                navHostFragment?.navigateToRequiredBuyPackage(
                                    title = it.requiredVip?.requireVipTitle ?: "",
                                    message = it.requiredVip?.requireVipDescription ?: "",
                                    titleNegative = it.requiredVip?.btnSkip ?: "",
                                    titlePosition = it.requiredVip?.btnActive ?: "",
                                    packageId = it.requiredVip?.requireVipPlan ?: "",
                                    popupToId = popupToId,
                                    navigationId = navigateToId,
                                    idToPlay = it.intent.id,
                                    continueWatch = true,
                                    requestFromCast = requestFromCast
                                )
                            }
                        }
                        is PairingControlViewModel.PairingControlIntent.CheckVODRequirePackage -> {
                            var popupToId = R.id.vod_detail_fragment
                            var navigateToId = R.id.action_global_to_vod
                            if (navHostFragment?.navController?.currentDestination?.id == R.id.vod_detail_fragment) {
                                navigateToId = -1
                            } else {
                                popupToId = -1
                            }
                            if (it.requiredVip?.isTvod == true) {
                                navHostFragment?.findNavController()?.navigate(HomeMainFragmentDirections.actionGlobalToPaymentInAppPurchase(
                                    title = it.requiredVip.requireVipTitle,
                                    message = it.requiredVip.requireVipDescription,
                                    negativeText = it.requiredVip.btnSkip,
                                    positiveText = it.requiredVip.btnActive,
                                    packageId = it.requiredVip.requireVipPlan,
                                    popupToId = popupToId,
                                    navigationId = navigateToId,
                                    idToPlay = it.intent.id,
                                    continueWatch = true,
                                    isDirect = false,
                                    requestFromCast = requestFromCast
                                ))
                            } else {

                                navHostFragment?.navigateToRequiredBuyPackage(
                                    title = it.requiredVip?.requireVipTitle ?: "",
                                    message = it.requiredVip?.requireVipDescription ?: "",
                                    titleNegative = it.requiredVip?.btnSkip ?: "",
                                    titlePosition = it.requiredVip?.btnActive ?: "",
                                    packageId = it.requiredVip?.requireVipPlan ?: "",
                                    popupToId = popupToId,
                                    navigationId = navigateToId,
                                    idToPlay = it.intent.id,
                                    continueWatch = true,
                                    requestFromCast = requestFromCast
                                )
                            }
                        }
                        is PairingControlViewModel.PairingControlIntent.CheckEventRequirePackage -> {
                            var popupToId = R.id.premiere_fragment
                            var navigateToId = R.id.action_global_to_premiere
                            if (navHostFragment?.navController?.currentDestination?.id == R.id.premiere_fragment) {
                                navigateToId = -1
                            } else {
                                popupToId = -1
                            }
                            when (it.intent.type) {
                                "eventtv" -> {
                                    if (it.requiredVip?.isTvod == true) {
                                        navHostFragment?.findNavController()?.navigate(HomeMainFragmentDirections.actionGlobalToPaymentInAppPurchase(
                                            title = it.requiredVip.requireVipTitle,
                                            message = it.requiredVip.requireVipDescription,
                                            negativeText = it.requiredVip.btnSkip,
                                            positiveText = it.requiredVip.btnActive,
                                            packageId = it.requiredVip.requireVipPlan,
                                            popupToId = popupToId,
                                            navigationId = navigateToId,
                                            idToPlay = it.intent.highlightId,
                                            continueWatch = true,
                                            isDirect = false,
                                            requestFromCast = requestFromCast
                                        ))
                                    } else {
                                        navHostFragment?.navigateToRequiredBuyPackage(
                                            title = it.requiredVip?.requireVipTitle ?: "",
                                            message = it.requiredVip?.requireVipDescription ?: "",
                                            titleNegative = it.requiredVip?.btnSkip ?: "",
                                            titlePosition = it.requiredVip?.btnActive ?: "",
                                            packageId = it.requiredVip?.requireVipPlan ?: "",
                                            popupToId = popupToId,
                                            navigationId = navigateToId,
                                            idToPlay = it.intent.highlightId,
                                            continueWatch = true,
                                            requestFromCast = requestFromCast
                                        )
                                    }
                                }
                                "event" -> {
                                    if (it.requiredVip?.isTvod == true) {
                                        navHostFragment?.findNavController()?.navigate(HomeMainFragmentDirections.actionGlobalToPaymentInAppPurchase(
                                            title = it.requiredVip.requireVipTitle,
                                            message = it.requiredVip.requireVipDescription,
                                            negativeText = it.requiredVip.btnSkip,
                                            positiveText = it.requiredVip.btnActive,
                                            packageId = it.requiredVip.requireVipPlan,
                                            popupToId = popupToId,
                                            navigationId = navigateToId,
                                            idToPlay = it.intent.highlightId,
                                            continueWatch = true,
                                            isDirect = false,
                                            requestFromCast = requestFromCast
                                        ))
                                    } else {
                                        navHostFragment?.navigateToRequiredBuyPackage(
                                            title = it.requiredVip?.requireVipTitle ?: "",
                                            message = it.requiredVip?.requireVipDescription ?: "",
                                            titleNegative = it.requiredVip?.btnSkip ?: "",
                                            titlePosition = it.requiredVip?.btnActive ?: "",
                                            packageId = it.requiredVip?.requireVipPlan ?: "",
                                            popupToId = popupToId,
                                            navigationId = navigateToId,
                                            idToPlay = it.intent.highlightId,
                                            continueWatch = true,
                                            requestFromCast = requestFromCast
                                        )
                                    }
                                }
                            }
                        }
                        else -> {}
                    }
                }
                else -> {}
            }
        }
    }


    private fun showWarningDialog(message: String, textClose: String? = null, onClose: (() -> Unit)? = null) {
        navHostFragment?.run {
            alertDialog?.dismissAllowingStateLoss()
            alertDialog = AlertDialog().apply {
                setMessage(message)
                setShowTitle(true)
                setOnlyConfirmButton(true)
                textClose?.let { setTextConfirm(it) }
                setListener(object : AlertDialogListener {
                    override fun onConfirm() {
                        onClose?.invoke()
                    }
                })
                isCancelable = false
            }
            alertDialog?.show(navHostFragment.childFragmentManager, "AlertDialog")
        }
    }

}