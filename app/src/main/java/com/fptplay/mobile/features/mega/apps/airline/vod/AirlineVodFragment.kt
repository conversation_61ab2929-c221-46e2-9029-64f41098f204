package com.fptplay.mobile.features.mega.apps.airline.vod

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.AirlineVodFragmentBinding
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineIntent.GetAirlineStructure
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.Done
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.Error
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.ErrorNoInternet
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.ErrorRequiredLogin
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.Loading
import com.fptplay.mobile.features.mega.apps.airline.AirlineViewModel.AirlineState.ResultAirlineStructure
import com.fptplay.mobile.features.mega.apps.airline.model.AirlineBrand
import com.fptplay.mobile.features.mega.apps.airline.util.AirlineUtils
import com.fptplay.mobile.features.mega.apps.airline.vod.pager.AirlineVodPagerAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class AirlineVodFragment: BaseFragment<AirlineViewModel.AirlineState, AirlineViewModel.AirlineIntent>() {

    //region Variables
    override val viewModel by activityViewModels<AirlineViewModel>()
    private var _binding: AirlineVodFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs by navArgs<AirlineVodFragmentArgs>()

    private lateinit var pagerAdapter: AirlineVodPagerAdapter
    // Use cached data because we can't reuse pagerAdapter after fragment's view is destroyed
    private val cachedFragments by lazy { mutableListOf<Fragment>() }
    private val cachedTitles by lazy { mutableListOf<String>() }
    //endregion

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = AirlineVodFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindComponent() {
        if (viewModel.airlineBrand() == AirlineBrand.VIETJET_AIR) {
//            binding.tabLayout.setTabTextColors(Color.parseColor("#888888"), Color.parseColor("#FF6400"))
//            binding.tabLayout.setTabTextColors(R.color.airline_bottom_navigation_text_color, R.color.airline_bottom_navigation_vietjet_air_focused_text_color)

            context?.let {
                binding.tabLayout.setTabTextColors(
                    it.getColor(R.color.airline_bottom_navigation_text_color),
                    it.getColor(R.color.airline_bottom_navigation_vietjet_air_focused_text_color)
                )

            }
        }

        pagerAdapter = AirlineVodPagerAdapter(childFragmentManager, lifecycle)
        binding.viewPager.adapter = pagerAdapter
        if(cachedTitles.size > 3){
            binding.viewPager.offscreenPageLimit = cachedTitles.size-1
        }else binding.viewPager.offscreenPageLimit = 1

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = pagerAdapter.title(position)
        }.attach()
    }

    override fun bindData() {
        if (cachedFragments.isEmpty()) {
            viewModel.dispatchIntent(GetAirlineStructure(getAirlineSourceProvider()))
        } else {
            pagerAdapter.restoreData(cachedFragments, cachedTitles)
            viewModel.triggerUpdateNoInternetView(isShow = true)
        }
    }

    override fun AirlineViewModel.AirlineState.toUI() {
        Timber.d("AirlineState: $this")
        when (this) {
            is Loading -> {}
            is ResultAirlineStructure -> {
                binding.clContentNoInternet.hide()
                viewModel.triggerUpdateNoInternetView(isShow = true)
                pagerAdapter.bindData(requireContext(), data)
                cachedFragments.clear()
                cachedTitles.clear()
                cachedFragments.addAll(pagerAdapter.fragments())
                cachedTitles.addAll(pagerAdapter.titles())
            }
            is Error -> {}
            is ErrorNoInternet -> {
                binding.clContentNoInternet.show()
                viewModel.triggerUpdateNoInternetView(isShow = false)
            }
            is ErrorRequiredLogin -> {}
            is Done -> {}
            else -> {}
        }
    }
    //endregion

    //region Commons
    private fun getAirlineSourceProvider(): String {
        val sourceProvider = when (viewModel.airlineBrand()) {
            AirlineBrand.VN_AIRLINE -> {
                when (safeArgs.type) {
                    AirlineUtils.AIRLINE_MOVIE -> AirlineUtils.SOURCE_PROVIDER_VN_AIRLINE_MOVIE
                    AirlineUtils.AIRLINE_TV -> AirlineUtils.SOURCE_PROVIDER_VN_AIRLINE_TV
                    AirlineUtils.AIRLINE_KID -> AirlineUtils.SOURCE_PROVIDER_VN_AIRLINE_KID
                    else -> null
                }
            }
            AirlineBrand.BAMBOO_AIRWAYS -> {
                when (safeArgs.type) {
                    AirlineUtils.AIRLINE_MOVIE -> AirlineUtils.SOURCE_PROVIDER_BAMBOO_AIRWAYS_MOVIE
                    AirlineUtils.AIRLINE_TV -> AirlineUtils.SOURCE_PROVIDER_BAMBOO_AIRWAYS_TV
                    AirlineUtils.AIRLINE_KID -> AirlineUtils.SOURCE_PROVIDER_BAMBOO_AIRWAYS_KID
                    else -> null
                }
            }
            AirlineBrand.VIETJET_AIR -> {
                when (safeArgs.type) {
                    AirlineUtils.AIRLINE_MOVIE -> AirlineUtils.SOURCE_PROVIDER_VIETJET_AIR_MOVIE
                    AirlineUtils.AIRLINE_TV -> AirlineUtils.SOURCE_PROVIDER_VIETJET_AIR_TV
                    AirlineUtils.AIRLINE_KID -> AirlineUtils.SOURCE_PROVIDER_VIETJET_AIR_KID
                    AirlineUtils.AIRLINE_SPORT -> AirlineUtils.SOURCE_PROVIDER_VIETJET_AIR_SPORT
                    else -> null
                }
            }
            else -> null
        }
        return sourceProvider ?: ""
    }
    //endregion
}