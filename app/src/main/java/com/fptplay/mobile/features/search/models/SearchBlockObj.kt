package com.fptplay.mobile.features.search.models

import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.search.SearchV2.SearchItemV2

data class SearchBlockObj(
    val title: String = "",
    val blockType: BlockType = BlockType.VOD,
    val description: String = "",
    val searchList: List<SearchItemV2>
    ) : BaseObject()

enum class BlockType {
    VOD,
    RECENT,
    TRENDING,
    TYPING,
    TOP_MOMENT,
    SUGGEST_MOMENT
}
