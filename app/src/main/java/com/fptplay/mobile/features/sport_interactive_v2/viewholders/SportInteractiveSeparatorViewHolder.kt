package com.fptplay.mobile.features.sport_interactive_v2.viewholders

import com.fptplay.mobile.databinding.SportInteractiveSeparatorViewBinding
import com.fptplay.mobile.features.sport_interactive_v2.adpters.BaseSportInteractiveViewHolder
import com.fptplay.mobile.features.sport_interactive_v2.models.common.SportInteractiveSeparatorData

class SportInteractiveSeparatorViewHolder(private val binding: SportInteractiveSeparatorViewBinding): BaseSportInteractiveViewHolder<SportInteractiveSeparatorData>(binding) {
    override fun bind(data: SportInteractiveSeparatorData) {
        //nothing
    }
}