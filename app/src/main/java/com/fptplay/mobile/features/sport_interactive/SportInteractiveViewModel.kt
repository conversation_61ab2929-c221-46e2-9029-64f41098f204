package com.fptplay.mobile.features.sport_interactive

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.features.sport_interactive.model.SportMatchDetail
import com.fptplay.mobile.features.sport_interactive.model.SportMatchLiveScores
import com.fptplay.mobile.features.sport_interactive.model.SportMatchProcess
import com.fptplay.mobile.features.sport_interactive.model.SportMatchStatistic
import com.fptplay.mobile.features.sport_interactive.model.SportTeamSquad
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.repository.fplay.SportRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SportInteractiveViewModel @Inject constructor(
    private val savedState: SavedStateHandle,
    private val sportRepository: SportRepository,
): BaseViewModel<SportInteractiveViewModel.SportInteractiveIntent, SportInteractiveViewModel.SportInteractiveState>() {

    // common info
    private var _sportMatchDetailUpdate = MutableLiveData<SportMatchDetail?>()
    val sportMatchDetail get() = _sportMatchDetailUpdate

    fun triggerSportMatchDetailUpdate(newValue: SportMatchDetail?) {
        _sportMatchDetailUpdate.postValue(newValue)
    }

    // stats: Thông số
    private var _sportMatchStatisticUpdate = MutableLiveData<SportMatchStatistic?>()
    val sportMatchStatistic get() = _sportMatchStatisticUpdate

    fun triggerSportMatchStatisticUpdate(newValue: SportMatchStatistic?) {
        _sportMatchStatisticUpdate.postValue(newValue)
    }

    // Match process: Diễn biến
    private var _sportMatchProcessUpdate = MutableLiveData<SportMatchProcess?>()
    val sportMatchProcess get() = _sportMatchProcessUpdate

    fun triggerSportMatchProcessUpdate(newValue: SportMatchProcess?) {
        _sportMatchProcessUpdate.postValue(newValue)
    }

    // Squad: Đội hình
    private var _sportTeamSquadUpdate = MutableLiveData<SportTeamSquad?>()
    val sportTeamSquad get() = _sportTeamSquadUpdate

    fun triggerSportTeamSquadUpdate(newValue: SportTeamSquad?) {
        _sportTeamSquadUpdate.postValue(newValue)
    }

    // LiveScore
    private var _sportMatchLiveScoreUpdate = MutableLiveData<SportMatchLiveScores?>()
    val sportMatchLiveScore get() = _sportMatchLiveScoreUpdate

    fun triggerSportMatchLiveScoreUpdate(newValue: SportMatchLiveScores?) {
        _sportMatchLiveScoreUpdate.postValue(newValue)
    }

    //region Overrides
    override fun dispatchIntent(intent: SportInteractiveIntent) {
        safeLaunch {
            when (intent) {
                else -> {}
            }
        }
    }
    override fun <T> Result<T>.reduce(
        intent: SportInteractiveIntent?,
        successFun: (Boolean, T) -> SportInteractiveState
    ): SportInteractiveState {
        return when (this) {
            is Result.Init -> SportInteractiveState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.UserError.RequiredLogin -> SportInteractiveState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )
            is Result.Error -> SportInteractiveState.Error(this.message, intent = intent)
            Result.Done -> SportInteractiveState.Done(intent = intent)
        }
    }
    //endregion

    //region Intent, State
    sealed class SportInteractiveState: ViewState {
        data class Loading(val intent: SportInteractiveIntent? = null) : SportInteractiveState()
        data class Error(val message: String, val intent: SportInteractiveIntent? = null) : SportInteractiveState()
        data class ErrorRequiredLogin(val message: String, val intent: SportInteractiveIntent? = null) : SportInteractiveState()
        data class Done(val intent: SportInteractiveIntent? = null) : SportInteractiveState()
    }

    sealed class SportInteractiveIntent: ViewIntent {
    }
    //endregion
}