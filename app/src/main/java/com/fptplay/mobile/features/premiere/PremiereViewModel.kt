package com.fptplay.mobile.features.premiere

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.fptplay.mobile.R
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppView
import com.fptplay.mobile.common.global.SourcePlayObject
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.game_30s.vote.entites.VoteEntities
import com.fptplay.mobile.features.premiere.data.PremiereLiveState
import com.fptplay.mobile.features.premiere.data.PremiereTabItem
import com.fptplay.mobile.features.premiere.data.PremiereTabRequest
import com.fptplay.mobile.features.premiere.live_chat.LiveChatV2ViewModel.LiveChatV2Intent
import com.fptplay.mobile.features.premiere.live_chat.LiveChatV2ViewModel.LiveChatV2State
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.handler.drm.DrmApi
import com.tear.modules.player.util.PlayerControlView
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.common.Constant
import com.xhbadxx.projects.module.domain.entity.fplay.common.ChatMessagePage
import com.xhbadxx.projects.module.domain.entity.fplay.common.Status
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.drm.DrmKey
import com.xhbadxx.projects.module.domain.entity.fplay.drm.Ping
import com.xhbadxx.projects.module.domain.entity.fplay.drm.PingStreamV2
import com.xhbadxx.projects.module.domain.entity.fplay.game.gamelive.GameLive
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemContainer
import com.xhbadxx.projects.module.domain.entity.fplay.premier.Details
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.domain.repository.fplay.*
import com.xhbadxx.projects.module.domain.required.LivePreviewInfo
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.zip
import javax.inject.Inject
import kotlin.system.measureTimeMillis

@HiltViewModel
class PremiereViewModel @Inject constructor(
    private val savedState: SavedStateHandle,
    private val commonRepository: CommonRepository,
    private val userRepository: UserRepository,
    private val liveRepository: LiveRepository,
    private val vodRepository: VodRepository,
    private val drmRepository: DrmRepository,
    private val premiereRepository: PremierRepository,
    private val homeRepository: HomeOs4Repository,
    private val sharedPreferences: SharedPreferences
) : BaseViewModel<PremiereViewModel.PremiereIntent, PremiereViewModel.PremiereState>(), DrmApi {

    private val getStreamScope = CoroutineScope(Dispatchers.IO)
    private var getStreamJob: Job? = null

    private var _initPlayer = MutableLiveData<Nothing?>()
    val initPlayer get() = _initPlayer

    private var _playPremiere = MutableLiveData<String?>() // Pair<id>
    val playPremiere get() = _playPremiere

    private var _isShowRanking = MutableLiveData<Boolean?>() // Pair<id>
    val isShowRanking get() = _isShowRanking

    private var _restartPlayer = MutableLiveData<Boolean?>()
    val restartPlayer get() = _restartPlayer

    private var _playRequiredVipTrailer = MutableLiveData<String?>()
    val playRequiredVipTrailer get() = _playRequiredVipTrailer

    private var _isFullScreen = MutableLiveData<Triple<Boolean, Boolean, Boolean>?>() // Triple<isFullscreen, isLandscape, isPlayerScaled>
    val isFullScreen get() = _isFullScreen

    private var _playPreview = MutableLiveData<LivePreviewInfo?>()
    val playPreview get() = _playPreview

    private var _buyPackageForPreview = MutableLiveData<Boolean?>()
    val buyPackageForPreview get() = _buyPackageForPreview

    val hasPreview get() = isVipRequired?.second?.enablePreview ?: false

    val availablePreview get() = isVipRequired?.second?.livePreviewInfo?.available ?: false

    val canPreview get() = hasPreview && availablePreview && !isVipRequired?.second?.livePreviewInfo?.url.isNullOrBlank()

    private var publicIp = ""

    private var isFollow = false
    private var isFirstTimePlay = true
    private var detail: Details? = null
    private var tabData: List<PremiereTabItem>? = null
    private var isVipRequired : Pair<Boolean, RequiredVip?>? = null
    private var _playerBitrates: List<PlayerControlView.Data.Bitrate>? = null
    private var _playerTracks: List<PlayerControlView.Data.Track>? = null

    private var eventType: EventType = EventType.EVENT

    override fun dispatchIntent(intent: PremiereIntent) {
        safeLaunch {
            when (intent) {
                is PremiereIntent.TriggerMulticam -> {
                    _state.value = PremiereState.ResultTriggerMulticam
                }
                is PremiereIntent.GetPublicIp -> {
                    commonRepository.getPublicIp().collect {
                        if (it is Result.Success) {
                            publicIp = it.successData
                        }
                    }
                }
                is PremiereIntent.GetDetail -> {
                    val dataType: String =
                        if (intent.premiereId == SourcePlayObject.SourcePlayPremier.getBlockDataType()?.first) {
                            SourcePlayObject.SourcePlayPremier.getBlockDataType()?.second ?: ""
                        } else {
                            SourcePlayObject.SourcePlayPremier.clearBlockDataType()
                            ""
                        }

                    premiereRepository.getDetail(premierId = intent.premiereId, dataType = dataType).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            saveDataDetail(data = it.data)
                            PremiereState.ResultDetail(isCached = isCached, data = data, intent = intent)
                        }
                    }
                }
                is PremiereIntent.GetBlockItems -> {
                    getTabItems(intent = intent)
                }
                is PremiereIntent.TriggerGetStream -> _state.value = PremiereState.ResultTriggerGetStream(requireCheckPackage = intent.requireCheckPackage)
                is PremiereIntent.TriggerUpdateCheckEndTime -> _state.value = PremiereState.ResultTriggerUpdateCheckEndTime
                is PremiereIntent.TriggerUnlockPlayerControl -> _state.value = PremiereState.ResultTriggerUnlockPlayerControl
                is PremiereIntent.TriggerLoginSuccess -> _state.value = PremiereState.ResultTriggerLoginSuccess
                is PremiereIntent.TriggerStopPlayer -> _state.value = PremiereState.ResultTriggerStopPlayer
                is PremiereIntent.TriggerUpdatePlayState -> _state.value = PremiereState.ResultTriggerUpdatePlayState
                is PremiereIntent.GetStream -> {
                    getStreamJob?.cancel()
                    if (intent.delay != 0L) {
                        getStreamJob = getStreamScope.launch {
                            delay(intent.delay)
                            getStream(intent = intent)
                        }
                    } else {
                        getStream(intent = intent)
                    }
                }
                is PremiereIntent.CheckFollow -> {
                    if (intent.isPremiere) {
                        vodRepository.checkFollow(type = Constant.VOD_TYPE, id = intent.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultCheckFollow(isCached = isCached, data = data, intent = intent)}
                        }
                    } else {
                        liveRepository.checkFollow(type = Constant.LIVE_TV_TYPE, id = intent.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultCheckFollow(isCached = isCached, data = data, intent = intent)}
                        }
                    }
                }
                is PremiereIntent.AddFollow -> {
                    if (intent.isPremiere) {
                        vodRepository.addFollow(type = Constant.VOD_TYPE, id = intent.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultAddFollow(isCached = isCached, data = data, intent = intent)}
                        }
                    } else {
                        liveRepository.addFollow(type = Constant.LIVE_TV_TYPE, id = intent.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultAddFollow(isCached = isCached, data = data, intent = intent)}
                        }
                    }
                }
                is PremiereIntent.DeleteFollow -> {
                    if (intent.isPremiere) {
                        vodRepository.deleteFollow(type = Constant.VOD_TYPE, id = intent.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultDeleteFollow(isCached = isCached, data = data, intent = intent)}
                        }
                    } else {
                        liveRepository.deleteFollow(type = Constant.LIVE_TV_TYPE, id = intent.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultDeleteFollow(isCached = isCached, data = data, intent = intent)}
                        }
                    }
                }
                is PremiereIntent.GetInfoUser -> {
                    userRepository.getUserInfo().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultUserInfo(isCached = isCached, data = data) }
                    }
                }
                is PremiereIntent.GetChat -> {
                    commonRepository.getChats(roomId = intent.roomId, page = intent.page).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultGetChat(isCached = isCached, data = data) }
                    }
                }
                is PremiereIntent.GetMoreChat -> {
                    commonRepository.getChats(roomId = intent.roomId, page = intent.page).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultGetMoreChat(isCached = isCached, data = data) }
                    }
                }
                is PremiereIntent.TriggerPlayerLayout -> {
                    _state.value = PremiereState.ResultTriggerPlayerLayout(isScale = intent.isScale)
                }
                is PremiereIntent.TriggerOpenLiveChatFullScreen -> {
                    _state.value = PremiereState.ResultTriggerOpenLiveChatFullScreen(isOpen = intent.isOpen)
                }
                is PremiereIntent.TriggerClickPremiereDescription -> {
                    _state.value = PremiereState.ResultClickPremiereDescription(isOpen = intent.isOpen)
                }

                is PremiereIntent.TriggerEndTime -> _state.value = PremiereState.ResultTriggerEndTime

                is PremiereIntent.ReflectionDetailUI -> _state.value = PremiereState.ResultReflectionDetailUI
                is PremiereIntent.ReflectionFollowButtonUI -> _state.value = PremiereState.ResultReflectionFollowButtonUI
                is PremiereIntent.ReflectionMulticamUI -> _state.value = PremiereState.ResultReflectionMulticamUI
                is PremiereIntent.ReflectionOpenLiveChat -> _state.value = PremiereState.ResultReflectionOpenLiveChat(isOpen = intent.isOpen)
                is PremiereIntent.ReflectionTabsUI -> _state.value = PremiereState.ResultReflectionTabsUI
                is PremiereIntent.ReflectionUpdateLiveState -> _state.value = PremiereState.ResultReflectionUpdateLiveState(text = intent.text, state = intent.state)
                is PremiereIntent.TriggerOpenVotingPopUp -> {
                    _state.value =
                        PremiereState.OpenVotingPopUp(jsonData = intent.jsonData, vote = intent.vote)
                }
                is PremiereIntent.GetViewRankingCommon -> {
                    commonRepository.getGameLive(intent.gameId).collect{
                        _state.value = it.reduce(intent = intent) { isCached, data -> PremiereState.ResultViewRankingCommon(isCached = isCached,data = data)
                        }
                    }
                }

                is PremiereIntent.SwitchPlayerMode -> {
                    _state.value = PremiereState.ResultSwitchPlayerMode(modeFullscreen = intent.modeFullscreen)
                }
                is PremiereIntent.TriggerOpenTabDataFeelExistFullScreen->{
                    _state.value = PremiereState.ResultTriggerOpenTabDataFeelExistFullScreen

                }
                is PremiereIntent.TriggerShowMsgUserReport ->{
                    _state.value = PremiereState.ResultTriggerMsgUserReport(
                        isReported = intent.isReported, message = intent.message
                    )
                    _state.value = PremiereState.Done(intent = intent)
                }
                PremiereIntent.TriggerStopSportInteractive -> {
                    _state.value = PremiereState.StopSportInteractive
                }

                is PremiereIntent.SaveDRMKey -> {
                    drmRepository.insert(drmKey = intent.drmKey).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            PremiereState.ResultSaveDRMKey(isCached = isCached, data = data)
                        }
                    }
                }

                is PremiereIntent.AutoOpenLiveChat -> {
                    _state.value = PremiereState.OpenLiveChat
                }
            }
        }
    }
    private suspend fun getStream(intent: PremiereIntent.GetStream) {
        if (intent.isPremiere) {
            //Event is using VOD Stream
            vodRepository.getStream(
                id = intent.id,
                episodeId = intent.episodeId,
                bitrateId = intent.bitrateId,
                dataType = SourcePlayObject.SourcePlayPremier.getBlockDataType()?.second
                    ?: ""
            ).zip(
                drmRepository.getDrmOfflineKey(
                    PlayerUtils.getEventDrmUid(
                        isPremiere = true,
                        vodId = intent.id,
                        episodeId = intent.episodeId
                    )
                )
            ) { stream, drmKey ->
                when (stream) {
                    is Result.Success -> {
                        if (drmKey is Result.Success) {
                            PremiereState.ResultStream(
                                isCached = stream.isCached,
                                data = stream.successData,
                                drmKey = drmKey.successData,
                                intent = intent
                            )
                        } else {
                            PremiereState.ResultStream(
                                isCached = stream.isCached,
                                data = stream.successData,
                                drmKey = null,
                                intent = intent
                            )
                        }
                    }

                    else -> {
                        stream.reduce(intent) { isCached, data ->
                            PremiereState.ResultStream(
                                isCached = isCached,
                                data = data,
                                drmKey = null,
                                intent = intent
                            )
                        }
                    }
                }
            }.collect {
                viewModelScope.launch(Dispatchers.Main) {
                    _state.value = it
                }
            }
        } else {
            liveRepository.getTvChannelStream(
                id = intent.id,
                bitrateId = intent.bitrateId,
                blockDataType = SourcePlayObject.SourcePlayPremier.getBlockDataType()?.second
                    ?: "",
                enablePreview = intent.enablePreview
            )
                .zip(
                    drmRepository.getDrmOfflineKey(
                        PlayerUtils.getEventDrmUid(
                            isPremiere = false,
                            channelId = intent.id
                        )
                    )
                ) { stream, drmKey ->
                    when (stream) {
                        is Result.Success -> {
                            stream.data?.let { data ->
                                when (data.codeError) {
                                    4 -> {
                                        PremiereState.ErrorRequiredVip(
                                            message = data.message,
                                            intent = intent,
                                            requiredVip = RequiredVip(
                                                requireVipTitle = data.requireVipTitle,
                                                message = data.message,
                                                btnActive = data.btnActive,
                                                btnSkip = data.btnSkip,
                                                requireVipImage = data.vipImage,
                                                requireVipName = data.name,
                                                trailerUrl = data.urlTrailer,
                                                requireVipPlan = data.vipPlan,
                                            )
                                        )
                                    }

                                    else -> {
                                        if (drmKey is Result.Success) {
                                            PremiereState.ResultStream(
                                                isCached = stream.isCached,
                                                data = data,
                                                drmKey = drmKey.successData,
                                                intent = intent
                                            )
                                        } else {
                                            PremiereState.ResultStream(
                                                isCached = stream.isCached,
                                                data = data,
                                                drmKey = null,
                                                intent = intent
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        else -> {
                            stream.reduce(intent) { isCached, data ->
                                PremiereState.ResultStream(
                                    isCached = isCached,
                                    data = data,
                                    drmKey = null,
                                    intent = intent
                                )
                            }
                        }
                    }
                }.collect {
                    viewModelScope.launch(Dispatchers.Main) {
                        _state.value = it
                    }
                }
        }
    }

    private suspend fun getTabItems(
        intent: PremiereIntent.GetBlockItems
    ) {
        val measureTime = measureTimeMillis {
            withContext(Dispatchers.IO) {
                try {
                    val arrDeferred: ArrayList<Deferred<Unit>> = arrayListOf()
                    val requests = intent.requests
                    val listResult = Array<PremiereState>(requests.size) { PremiereState.Loading() }
                    withTimeoutOrNull(10_000L) {
                        requests.forEachIndexed { index, premiereTabRequest ->
                            arrDeferred.add(async {
                                homeRepository.getStructureItemWithMeta(
                                    type = premiereTabRequest.type,
                                    blockId = premiereTabRequest.blockId,
                                    pageIndex = premiereTabRequest.pageIndex,
                                    pageSize = premiereTabRequest.pageSize,
                                    customData = "",
                                    watchingVersion = null,
                                    blockType = ""
                                ).process { isCached, data -> listResult[index] = (PremiereState.ResultBlockItems(isCached = isCached, data = data, intent = intent)) }
                            })
                        }
                        // Parallel the all requests
                        arrDeferred.forEach { it.await() }
                    }
                    withContext(Dispatchers.Main) {
                        _state.value = PremiereState.ResultAllBlockItems(data = listResult.toList())
                    }
                }
                catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
            } // Process timeout or return null
            withContext(Dispatchers.Main) {
                _state.value = PremiereState.Done()
            }
        }
        Logger.d("Measure time of GetTabItems: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }

    override fun <T> Result<T>.reduce(
        intent: PremiereIntent?,
        successFun: (Boolean, T) -> PremiereState
    ): PremiereState {
        return when (this) {
            is Result.Init -> PremiereState.Loading(intent = intent)
            is Result.Success -> successFun(this.isCached, this.successData)
            is Result.UserError.RequiredLogin -> {
                if(intent is PremiereIntent.CheckFollow) {
                    PremiereState.Error(message = this.message, intent = intent)
                } else {
                    PremiereState.ErrorRequiredLogin(
                        message = this.message,
                        intent = intent,
                        requiredLogin = this.requiredLogin
                    )
                }
            }
            is Result.UserError.RequiredVip -> PremiereState.ErrorRequiredVip(
                message = this.message,
                intent = intent,
                requiredVip = this.requiredVip
            )
            is Result.ServerError.ItemNotFound -> PremiereState.ErrorItemNotFound(message = this.message, intent = intent)
            is Result.Error -> {
                when (this) {
                    is Result.Error.Intenet -> PremiereState.ErrorNoInternet(message = this.message, intent = intent)
                    else -> PremiereState.Error(message = this.message, intent = intent)
                }
            }
            is Result.Done -> PremiereState.Done(intent = intent)
        }
    }

    private suspend fun <T> Flow<Result<T>>.process(
        successFun: (Boolean, T) -> Unit
    ) {
        this
            .collect {
                if (it is Result.Success) {
                    it.let { res ->
                        successFun(res.isCached, res.successData)
                    }
                }
            }
    }

    override fun resetState() {
        _playPremiere.value = null
        _restartPlayer.value = null
        _playRequiredVipTrailer.value = null
        _playPreview.value = null
        _buyPackageForPreview.value = null
    }

    fun cancelGetStreamJob() {
        getStreamJob?.cancel()
    }

    //region Save Data
    fun getPublicIp() = publicIp

    fun savePremiereId(premiereId: String) { savedState.set("premiere_id", premiereId) }
    fun getPremiereId() = savedState.get<String>("premiere_id") ?: ""

    fun saveId(id: String) { savedState.set("id", id) }
    fun getId() = savedState.get<String>("id") ?: ""

    fun saveDataDetail(data: Details?) {
        detail = data
        saveDetailDataForTrackingLog()
    }
    fun getDataDetail(): Details? { return detail }

    fun saveTabData(tabData: List<PremiereTabItem>?) { this.tabData = tabData }
    fun getTabData(): List<PremiereTabItem>? { return this.tabData }

    fun saveFollow(isFollow: Boolean) { this.isFollow = isFollow }
    fun isFollow() = this.isFollow

    fun saveFirstTimePlay(isFirstTimePlay: Boolean) { this.isFirstTimePlay = isFirstTimePlay}
    fun isFirstTimePlay() = this.isFirstTimePlay

    fun saveEventEndTime(endTime: Long) = savedState.set("eventEndTime", endTime)
    fun getEventEndTime() = savedState.get<Long>("eventEndTime") ?: 0L

    fun saveVipRequired(isRequired: Boolean, requiredVip: RequiredVip? = null) { isVipRequired = Pair(isRequired, requiredVip)}
    fun getVipRequired() = isVipRequired

    fun saveCurrentPlayerBitrates(bitrates: List<PlayerControlView.Data.Bitrate>?) { _playerBitrates = bitrates }
    fun getCurrentPlayerBitrates() = _playerBitrates

    fun saveTracks(tracks: List<PlayerControlView.Data.Track>?) { _playerTracks = tracks }
    fun getTracks() = _playerTracks

    fun savePlayerOptionsType(type: String) { savedState.set("playerOptionsType", type) }
    fun getPlayerOptionsType() = savedState.get<String>("playerOptionsType") ?: ""

    fun savePlayerOptionsCurItem(idCurItem: String) { savedState.set("playerOptionsCurItem", idCurItem) }
    fun getPlayerOptionsCurItem() = savedState.get<String>("playerOptionsCurItem") ?: ""

    fun saveBitrateId(bitrateId: String) { savedState.set("bitrateId", bitrateId) }
    fun getBitrateId(): String = savedState.get<String>("bitrateId") ?: ""

    fun saveClickTimeToPlay(clickTime: Long) { savedState.set("clickTimeToPlay", clickTime) }
    fun getClickTimeToPlay(): Long = savedState.get<Long>("clickTimeToPlay") ?: 0L

    fun savePrepareSourceTimeInMs(time: Long) = savedState.set("prepareSourceTimeInMs", time)
    fun getPrepareSourceTimeInMs(): Long = savedState.get("prepareSourceTimeInMs") ?: 0

    fun saveIsRetryPlay(isRetryPlay: Boolean) { savedState.set("isRetryPlay", isRetryPlay) }
    fun getIsRetryPlay(): Boolean = savedState.get<Boolean>("isRetryPlay") ?: false

    fun saveEventType(value: String) {
        eventType = (EventType from value.lowercase()) ?: EventType.EVENT
    }
    fun getEventType() = eventType

    fun getTextDescriptionBellowPlayer(context: Context): String {
        return getDataDetail()?.let {
            it.buyNoticeStream.ifBlank { context.getString(R.string.please_register_to_continue_watching) }
        } ?: kotlin.run {
            context.getString(R.string.please_register_to_continue_watching)
        }
    }

    fun getTextButtonBellowPlayer(context: Context): String {
        return getDataDetail()?.let {
            it.btnBuyStream.ifBlank { context.getString(R.string.register) }
        } ?: kotlin.run {
            context.getString(R.string.register)
        }
    }

    //endregion

    //region game
    fun saveIsShowRanking(isShowRanking: Boolean)  { savedState.set("IsShowRanking", isShowRanking) }
    fun getIsShowRanking() :Boolean=savedState.get<Boolean>("IsShowRanking") ?: false
    fun deleteIsShowRanking() =savedState.clearSavedStateProvider("IsShowRanking")
    // endregion game
    private fun saveDetailDataForTrackingLog(){
        detail?.let {
            TrackingUtil.setIdContentAndRefId(idContent = it.id, refId = it.refId)
            TrackingUtil.setRefEpisodeId(epiId = it.episodeId, refEpisode = it.refEpisodeId)
        }?: kotlin.run {
            TrackingUtil.resetDataPlaying()
        }
    }


    //region Trigger Event
    fun triggerInitPlayer() {
        _initPlayer.postValue(null)
    }

    fun triggerPlayPremiere(id: String) {
        _playPremiere.postValue(id)
    }
    
    fun triggerRestartPlayer() {
        _restartPlayer.postValue(true)
    }
    
    fun triggerPlayRequiredVipTrailer(url: String) {
        _playRequiredVipTrailer.postValue(url)
    }

    fun triggerPlayPreview(info: LivePreviewInfo?) {
        _playPreview.postValue(info)
    }

    fun triggerBuyPackageForPreview(showRequirePackage: Boolean?) {
        _buyPackageForPreview.postValue(showRequirePackage)
    }
    fun triggerFullScreen(isFull: Boolean, isLandscapeMode: Boolean, isScaled: Boolean) {
        _isFullScreen.postValue(Triple(isFull, isLandscapeMode, isScaled))
    }

    fun triggerOpenLiveChatSuccess() {
        _state.postValue(PremiereState.OpenLiveChatSuccess)
    }
    //endregion

    sealed class PremiereIntent: ViewIntent {
        object GetPublicIp : PremiereIntent()
        object TriggerMulticam : PremiereIntent()
        object GetInfoUser : PremiereIntent()
        data class GetDetail(val premiereId: String) : PremiereIntent()
        data class GetBlockItems(val requests: List<PremiereTabRequest>) : PremiereIntent()
        data class TriggerGetStream(val requireCheckPackage : Boolean = false) : PremiereIntent()
        object TriggerUpdatePlayState : PremiereIntent()
        object TriggerUpdateCheckEndTime : PremiereIntent()
        object TriggerUnlockPlayerControl : PremiereIntent()
        data class GetStream(
            val requireCheckPackage : Boolean = false,
            val isPremiere: Boolean,
            val id: String, val episodeId: String,
            val bitrateId: String, val delay: Long = 0L,
            val enablePreview: String,
            val isRetry: Boolean = false,
        ) : PremiereIntent()
        data class CheckFollow(val isPremiere: Boolean, val id: String) : PremiereIntent()
        data class AddFollow(val isPremiere: Boolean, val id: String) : PremiereIntent()
        data class DeleteFollow(val isPremiere: Boolean, val id: String) : PremiereIntent()
        data class GetChat(val roomId: String, val page: String) : PremiereIntent()
        data class GetMoreChat(val roomId: String, val page: String) : PremiereIntent()
        data class TriggerPlayerLayout(val isScale: Boolean) :  PremiereIntent()
        object TriggerOpenTabDataFeelExistFullScreen:  PremiereIntent()
        data class TriggerOpenLiveChatFullScreen(val isOpen: Boolean) : PremiereIntent()
        data class TriggerClickPremiereDescription(val isOpen: Boolean? = null) : PremiereIntent()
        object TriggerEndTime : PremiereIntent()
        object TriggerLoginSuccess : PremiereIntent()
        object TriggerStopPlayer : PremiereIntent()
        /**
         * @param modeFullscreen: If you want to enter fullscreen, pass "true" to param, otherwise pass "false" -> exit fullscreen
         */
        data class SwitchPlayerMode(val modeFullscreen: Boolean) : PremiereIntent()

        //
        object ReflectionDetailUI : PremiereIntent()
        object ReflectionTabsUI : PremiereIntent()
        object ReflectionFollowButtonUI : PremiereIntent()
        object ReflectionMulticamUI : PremiereIntent()
        data class ReflectionOpenLiveChat(val isOpen: Boolean) : PremiereIntent()
        data class ReflectionUpdateLiveState(val text: String, val state: PremiereLiveState) : PremiereIntent()
        //
        // region game
        data class TriggerOpenVotingPopUp(val jsonData:String,val vote: VoteEntities?) :  PremiereIntent()
        data class GetViewRankingCommon(val gameId: String) : PremiereIntent()

        // endregion game

        object TriggerStopSportInteractive: PremiereIntent()


        //region ReportPlayer
        data class TriggerShowMsgUserReport(val isReported: Boolean = false , val message: String): PremiereIntent()
        //endregion

        //region drm
        data class SaveDRMKey(val drmKey: DrmKey) : PremiereIntent()
        //endregion

        object AutoOpenLiveChat: PremiereIntent()

    }

    sealed class PremiereState: ViewState {
        data class Loading(val intent: PremiereIntent? = null) : PremiereState()
        data class ResultDetail(val isCached: Boolean, val data: Details ?= null, val intent: PremiereIntent? = null) : PremiereState()
        object ResultGetDetailDone : PremiereState()
        data class ResultBlockItems(val isCached: Boolean, val data: StructureItemContainer?= null, val intent: PremiereIntent? = null) : PremiereState()
        data class ResultAllBlockItems(val data: List<PremiereState> ?= null) : PremiereState()
        data class ResultTriggerGetStream(val requireCheckPackage : Boolean = false) : PremiereState()
        object ResultTriggerUpdatePlayState : PremiereState()
        object ResultTriggerUpdateCheckEndTime : PremiereState()
        object ResultTriggerUnlockPlayerControl : PremiereState()
        data class ResultStream(val isCached: Boolean, val data: Stream, val drmKey: DrmKey?, val intent: PremiereIntent.GetStream) : PremiereState()
        data class ResultCheckFollow(val isCached: Boolean, val data: Status, val intent: PremiereIntent.CheckFollow) : PremiereState()
        data class ResultAddFollow(val isCached: Boolean, val data: Status, val intent: PremiereIntent.AddFollow) : PremiereState()
        data class ResultDeleteFollow(val isCached: Boolean, val data: Status, val intent: PremiereIntent.DeleteFollow) : PremiereState()
        data class ResultGetChat(val isCached: Boolean, val data: ChatMessagePage?) : PremiereState()
        data class ResultGetMoreChat(val isCached: Boolean, val data: ChatMessagePage?) : PremiereState()
        data class ResultUserInfo(val isCached: Boolean, val data: UserInfo) : PremiereState()
        data class ResultTriggerPlayerLayout(val isScale: Boolean) : PremiereState()
        object ResultTriggerOpenTabDataFeelExistFullScreen : PremiereState()
        data class ResultTriggerOpenLiveChatFullScreen(val isOpen: Boolean) : PremiereState()
        data class ResultSwitchPlayerMode(val modeFullscreen: Boolean) : PremiereState()
        data class Error(val message: String, val intent: PremiereIntent ?= null) : PremiereState()
        data class ErrorItemNotFound(val message: String, val intent: PremiereIntent ?= null) : PremiereState()
        data class ErrorNoInternet(val message: String, val intent: PremiereIntent ?= null) : PremiereState()
        data class ErrorRequiredLogin(val message: String, val intent: PremiereIntent? = null, val requiredLogin: RequiredLogin?) : PremiereState()
        data class ErrorRequiredVip(val message: String, val intent: PremiereIntent? = null, val requiredVip: RequiredVip?) : PremiereState()
        data class Done(val intent: PremiereIntent? = null) : PremiereState()
        object ResultTriggerMulticam : PremiereState()
        data class ResultClickPremiereDescription(val isOpen: Boolean? = null) : PremiereState()
        object ResultTriggerEndTime : PremiereState()
        object ResultTriggerLoginSuccess : PremiereState()
        object ResultTriggerStopPlayer : PremiereState()

        //
        object ResultReflectionDetailUI : PremiereState()
        object ResultReflectionTabsUI : PremiereState()
        object ResultReflectionFollowButtonUI : PremiereState()
        object ResultReflectionMulticamUI : PremiereState()
        data class ResultReflectionOpenLiveChat(val isOpen: Boolean) : PremiereState()
        data class ResultReflectionUpdateLiveState(val text: String, val state: PremiereLiveState) : PremiereState()

        // region game
        data class OpenVotingPopUp(val jsonData:String,val vote:VoteEntities?) : PremiereState()
        data class ResultViewRankingCommon(val isCached: Boolean,val data: GameLive) : PremiereState()

        // endregion game
        object Init: PremiereState()

        object StopSportInteractive: PremiereState()

        //region Report Player
        data class ResultTriggerMsgUserReport(val isReported: Boolean, val message: String) :PremiereState()
        //endregion

        //region drm
        data class ResultSaveDRMKey(val isCached: Boolean, val data: Boolean) : PremiereState()
        //endregion

        object OpenLiveChatSuccess : PremiereState()

        object OpenLiveChat : PremiereState()

    }

    override suspend fun pingPlay(id: String, session: String, lastSession: String, encryptData: Boolean, type: String, eventId: String): Flow<Result<PingStreamV2>> {
        return drmRepository.pingPlay(id = id, session = session, lastSession = lastSession, encryptData = encryptData, type = type, eventId = eventId)
    }

    override suspend fun pingPlay(id: String, type: String, eventId: String): Flow<Result<Ping>> {
        return drmRepository.pingPlay(id = id, type = type, eventId = eventId)
    }

    override suspend fun pingPause(id: String, type: String, eventId: String): Flow<Result<Ping>> {
        return drmRepository.pingPause(id = id, type = type, eventId = eventId)
    }

    override suspend fun pingPlayHbo(operatorId: String, sessionId: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingPlayHbo(operatorId = operatorId, sessionId = sessionId)
    }

    override suspend fun pingPlayHboByToken(token: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingPlayHbo(token = token)
    }

    override suspend fun pingEndHbo(token: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingEndHbo(token = token)
    }

    override suspend fun refreshToken(operatorId: String, sessionId: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.refreshTokenHbo(operatorId = operatorId, sessionId = sessionId)
    }

    override suspend fun getTvChannelStream(id: String, bitrateId: String): Flow<Result<Stream>> {
        return drmRepository.getTvStream(id = id, bitrateId = bitrateId, blockDataType = "")
    }

    override suspend fun getVodStream(id: String, episodeId: String, bitrateId: String): Flow<Result<Stream>> {
        return drmRepository.getVodStream(id = id, episodeId = episodeId, bitrateId = bitrateId, dataType = "")
    }

    enum class EventType(val value: String) {
        EVENT_TV(value = "eventtv"),
        EVENT(value = "event");
        companion object {
            infix fun from(value: String): EventType? = EventType.values().firstOrNull { it.value == value }
        }

    }

}