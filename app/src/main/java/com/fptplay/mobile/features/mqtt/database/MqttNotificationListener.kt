package com.fptplay.mobile.features.mqtt.database

import com.fptplay.mobile.features.mqtt.model.MqttNotification
import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail
import com.google.firebase.firestore.*
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import timber.log.Timber


class MqttNotificationListener(
    private val callback: IMqttNotificationListener? = null
){
    companion object {
        private const val LOG_TAG = "MqttConnectManager"
        private const val COLLECTION_PATH = "notification"
        private const val DOCUMENT_MQTT = "mqtt"
        private const val FIELD_MQTT = "detail"
//        private val ROOM_TYPE get() = MainApplication.INSTANCE.appConfig.mqttRoomType
//        private val ROOM_ID get() = MainApplication.INSTANCE.appConfig.mqttRoomType
        
        private val ROOM_TYPE get() = "android"
        private val ROOM_ID get() = "android"
    }

    private var fireStore: FirebaseFirestore? = null
    private var notificationRegistration: ListenerRegistration? = null

    init {
        setUp()
    }
    private fun setUp() {
        fireStore = Firebase.firestore
        val settings = FirebaseFirestoreSettings.Builder()
            .setPersistenceEnabled(true)
            .setCacheSizeBytes(100 * 1024 * 1024)
            .build()
        fireStore?.firestoreSettings = settings
        Timber.tag(LOG_TAG).d("MqttNotificationListener init set up")

    }

    fun subscriptionTopicFromFireStore() {
        if (notificationRegistration != null) {
            Timber.tag(LOG_TAG).d("Already listening to notifications")
            return
        }
        Timber.tag(LOG_TAG).d("Starting to listen to MQTT notifications from path: /$COLLECTION_PATH/$DOCUMENT_MQTT/$ROOM_TYPE/$ROOM_ID")
        try {
            fireStore?.clearPersistence()
            notificationRegistration = fireStore
                ?.collection(COLLECTION_PATH)
                ?.document(DOCUMENT_MQTT)
                ?.collection(ROOM_TYPE)
                ?.document(ROOM_ID)
                ?.addSnapshotListener { querySnapshot: DocumentSnapshot?, e: FirebaseFirestoreException? ->
                    if (e != null) {
                        Timber.tag(LOG_TAG).w(e, "Listen failed for MQTT notifications")
                        callback?.onMqttConfigError(e.message ?: "Unknown error")
                        return@addSnapshotListener
                    }
                    if (querySnapshot != null ) {
                        Timber.tag(LOG_TAG).d("Received MQTT notification signals from Firestore")
                        parseNotificationData(querySnapshot)
                    } else {
                        Timber.tag(LOG_TAG).d("No MQTT notification data found in Firestore")
                    }
                }
            if (notificationRegistration != null) {
                Timber.tag(LOG_TAG).d("Successfully set up notification listener")
            } else {
                Timber.tag(LOG_TAG).e("Failed to create notification listener - registration is null")
                callback?.onMqttConfigError("Failed to create listener")
            }

        } catch (e: Exception) {
            Timber.tag(LOG_TAG).e(e, "Error setting up notification listener")
            callback?.onMqttConfigError(e.message ?: "Setup error")
        }
    }

    fun unSubscriptionTopic() {
        notificationRegistration?.remove()
        notificationRegistration = null
        Timber.tag(LOG_TAG).d("Stopped listening to MQTT notifications")
    }

    private fun parseNotificationData(querySnapshot: DocumentSnapshot) {
        try {
            val dataJson = querySnapshot.data
            Timber.tag(LOG_TAG).d("MQTT notification data: $dataJson")
            dataJson?.forEach { (key, value) ->
                if (key == FIELD_MQTT) {
                    val gson = Gson()
                    val json: String = gson.toJson(value)
                    val notification = gson.fromJson(json, MqttNotificationDetail::class.java)
                    if (notification != null) {
                        callback?.onNotificationsReceived(notification)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.tag(LOG_TAG).e(e, "Error parsing notification data")
            callback?.onMqttConfigError(e.message ?: "Parse error")
        }
    }
}


