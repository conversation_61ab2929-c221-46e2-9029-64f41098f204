package com.fptplay.mobile.features.about

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import coil.load
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.AboutAppIntroduceFragmentBinding
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


@AndroidEntryPoint
class AboutAppIntroduceFragment :
    BaseFragment<AboutViewModel.AboutState, AboutViewModel.AboutIntent>() {

    override val viewModel: AboutViewModel by activityViewModels()
    private val safeArgs: AboutAppInfoFragmentArgs by navArgs()
    private var _binding: AboutAppIntroduceFragmentBinding? = null
    private val binding get() = _binding!!
    private val moitImageUrl = MainApplication.INSTANCE.appConfig.govMoitImgUrl
    private val moitBrowserUrl = MainApplication.INSTANCE.appConfig.govMoitBrowserUrl

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding =
            AboutAppIntroduceFragmentBinding.inflate(LayoutInflater.from(context), container, false)
        return binding.root
    }

    override fun bindData() {
        binding.toolbar.title = safeArgs.title
        binding.tvHtmlContent.isVisible = true
        binding.tvVersion.isVisible = true
        binding.tvVersion.text = String.format(
            getString(R.string.version_param),
            "${MainApplication.INSTANCE.appConfig.nameOs} ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
        )

        viewModel.dispatchIntent(
            AboutViewModel.AboutIntent.GetInformation(safeArgs.type)
        )


    }

    override fun bindComponent() {
        binding.toolbar.setTitleTextColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.about_title
            )
        )
      //  binding.toolbar.setBackgroundResource(R.color.about_button_bg)

    }

    override fun bindEvent() {
        binding.toolbar.setOnClickListener {
            findNavController().navigateUp()
        }
        binding.ivMoit.setOnClickListener {
            if(moitBrowserUrl.isNotBlank()) {
                openWebBrowser(moitBrowserUrl)
            }
        }

    }

    override fun AboutViewModel.AboutState.toUI() {
        when (this) {
            is AboutViewModel.AboutState.Loading -> {
            }

            is AboutViewModel.AboutState.Error -> {
            }

            is AboutViewModel.AboutState.Done -> {
            }

            is AboutViewModel.AboutState.ResultInformation -> {
                Timber.tag("tam-about").d("ResultInformation ${data.first().blockHtml.htmlMobile}")
                val htmlCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    Html.fromHtml(data.first().blockHtml.htmlMobile, HtmlCompat.FROM_HTML_MODE_LEGACY)
                } else {
                    Html.fromHtml(data.first().blockHtml.htmlMobile)

                }
                binding.tvHtmlContent.text = htmlCode.trim()
                setupMoitIcon()
            }
        }
    }

    private fun setupMoitIcon() {
        if (moitImageUrl.isNotBlank()) {
            binding.ivMoit.load(moitImageUrl)
            binding.ivMoit.show()

        } else {
            binding.ivMoit.hide()

        }
    }

    private fun openWebBrowser(url: String) {
        try {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            activity?.startActivity(browserIntent)
        } catch (e: Exception) {
            Timber.e(e, "openWebBrowser error")
        }


    }
}