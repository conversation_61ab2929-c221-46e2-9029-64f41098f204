package com.fptplay.mobile.features.multi_profile.utils

import timber.log.Timber

class LoadMorePreloadViewingHistoryHandler (
    private var totalItem: Int,
    private val totalItemInPage: Int,
    private val totalItemInRow: Int,
    open val preloadOffset: Int = 0,
    private val onScroll: (Int) -> Unit,
){
    //region Variables
    private var totalRow: Int = 0
    private var currentRow: Int = 0
    private var currentPage: Int = 1

    private var isLoadingData: Boolean = false
    private var endPage: Boolean = false
    //endregion

    //region Common
    open fun canScroll(position: Int): Boolean{
        Timber.d("LoadMorePreloadViewingHistoryHandler -> isLoadingData: $isLoadingData, endRow: $endPage,position: $position")
        if (isLoadingData || endPage) return false
        currentRow = (position / totalItemInRow) + 1 //Row's started from 1
        Timber.d("LoadMorePreloadViewingHistoryHandler -> totalItem: $totalItem, totalItemInRow: $totalItemInRow, totalRow: $totalRow, currentRow: $currentRow")
        if ((currentRow > 1) && (currentRow >= (totalRow - preloadOffset) && currentRow <= totalRow)){
            Timber.d("LoadMorePreloadViewingHistoryHandler -> currentPage: $currentPage")
            isLoadingData = true
            onScroll(++currentPage)
            return true
        }
        return false
    }

    fun refresh(totalItem: Int, endPage: Boolean = false,addToFirst:Boolean = false){
        this.totalItem = totalItem
        this.totalRow = if (totalItemInRow == 0) 0 else (totalItem + totalItemInRow - 1) / totalItemInRow
        this.isLoadingData = false
        this.endPage = endPage
        if (addToFirst) {
            currentPage = 1
        }
    }
    //endregion
}