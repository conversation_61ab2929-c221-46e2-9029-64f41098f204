package com.fptplay.mobile.features.download.fragment

import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.listener.DownloadListener
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fplay.module.downloader.utils.VideoStorageUtils
import com.fptplay.mobile.*
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.isAirlineLayout
import com.fptplay.mobile.common.extensions.runOnUiThread
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.DownloadFragmentBinding
import com.fptplay.mobile.databinding.DownloadHeaderViewBinding
import com.fptplay.mobile.features.download.fragment.DownloadFragmentDirections
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.download.DownloadViewModel
import com.fptplay.mobile.features.download.adapter.VideoDownloadListAdapter
import com.fptplay.mobile.features.download.model.UpdateDownloadProgress
import com.fptplay.mobile.features.download.model.UpdateDownloadStatus
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.checkToShowContent
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*

class DownloadFragment :
    BaseFragment<DownloadViewModel.DownloadState, DownloadViewModel.DownloadIntent>() {

    //region Variable
    private var _binding: DownloadFragmentBinding? = null
    private val binding get() = _binding!!
    override val viewModel: DownloadViewModel by activityViewModels()

    private val mAdapter: VideoDownloadListAdapter by lazy { VideoDownloadListAdapter() }

    private var _headerBinding: DownloadHeaderViewBinding? = null
    private val headerBinding get() = _headerBinding!!
    //endregion

    //region Override
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        VideoDownloadManager.instance.setGlobalDownloadListener(mListener)
        _binding = DownloadFragmentBinding.inflate(inflater, container, false)
        _headerBinding = DownloadHeaderViewBinding.bind(binding.root)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _headerBinding = null
    }

    override fun bindData() {
        viewModel.dispatchIntent(DownloadViewModel.DownloadIntent.GetAllDownloadVideos(isAirline = true))
    }

    override fun DownloadViewModel.DownloadState.toUI() {
        when (this) {
            is DownloadViewModel.DownloadState.Loading -> {
                when(data) {
                    is DownloadViewModel.DownloadIntent.CheckDownload,
                    is DownloadViewModel.DownloadIntent.FetchDownloadLink -> {
                        binding.pbLoading.root.show()
                    }
                    else -> {}
                }
            }
            is DownloadViewModel.DownloadState.Error -> {
                when(data) {
                    is DownloadViewModel.DownloadIntent.CheckDownload,
                    is DownloadViewModel.DownloadIntent.FetchDownloadLink -> {
                        binding.pbLoading.root.hide()
                        showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                    }
                    else -> {
                        binding.pbLoading.root.hide()
                    }
                }
            }
            is DownloadViewModel.DownloadState.GetAllDownloadVideosResult -> {
                mAdapter.add(data = this.data.filter { item -> item.listChapters.isNotEmpty() }
                    .map {
                        val taskItem = it.toCollectionVideoTaskItem()
                        // Recalculate downloadSize for downloaded item in case downloadSize=0
                        taskItem.listChapters.map { chapter ->
                            if (chapter.downloadSize == 0L && chapter.taskState == VideoTaskState.SUCCESS) {
                                chapter.downloadSize =
                                    VideoStorageUtils.countTotalSize(chapter.filePath!!)
                                VideoDownloadManager.instance.updateDownloadedItem(chapter)
                            }
                        }
                        taskItem
                    }, true) {
                    binding.pbLoading.root.hide()
                    if (mAdapter.size() == 0)
                        binding.tvNoData.show()
                    else
                        binding.tvNoData.hide()
                }

                data.filter { item -> item.listChapters.isEmpty() }.forEach { item ->
                    VideoDownloadManager.instance.removeCollection(item.collection)
                }
            }
            is DownloadViewModel.DownloadState.ResultCheckDownload -> {
                binding.pbLoading.root.hide()
                if (this.vodDownloadInfo.downloadUrl.isNotBlank()) {
                    DownloadUtils.extendDownloadTaskItem(item = this.item, info = this.vodDownloadInfo)
                } else {
                    showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                }
            }
            is DownloadViewModel.DownloadState.ResultFetchDownloadLink -> {
                binding.pbLoading.root.hide()
                if (this.vodDownloadInfo.downloadUrl.isNotBlank()) {
                    DownloadUtils.startDownloadWithNewUrl(this.vodDownloadInfo.downloadUrl,this.item,this.vodDownloadInfo.d2gTime, this.vodDownloadInfo.streamSession)
                } else {
                    showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                }
            }
            is DownloadViewModel.DownloadState.Done -> {
                when(data) {
                    is DownloadViewModel.DownloadIntent.CheckDownload,
                    is DownloadViewModel.DownloadIntent.GetDownloadChaptersByMovieId,
                    is DownloadViewModel.DownloadIntent.FetchDownloadLink -> {
                        binding.pbLoading.root.hide()
                    }
                    else -> {}
                }
            }
            else -> {}
        }
    }

    override fun bindComponent() {
        binding.downloadListview.apply {
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            adapter = mAdapter
            addItemDecoration(
                object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        outRect.bottom = resources.getDimensionPixelSize(R.dimen.payment_package_margin)
                    }
                }
            )
        }

        headerBinding.apply {
            toolbar.setNavigationIcon(R.drawable.ic_arrow_down)
            toolbar.setTitle(R.string.download_list_downloaded_title)
            toolbar.setNavigationOnClickListener {
                backHandler()
            }
        }
        checkStatusStorage()
    }

    override fun bindEvent() {
        mAdapter.eventListener = object : EventListener,
            IEventListener<CollectionVideoTaskItem> {
            override fun onClickedItem(position: Int, data: CollectionVideoTaskItem) {
                if (data.listChapters.size == 1 && !data.isSeries) {
                    if (data.listChapters[0].taskState == VideoTaskState.SUCCESS) {
                        if (DownloadUtils.calculateTimeLeft(data.listChapters[0].lastUpdateTime,data.listChapters[0].expiredTime) > 0)
                            openVodDetailOffline(
                                data.movieId,
                                data.listChapters[0].chapterId,
                                data.listChapters[0].fileHash ?: ""
                            )
                        else {
                            showConfirmMovieDialog(data.listChapters[0])
                        }
                    }
                } else
                    findNavController().navigate(
                        DownloadFragmentDirections.actionDownloadFragmentToDownloadDetailFragment(
                            data.movieId,
                            data.title
                        )
                    )
            }

            override fun onClickView(position: Int, view: View?, data: CollectionVideoTaskItem) {
                if (view?.id == R.id.ivBtnMore && data.listChapters.size == 1 && !data.isSeries) {
                    showConfirmMovieDialog(data.listChapters[0])
                }
                else
                    onClickedItem(position, data)
            }
        }
        bindEventFragmentResult()
    }
    //endregion

    private fun showConfirmMovieDialog(
        item: VideoTaskItem
    ) {
        DownloadUtils.bindEventFragmentResult(item.chapterId,this@DownloadFragment, eventDelete =  {
            binding.pbLoading.root.show()
            lifecycleScope.launch(Dispatchers.IO) {
                VideoDownloadManager.instance.deleteVideoTask(item.chapterId, true)
            }
        }, eventExtend = {
            viewModel.dispatchIntent(DownloadViewModel.DownloadIntent.CheckDownload(item))
        }, checkInternet = {
            if(NetworkUtils.isNetworkAvailable()) {
                true
            } else {
                showWarningDialog(<EMAIL>(R.string.error_no_intent))
                false
            }
        })
        val optionDown = DownloadUtils.setOptionResult(item.taskState,item.lastUpdateTime,item.expiredTime)
        findNavController().navigate(
            NavDownloadDirections.actionGlobalToVodOptionDialogFragment(
                optionDown,hasEdgeToEdge,isDownloadType = true
            )
        )
    }

    private fun openVodDetailOffline(movieId: String, chapterId: String, fileHash: String) {
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Cast -> {
                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                    navHostFragment = activity?.findNavHostFragment(),
                    onStopCastAndNavigate = {
                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                        parentFragment?.parentFragment?.parentFragment?.parentFragment?.findNavController()?.navigate(NavAirlineDirections.actionGlobalToVodDetailOfflineFragment(movieId, chapterId, fileHash)) ?: run {
                            findNavController().navigate(DownloadFragmentDirections.actionDownloadFragmentToVodDetailOfflineFragment(movieId, chapterId, fileHash))
                        }
                    },
                    onNavigate = {
                        parentFragment?.parentFragment?.parentFragment?.parentFragment?.findNavController()?.navigate(NavAirlineDirections.actionGlobalToVodDetailOfflineFragment(movieId, chapterId, fileHash)) ?: run {
                            findNavController().navigate(DownloadFragmentDirections.actionDownloadFragmentToVodDetailOfflineFragment(movieId, chapterId, fileHash))
                        }
                    },
                    onCancel = {}
                )
            }
            else -> {
                parentFragment?.parentFragment?.parentFragment?.parentFragment?.findNavController()
                    ?.navigate(
                        NavAirlineDirections.actionGlobalToVodDetailOfflineFragment(
                            movieId,
                            chapterId,
                            fileHash
                        )
                    ) ?: run {
                    findNavController().navigate(
                        DownloadFragmentDirections.actionDownloadFragmentToVodDetailOfflineFragment(
                            movieId,
                            chapterId,
                            fileHash
                        )
                    )
                }
            }
        }
    }

    private fun showConfirmDeleteMovieDialog(dialogMessage: String, chapterId: String) {
        findNavController().navigate(
            DownloadFragmentDirections.actionDownloadFragmentToDownloadMoreOptionBottomSheetDialogFragment(
                type = Utils.DOWNLOAD_OPTION_DIALOG_DELETE_TYPE,
                title = dialogMessage,
                optionOneText = getString(R.string.delete),
                optionOneValue = chapterId,
                optionTwoText = getString(R.string.warning_dialog_button_negative_text),
                optionTwoValue = "",
            )
        )
    }

    private fun bindEventFragmentResult() {
        findNavController().run {
            setFragmentResultListener(Utils.DOWNLOAD_OPTION_DIALOG_DELETE_TYPE) { _, bundle ->
                val chapterId = bundle.getString(Utils.DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY, "")
                if (chapterId.isNotEmpty()) {
                    binding.pbLoading.root.show()
                    VideoDownloadManager.instance.deleteVideoTask(chapterId, true)
                }
            }
        }
    }

    private val mListener: DownloadListener = object : DownloadListener() {
        private var mLastProgressTimeStamp: Long = 0

        override fun onDownloadDefault(oldState: Int, item: VideoTaskItem) {
            Timber.d("onDownloadDefault: $item")
            val pos = mAdapter.data().indexOfFirst { collectionVideoTaskItem -> collectionVideoTaskItem.movieId == item.movieId}
            if (pos != -1) {
                runOnUiThread {
                    mAdapter.removeIndex(pos) {
                        mAdapter.notifyItemRemoved(pos)
                        binding.pbLoading.root.hide()
                        if (mAdapter.size() == 0) binding.tvNoData.show()
                    }
                }
            }
        }

        override fun onDownloadPrepare(item: VideoTaskItem) {
            Timber.d("onDownloadPrepare: $item")
            val pos = mAdapter.data().indexOfFirst { collectionVideoTaskItem -> collectionVideoTaskItem.movieId == item.movieId}
            if (pos != -1)
                runOnUiThread { mAdapter.notifyItemChanged(pos) }
            else {
                mAdapter.add(item.toCollectionVideoTaskItem())
                runOnUiThread {
                    mAdapter.notifyItemRemoved(pos)
                    mAdapter.notifyItemInserted(pos)
                    binding.tvNoData.hide()
                }
            }
        }

        override fun onDownloadProgress(item: VideoTaskItem) {
            val currentTimeStamp = System.currentTimeMillis()
            if (currentTimeStamp - mLastProgressTimeStamp > 1000) {
                Timber.d("onDownloadProgress: ${item.percentString}, curTs=${item.curTs}, totalTs=${item.totalTs}")
                notifyChanged(
                    item,
                    UpdateDownloadProgress(item.percentString, item.downloadSizeString)
                )
                mLastProgressTimeStamp = currentTimeStamp
            }
        }

        override fun onDownloadPause(item: VideoTaskItem) {
            super.onDownloadPause(item)
            Timber.d("onDownloadPause: %s", item.url)
            notifyChanged(item, UpdateDownloadStatus(VideoTaskState.PAUSE))
        }

        override fun onDownloadError(item: VideoTaskItem) {
            Timber.d("onDownloadError: %s", item.url)
            if (item.hasLinkRefetch) {
                viewModel.dispatchIntent(DownloadViewModel.DownloadIntent.FetchDownloadLink(item))
            } else {
                notifyChanged(item, UpdateDownloadStatus(VideoTaskState.ERROR))
            }
        }

        override fun onDownloadSuccess(item: VideoTaskItem) {
            Timber.d("onDownloadSuccess: $item")
            notifyChanged(item, UpdateDownloadStatus(VideoTaskState.SUCCESS))
        }
    }

    private fun notifyChanged(item: VideoTaskItem, payloads: Any? = null) {
        val pos = mAdapter.data().indexOfFirst { collectionVideoTaskItem ->
            collectionVideoTaskItem.movieId == item.movieId
        }
        if (pos != -1) {
            val collection = mAdapter.data()[pos]
            val chapPos = collection.listChapters.indexOfFirst { chap ->
                chap.chapterId == item.chapterId
            }
            if (chapPos != -1) {
                val itemCur = collection.listChapters[chapPos]
                itemCur.copyFrom(item)

                runOnUiThread {
                    if (payloads != null) {
                        if (payloads is UpdateDownloadProgress && collection.listChapters.size > 1) {
                            payloads.size = collection.downloadSizeAllString
                            payloads.progress = collection.percentAllString
                        }
                        mAdapter.notifyItemChanged(pos, payloads)
                    } else
                        mAdapter.notifyItemChanged(pos)
                }
            }
        }
    }

    // region Commons
    private fun checkStatusStorage() {
        requireContext().let {
            headerBinding.tvStorageInternalStatus.checkToShowContent(
                getString(
                    R.string.download_fragment_title_storage_internal,
                    Utils.convertByteToGigabyte(VideoStorageUtils.getInternalAvailableSpaceInBytes(it))
                ),
                goneViewWhenNoText = true
            )
            if(VideoStorageUtils.checkSDCardAvailable(it)) {
                headerBinding.tvStorageExternalStatus.checkToShowContent(
                    getString(
                        R.string.download_fragment_title_storage_external,
                        Utils.convertByteToGigabyte(VideoStorageUtils.getExternalAvailableSpaceInBytes(it))
                    ),
                    goneViewWhenNoText = true
                )
            } else {
                headerBinding.tvStorageExternalStatus.hide()
            }
        }
    }

    override fun backHandler() {
        if (!findNavController().navigateUp()) {
            parentFragment?.parentFragment?.findNavController()?.navigateUp()
        }
    }

    // endregion Commons
}