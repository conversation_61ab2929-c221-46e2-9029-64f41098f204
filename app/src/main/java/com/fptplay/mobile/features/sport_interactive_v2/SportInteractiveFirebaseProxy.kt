package com.fptplay.mobile.features.sport_interactive_v2

import androidx.lifecycle.LifecycleOwner
import com.fptplay.mobile.common.utils.firestore.LifecycleEventDispatcher
import com.fptplay.mobile.features.sport_interactive.model.SportInteractiveData
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils
import com.fptplay.mobile.features.sport_interactive_v2.cook_data.SportInteractiveDataMapper.toHeaderData
import com.fptplay.mobile.features.sport_interactive_v2.cook_data.SportInteractiveDataMapper.toListMenuData
import com.fptplay.mobile.features.sport_interactive_v2.cook_data.SportInteractiveDataMapper.toSportInteractiveUIData
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveGeneralData
import com.fptplay.mobile.features.sport_interactive_v2.models.SportInteractiveUIData
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.ListenerRegistration
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Types
import timber.log.Timber

class SportInteractiveFirebaseProxy {
    val db = Firebase.firestore
    private var registration: ListenerRegistration? = null
    var listener: SportEventListener? = null

    private var eventId: String = ""
    private var sportInteractiveData: SportInteractiveUIData? = null
    private var fromBackground = false

    private fun setListener(listener: SportEventListener?, eventId: String?) {
        Timber.tag("tam-sport").d("*****Set listener: eventId: $eventId")
        this.listener = listener
        this.eventId = eventId ?: ""
    }

    private fun invokeListener() {
        Timber.tag("tam-sport").d("*****invoke listener: $listener")
        if(sportInteractiveData == null) {
            Timber.tag("tam-sport").d("sportInteractiveData null")
            listener?.onDataChange(getDefaultInteractiveData())
        } else {
            listener?.onDataChange(sportInteractiveData)
        }
    }

    fun stopListenEvent() {
        registration?.remove()
        registration = null
        sportInteractiveData = null
        eventId = ""
    }

    fun listenSportEventData(
        lifecycleOwner: LifecycleOwner,
        eventId: String,
        listener: (data: SportInteractiveUIData?) -> Unit
    ) {
        if (this.eventId != eventId || registration == null) {

            val sportEventListener = object : SportEventListener {
                override fun onDataChange(data: SportInteractiveUIData?) {
                    listener(data)
                }
            }
            LifecycleEventDispatcher(
                lifecycleOwner,
                onStart = {
                    Timber.tag("tam-sport").d("*****onstart listenSportEventData")
                    setListener(sportEventListener, eventId)
                    if (fromBackground) {
                        invokeListener()
                    }
                    fromBackground = false

                },
                onStop = {
                    Timber.tag("tam-sport").d("*****onstop")
                    setListener(null, null)
                    fromBackground = true
                }
            )
            val docRef = db.collection(SPORT_INTERACTION_COLLECTION).document(eventId)
            this.eventId = eventId
            sportInteractiveData = null
            registration?.remove()

            registration = docRef.addSnapshotListener { snapshot, e ->
                if (e != null) {
                    Timber.tag("tam-sport").e("*****Listen failed. $e")
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    Timber.tag("tam-sport").d("*****Current data: ${snapshot.data}")
                    try {
                        sportInteractiveData = getDataFromFirestore(snapshot)
                    } catch (e: Exception) {
                        Timber.tag("tam-sport").e(e, "getDataFromFirestore")
                        sportInteractiveData = null
                    } finally {
                        invokeListener()
                    }
                } else {
                    Timber.tag("tam-sport").e("*****Current data: null")
                    sportInteractiveData = null
                    invokeListener()
                }
            }

        }

    }

    private fun getDataFromFirestore(snapshot: DocumentSnapshot): SportInteractiveUIData? {
        return try {
            val moshi = SportInteractiveUtils.moshi()
            val jsonAdapterSportInteractiveData: JsonAdapter<SportInteractiveData> =
                moshi.adapter(SportInteractiveData::class.java)

            val jsonAdapter: JsonAdapter<Map<String, Any>> = moshi.adapter(
                Types.newParameterizedType(
                    MutableMap::class.java,
                    String::class.java,
                    Any::class.java
                )
            )
            val jsonString: String = jsonAdapter.toJson(snapshot.data)
            Timber.tag("tam-sport").i("getDataFromFirestore jsonString : $jsonString")
            val preProcessData = try {
                jsonAdapterSportInteractiveData.fromJson(jsonString)
            } catch (ex: Exception) {
                ex.printStackTrace()
                SportInteractiveData()
            }

            SportInteractiveUIData(
                titlesData = preProcessData.toListMenuData(),
                statisticData = preProcessData?.statistic?.toSportInteractiveUIData(
                    header = preProcessData.commonData.toHeaderData()
                ) ?: SportInteractiveGeneralData(),
                matchProcessData = preProcessData?.matchProcess?.toSportInteractiveUIData(
                    header = preProcessData.commonData.toHeaderData()
                ) ?: SportInteractiveGeneralData(),
                squadData = preProcessData?.squad?.toSportInteractiveUIData(
                    header = preProcessData.commonData.toHeaderData(useShortName = true)
                ) ?: SportInteractiveGeneralData(),
                liveScoreData = preProcessData?.liveScore?.toSportInteractiveUIData(
                ) ?: SportInteractiveGeneralData(),
            )
        } catch (ex: Exception) {
            Timber.tag("tam-sport").e(ex, "getDataFromFirestore")
            ex.printStackTrace()
            null
        }
    }

    private fun getDefaultInteractiveData(): SportInteractiveUIData {
        val preProcessData = SportInteractiveData()
        return SportInteractiveUIData(
            titlesData = preProcessData.toListMenuData(),
            statisticData = preProcessData.statistic.toSportInteractiveUIData(
                header = preProcessData.commonData.toHeaderData()
            ),
            matchProcessData = preProcessData.matchProcess.toSportInteractiveUIData(
                header = preProcessData.commonData.toHeaderData()
            ),
            squadData = preProcessData.squad.toSportInteractiveUIData(
                header = preProcessData.commonData.toHeaderData(useShortName = true)
            ),
            liveScoreData = preProcessData.liveScore.toSportInteractiveUIData(
            ),
        )
    }

    companion object {
        const val SPORT_INTERACTION_COLLECTION = "interactive_sports"
    }

    interface SportEventListener {
        fun onDataChange(data: SportInteractiveUIData?)
    }

}