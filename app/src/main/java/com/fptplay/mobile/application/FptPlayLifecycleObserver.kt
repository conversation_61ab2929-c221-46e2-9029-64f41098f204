package com.fptplay.mobile.application

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FptPlayLifecycleObserver @Inject constructor() : DefaultLifecycleObserver {
    companion object {
        var appInBackground = true
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        appInBackground = false
        TrackingGA4Proxy.sendTrackingCrashReportModeApp(modeApp = "appInForeground")
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        appInBackground = true
        TrackingGA4Proxy.sendTrackingCrashReportModeApp(modeApp = "appInBackground")
    }
}