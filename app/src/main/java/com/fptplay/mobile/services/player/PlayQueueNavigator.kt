package com.fptplay.mobile.services.player

import android.app.Service
import android.media.session.PlaybackState.*
import android.net.Uri
import android.os.Bundle
import android.os.ResultReceiver
import android.support.v4.media.session.MediaSessionCompat
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ext.mediasession.MediaSessionConnector.QueueNavigator
import com.tear.modules.player.exo.ExoPlayerProxy
import com.xhbadxx.projects.module.util.logger.Logger


class PlayQueueNavigator(
    private val listener: OnPlayQueueNavigatorListener
) : QueueNavigator {

    override fun getSupportedQueueNavigatorActions(player: Player): Long {
        Logger.d("PlayQueueNavigator -> getSupportedQueueNavigatorActions")
        return listener.getSupportedQueueNavigatorActions(player = player)
    }


    override fun onTimelineChanged(player: Player) {
        Logger.d("PlayQueueNavigator -> onTimelineChanged")
    }
    override fun onCurrentMediaItemIndexChanged(player: Player) {
        Logger.d("PlayQueueNavigator -> onCurrentMediaItemIndexChanged")
    }
    override fun getActiveQueueItemId(player: Player?): Long {
        Logger.d("PlayQueueNavigator -> getActiveQueueItemId")
        return 0
    }

    override fun onSkipToNext(player: Player) {
        Logger.d("PlayQueueNavigator -> onSkipToNext")
        listener.onSkipToNext(player = player)
    }

    override fun onSkipToPrevious(player: Player) {
        Logger.d("PlayQueueNavigator -> onSkipToPrevious")
        listener.onSkipToPrevious(player = player)
    }

    override fun onSkipToQueueItem(player: Player, id: Long) {
        Logger.d("PlayQueueNavigator -> onSkipToQueueItem")
    }

    override fun onCommand(
        player: Player,
        command: String,
        extras: Bundle?,
        cb: ResultReceiver?
    ): Boolean {
        Logger.d("PlayQueueNavigator -> onCommand")
        return false
    }

    interface OnPlayQueueNavigatorListener {
        fun getSupportedQueueNavigatorActions(player: Player): Long
        fun onSkipToNext(player: Player)
        fun onSkipToPrevious(player: Player)
    }
}