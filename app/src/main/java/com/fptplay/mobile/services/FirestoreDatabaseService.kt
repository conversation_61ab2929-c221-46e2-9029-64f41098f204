package com.fptplay.mobile.services

import android.content.ContentValues.TAG
import android.util.Log
import com.fptplay.mobile.features.game_30s.vote.entites.VoteEntities
import com.google.firebase.firestore.*
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
class FirestoreDatabaseService(
    var callback: IEventListenerFirestore? = null
) {
    var fireStore: FirebaseFirestore? = null
    var registration: ListenerRegistration? = null

    init {
        setUp()
    }

    companion object {
        // game
        private const val COLLECTIONS_PATH = "gameshows"
        private const val DOCUMENTS_PATH = "on_off_voting"
        private const val DATA_PATH = "data_json"
        private const val DOCUMENTS_ASK_AND_ANSWER_PATH = "data_json"

        //interactive_sports
        private const val COLLECTIONS_PATH_SPORT_INTERACTIVE = "interactive_sports"
        private const val COLLECTIONS_SPORT_INTERACTIVE_COMMON = "common"
        private const val COLLECTIONS_SPORT_INTERACTIVE_STAT = "stat"
    }

    fun setUp() {
        fireStore = Firebase.firestore
    }

    fun callbackEventFirestore(callback: IEventListenerFirestore) {
        this.callback = callback
    }

    fun stopListenFireStore() {
        if (registration != null) {
            registration?.remove()
        }
    }

    fun openPanelVoting() {
        fireStore?.clearPersistence()
        registration = fireStore?.collection(COLLECTIONS_PATH)
            ?.document(DOCUMENTS_PATH)
            ?.addSnapshotListener { querySnapshot: DocumentSnapshot?, e: FirebaseFirestoreException? ->
                if (e != null) {
                    Log.w(TAG, "Listen failed.", e)
                    return@addSnapshotListener
                }
                parseJsonToVoteObject(querySnapshot, e)
            }
    }

    private fun parseJsonToVoteObject(
        querySnapshot: DocumentSnapshot?,
        e: FirebaseFirestoreException?
    ) {
        if (querySnapshot != null && querySnapshot.exists()) {
            try {
                val entities = VoteEntities(
                    action_code = querySnapshot.data?.get("action_code")?.toString() ?: "",
                    dataCollection = querySnapshot.data?.get("dataCollection")?.toString() ?: "",
                    idEvent = querySnapshot.data?.get("idEvent")?.toString() ?: "",
                    openPanelVoting = (querySnapshot.data?.get("openPanelVoting") as? Boolean
                        ?: false),
                    single_choice = querySnapshot.data?.get("single_choice") as? Boolean ?: false,
                    timeExpired = querySnapshot.data?.get("timeExpired")?.toString() ?: "",
                    url = querySnapshot.data?.get("url")?.toString() ?: "",
                    data_json = querySnapshot.data?.get("data_json")?.toString() ?: "",
                    isScale = querySnapshot.data?.get("isScale") as? Boolean ?: true,
                )
                Log.w(TAG, "Listen openPanelVoting: $entities", e)
                callback?.openPanelVoting(entities)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } else {
            Log.d(TAG, "Current openPanelVoting: null")
        }
    }

    fun getPlayer30sList(document: String) {
        fireStore?.collection(COLLECTIONS_PATH)
            ?.document(DOCUMENTS_PATH)
            ?.collection(document)
            ?.document(DATA_PATH)
            ?.get()
            ?.addOnSuccessListener { result ->
                Log.w(TAG, "Data json: ${result.data}")
                try {
                    val gson = Gson()
                    val json = gson.toJson(result.data).toString() ?: ""
                    if (json != null) {
                        Log.w(TAG, "Data json: ${json}")
                        callback?.onPlayerEvent(json)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback?.onPlayerEvent("")
                }
            }
            ?.addOnFailureListener { exception ->
                Log.w(TAG, "Listen getPlayer30sList fail: $exception")
            }
    }

}

interface IEventListenerFirestore {
    open fun onPlayerEvent(dataJson: String)
    open fun openPanelVoting(vote: VoteEntities?)
}



