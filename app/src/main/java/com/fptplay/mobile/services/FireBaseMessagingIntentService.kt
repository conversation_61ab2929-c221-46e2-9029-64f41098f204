package com.fptplay.mobile.services

import android.app.IntentService
import android.content.Intent
import com.fptplay.mobile.WelcomeActivity
import com.fptplay.mobile.application.FptPlayLifecycleObserver
import com.fptplay.mobile.common.utils.CheckValidUtil
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import timber.log.Timber

class FireBaseMessagingIntentService : IntentService("FireBaseMessagingIntentService") {
    override fun onHandleIntent(intent: Intent?) {
        Timber.d("*** run in intent service")
        if (intent != null) {
            val type = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE)
            val typeId = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID)
            val url = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_URL)
            val title = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TITLE)
            if (CheckValidUtil.checkValidString(type)) {
                if (!FptPlayLifecycleObserver.appInBackground) {
                    val eventIntent = Intent()
                    eventIntent.action = Constants.FIREBASE_NOTIFICATION_NEW_ACTION
                    eventIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE, type)
                    eventIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID, typeId)
                    eventIntent.putExtra(Constants.FIREBASE_NOTIFICATION_URL, url)
                    eventIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TITLE, title)
                    applicationContext.sendBroadcast(eventIntent)
                    Timber.e("eventIntent")
                } else {
                    val welcomeIntent = Intent(applicationContext, WelcomeActivity::class.java)
                    welcomeIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                    welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_NEW, true)
                    welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE, type)
                    welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID, typeId)
                    welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_URL, url)
                    welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TITLE, title)
                    applicationContext.startActivity(welcomeIntent)
                    Timber.e("***welcomeIntent")
                }
            }
        }
    }
}