package com.fptplay.mobile.services.notification.util

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.graphics.Bitmap
import android.media.RingtoneManager
import android.os.Build
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.fptplay.mobile.R
import com.fptplay.mobile.services.notification.ImageSource
import com.fptplay.mobile.services.notification.NotificationBuilder
import com.fptplay.mobile.services.notification.NotificationBuilder.Companion.BIG_MESSAGE_NOTIFICATION
import com.fptplay.mobile.services.notification.NotificationBuilder.Companion.CUSTOM_NOTIFICATION
import com.fptplay.mobile.services.notification.NotificationBuilder.Companion.PICTURE_NOTIFICATION
import kotlin.Exception

class Notification(notificationBuilder: NotificationBuilder) {
    companion object {
        const val CHANNEL_ID = "fpt_play_chanel_1"
    }

    // region variables
    private val context: Context
    private val notificationMode: Int
    private val titleNotification: String
    private val textNotification: String
    private val subtextNotification: String?
    private val imgSourceLargeIcon: ImageSource?
    private val resIdSmallIcon: Int
    private val resIdSmallIconTransparent: Int
    private val strMoreText: String?
    private val pendingIntentWhenClickNotification: PendingIntent?
    private val listAction: List<NotificationCompat.Action>?
    private val widthImage: Int
    private val heightImage: Int
    private val idColorDrawable: Int
    private val strTicker: String?
    private val notificationId: Int

    private var notificationManager: NotificationManager? = null
    private var mBuilder: NotificationCompat.Builder? = null

    private val DEFAULT_MAX_LINE_CONTENT_TEXT_EXPANSE = 3
    // end region

    // end region
    //region constructor by builder
    init {
        pendingIntentWhenClickNotification = notificationBuilder.getPendingIntentWhenClickNotification()
        notificationMode = notificationBuilder.getNotificationMode()
        titleNotification = notificationBuilder.getTitleNotification() ?: ""
        textNotification = notificationBuilder.getTextNotification() ?: ""
        imgSourceLargeIcon = notificationBuilder.getImgSourceLargeIcon()
        resIdSmallIcon = notificationBuilder.getResIdSmallIcon()
        strMoreText = notificationBuilder.getStrMoreText()
        context = notificationBuilder.getContext()
        listAction = notificationBuilder.getListAction()
        heightImage = notificationBuilder.getHeightImage()
        widthImage = notificationBuilder.getWidthImage()
        subtextNotification = notificationBuilder.getSubTextNotification()
        idColorDrawable = notificationBuilder.getColorDrawable()
        strTicker = notificationBuilder.getStrTicker()
        notificationId = notificationBuilder.getNotificationId()
        resIdSmallIconTransparent = notificationBuilder.getResIdSmallIconTransparent()
    }
    //end region

    //end region
    // region methods
    fun process() {
        try {
            initNotificationComponent()
            if (notificationManager == null || mBuilder == null || resIdSmallIcon == 0) return

            if (subtextNotification != null) {
                mBuilder!!.setSubText(subtextNotification)
            }
            if (strTicker != null) {
                mBuilder!!.setTicker(strTicker)
            }
            if (idColorDrawable != 0) {
                mBuilder!!.color = ContextCompat.getColor(context, idColorDrawable)
            }
            mBuilder!!.setShowWhen(true)
                .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
                .setContentTitle(titleNotification)
                .setContentText(textNotification)
                .setContentIntent(pendingIntentWhenClickNotification)
                .setAutoCancel(true)
            if (resIdSmallIconTransparent != 0) {
                mBuilder!!.setSmallIcon(resIdSmallIconTransparent)
            } else {
                mBuilder!!.setSmallIcon(resIdSmallIcon)
            }
            mBuilder!!.setDefaults(Notification.DEFAULT_SOUND or Notification.DEFAULT_VIBRATE)
            if (listAction != null) {
                for (action in listAction) {
                    mBuilder!!.addAction(action)
                }
            }
            when (notificationMode) {
                BIG_MESSAGE_NOTIFICATION -> {
                    mBuilder!!.setStyle(
                        NotificationCompat.BigTextStyle()
                            .bigText(strMoreText)
                    )
                    imgSourceLargeIcon?.run {
                        if (imgSourceLargeIcon.getType() != ImageSource.TYPE_URL) {
                            mBuilder!!.setLargeIcon(
                                convertImageOfflineToBitMap(
                                    widthImage,
                                    heightImage
                                )
                            )
                        } else {
                            onLoadOnlineImage(object : CallBackResourceReady {
                                override fun onResourceReady(bitmap: Bitmap?) {
                                    mBuilder!!.setLargeIcon(bitmap)
                                    notificationManager!!.notify(notificationId, mBuilder!!.build())
                                }

                                override fun onFail() {
                                    notificationManager!!.notify(notificationId, mBuilder!!.build())
                                }
                            }, widthImage, heightImage)
                            return
                        }
                    }
                }
                PICTURE_NOTIFICATION -> {

                    imgSourceLargeIcon?.run {
                        if (getType() != ImageSource.TYPE_URL) {
                            val bitmap: Bitmap? = convertImageOfflineToBitMap(widthImage, heightImage)
                            mBuilder!!.setLargeIcon(bitmap)
                                .setStyle(
                                    NotificationCompat.BigPictureStyle()
                                        .bigPicture(bitmap)
                                )
                        } else {
                            onLoadOnlineImage(object : CallBackResourceReady {
                                override fun onResourceReady(bitmap: Bitmap?) {
                                    mBuilder!!.setLargeIcon(bitmap)
                                        .setStyle(
                                            NotificationCompat.BigPictureStyle()
                                                .bigPicture(bitmap)
                                        )
                                    notificationManager!!.notify(notificationId, mBuilder!!.build())
                                }

                                override fun onFail() {
                                    notificationManager!!.notify(notificationId, mBuilder!!.build())
                                }
                            }, widthImage, heightImage)
                            return
                        }
                    }
                }
                CUSTOM_NOTIFICATION -> {
                    imgSourceLargeIcon?.run {
                        if (getType() != ImageSource.TYPE_URL) {
                            convertImageOfflineToBitMap(widthImage, heightImage)?.let {
                                setDataImageForCustomViewAndNotify(it, mBuilder!!)
                            }
                        } else {
                            onLoadOnlineImage(object : CallBackResourceReady {
                                override fun onResourceReady(bitmap: Bitmap?) {
                                    bitmap?.let {
                                        setDataImageForCustomViewAndNotify(it, mBuilder!!)
                                    }
                                }

                                override fun onFail() {
                                    notificationManager!!.notify(notificationId, mBuilder!!.build())
                                }
                            }, widthImage, heightImage)
                        }
                    }
                    return
                }
                else -> {
                }
            }
            notificationManager!!.notify(notificationId, mBuilder!!.build())
        } catch (exception: Exception) {
            exception.printStackTrace()
        }
    }

    private fun setDataImageForCustomViewAndNotify(bitmap: Bitmap, mBuilder: NotificationCompat.Builder) {
        try {
            mBuilder.setStyle(NotificationCompat.DecoratedCustomViewStyle())
            val remoteViews = RemoteViews(context.packageName, R.layout.custom_remote_view)
            val remoteViewsExpands = RemoteViews(context.packageName, R.layout.custom_remote_view_expanse)
            remoteViews.setTextViewText(R.id.v_title, titleNotification)
            remoteViews.setTextViewText(R.id.v_content, textNotification)
            remoteViewsExpands.setTextViewText(R.id.v_title, titleNotification)
            remoteViewsExpands.setTextViewText(R.id.v_content, strMoreText)
            mBuilder.setCustomContentView(remoteViews)
                .setCustomBigContentView(remoteViewsExpands)
            notificationManager!!.notify(notificationId, mBuilder.build())
        } catch (exception: Exception) {
            exception.printStackTrace()
        }
    }

    private fun initNotificationComponent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name: CharSequence = context.getString(R.string.channel_name)
            val description = context.getString(R.string.channel_description)
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, name, importance)
            channel.description = description
            notificationManager = context.getSystemService(NotificationManager::class.java)
            if (notificationManager != null) {
                notificationManager!!.createNotificationChannel(channel)
                mBuilder = NotificationCompat.Builder(context, CHANNEL_ID)
            }
        } else {
            notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            mBuilder = NotificationCompat.Builder(context, CHANNEL_ID).apply {
                priority = NotificationCompat.PRIORITY_MAX
            }
        }
    }
    //end region methods
}