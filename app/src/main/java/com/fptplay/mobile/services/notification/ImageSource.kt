package com.fptplay.mobile.services.notification

import android.Manifest
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.util.Log
import androidx.annotation.DrawableRes
import androidx.annotation.IntDef
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.services.notification.util.CallBackResourceReady
import com.xhbadxx.projects.module.util.image.ImageProxy
import timber.log.Timber
import java.io.File
import java.io.IOException

@SuppressWarnings("unused")
class ImageSource private constructor(
    private val context: Context,
    @ImageResourceType private val type: Int,
    private val resourceId: Int,
    private val file: File?,
    private val uri: Uri?,
    private val url: String,
    private val resourceIdDefaultImage: Int
) {

    var TAG: String = this::class.java.simpleName

    companion object {
        const val TYPE_NONE = 0
        const val TYPE_RESOURCE_ID = TYPE_NONE + 1
        const val TYPE_FILE = TYPE_NONE + 2
        const val TYPE_URI = TYPE_NONE + 3
        const val TYPE_URL = TYPE_NONE + 4

        fun fromResource(context: Context, @DrawableRes resourceId: Int): ImageSource {
            return ImageSource(context, TYPE_RESOURCE_ID, resourceId, null, null, "", 0)
        }

        fun fromFile(context: Context, file: File?): ImageSource {
            return ImageSource(context, TYPE_FILE, 0, file, null, "", 0)
        }

        fun fromUri(context: Context, uri: Uri?): ImageSource {
            return ImageSource(context, TYPE_URI, 0, null, uri, "", 0)
        }

        fun fromUrl(context: Context, url: String, @DrawableRes resourceIdDefaultImage: Int): ImageSource {
            return ImageSource(context, TYPE_URL, 0, null, null, url, resourceIdDefaultImage)
        }
    }

    // region int def for image source
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @IntDef(TYPE_NONE, TYPE_RESOURCE_ID, TYPE_FILE, TYPE_URI)
    annotation class ImageResourceType

    // endregion

    // endregion
    // region variables

    private val WIDTH_IMAGE = 464
    private val HEIGHT_IMAGE = 261
    // end region

    //region getter
    fun getType(): Int {
        return type
    }

    private fun getResourceId(): Int {
        return resourceId
    }

    private fun getFile(): File? {
        return file
    }

    private fun getUri(): Uri? {
        return uri
    }

    private fun getUrl(): String {
        return url
    }

    private fun getResourceIdDefaultImage(): Int {
        return resourceIdDefaultImage
    }
    //end region getter

    //end region getter
    //region methods

    fun onLoadOnlineImage(callBackResourceReady: CallBackResourceReady?, widthImage: Int, heightImage: Int) {
        Handler(Looper.getMainLooper()).post {
            val optimizeImage = if(widthImage * heightImage != 0) ImageProxy.optimizeUrl(getUrl(), widthImage, heightImage) else getUrl()
            Timber.d("-------Load image for noti: $optimizeImage")
            GlideApp.with(context.applicationContext)
                .asBitmap()
                .load(optimizeImage)
                .into(object : CustomTarget<Bitmap>() {
                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        callBackResourceReady?.onFail()
                    }

                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        callBackResourceReady?.onResourceReady(resource)
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                        // this is called when imageView is cleared on lifecycle call or for
                        // some other reason.
                        // if you are referencing the bitmap somewhere else too other than this imageView
                        // clear it here as you can no longer have the bitmap
                    }
                })

        }
    }

    private fun getWidthImageDownload(widthImage: Int): Int {
        return if (widthImage == 0 || Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) WIDTH_IMAGE else widthImage
    }

    private fun getHeightImageDownload(heightImage: Int): Int {
        return if (heightImage == 0 || Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) HEIGHT_IMAGE else heightImage
    }

    fun convertImageOfflineToBitMap(widthImage: Int, heightImage: Int): Bitmap? {
        val bmOptions = BitmapFactory.Options()
        var bitmap: Bitmap?
        when (type) {
            TYPE_RESOURCE_ID -> return BitmapFactory.decodeResource(context.resources, getResourceId())
            TYPE_FILE -> {
                if (Utils.hasPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) && Utils.hasPermission(
                        context,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                ) {
                    bitmap = BitmapFactory.decodeFile(getFile()!!.absolutePath, bmOptions)
                    if (bitmap != null) {
                        bitmap =
                            if (widthImage != 0 && heightImage != 0 && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                                Bitmap.createScaledBitmap(bitmap, widthImage, heightImage, true)
                            } else {
                                Bitmap.createScaledBitmap(bitmap, WIDTH_IMAGE, HEIGHT_IMAGE, true)
                            }
                    }
                    return bitmap
                }
                return null
            }
            TYPE_URI -> return try {
                MediaStore.Images.Media.getBitmap(context.contentResolver, getUri())
            } catch (e: IOException) {
                Log.d(TAG, e.message!!)
                null
            }
            else -> {
            }
        }
        return null
    }

    // end region methods
}