package com.fptplay.mobile.services.notification

import android.app.PendingIntent
import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.IntDef
import androidx.core.app.NotificationCompat
import com.fptplay.mobile.services.notification.util.Notification

class NotificationBuilder(private val context: Context) {
    // region deprecated int def for mode
    companion object {
        const val STANDARD_NOTIFICATION = 0
        const val BIG_MESSAGE_NOTIFICATION = 1
        const val PICTURE_NOTIFICATION = 2
        const val CUSTOM_NOTIFICATION = 3
    }

    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @IntDef(STANDARD_NOTIFICATION, BIG_MESSAGE_NOTIFICATION, PICTURE_NOTIFICATION, CUSTOM_NOTIFICATION)
    annotation class NotificationMode

    // end region

    // end region
    // region variables
    private var notificationMode = 0
    private var titleNotification: String? = null
    private var textNotification: String? = null
    private var subTextNotification: String? = null
    private var imgSourceLargeIcon: ImageSource? = null
    private var strMoreText: String? = null
    private var pendingIntentWhenClickNotification: PendingIntent? = null
    private var resIdSmallIcon = 0
    private var resIdSmallIconTransparent = 0
    private var listAction: List<NotificationCompat.Action>? = null
    private var heightImage = 0
    private var widthImage = 0
    private var colorDrawable = 0
    private var strTicker: String? = null
    private var notificationId = 0

    // end region

    // region getter setter

    // end region
    // region getter setter
    fun setResIdSmallIconTransparent(resIdSmallIconTransparent: Int): NotificationBuilder {
        this.resIdSmallIconTransparent = resIdSmallIconTransparent
        return this
    }

    fun setNotificationId(notificationId: Int): NotificationBuilder {
        this.notificationId = notificationId
        return this
    }

    fun setDimensImage(widthPicture: Int, heightPicture: Int): NotificationBuilder {
        widthImage = widthPicture
        heightImage = heightPicture
        return this
    }

    fun setStrTicker(strTicker: String?): NotificationBuilder {
        this.strTicker = strTicker
        return this
    }

    fun setColorDrawable(@ColorRes colorDrawable: Int): NotificationBuilder {
        this.colorDrawable = colorDrawable
        return this
    }

    fun setSubtextNotification(subTextNotification: String?): NotificationBuilder {
        this.subTextNotification = subTextNotification
        return this
    }

    fun setListAction(listAction: List<NotificationCompat.Action>?): NotificationBuilder {
        this.listAction = listAction
        return this
    }

    fun setResIdSmallIcon(@DrawableRes resIdSmallIcon: Int): NotificationBuilder {
        this.resIdSmallIcon = resIdSmallIcon
        return this
    }

    fun setImageSourceLargeIcon(imgSourceLargeIcon: ImageSource?): NotificationBuilder {
        this.imgSourceLargeIcon = imgSourceLargeIcon
        return this
    }

    fun setTypeNotification(@NotificationMode notificationMode: Int): NotificationBuilder {
        this.notificationMode = notificationMode
        return this
    }

    fun setTitleNotification(titleNotification: String?): NotificationBuilder {
        this.titleNotification = titleNotification
        return this
    }

    fun setTextNotification(textNotification: String?): NotificationBuilder {
        this.textNotification = textNotification
        return this
    }

    fun setPendingIntentWhenClickNotification(pendingIntentWhenClickNotification: PendingIntent?): NotificationBuilder {
        this.pendingIntentWhenClickNotification = pendingIntentWhenClickNotification
        return this
    }

    fun setStrMoreText(strMoreText: String?): NotificationBuilder {
        this.strMoreText = strMoreText
        return this
    }

    fun getNotificationId(): Int {
        return notificationId
    }

    fun getStrTicker(): String? {
        return strTicker
    }

    fun getNotificationMode(): Int {
        return notificationMode
    }

    fun getTitleNotification(): String? {
        return titleNotification
    }

    fun getTextNotification(): String? {
        return textNotification
    }

    fun getStrMoreText(): String? {
        return strMoreText
    }

    fun getPendingIntentWhenClickNotification(): PendingIntent? {
        return pendingIntentWhenClickNotification
    }

    fun getColorDrawable(): Int {
        return colorDrawable
    }

    fun getContext(): Context {
        return context
    }

    fun getImgSourceLargeIcon(): ImageSource? {
        return imgSourceLargeIcon
    }

    fun getResIdSmallIcon(): Int {
        return resIdSmallIcon
    }

    fun getHeightImage(): Int {
        return heightImage
    }

    fun getWidthImage(): Int {
        return widthImage
    }

    fun getSubTextNotification(): String? {
        return subTextNotification
    }

    fun getListAction(): List<NotificationCompat.Action>? {
        return listAction
    }

    fun getResIdSmallIconTransparent(): Int {
        return resIdSmallIconTransparent
    }

    // end region

    // end region
    // region methods
    fun build(): Notification {
        return Notification(this)
    }
}