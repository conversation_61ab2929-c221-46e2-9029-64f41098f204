package com.fptplay.mobile.services.player

import android.annotation.SuppressLint
import android.app.KeyguardManager
import android.app.Notification
import android.app.PendingIntent
import android.app.Service
import android.content.*
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.media.session.PlaybackState
import android.os.Build
import android.os.IBinder
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.view.KeyEvent
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ServiceLifecycleDispatcher
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.application.FptPlayLifecycleObserver
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.extensions.modifyPlayerListener
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.player.PlayerUtils.isTheSame
import com.fptplay.mobile.player.PlayerUtils.isTheSameUrlRequest
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.media_session.MediaSessionHandler.DataSource
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ext.mediasession.MediaSessionConnector
import com.google.android.exoplayer2.ui.PlayerNotificationManager
import com.google.android.exoplayer2.util.NotificationUtil
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.History
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.domain.repository.fplay.VodRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit

class BackgroundPlayerService : Service(),
    PlayerNotificationManager.MediaDescriptionAdapter,
    PlayerNotificationManager.NotificationListener,
    LifecycleOwner {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface BackgroundPlayerServiceInterface {
        val sharedPreferences: SharedPreferences
        val vodRepository: VodRepository
    }

    private val sharedPreferences by lazy {
        EntryPointAccessors.fromApplication(
            applicationContext,
            BackgroundPlayerServiceInterface::class.java
        ).sharedPreferences
    }
    private val vodRepository by lazy {
        EntryPointAccessors.fromApplication(
            applicationContext,
            BackgroundPlayerServiceInterface::class.java
        ).vodRepository
    }

    //region object
    companion object {
        const val INTENT_NOTIFICATION_CUSTOM_ACTION = "notification_custom_action"
        const val ACTION_KEY = "action_key"
        const val ACTION_VALUE_ERROR = "error"
        const val VALUE_ERROR = "error_value"
        const val VALUE_BACK_GROUND = "back_ground"
        const val VALUE_FORCE_GROUND = "force_ground"
        const val ACTION_EPISODE_INDEX_KEY = "action_episode_index_key"

        const val BACKGROUND_PLAYBACK_NOTIFICATION_ID = 99
        const val CHANNEL_ID_BACKGROUND_PLAYBACK = "background_playback"
        private const val ACTION_START_IN_BACKGROUND =
            "com.fplay.activity.controller.action.START_IN_BACKGROUND"

        const val VALUE_PIP_PLAY_NEW_EPISODE_ACTION_NEXT = "pip_play_new_episode_action_next"
        const val VALUE_PIP_PLAY_NEW_EPISODE_ACTION_PREVIOUS = "pip_play_new_episode_action_previous"


        /**
         * Binds this service to the given context.
         *
         * @param context
         * @param serviceConnection will be used to pass a [Binder] instance
         * for further communication with this service
         */
        @JvmStatic
        fun bind(context: Context, serviceConnection: ServiceConnection?) {
            Logger.d("BackgroundPlayerService - bind")
            val intent = Intent(context, BackgroundPlayerService::class.java)
            context.bindService(intent, serviceConnection!!, Context.BIND_AUTO_CREATE)
        }

        private fun startInBackground(context: Context) {
            Logger.d("BackgroundPlayerService - startInBackground")
            val intent = Intent(context, BackgroundPlayerService::class.java)
            intent.action = ACTION_START_IN_BACKGROUND
            context.startService(intent)
        }
    }
    //endregion object

    //region Variables
    private var mediaSession: MediaSessionCompat ?= null
    private var mediaSessionConnector: MediaSessionConnector ?= null
    private var exoPlayerProxy: ExoPlayerProxy? = null
    private var initialRequest: IPlayer.Request? = null
    private val playlist: ArrayList<Details.Episode> by lazy { arrayListOf() }
    private var id = ""
    private var currentEpisodePos = 0
    private val binder = Binder()
    private val lifecycleDispatcher: ServiceLifecycleDispatcher = ServiceLifecycleDispatcher(this)
    private var playerNotificationManager: PlayerNotificationManager? = null
    private var foregroundActivityIntent: Intent? = null
    private var isPlaybackInBackground = false
    private val playerEvent: IPlayer.IPlayerCallback by lazy { PlayerEvents() }
    private val notificationReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    PlayerNotificationManager.ACTION_NEXT -> { onNext() }
                    PlayerNotificationManager.ACTION_PREVIOUS -> { onPrevious() }
                    else -> { }
                }
            }
        }
    }
    private var contentBgUrl = ""
    private val episodeBgUrls = mutableListOf<String>()
    private var getBitmapJob: Job? = null
    private var bgBitmap: Bitmap?= null
    private var dataSource: DataSource? = null
    private var isBackgroundAudioForPiP: Boolean = false
    private var userHistory: History ?= null
    private var serviceHistoryScope: CoroutineScope? = null
    //endregion Variables

    //
    private fun onNext() {
        val actionIntent = Intent(INTENT_NOTIFICATION_CUSTOM_ACTION)
        LocalBroadcastManager.getInstance(this@BackgroundPlayerService).sendBroadcast(actionIntent)
        if(currentEpisodePos < playlist.size - 1) {
            currentEpisodePos += 1
            actionIntent.putExtra(ACTION_EPISODE_INDEX_KEY, currentEpisodePos)
            // Update index for playlist
            if (playlist[currentEpisodePos].isItemOfPlaylist) {
                exoPlayerProxy?.dataPlayerControl()?.episodeIndex = currentEpisodePos
            }
            //

            if (isBackgroundAudioForPiP && !isLockScreen()) {
                LocalBroadcastManager.getInstance(this@BackgroundPlayerService).sendBroadcast(Intent(INTENT_NOTIFICATION_CUSTOM_ACTION).apply {
                    putExtra(ACTION_KEY, VALUE_PIP_PLAY_NEW_EPISODE_ACTION_NEXT)
                })
            } else {
                runBlocking {
                    if (playlist[currentEpisodePos].isItemOfPlaylist) { // Case: Playlist
                        vodRepository.getBookmarkChapter(vodId = playlist[currentEpisodePos].vodId, chapterId = "0").collect {
                            if(it is Result.Success) {
                                userHistory = it.data
                            }
                        }
                        vodRepository.getStream(id = playlist[currentEpisodePos].vodId, episodeId = "0", bitrateId = playlist[currentEpisodePos].autoProfile, dataType = "").collect {
                            if(it is Result.Success) {
                                it.data?.run {
                                    exoPlayerProxy?.prepare(
                                        request = IPlayer.Request(
                                            id = "0",
                                            url = IPlayer.Request.Url(
                                                dolbyVisionUrl = urlDashDolbyVision,
                                                h265HDR10PlusUrl = urlDashH265Hdr10Plus,
                                                h265HDR10Url = urlDashH265Hdr,
                                                h265HlgUrl = urlDashH265Hlg,
                                                h265Url = urlDashH265,
                                                av1Url = urlDashAv1,
                                                vp9Url = urlDashVp9,
                                                url = urlSub.ifEmpty { url }
                                            ),
                                            startPosition = 0,
                                            clearRequestWhenOnStop = false,
                                            headerRequestProperties = Utils.getHeaderForPlayerRequest(streamSession)
                                        ))
                                    updateDataPlayerControl()
                                    binder.nextVideoNotificationBackground()
                                }
                            }
                        }
                    } else {
                        vodRepository.getBookmarkChapter(vodId = id, chapterId = playlist[currentEpisodePos].id).collect {
                            if(it is Result.Success) {
                                userHistory = it.data
                            }
                        }
                        vodRepository.getStream(id = id, episodeId = playlist[currentEpisodePos].id, bitrateId = playlist[currentEpisodePos].autoProfile, dataType = "").collect {
                            if(it is Result.Success) {
                                it.data?.run {
                                    exoPlayerProxy?.prepare(
                                        request = IPlayer.Request(
                                            id = playlist[currentEpisodePos].id,
                                            url = IPlayer.Request.Url(
                                                dolbyVisionUrl = urlDashDolbyVision,
                                                h265HDR10PlusUrl = urlDashH265Hdr10Plus,
                                                h265HDR10Url = urlDashH265Hdr,
                                                h265HlgUrl = urlDashH265Hlg,
                                                h265Url = urlDashH265,
                                                av1Url = urlDashAv1,
                                                vp9Url = urlDashVp9,
                                                url = urlSub.ifEmpty { url }
                                            ),
                                            startPosition = 0,
                                            clearRequestWhenOnStop = false
                                        )
                                    )
                                    updateDataPlayerControl()
                                    binder.nextVideoNotificationBackground()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun onPrevious() {
        val actionIntent = Intent(INTENT_NOTIFICATION_CUSTOM_ACTION)
        LocalBroadcastManager.getInstance(this@BackgroundPlayerService).sendBroadcast(actionIntent)
        if(currentEpisodePos > 0) {
            currentEpisodePos -= 1
            actionIntent.putExtra(ACTION_EPISODE_INDEX_KEY, currentEpisodePos)
            // Update index for playlist
            if (playlist[currentEpisodePos].isItemOfPlaylist) {
                exoPlayerProxy?.dataPlayerControl()?.episodeIndex = currentEpisodePos
            }
            //
            if (isBackgroundAudioForPiP && !isLockScreen()) {
                LocalBroadcastManager.getInstance(this@BackgroundPlayerService).sendBroadcast(Intent(INTENT_NOTIFICATION_CUSTOM_ACTION).apply {
                    putExtra(ACTION_KEY, VALUE_PIP_PLAY_NEW_EPISODE_ACTION_PREVIOUS)
                })
            } else {
                runBlocking {
                    if (playlist[currentEpisodePos].isItemOfPlaylist) { // Case: Playlist
                        vodRepository.getBookmarkChapter(vodId = playlist[currentEpisodePos].vodId, chapterId = "0").collect {
                            if(it is Result.Success) {
                                userHistory = it.data
                            }
                        }
                        vodRepository.getStream(id = playlist[currentEpisodePos].vodId, episodeId = "0", bitrateId = playlist[currentEpisodePos].autoProfile, dataType = "").collect {
                            if(it is Result.Success) {
                                it.data?.run {
                                    exoPlayerProxy?.prepare(
                                        request = IPlayer.Request(
                                            id = "0",
                                            url = IPlayer.Request.Url(
                                                dolbyVisionUrl = urlDashDolbyVision,
                                                h265HDR10PlusUrl = urlDashH265Hdr10Plus,
                                                h265HDR10Url = urlDashH265Hdr,
                                                h265HlgUrl = urlDashH265Hlg,
                                                h265Url = urlDashH265,
                                                av1Url = urlDashAv1,
                                                vp9Url = urlDashVp9,
                                                url = urlSub.ifEmpty { url }
                                            ),
                                            startPosition = 0,
                                            clearRequestWhenOnStop = false,
                                            headerRequestProperties = Utils.getHeaderForPlayerRequest(streamSession)
                                        )
                                    )
                                    updateDataPlayerControl()
                                    binder.nextVideoNotificationBackground()
                                }
                            }
                        }
                    } else {
                        vodRepository.getBookmarkChapter(vodId = id, chapterId = playlist[currentEpisodePos].id).collect {
                            if(it is Result.Success) {
                                userHistory = it.data
                            }
                        }
                        vodRepository.getStream(id = id, episodeId = playlist[currentEpisodePos].id, bitrateId = playlist[currentEpisodePos].autoProfile, dataType = "").collect {
                            if(it is Result.Success) {
                                it.data?.run {
                                    exoPlayerProxy?.prepare(
                                        request = IPlayer.Request(
                                            id = playlist[currentEpisodePos].id,
                                            url = IPlayer.Request.Url(
                                                dolbyVisionUrl = urlDashDolbyVision,
                                                h265HDR10PlusUrl = urlDashH265Hdr10Plus,
                                                h265HDR10Url = urlDashH265Hdr,
                                                h265HlgUrl = urlDashH265Hlg,
                                                h265Url = urlDashH265,
                                                av1Url = urlDashAv1,
                                                vp9Url = urlDashVp9,
                                                url = urlSub.ifEmpty { url }
                                            ),
                                            startPosition = 0,
                                            clearRequestWhenOnStop = false,
                                            headerRequestProperties = Utils.getHeaderForPlayerRequest(streamSession)
                                        )
                                    )
                                    updateDataPlayerControl()
                                    binder.nextVideoNotificationBackground()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    //


    //region Overrides
    override fun onCreate() {
        lifecycleDispatcher.onServicePreSuperOnCreate()
        try {
            ContextCompat.registerReceiver(this, notificationReceiver, IntentFilter().apply {
                addAction(PlayerNotificationManager.ACTION_NEXT)
                addAction(PlayerNotificationManager.ACTION_PREVIOUS)
            }, ContextCompat.RECEIVER_NOT_EXPORTED)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        super.onCreate()
//        lifecycleScope.launchWhenCreated {
//            onPlayerError()
//        }
    }

    override fun onBind(intent: Intent): IBinder {
        lifecycleDispatcher.onServicePreSuperOnBind()
        return binder
    }

    override fun onStart(intent: Intent?, startId: Int) {
        Logger.d("BackgroundPlayerService - onStart")
        lifecycleDispatcher.onServicePreSuperOnStart()
        super.onStart(intent, startId)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.d("BackgroundPlayerService - onStartCommand")
        initialRequest?.let { initRequest ->
            if (exoPlayerProxy?.request?.url?.isTheSame(initRequest.url) == false) {
                exoPlayerProxy?.prepare(initialRequest)
            }
        }
        handleIntent(intent)
        return START_STICKY
    }

    override val lifecycle: Lifecycle = lifecycleDispatcher.lifecycle


    override fun onTaskRemoved(rootIntent: Intent) {
        super.onTaskRemoved(rootIntent)
        onDestroy()
    }

    override fun onDestroy() {
        try { // Case: Receiver not registered
            unregisterReceiver(notificationReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        movePlaybackToForeground()

        GlobalScope.launch(Dispatchers.Main) {
            exoPlayerProxy?.modifyPlayerListener(listener = playerEvent, isRegister = false)
            exoPlayerProxy?.stop()
            exoPlayerProxy?.release()
            playerNotificationManager?.setPlayer(null)
            mediaSessionConnector?.setPlayer(null)
            mediaSession?.isActive = false
        }
        stopTaskUpdateBookmark()
        lifecycleDispatcher.onServicePreSuperOnDestroy()
        super.onDestroy()
    }

    override fun getCurrentContentTitle(player: Player): String {
        return exoPlayerProxy?.dataPlayerControl()?.title ?: ""
    }

    override fun createCurrentContentIntent(player: Player): PendingIntent {
        Timber.i("createCurrentContentIntent: %s", foregroundActivityIntent!!.component)

        // a notification click will bring us back to the activity that launched it
        return getNotificationCLickedPendingIntent(foregroundActivityIntent)
    }

    override fun getCurrentContentText(player: Player): String =
        exoPlayerProxy?.dataPlayerControl()?.des ?: ""

    override fun getCurrentLargeIcon(
        player: Player,
        callback: PlayerNotificationManager.BitmapCallback
    ): Bitmap? = bgBitmap

    override fun onNotificationPosted(
        notificationId: Int,
        notification: Notification,
        ongoing: Boolean
    ) {
        if (ongoing) {
            startForeground(notificationId, notification)
        } else {
            stopForeground(false)
        }
    }

    //endregion Overrides

    //region methods
    private fun updateDataPlayerControl() {
        exoPlayerProxy?.dataPlayerControl()?.apply {
            des = String.format("%s", playlist[currentEpisodePos].titleVietnam)
        }
    }

    private fun onPlayerError(messageDetail: String) {

        val wasPlayingInBackground = isPlaybackInBackground

        movePlaybackToForeground()

        if (wasPlayingInBackground) {
//            foregroundActivityIntent?.putExtra(Constants.REQUIRE_RESUME_ACTIVITY, false)
//            val pendingIntent = getNotificationCLickedPendingIntent(foregroundActivityIntent)
//
//            val builder =
//                NotificationCompat.Builder(this, CHANNEL_ID_BACKGROUND_PLAYBACK)
//                    .setContentTitle(videoInformation?.title ?: "")
//                    .setContentText(messageDetail)
//                    .setColor(ContextCompat.getColor(this, R.color.colorPrimaryDark))
//                    .setSmallIcon(if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
//                        R.drawable.ic_notification_transparent else R.drawable.ic_launcher_notification)
//                    .setOnlyAlertOnce(true)
//                    .setAutoCancel(true)
//                    .setContentIntent(pendingIntent)
//
//            val notificationManager =
//                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
//            notificationManager.notify(
//                BACKGROUND_PLAYBACK_NOTIFICATION_ID,
//                builder.build()
//            )
        }
    }

    private fun handleIntent(intent: Intent?) {
        if (intent == null || intent.action == null) {
            return
        }

        if (ACTION_START_IN_BACKGROUND == intent.action) {
            handleStartInBackground()
        }
    }

    /**
     * @see Binder.movePlaybackToBackground
     */
    private fun movePlaybackToBackground() {

        val intent = Intent(INTENT_NOTIFICATION_CUSTOM_ACTION)
        intent.putExtra(ACTION_KEY, VALUE_BACK_GROUND)
        LocalBroadcastManager.getInstance(this@BackgroundPlayerService).sendBroadcast(intent)

        // start long running task
        startInBackground(this)
    }

    /**
     * @see Binder.movePlaybackToForeground
     */
    private fun movePlaybackToForeground() {

        val intent = Intent(INTENT_NOTIFICATION_CUSTOM_ACTION)
        intent.putExtra(ACTION_KEY, VALUE_FORCE_GROUND)
        LocalBroadcastManager.getInstance(this@BackgroundPlayerService).sendBroadcast(intent)
        isPlaybackInBackground = false

        stopForeground(true)
        stopSelf()

        exoPlayerProxy?.modifyPlayerListener(listener = playerEvent, isRegister = false)
        playerNotificationManager?.apply {
            setPlayer(null)
            mediaSessionConnector?.setPlayer(null)
            mediaSession?.isActive = false
        }
    }

    @SuppressLint("WrongConstant")
    private fun handleStartInBackground() {
        try {
            if (currentEpisodePos in 0 until playlist.size) {
                val newVodId = if (playlist[currentEpisodePos].isItemOfPlaylist) { // Case: Playlist
                    playlist[currentEpisodePos].vodId
                } else id
                val newEpisodeId = if (playlist[currentEpisodePos].isItemOfPlaylist) { // Case: Playlist
                    "0"
                } else playlist[currentEpisodePos].id
                if (userHistory != null) {
                    if (newVodId != userHistory?.movieId || newEpisodeId != userHistory?.episodeId) {
                        userHistory = null
                    }
                }
            }

            isPlaybackInBackground = true

            bgBitmap = null
            playerNotificationManager?.setPlayer(null)
            mediaSessionConnector?.setPlayer(null)
            mediaSession?.isActive = false

            val mediaDescriptionAdapter =
                DefaultMediaDescriptionAdapter(
                    getNotificationCLickedPendingIntent(foregroundActivityIntent),
                    exoPlayerProxy?.dataPlayerControl()
                )
            NotificationUtil.createNotificationChannel(
                this,
                CHANNEL_ID_BACKGROUND_PLAYBACK,
                R.string.all_background_audio_channel_name,
                R.string.all_background_audio_channel_description,
                NotificationUtil.IMPORTANCE_LOW
            )

            exoPlayerProxy?.let { exoPlayerProxy ->
                val isVodContent = exoPlayerProxy.dataPlayerControl()?.isLive == false
                playerNotificationManager = PlayerNotificationManager.Builder(
                    this,
                    BACKGROUND_PLAYBACK_NOTIFICATION_ID,
                    CHANNEL_ID_BACKGROUND_PLAYBACK
                )
                    .setMediaDescriptionAdapter(mediaDescriptionAdapter)
                    .setNotificationListener(this)
                    .setCustomActionReceiver(object : PlayerNotificationManager.CustomActionReceiver {
                        override fun getCustomActions(player: Player): MutableList<String> {
                            val stringActions: MutableList<String> = java.util.ArrayList()
//                            if (isVodContent && currentEpisodePos < playlist.size - 1) {
//                                stringActions.add(PlayerNotificationManager.ACTION_NEXT)
//                            }
                            return stringActions
                        }
                        override fun onCustomAction(player: Player, action: String, intent: Intent) {}
                        override fun createCustomActions(context: Context, instanceId: Int): MutableMap<String, NotificationCompat.Action> {
                            return mutableMapOf()
                        }
                    })
                    .build()

                mediaSession = MediaSessionCompat(this, "PlayerService")
                mediaSession?.let { mediaSession ->
                    mediaSessionConnector = MediaSessionConnector(mediaSession).also {
                        it.setQueueNavigator(PlayQueueNavigator(listener = onPlayQueueNavigatorListener))
                        it.setMediaButtonEventHandler(mediaButtonEventHandler)
                        it.setMediaMetadataToMediaConnector(exoPlayerProxy = exoPlayerProxy, bitmap = null)
                        it.setPlayer(exoPlayerProxy.internalPlayer() as? ExoPlayer)
                    }
                    mediaSession.isActive = true
                }

                //
                triggerGetImageBitmap(url = getBgImageUrl(contentIndex = currentEpisodePos))
                //

                playerNotificationManager?.also {
                    it.setUsePlayPauseActions(true)
                    it.setSmallIcon(
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
                            R.drawable.ic_notification_transparent else R.drawable.ic_launcher
                    )
                    it.setColor(ContextCompat.getColor(this, R.color.color_bg_audio))
                    it.setColorized(true)
                    it.setPlayer(exoPlayerProxy.internalPlayer() as? ExoPlayer)
                    it.setUseRewindAction(isVodContent)
                    it.setUseRewindActionInCompactView(isVodContent)
                    it.setUsePreviousAction(isVodContent && currentEpisodePos > 0)
                    it.setUseNextAction(isVodContent && currentEpisodePos < playlist.size - 1)
                    it.setUseFastForwardAction(isVodContent)
                    it.setUseFastForwardActionInCompactView(isVodContent)
                    it.setBadgeIconType(NotificationCompat.BADGE_ICON_SMALL)
                    mediaSession?.let { mediaSession ->
                        it.setMediaSessionToken(mediaSession.sessionToken)
                    }
                }
            }

            val isVodContent = exoPlayerProxy?.dataPlayerControl()?.isLive == false
            if (isVodContent) {
                startTaskUpdateBookmark()
            }

        } catch (exception: Exception) {

        }
    }

    @SuppressLint("UnspecifiedImmutableFlag")
    private fun getNotificationCLickedPendingIntent(intent: Intent?): PendingIntent {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        } else {
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT
            )
        }
    }

    fun updateIndexEpisodeToPendingIntent() {
        if (foregroundActivityIntent != null && exoPlayerProxy?.dataPlayerControl() != null) {
//            foregroundActivityIntent?.putExtra(Constants.DETAIL_VOD_INDEX_KEY, exoPlayerProxy?.dataPlayerControl()?.episodeIndex)
        }
    }

    private val onPlayQueueNavigatorListener = object : PlayQueueNavigator.OnPlayQueueNavigatorListener {
        override fun getSupportedQueueNavigatorActions(player: Player): Long {
            mediaSessionConnector?.setMediaMetadataToMediaConnector(exoPlayerProxy = exoPlayerProxy, bitmap = bgBitmap)
            val isVodContent = exoPlayerProxy?.dataPlayerControl()?.isLive == false
            return if (isVodContent) {
                if (playlist.size == 1) {
                    PlaybackState.ACTION_SEEK_TO
                } else if (currentEpisodePos > 0 && currentEpisodePos < playlist.size - 1) {
                    (PlaybackState.ACTION_SKIP_TO_NEXT or PlaybackState.ACTION_SKIP_TO_PREVIOUS or PlaybackState.ACTION_SEEK_TO)
                } else if (currentEpisodePos == playlist.size - 1) {
                    (PlaybackState.ACTION_SKIP_TO_PREVIOUS or PlaybackState.ACTION_SEEK_TO)
                } else if (currentEpisodePos == 0) {
                    (PlaybackState.ACTION_SKIP_TO_NEXT or PlaybackState.ACTION_SEEK_TO)
                } else {
                    (PlaybackState.ACTION_SKIP_TO_NEXT or PlaybackState.ACTION_SKIP_TO_PREVIOUS or PlaybackState.ACTION_SEEK_TO)
                }
            } else {
                0
            }
        }

        override fun onSkipToNext(player: Player) {
            onNext()
        }

        override fun onSkipToPrevious(player: Player) {
            onPrevious()
        }

    }

    private val mediaButtonEventHandler = object : MediaSessionConnector.MediaButtonEventHandler {
        override fun onMediaButtonEvent(player: Player, mediaButtonEvent: Intent): Boolean {
            val keyEvent = mediaButtonEvent.getParcelableExtra<KeyEvent>(Intent.EXTRA_KEY_EVENT)
            Logger.d("BackgroundPlayerService -> MediaButtonEventHandler -> KeyEvent: $keyEvent -> keycode: ${keyEvent?.keyCode}")
            if (keyEvent != null && keyEvent.action == KeyEvent.ACTION_DOWN) {
                when (keyEvent.keyCode) {
                    KeyEvent.KEYCODE_MEDIA_PLAY,
                    KeyEvent.KEYCODE_MEDIA_PAUSE -> {
                        return !checkHandlePlayPause()
                    }
                }
            }
            return false
        }
    }

    private fun checkHandlePlayPause() : Boolean {
        return checkEnableAllActionEvent(dataSource ?: DataSource())
    }

    private fun checkEnableAllActionEvent(dataSource: DataSource): Boolean {
        val isEnableAllActions = if (dataSource.isPlayVipTrailer) {
            false
        } else when (dataSource.screenType) {
            is PlayerHandler.ScreenType.Vod -> true
            is PlayerHandler.ScreenType.Live -> dataSource.isPlayTimeShift
            is PlayerHandler.ScreenType.Premiere -> false
            else -> false
        }
        Logger.d("BackgroundPlayerService -> checkEnableAllActionEvent -> isEnableAllActions: $isEnableAllActions")
        return isEnableAllActions
    }
    //endregion methods

    //region Player Events
    inner class PlayerEvents : IPlayer.IPlayerCallback{
        override fun onReady() {
            CoroutineScope(Dispatchers.IO).launch {
                delay(200)
                if (isBackgroundAudioForPiP && !isLockScreen()) {
                    // Do nothing
                } else {
                    userHistory?.let {
                        val startPositionSeconds = userHistory?.startPosition ?: 0L
                        withContext(Dispatchers.Main) {
                            exoPlayerProxy?.seek(TimeUnit.SECONDS.toMillis(startPositionSeconds))
                        }
                    }
                }
            }
        }
        override fun onEnd() {
            if (isBackgroundAudioForPiP && !isLockScreen()) {
                // Do nothing
            } else if (!isBackgroundAudioForPiP && !FptPlayLifecycleObserver.appInBackground) {
                // Do nothing
            } else {
                onNext()
            }
        }
    }
    //endregion

    //region Binder
    inner class Binder : android.os.Binder() {
        /**
         * @return Player instance that will live as long as this service is up and running.
         */
        fun getPlayer(): ExoPlayerProxy? {
            return exoPlayerProxy
        }

        fun setInitialRequest(request: IPlayer.Request?) {
            initialRequest = request
        }

        fun setPlayer(exoPlayerProxy: ExoPlayerProxy?) {
            Logger.d("BackgroundPlayerService - setPlayer")
            <EMAIL> = exoPlayerProxy
            <EMAIL>?.modifyPlayerListener(listener = playerEvent, isRegister = false)
            <EMAIL>?.modifyPlayerListener(listener = playerEvent, isRegister = true)
        }


        /**
         * @param intent used to bring the calling activity to front
         * after finishing background playback
         */
        fun setForegroundActivityIntent(intent: Intent?) {
            foregroundActivityIntent = intent
        }

        fun nextVideoNotificationBackground() {
            updateIndexEpisodeToPendingIntent()
            if (<EMAIL>) {
                handleStartInBackground()
            }
        }

        /**
         * Displays a player notification and starts keeping this service alive
         * in background. Once called the service will resume running until [.movePlaybackToForeground]
         * is called or the notification is dismissed.
         */
        fun movePlaybackToBackground() = <EMAIL>()

        /**
         * Call this once the playback is visible to the user. This will allow this service to
         * be destroyed as soon as no ui component is bound any more.
         * This will dismiss the playback notification.
         */
        fun movePlaybackToForeground() = <EMAIL>()

        fun showErrorPlayer(message: String) = <EMAIL>(message)

        fun setContentId(id: String) {
            <EMAIL> = id
        }

        fun setContentBgUrl(url: String) {
            <EMAIL> = url
        }

        fun setEpisodeBgUrls(data: List<String>) {
            <EMAIL>()
            <EMAIL>(data)
        }

        fun setStartIndex(index: Int) {
            currentEpisodePos = index

            // Update index for playlist
            if (currentEpisodePos >= 0 && currentEpisodePos < playlist.size) {
                if (playlist[currentEpisodePos].isItemOfPlaylist) {
                    exoPlayerProxy?.dataPlayerControl()?.episodeIndex = currentEpisodePos
                }
            }
        }

        fun setPlaylist(playlist: List<Details.Episode>) {
            <EMAIL> {
                clear()
                addAll(playlist)
            }

        }

        fun setDataSource(dataSource: DataSource) {
            <EMAIL> = dataSource
        }

        fun setIsBackgroundAudioForPiP(forPiP: Boolean) {
            <EMAIL> = forPiP
        }
    }
    //endregion

    //region class internal
    inner class DefaultMediaDescriptionAdapter(
        private val pendingIntent: PendingIntent,
        private val videoInformation: PlayerControlView.Data?,
    ) : PlayerNotificationManager.MediaDescriptionAdapter {

        override fun getCurrentContentTitle(player: Player): CharSequence {
            return videoInformation?.title ?: ""
        }

        override fun createCurrentContentIntent(player: Player): PendingIntent? {
            return pendingIntent
        }

        override fun getCurrentContentText(player: Player): CharSequence? {
            return videoInformation?.des ?: ""
        }

        override fun getCurrentLargeIcon(player: Player, callback: PlayerNotificationManager.BitmapCallback): Bitmap? {
            return bgBitmap
        }

    }
    //endregion class internal


    //region MediaSession
    private fun getBgImageUrl(contentIndex: Int): String {
        episodeBgUrls.forEachIndexed { index, data ->
            if (index == contentIndex) {
                return data.ifEmpty { contentBgUrl }
            }
        }
        return contentBgUrl
    }
    private fun triggerGetImageBitmap(url: String?) {
        if (!url.isNullOrEmpty()) {
            Logger.d("BackgroundPlayerService -> triggerGetImageBitmap -> url: $url")
            val widthImage = MainApplication.INSTANCE.applicationContext.getDisplayWidth() / 2
            val heightImage = (widthImage / Constants.HORIZONTAL_IMAGE_RATIO).toInt()
            val optimizeImage = if(widthImage * heightImage != 0) ImageProxy.optimizeUrl(url, widthImage, heightImage) else url
            getBitmap(url = optimizeImage) {
                mediaSessionConnector.setMediaMetadataToMediaConnector(exoPlayerProxy = exoPlayerProxy, bitmap = it)
            }
        }
    }

    private fun getBitmap(url: String, onSuccess: (Bitmap) -> Unit) {
        cancelBitmapJob()
        getBitmapJob = CoroutineScope(Dispatchers.IO).launch {
            GlideApp.with(MainApplication.INSTANCE.applicationContext)
                .asBitmap()
                .load(url)
                .into(object : CustomTarget<Bitmap>() {
                    override fun onLoadFailed(errorDrawable: Drawable?) {

                    }
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap>?
                    ) {
                        Logger.d("BackgroundPlayerService -> getBitmap -> Success")
                        bgBitmap = resource
                        onSuccess.invoke(resource)
                        //
                    }
                    override fun onLoadCleared(placeholder: Drawable?) {}
                })
        }
    }

    private fun cancelBitmapJob() {
        getBitmapJob?.cancel()
        getBitmapJob = null
    }

    private fun MediaSessionConnector?.setMediaMetadataToMediaConnector(exoPlayerProxy: ExoPlayerProxy?, bitmap: Bitmap ?= null) {
        Logger.d("BackgroundPlayerService -> setMediaMetadataToMediaConnector -> Bitmap = $bitmap")
        CoroutineScope(Dispatchers.IO).launch {
            kotlin.runCatching {
                withContext(Dispatchers.Main) {
                    this@setMediaMetadataToMediaConnector?.setMediaMetadataProvider {
                        if (exoPlayerProxy?.dataPlayerControl()?.isLive == true) {
                            MediaMetadataCompat.Builder()
                                .putString(MediaMetadataCompat.METADATA_KEY_TITLE, exoPlayerProxy.dataPlayerControl()?.title ?: "")
                                .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, exoPlayerProxy.dataPlayerControl()?.des ?: "")
                                .putBitmap(MediaMetadataCompat.METADATA_KEY_ALBUM_ART, bitmap)
                                .build()
                        } else {
                            MediaMetadataCompat.Builder()
                                .putString(MediaMetadataCompat.METADATA_KEY_TITLE, exoPlayerProxy?.dataPlayerControl()?.title ?: "")
                                .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, exoPlayerProxy?.dataPlayerControl()?.des ?: "")
                                .putBitmap(MediaMetadataCompat.METADATA_KEY_ALBUM_ART, bitmap)
                                .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, exoPlayerProxy?.totalDuration() ?: 0L)
                                .build()
                        }
                    }
                }
            }.getOrElse {
                it.printStackTrace()
            }
        }
    }
    //endregion


    private fun isLockScreen(): Boolean {
        val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager
        return keyguardManager?.isKeyguardLocked ?: false
    }


    // region Update bookmark
    private fun startTaskUpdateBookmark() {
        stopTaskUpdateBookmark()
        serviceHistoryScope = CoroutineScope(Dispatchers.IO).apply {
            launch {
                while (isActive) {
                    withContext(Dispatchers.Main) { updateBookmark() }
                    delay(1000L)
                }
            }
        }
    }

    private fun stopTaskUpdateBookmark() {
        serviceHistoryScope?.cancel()
        serviceHistoryScope = null
    }

    private fun updateBookmark() {
        try {
            if (exoPlayerProxy?.isPlaying() == true) {
                val startPosition = TimeUnit.MILLISECONDS.toSeconds(exoPlayerProxy?.currentDuration() ?: 0L)
                userHistory = userHistory?.copy(startPosition = startPosition) ?: kotlin.run {
                    if (currentEpisodePos in 0 until playlist.size) {
                        if (playlist[currentEpisodePos].isItemOfPlaylist) { // Case: Playlist
                            History(movieId = playlist[currentEpisodePos].vodId, episodeId = "0", startPosition = startPosition)
                        } else {
                            History(movieId = id, episodeId =  playlist[currentEpisodePos].id, startPosition = startPosition)
                        }
                    } else null
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    // endregion
}