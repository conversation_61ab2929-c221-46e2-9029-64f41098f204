package com.fptplay.mobile.services

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import com.clevertap.android.sdk.CleverTapAPI
import com.clevertap.android.sdk.pushnotification.fcm.CTFcmMessageHandler
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.WelcomeActivity
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.utils.CheckValidUtil
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.services.notification.ImageSource
import com.fptplay.mobile.services.notification.NotificationBuilder
import com.fptplay.mobile.services.notification.util.Notification
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.runBlocking
import timber.log.Timber


class FireBaseMessagingManagerService : FirebaseMessagingService() {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface MyMessagingServiceInterface {
        val sharedPreferences: SharedPreferences
        val commonRepository: CommonRepository
    }

    private val sharedPreferences by lazy {
        EntryPointAccessors.fromApplication(
            applicationContext,
            MyMessagingServiceInterface::class.java
        ).sharedPreferences
    }
    private val commonRepository by lazy {
        EntryPointAccessors.fromApplication(
            applicationContext,
            MyMessagingServiceInterface::class.java
        ).commonRepository
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Timber.e("*** refresh token: $token")
        saveDeviceRegistrationToken(token)
        sendDeviceRegistrationTokenToServer(token)
        val cleverTapAPI = CleverTapAPI.getDefaultInstance(this)
        cleverTapAPI?.pushFcmRegistrationId(token, true)
    }

    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        Timber.d("*** remote message: ${message.data}")
        sendNotification(message)
    }

    private fun saveDeviceRegistrationToken(token: String) {
        sharedPreferences.saveFCMToken(token)
    }

    private fun sendDeviceRegistrationTokenToServer(refreshToken: String) {
        runBlocking {
            commonRepository.addDeviceRegistrationToken(refreshToken, sharedPreferences.userId().ifBlank { null }).collect {
                Timber.d("*** Send token to server result: $it")
            }
        }
    }

    fun sendNotification(remoteMessage: RemoteMessage) {
        Timber.d("***RemoteMessage: %s", remoteMessage.toString())
        GlobalEvent.pushEvent(GlobalEvent.NOTIFICATION_RECEIVED, null)
        MainApplication.INSTANCE.appConfig.hasNewNotification = true
        try {
            if (remoteMessage.data.isNotEmpty()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    CleverTapAPI.createNotificationChannel(
                        applicationContext, Notification.CHANNEL_ID,
                        applicationContext.getString(R.string.channel_name),
                        applicationContext.getString(R.string.channel_description),
                        NotificationManager.IMPORTANCE_MAX, true
                    )
                }
                val ctFcmMessageHandler = CTFcmMessageHandler()
                if (!ctFcmMessageHandler.createNotification(applicationContext, remoteMessage)) {
                    val title = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_TITLE]
                    val message = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_MESSAGE]
                    val url = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_URL]
                    val messageId = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_MESSAGE_ID]
                    val type = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_TYPE]
                    val typeId = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_TYPE_ID]
                    val image = remoteMessage.data[Constants.FIREBASE_NOTIFICATION_FILED_IMAGE]
                    for ((key, value) in remoteMessage.data) {
                        Timber.e("$key/$value")
                    }
//                    if (CheckValidUtil.checkValidString(type) && type.equals(
//                            Constants.GROUP_CHAT_NOTIFICATION_TYPE,
//                            ignoreCase = true
//                        )
//                    ) {
//                        val currentRoomId: String = sharedPreferences.groupChatIdKey()
//                        if (CheckValidUtil.checkValidString(currentRoomId) && CheckValidUtil.checkValidString(typeId) && currentRoomId.equals(
//                                typeId,
//                                ignoreCase = true
//                            )
//                        ) {
//                            return
//                        }
//                    }

                    if(!continueSendNotification(type, typeId)) {
                        return
                    }
                    val pendingIntent: PendingIntent = buildPendingIntentForNotification(messageId?.hashCode() ?: 0, type, typeId, title, url)
                    val myNotificationBuilder: NotificationBuilder = NotificationBuilder(applicationContext)
                        .setColorDrawable(R.color.accent)
                        .setResIdSmallIcon(R.drawable.ic_launcher)
                        .setResIdSmallIconTransparent(R.drawable.ic_notification_transparent)
                        .setTitleNotification(title)
                        .setTextNotification(message)
                        .setNotificationId(messageId?.hashCode() ?: 0)
                        .setPendingIntentWhenClickNotification(pendingIntent)
                    val widthDevice: Int = applicationContext.getDisplayWidth()
                    if (CheckValidUtil.checkValidString(image) && widthDevice != -1) {
                        myNotificationBuilder.setTypeNotification(NotificationBuilder.PICTURE_NOTIFICATION)
                            .setDimensImage(widthDevice, (widthDevice / Constants.HORIZONTAL_IMAGE_RATIO).toInt())
                            .setImageSourceLargeIcon(
                                image?.run {
                                    ImageSource.fromUrl(
                                        applicationContext,
                                        this,
                                        R.drawable.ic_launcher
                                    )
                                }
                            )
                    } else {
                        myNotificationBuilder.setTypeNotification(NotificationBuilder.STANDARD_NOTIFICATION)
                    }
                    val myNotification: Notification = myNotificationBuilder.build()
                    myNotification.process()
                }
            }
        } catch (t: Throwable) {
            Timber.d("***Error parsing FCM message%s", t.message)
        }
    }

    private fun continueSendNotification(type: String?, typeId: String?): Boolean {
        Timber.i("*** check continueSendNotification: $type $typeId")
        if(type.isNullOrEmpty() || typeId.isNullOrEmpty()) {
            // not check
            return true
        }
        return when(type.lowercase()) {
            Constants.GROUP_CHAT_NOTIFICATION_TYPE.lowercase() -> {
                val currentRoomId: String = sharedPreferences.groupChatIdKey()
                !(CheckValidUtil.checkValidString(currentRoomId)
                        && CheckValidUtil.checkValidString(typeId)
                        && currentRoomId.equals(typeId, ignoreCase = true)
                        )
            }
//            Constants.FIREBASE_NOTIFICATION_FILED_TYPE_VOD.lowercase(), // not apply for vod
            Constants.FIREBASE_NOTIFICATION_FILED_TYPE_LIVE_TV.lowercase(),
            Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT_TV.lowercase(),
            Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT.lowercase() -> {
                val currentContentId = sharedPreferences.currentContentId()
                val currentContentType = sharedPreferences.currentContentType()
                if(currentContentType.isNotEmpty() && currentContentType.equals(type, ignoreCase = true)) {
                    // if same type && same id -> not send noti -> return false
                    !(currentContentId.isNotEmpty() && currentContentId.equals(typeId, ignoreCase = true))
                } else {
                    true
                }
            }
            Constants.FIREBASE_NOTIFICATION_FILED_TYPE_PREMIERE.lowercase() -> {
                val currentContentId = sharedPreferences.currentContentId()
                val currentContentType = sharedPreferences.currentContentType()
                if(currentContentType.isNotEmpty() && currentContentType.equals(Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT, ignoreCase = true)) {
                    // because premiere and event use the same fragment
                    // if same type && same id -> not send noti -> return false
                    !(currentContentId.isNotEmpty() && currentContentId.equals(typeId, ignoreCase = true))
                } else {
                    true
                }

            }


            else -> true
        }
    }

    fun buildPendingIntentForNotification(
        messageId: Int,
        type: String?,
        typeId: String?,
        title: String?,
        url: String?
    ): PendingIntent {
        val pendingIntent: PendingIntent
//        val intent = Intent(applicationContext, FireBaseMessagingIntentService::class.java)
//        intent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE, type)
//        intent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID, typeId)
//        intent.putExtra(Constants.FIREBASE_NOTIFICATION_URL, if (CheckValidUtil.checkValidString(url)) url else "")
//        intent.putExtra(Constants.FIREBASE_NOTIFICATION_TITLE, title)


//        pendingIntent = PendingIntent.getService(
//            applicationContext, 0,
//            intent, PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
//        )
        
        val welcomeIntent = Intent(applicationContext, WelcomeActivity::class.java)
        welcomeIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_NEW, true)
        welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE, type)
        welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID, typeId)
        welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_URL, url)
        welcomeIntent.putExtra(Constants.FIREBASE_NOTIFICATION_TITLE, title)


        pendingIntent = PendingIntent.getActivity(
            applicationContext,
            messageId,
            welcomeIntent, PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
//        Timber.tag("tam-noti").d("buildPendingIntentForNotification ${pendingIntent} ${welcomeIntent.extras}")

        return pendingIntent
    }

}