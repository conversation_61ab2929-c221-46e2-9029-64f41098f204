package com.fptplay.mobile.services

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.fptplay.mobile.common.utils.CheckValidUtil
import com.fptplay.mobile.services.callback.OnFireBaseNotificationInForegroundListener
import com.xhbadxx.projects.module.util.common.Constants
import com.xhbadxx.projects.module.util.logger.Logger

class FireBaseMessagingLifecycleObserver(
    private val context: Context,
    private val onFireBaseNotificationInForegroundListener: OnFireBaseNotificationInForegroundListener): DefaultLifecycleObserver {
    private val firebaseNotificationFilter: IntentFilter by lazy { IntentFilter() }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        firebaseNotificationFilter.addAction(Constants.FIREBASE_NOTIFICATION_NEW_ACTION)
        ContextCompat.registerReceiver(context, notificationBroadcastReceiver, firebaseNotificationFilter, ContextCompat.RECEIVER_NOT_EXPORTED)
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        context.unregisterReceiver(notificationBroadcastReceiver)
    }

    //endregion
    //region Interfaces
    private val notificationBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action != null && (intent.action == Constants.FIREBASE_NOTIFICATION_NEW_ACTION)) {
                val type = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE)
                val typeId = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID)
                val url = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_URL)
                val title = intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TITLE)
                Logger.d("trangtest noti ==== title $title")
                if (CheckValidUtil.checkValidString(type) && CheckValidUtil.checkValidString(typeId)) {
                    onFireBaseNotificationInForegroundListener.onFireBaseNotificationInForegroundListener(type, typeId, title, url)
                }
            }
        }
    }
    //endregion
}