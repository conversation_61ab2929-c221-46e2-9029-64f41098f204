package com.fptplay.mobile.services

import android.app.IntentService
import android.content.Intent
import com.fptplay.mobile.WelcomeActivity
import com.fptplay.mobile.common.utils.Constants

class NotificationIntentService : IntentService("AlarmEventIntentService") {
    override fun onHandleIntent(intent: Intent?) {
        if (intent != null) {
            val data = intent.getBundleExtra(Constants.NOTIFICATION_FIRE_STORE_BUNDLE_KEY)
            val welcomeIntent = Intent(applicationContext, WelcomeActivity::class.java)
            welcomeIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            welcomeIntent.putExtra(Constants.NOTIFICATION_FIRE_STORE_BUNDLE_KEY, data)
            welcomeIntent.putExtra(Constants.NOTIFICATION_FIRE_STORE_HAVE_DATA_KEY, true)
            applicationContext.startActivity(welcomeIntent)
        }
    }
}