package com.fptplay.mobile.vod.adapter

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.fptplay.mobile.R
import com.fptplay.mobile.vod.*

class VodTablayoutActorAdapter(context: Context, des: String, fm: FragmentManager, lifeCycle: Lifecycle) : FragmentStateAdapter(fm, lifeCycle) {

    val tabName = arrayListOf<String>()
        private val fragments = mutableListOf<Fragment>().apply {
            if(des.isBlank()){
                add(VodFilmActorFragment())

                tabName.add(context.getString(R.string.vod_actor_film))
            }else {
                add(VodInfoActorFragment.newInstance(description = des))
                add(VodFilmActorFragment())
                tabName.add(context.getString(R.string.vod_actor_info))
                tabName.add(context.getString(R.string.vod_actor_film))
            }
        }

        override fun getItemCount() = fragments.size

        override fun createFragment(position: Int) = fragments[position]

    }