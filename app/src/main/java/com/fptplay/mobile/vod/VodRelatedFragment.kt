package com.fptplay.mobile.vod

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.viewutils.GridItemDecoration
import com.fptplay.mobile.databinding.VodRelatedFragmentBinding
import com.fptplay.mobile.vod.adapter.VodRelatedAdapter
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.IEventListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VodRelatedFragment : BaseFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {
    override val viewModel: VodDetailViewModel by activityViewModels()

    private var _binding: VodRelatedFragmentBinding? = null
    private val binding get() = _binding!!
    private val mAdapter by lazy { VodRelatedAdapter() }

    private val gridDecoration by lazy { GridItemDecoration(spanCount = if (context.isTablet()) 3 else 2, spacing = resources.getDimensionPixelSize(R.dimen.app_related_margin), includeEdge = false) }
    private val spacingDecoration by lazy {
        object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                outRect.bottom = resources.getDimensionPixelSize(R.dimen.app_related_margin)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = VodRelatedFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindComponent() {
        binding.rvRelated.apply {
            adapter = mAdapter
            layoutManager = GridLayoutManager(binding.root.context, if (context.isTablet()) 3 else 2, RecyclerView.VERTICAL, false)
            addItemDecoration(gridDecoration)
        }
        if(mAdapter.size() == 0 && viewModel.getDataDetail()?.blockRelated?.relateds?.isNotEmpty() == true) {
            mAdapter.bind(viewModel.getDataDetail()?.blockRelated?.relateds ?: emptyList())
        }
    }

    override fun bindEvent() {
        mAdapter.eventListener = object : IEventListener<Details.RelatedVod> {
            override fun onClickedItem(position: Int, data: Details.RelatedVod) {
                MainApplication.INSTANCE.pairingConnectionHelper.showQuestionDialog(navHostFragment = activity?.findNavHostFragment(),
                    data = data,
                    onCancel = {},
                    onNavigate = {
                        TrackingUtil.idRelated = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id ?: "" else viewModel.getId()
                        TrackingUtil.itemIndex = position.toString()
                        viewModel.triggerPlayVod(id = data.id, Utils.VOD_PLAY_TYPE_RELATED)
                    })
            }
        }
    }

    override fun observeState() {
        super.observeState()
        viewModel.playVod.observe(viewLifecycleOwner) {
            it?.let {
                binding.root.scrollTo(0,0)
            }
        }
        if (context.isTablet()) {
            viewModel.isFullScreen.observe(this) {
                it?.let {
                    if (_binding != null) {
                        if (it.second) {
                            binding.rvRelated.apply {
                                for (index in 0 until itemDecorationCount) {
                                    removeItemDecorationAt(index)
                                }
                                layoutManager = LinearLayoutManager(binding.root.context, RecyclerView.VERTICAL, false)
                                addItemDecoration(spacingDecoration)
                            }
                            if (viewModel.getDataDetail()?.blockRelated?.relateds?.isNotEmpty() == true) {
                                mAdapter.bind(viewModel.getDataDetail()?.blockRelated?.relateds ?: emptyList())
                            }
                        } else {
                            binding.rvRelated.apply {
                                for (index in 0 until itemDecorationCount) {
                                    removeItemDecorationAt(index)
                                }
                                layoutManager = GridLayoutManager(binding.root.context, if (context.isTablet()) 3 else 2, RecyclerView.VERTICAL, false)
                                addItemDecoration(gridDecoration)
                            }
                            if(viewModel.getDataDetail()?.blockRelated?.relateds?.isNotEmpty() == true) {
                                mAdapter.bind(viewModel.getDataDetail()?.blockRelated?.relateds ?: emptyList())
                            }
                        }
                    }
                }
            }
        }
    }

    override fun VodDetailViewModel.VodDetailState.toUI() {
        when (this) {
            is VodDetailViewModel.VodDetailState.ResultDetailDataChange -> {
                mAdapter.bind(viewModel.getDataDetail()?.blockRelated?.relateds ?: emptyList())
            }
            else -> {}
        }
    }
}