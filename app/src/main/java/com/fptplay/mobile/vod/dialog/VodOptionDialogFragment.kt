package com.fptplay.mobile.vod.dialog

import android.app.Dialog
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.RenderEffect
import android.graphics.Shader
import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.DialogOptionBinding
import com.fptplay.mobile.features.download.model.DownloadPlayerData
import com.fptplay.mobile.features.download.model.DownloadTaskState
import com.fptplay.mobile.player.dialog.OptionAdapter
import com.fptplay.mobile.player.dialog.data.BitratePlayerData
import com.fptplay.mobile.player.dialog.data.ExpandPlayerData
import com.fptplay.mobile.player.dialog.data.SubtitlePlayerData
import com.fptplay.mobile.vod.VodDetailViewModel
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.util.common.IEventListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VodOptionDialogFragment :
    BaseFullDialogFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {

    override val hasEdgeToEdge by lazy { if (context.isTablet() || safeArgs.isDownloadType) safeArgs.hasEdgeToEdge else MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE }

    override val viewModel: VodDetailViewModel by activityViewModels()

    private var _binding: DialogOptionBinding? = null
    private val binding get() = _binding!!
    private val safeArgs : VodOptionDialogFragmentArgs by navArgs()
    private val itemAdapter: OptionAdapter by lazy { OptionAdapter() }
    override val handleBackPressed: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogDark)
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = LayoutInflater.from(MainApplication.INSTANCE.applicationContext).inflate(R.layout.dialog_option, container, false)
        _binding = DialogOptionBinding.bind(view)
        return binding.root
    }

    override fun backHandler() {
        setFragmentResultEmptyData()
        super.backHandler()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        refresh()
    }

    private fun refresh() {
        findNavController().currentDestination?.id?.let {
            findNavController().popBackStack(it, inclusive = true)
            findNavController().navigate(it)
        }
    }

    override fun bindData() {
        when (viewModel.getPlayerOptionsType()) {
            Utils.OPTION_DIALOG_BITRATE -> {
                binding.tvTitle.text = getString(R.string.player_resolution)
                val data = viewModel.getCurrentPlayerBitrates() ?: listOf()
                data.reversed().run {
                    itemAdapter.bind(this.map { item ->
                        BitratePlayerData(
                            id = item.id,
                            title = item.name,
                            resId = null
                        )
                    })
                }
            }
            Utils.OPTION_DIALOG_SUBTITLE -> {
                binding.tvTitle.text = getString(R.string.player_subtitle)
                val data = viewModel.getTracks() ?: listOf()
                data.run {
                    itemAdapter.bind(this.map { item ->
                        SubtitlePlayerData(
                            id = item.id,
                            name = item.name,
                            iconVip = item.iconVip,
                            trackGroupIndex = item.trackGroupIndex,
                            trackIndex = item.trackIndex,
                            isSelected = item.isSelected
                        )
                    })
                }
            }
            Utils.OPTION_DIALOG_EXPAND -> {
                binding.tvTitle.text = getString(R.string.player_expand)
                val data = ExpandPlayerData.getDefaultData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DOWNLOADED -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadedData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DOWNLOADED_BUT_EXPIRED -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadedButExpiredData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DOWNLOADING -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadingData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DOWNLOADING_FOR_VOD -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadingForVodData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DOWNLOAD_PAUSE -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadPauseData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DOWNLOAD_PAUSE_FOR_VOD -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadPauseForVodData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_DELETE_FROM_DOWNLOAD -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDeleteFromDownloadData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_ERROR -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadRedownloadData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_ERROR_FOR_VOD -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadRedownloadForVodData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_EXPIRED -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadExtendData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_EXPIRED_FOR_VOD -> {
                binding.tvTitle.text = getString(R.string.player_more)
                val data = DownloadPlayerData.getDownloadExtendForVodData()
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_SET_STORAGE_FOR_DOWNLOAD -> {
                binding.tvTitle.text = getString(R.string.download_storage)
                val data = DownloadPlayerData.getStorageForDownloadData(requireContext(),safeArgs.availableInStorage,safeArgs.availableExStorage)
                itemAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_MORE -> {
                binding.tvTitle.text = getString(R.string.player_more)
            }
            else -> {

            }
        }
     }

    override fun bindComponent() {
        if(safeArgs.dataOption.isNotBlank()){
            viewModel.savePlayerOptionsType(safeArgs.dataOption)
        }
        binding.rvOptions.apply {
            adapter = itemAdapter
            layoutManager =
                LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
        }
    }

    override fun bindEvent() {
        itemAdapter.eventListener = object : IEventListener<BaseObject> {
            override fun onClickedItem(position: Int, data: BaseObject) {
                var isShowList = false
                when (data) {
                    is BitratePlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_BITRATE_KEY, bundleOf(
                                Utils.OPTION_DIALOG_BITRATE_ID_KEY to data.id,
                                Utils.OPTION_DIALOG_BITRATE_POSITION_KEY to position,
                            )
                        )
                    }
                    is SubtitlePlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_SUBTITLE_KEY,
                            bundleOf(Utils.OPTION_DIALOG_SUBTITLE_ID_KEY to data.id)
                        )
                    }
                    is ExpandPlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_EXPAND_KEY,
                            bundleOf(Utils.OPTION_DIALOG_EXPAND_ID_KEY to data.resizeModeId)
                        )
                    }
                    is DownloadPlayerData -> {
                        findNavController().popBackStack()
                        isShowList = true
                        setFragmentResult(
                            Utils.OPTION_DIALOG_DOWNLOAD_KEY,
                            bundleOf(Utils.OPTION_DIALOG_DOWNLOAD_ID_KEY to data.Id)
                        )
                    }
                }
                if(!isShowList) {
                    findNavController().popBackStack()
                }
            }
        }

        binding.ibClose.setOnClickListener {
            setFragmentResultEmptyData()
            findNavController().popBackStack()
        }
    }


    override fun VodDetailViewModel.VodDetailState.toUI() {

    }

    private fun setFragmentResultEmptyData() {
        when (viewModel.getPlayerOptionsType()) {
            Utils.OPTION_DIALOG_BITRATE -> setFragmentResult(Utils.OPTION_DIALOG_BITRATE_KEY, bundleOf())
            Utils.OPTION_DIALOG_SUBTITLE -> setFragmentResult(Utils.OPTION_DIALOG_SUBTITLE_KEY, bundleOf())
            Utils.OPTION_DIALOG_EXPAND -> setFragmentResult(Utils.OPTION_DIALOG_EXPAND_KEY, bundleOf())
            Utils.OPTION_DIALOG_DOWNLOAD_PAUSE,
            Utils.OPTION_DIALOG_DOWNLOADED,
            Utils.OPTION_DIALOG_DOWNLOADING,
            Utils.OPTION_DIALOG_ERROR,
            Utils.OPTION_DIALOG_EXPIRED,
            Utils.OPTION_DIALOG_DELETE_FROM_DOWNLOAD,
            Utils.OPTION_DIALOG_SET_STORAGE_FOR_DOWNLOAD -> setFragmentResult(Utils.OPTION_DIALOG_DOWNLOAD_KEY, bundleOf())
            else -> { }
        }
    }
}