package com.fptplay.mobile.vod

import android.animation.LayoutTransition
import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.*
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.fpl.plugin.mqtt.model.ReceivedMessage
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.utils.ImageStorageUtils.localUrl
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.dial.connection.android_tv.model.FAndroidTVChangeChapterResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomChangeChapterResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomExceptionMessageResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomMessageResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomPlayContentResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomStopSessionResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVMetadataUpdated
import com.fptplay.dial.connection.android_tv.model.FAndroidTVPlayContentResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVUpdateProgressResponse
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiver
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiverExternal
import com.fptplay.dial.connection.models.FBoxObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiverExternal
import com.fptplay.dial.connection.models.ObjectReceiver
import com.fptplay.dial.connection.models.transfer_model.*
import com.fptplay.dial.model.DeviceInfo
import com.fptplay.dial.model.FBoxDeviceInfoV2
import com.fptplay.dial.scanner.interfaces.FScannerAwakeListener
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavAirlineDirections
import com.fptplay.mobile.NavDowloadV2Directions
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.*
import com.fptplay.mobile.common.extensions.ActivityExtensions.checkAppInBackground
import com.fptplay.mobile.common.extensions.ActivityExtensions.checkToDismissFragmentDialogInPlayer
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.global.GlobalEventListener
import com.fptplay.mobile.common.log.data.LoadType
import com.fptplay.mobile.common.log.data.LogStreamInfo
import com.fptplay.mobile.common.log.VODPlaybackLogger
import com.fptplay.mobile.common.log.data.LogEnterPlayerScreenSource
import com.fptplay.mobile.common.log.data.LogUserTimeInfo
import com.fptplay.mobile.common.models.NextActionEvent
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertInfoDialog
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Navigation.navigateToRequiredBuyPackage
import com.fptplay.mobile.common.utils.StringUtils.truncateString
import com.fptplay.mobile.databinding.VodPlayerFragmentBinding
import com.fptplay.mobile.features.adjust.AbandonedReason
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.ads.AdsLogoListener
import com.fptplay.mobile.features.ads.AdsTvcListener
import com.fptplay.mobile.features.ads.tracking_ads.AdsTrackingProxy
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.download.fragment.VodDetailOfflineFragmentV2Directions
import com.fptplay.mobile.features.age_retrictions.AgeRestrictionHandler
import com.fptplay.mobile.features.age_retrictions.SituationWarningHandler
import com.fptplay.mobile.features.game_emoji.GameEmojiListener
import com.fptplay.mobile.features.game_emoji.utils.GameEmojiUtils
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.mega.view.OmniProductView
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.mapToPublisher
import com.fptplay.mobile.features.mqtt.model.MqttContentType
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.pairing_control.Utils.isFailure
import com.fptplay.mobile.features.pairing_control.Utils.isMyMessage
import com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning
import com.fptplay.mobile.features.pairing_control.Utils.shouldShowStopSessionToast
import com.fptplay.mobile.features.pairing_control.Utils.isSuccess
import com.fptplay.mobile.features.pairing_control.model.CastingItemState
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerState
import com.fptplay.mobile.features.report.utils.PlayerReportUtils
import com.fptplay.mobile.features.user_realtime_playing.UserRealtimePlayingTracker
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerUtils.getIndexOf
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.config.PlayerConfigBuilder
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.interfaces.IPlayerStateValidation
import com.fptplay.mobile.player.interfaces.IPlayerUIListener
import com.fptplay.mobile.player.retry.PlayerGetStreamRetryHandler
import com.fptplay.mobile.player.retry.PlayerPiPRetryHandler
import com.fptplay.mobile.player.retry.PlayerRetryHandler
import com.fptplay.mobile.player.utils.TrackHelper.findTrackById
import com.fptplay.mobile.player.utils.TrackHelper.findTrackByName
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.fptplay.mobile.vod.VodDetailViewModel.VodDetailIntent.*
import com.fptplay.mobile.vod.VodDetailViewModel.VodDetailState.*
import com.fptplay.mobile.vod.data.BuyPackageGuide
import com.fptplay.mobile.vod.data.BuyPackageGuide.Companion.mapToBuyPackageGuide
import com.fptplay.mobile.vod.data.VodPlayerInfo
import com.fptplay.mobile.vod.data.VodPreviewPlayerInfo
import com.google.android.exoplayer2.video.VideoSize
import com.google.gson.Gson
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.PlayerUtils.getHdrType
import com.tear.modules.player.util.TrackType
import com.tear.modules.player.util.UnValidResponseCode
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.CommonInfor
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.History
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.common.UserProfile
import com.xhbadxx.projects.module.domain.entity.fplay.game.gamevod.GameVOD
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.fromBase64Default
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.fplay.platform.Platform
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.collections.ArrayList

@AndroidEntryPoint
class VodPlayerFragment :
    BaseFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {

    private val TAG = this::class.java.simpleName

    override val handleConfigurationChange = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var platform : Platform

    @Inject
    lateinit var userRealtimePlayingTracker: UserRealtimePlayingTracker

    @Inject
    lateinit var ageRestrictionHandler : AgeRestrictionHandler

    @Inject
    lateinit var situationWarningHandler: SituationWarningHandler

    @Inject
    lateinit var playerRetryHandler: PlayerRetryHandler

    @Inject
    lateinit var playerGetStreamRetryHandler: PlayerGetStreamRetryHandler

    @Inject
    lateinit var playerPiPRetryHandler: PlayerPiPRetryHandler


    override val viewModel: VodDetailViewModel by activityViewModels()
    private val vodAdsViewModel: VodAdsViewModel by activityViewModels()

    private var _binding: VodPlayerFragmentBinding? = null
    private val binding get() = _binding!!

    private val vodPlaybackLogger : VODPlaybackLogger by lazy {
        VODPlaybackLogger(
            sharedPreferences = sharedPreferences,
            trackingProxy = trackingProxy,
            trackingInfo = trackingInfo,
            details = null,
            playlistId = if(viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id ?: "" else "",
            refPlaylistID = if(viewModel.isPlaylistContent()) viewModel.getPlaylist()?.refId ?: "" else "",
        ).apply {
            enterVODPlayback(
                LogEnterPlayerScreenSource(
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    subMenuId = TrackingUtil.blockId,
                    blockId = TrackingUtil.blockId,
                    blockPosition = TrackingUtil.blockIndex,
                )
            )
            setScreen(TrackingUtil.screen)
        }
    }

    private var userHistory: History? = null
    private var userProfile: UserProfile = UserProfile()
    private var details: Details? = null

    private val vodPlayerInfo = VodPlayerInfo(userProfile = userProfile)
    private val vodPreviewPlayerInfo = VodPreviewPlayerInfo()

    private var isPlayTrailer = false
    private var isResetPlayingPosition: Boolean? = null

    // Logic Bookmark
    private var isAutoNextVideo = false
    //

    // Tracking
    private var startTime = 0
    private var handlerTrackingHeartBeat: Handler? = null
    private var runnableTrackingHeartBeat = Runnable {
        sendTrackingHeartBeat()
    }
    private var currentDuration = 0L
    private var totalDuration = 0L

    private var userClickChangeBitrate = false

    // Tracking Ads
    private val trackingAds by lazy { AdsTrackingProxy(trackingProxy, trackingInfo) }
    //

    // Retry when internet available
    private var isPlayerErrorByInternet = false
    //

    // Retry task
    private var countDownTimerRetry: CountDownTimer? = null
    private var playerRetryDialog: AlertInfoDialog? = null
    //

    // Background Player
    private var currentDurationBackgroundPlayerService = -1L
    //

    // Pairing Control
    private val pairingScanner by lazy { MainApplication.INSTANCE.pairingScannerHelper }
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    private var isDetectDuration = false
    private var requestCheckShowIntroCredit = false

    // Tracking Log
    private var isSendLogSeek514  = false
    private var inforMobile = InforMobile() //save infor lại khi start 1 phim -> stop send log đúng infor này
    private var isNeedUpdatePlayingSession = false
    private var timeExecuteActionSeek: Long? = null
    private var typeActionSeek: String? = null

    //MQTT
    private var mqttPublisher : Publisher? = null

    private val playEventListener = object : GlobalEventListener {
        override fun onEventReceive(data: Any?) {
            try {
                binding.player.pause()
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    private val pauseEventListener = object : GlobalEventListener {
        override fun onEventReceive(data: Any?) {
            try {
                binding.player.play()
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = VodPlayerFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initData() {
        userProfile.updateBitrateId(sharedPreferences.bitrateVod())
    }

    override fun bindComponent() {
        binding.player.initPlayer(
            fragmentActivity = activity,
            viewLifecycleOwner = viewLifecycleOwner,
            screenType = PlayerHandler.ScreenType.Vod,
            viewModel = viewModel
        )
        Utils.setStateWhenShowLogin(this.parentFragment, object : OnShowLoginListener{
            override fun onShow() {
                binding.player.pause()
            }

            override fun onHide() {
                binding.player.play()
            }
        })
        resetPlayerLayout()
        GlobalEvent.registerEvent(DeeplinkConstants.DEEPLINK__NAVIGATE__START, playEventListener)
        GlobalEvent.registerEvent(DeeplinkConstants.DEEPLINK__NAVIGATE__STOP, pauseEventListener)

        // Ads
        logoAdsListener = initLogoAdsListener()
    }

    override fun bindEvent() {
        observeData()
        bindEventFragmentResult()
        bindEventInternetListener()
        // Pairing Control
        addPairingControlListener()
        // User realtime playing
        userRealtimePlayingTracker.addRealtimePlayingTrackingCallback(listener = userRealtimePlayingTrackerListener)
        // Age restriction handler
        ageRestrictionHandler.setup(userRealtimePlayingTracker = userRealtimePlayingTracker)
        ageRestrictionHandler.setListener(listener = ageRestrictionHandlerListener)
        // Situation warning handler
        situationWarningHandler.setup(userRealtimePlayingTracker = userRealtimePlayingTracker)
        situationWarningHandler.setListener(listener = situationWarningListener)
        viewLifecycleOwner.lifecycle.addObserver(situationWarningHandler)
        // Player error handler
        setPlayerRetryHandlerListener()
        // Player get stream retry
        setPlayerGetStreamRetryListener()

        binding.player.apply {
            // Player UI Events
            setPlayerUIListener(object : IPlayerUIListener {
                override fun onNext(position: Int, curData: Details.Episode, isAuto: Boolean, isSendEventToRemote: Boolean) {
                    viewModel.saveClickOnItemTimeInMs(System.currentTimeMillis())
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {}
                        PlayerView.PlayingType.Cast -> {
//                            if (!pairingConnection.isSessionRunning) {
//                                return
//                            }
                            if (!isSendEventToRemote) {
                                return
                            }
                        }
                    }

                    // Update bookmark local
                    updateBookmarkLocal()
                    <EMAIL> = isAuto
                    <EMAIL>()
                    <EMAIL>()
                    <EMAIL>()
                    userProfile.updateEpisodeIndex(index = position)
                    userProfile.updateEpisodeId(id = curData.id)

                    if (viewModel.isPlaylistContent()) {
                        isResetPlayingPosition = true
                        viewModel.saveId(id = curData.vodId)
                        userProfile.updateEpisodeIndex(index = 0) // Play a film in playlist (only 1 episode -> index always = 0)
                        getDetails(curData.vodId)
                    } else {
                        getStream(episode = curData, delayToPlay = false, fromChangeEpisode = Pair(true, 0)) // next vod
                        requestGameEmojiData(episode = curData, episodeIndex = userProfile.getEpisodeIndex(), details = details)
                    }

                    //MQTT
                    publishEndToTopic()

                    sendTrackingNextPrev(logId = "55", event = if(isPlayTrailer) "NextTrailer" else "NextMovie")
                }

                override fun onPrevious(position: Int, curData: Details.Episode, isAuto: Boolean) {
//                    if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast && !pairingConnection.isSessionRunning) {
//                        return
//                    }

                    viewModel.saveClickOnItemTimeInMs(System.currentTimeMillis())
                    // Update bookmark local
                    updateBookmarkLocal()
                    <EMAIL> = isAuto
                    <EMAIL>()
                    <EMAIL>()
                    <EMAIL>()
                    userProfile.updateEpisodeIndex(index = position)
                    userProfile.updateEpisodeId(id = curData.id)
                    if (viewModel.isPlaylistContent()) {
                        isResetPlayingPosition = true

                        viewModel.saveId(id = curData.vodId)
                        userProfile.updateEpisodeIndex(index = 0) // Play a film in playlist (only 1 episode -> index always = 0)
                        getDetails(curData.vodId)
                    } else {
                        getStream(episode = curData, delayToPlay = false, fromChangeEpisode = Pair(true, 0)) // previous vod
                        requestGameEmojiData(episode = curData, episodeIndex = userProfile.getEpisodeIndex(), details = details)
                    }

                    //MQTT
                    publishEndToTopic()

                    sendTrackingNextPrev(logId = "56", event = if(isPlayTrailer) "PreviousTrailer" else "PreviousMovie")
                }

                override fun onNextOffline(position: Int, curData: VideoTaskItem, isAuto: Boolean) {
                    <EMAIL> = isAuto
                    viewModel.saveId(id = curData.chapterId)
                    getDetails(curData.chapterId, isOffline = true)
                }

                override fun onPreviousOffline(
                    position: Int,
                    curData: VideoTaskItem,
                    isAuto: Boolean
                ) {
                    // TODO: Implement
                }

                override fun onPlayToggle() {
                    // Tracking
                    if (binding.player.isPlaying() == true) {
                        sendTrackingResume()
                    } else {
                        sendTrackingPause()
                    }

                    // Pairing control
                    if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast)
                    {
                        if (!pairingConnection.isSessionRunning) {
                            sendPlayContentMessage()
                        }
                    }
                    //
                }

                override fun onMulticam() {}
                override fun onCast() {

                    // Pairing control
                    if (sharedPreferences.userLogin()) {
                        if (!pairingConnection.isConnected) { // Navigate to search devices
                            navigateToPairingControl(type = 0)
                        } else {
                            navigateToPairingControl(type = 3)
                        }
                    } else {
                        parentFragment?.parentFragment?.navigateToLoginWithParams(navigationId = R.id.nav_pairing_control_host)
                    }
                    //

                }
                override fun onShare() {
                    // Send tracking
                    sendTracking(logId = "516", event = "Share")
                }

                override fun onExpand(curResolutionId: String) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.pause(force = true)
                            viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_EXPAND)
                            viewModel.savePlayerOptionsCurItem(idCurItem = curResolutionId)
                            parentFragment?.findNavController()
                                ?.navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToPlayerOptionDialogFragment())
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }
                    }
                }
                override fun onMore() {}
                override fun onLiveChatClick() {}
                override fun onSportInteractiveClick() {}
                override fun onFullScreen(
                    isFullscreen: Boolean,
                    isLandscapeMode: Boolean
                ) { // orientation change
                    if (context.isTablet()) {
                        viewModel.triggerFullScreen(binding.player.isFullscreen(), isLandscapeMode)
                        if (isLandscapeMode) {
                            if (isFullscreen) {
                                view?.run {
                                    (this as? ViewGroup)?.layoutTransition?.enableTransitionType(
                                        LayoutTransition.CHANGING
                                    )
                                    val lp = ConstraintLayout.LayoutParams(
                                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                                        ConstraintLayout.LayoutParams.MATCH_PARENT
                                    )
                                    layoutParams = lp
                                }
                            } else {
                                tabletPlayerLayout()
                            }
                        } else {
                            if (isFullscreen) {
                                view?.run {
                                    (this as? ViewGroup)?.layoutTransition?.enableTransitionType(
                                        LayoutTransition.CHANGING
                                    )
                                    val lp = ConstraintLayout.LayoutParams(
                                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                                        ConstraintLayout.LayoutParams.MATCH_PARENT
                                    )
                                    layoutParams = lp
                                }
                            } else {
                                resetPlayerLayout()
                            }
                        }
                    } else {
                        viewModel.triggerFullScreen(binding.player.isFullscreen(), isLandscapeMode)
                        if (binding.player.isFullscreen()) {
                            view?.run {
                                val lp = ConstraintLayout.LayoutParams(
                                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                                    ConstraintLayout.LayoutParams.MATCH_PARENT
                                )
                                layoutParams = lp
                            }
                        } else {
                            resetPlayerLayout()
                        }
                    }
                }

                override fun onSetting(bitrates: List<PlayerControlView.Data.Bitrate>?) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            if (!bitrates.isNullOrEmpty()) {
                                binding.player.pause(force = true)
                                viewModel.saveCurrentPlayerBitrates(bitrates)
                                viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_BITRATE)
                                val bitrateIndex = binding.player.playerData.bitrateIndex ?: 0
                                viewModel.savePlayerOptionsCurItem(idCurItem = bitrates[if (bitrateIndex < 0) 0 else bitrateIndex].id)
                                parentFragment?.findNavController()?.navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToPlayerOptionDialogFragment())
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }
                    }
                }

                override fun onAudioAndSubtitle(tracks: List<PlayerControlView.Data.Track>?) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.pause(force = true)
                        }
                        else -> {}
                    }
                    viewModel.saveTracks(tracks)
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_SUBTITLE)
                    if(binding.player.isPlayOffline()) {
                        if (isAirlineLayout()) {
                            parentFragment?.findNavController()?.navigateSafe(NavAirlineDirections.actionGlobalToPlayerOptionDialogFragment())
                        } else {
                            parentFragment?.findNavController()?.navigateSafe(NavDowloadV2Directions.actionGlobalToPlayerOptionDialogFragment())
                        }
                    } else {
                        parentFragment?.findNavController()
                            ?.navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToPlayerOptionDialogFragment())
                    }
                }

                override fun onPlayerSpeed() {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.pause(force = true)
                            viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_SPEED)
//                            viewModel.savePlayerOptionsCurItem(idCurItem = binding.player.playerData.speed ?: "1.0")
                            if(binding.player.isPlayOffline()) {
                                if (isAirlineLayout()) {
                                    parentFragment?.findNavController()?.navigateSafe(NavAirlineDirections.actionGlobalToPlayerOptionDialogFragment())
                                } else {
                                    parentFragment?.findNavController()?.navigateSafe(NavDowloadV2Directions.actionGlobalToPlayerOptionDialogFragment())
                                }
                            } else {
                                parentFragment?.findNavController()
                                    ?.navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToPlayerOptionDialogFragment())
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }
                    }
                }

                override fun onWatchCredit(isSendEventToRemote: Boolean) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {}
                        PlayerView.PlayingType.Cast -> {
                            if (isSendEventToRemote) {
                                pairingConnection.sendEventActionEvent(type = ActionEventType.HIDE_CREDIT)
                            }
                        }
                    }
                }

                override fun onRecommendClose() {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {}
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.sendEventActionEvent(type = ActionEventType.HIDE_CREDIT)
                        }
                    }
                }

                override fun onRecommendWatchNow(related: Details.RelatedVod?) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            related?.run {
                                <EMAIL>()
                                <EMAIL>()
                                <EMAIL> = true
                                nextMovieNavigation(this)
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            related?.run {
                                <EMAIL>()
                                <EMAIL>()
                                <EMAIL> = true
                                nextMovieNavigation(this)
                            }
                        }
                    }
                }

                override fun onRecommendPlayTrailer(related: Details.RelatedVod?) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            related?.run {
                                <EMAIL>()
                                <EMAIL>()
                                <EMAIL> = true
                                viewModel.triggerPlayVod(id = this.id, type = Utils.VOD_PLAY_TYPE_TRAILER)
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            related?.run {
                                <EMAIL>()
                                <EMAIL>()
                                <EMAIL> = true
                                viewModel.triggerPlayVod(id = this.id, type = Utils.VOD_PLAY_TYPE_TRAILER)
                            }
                        }
                    }
                }

                override fun onEpisodeChangedFromBackground(pos: Int, currentDuration: Long) {
                    if (vodPlayerInfo.isPlayingTrailer) return

                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            viewModel.getDataDetail()?.blockEpisode?.episodes?.let {
                                if (pos >= 0 && pos < it.size
                                    && PlayerUtils.checkRulePlayBAAndBAForPiP(isApiEnableBackgroundAudio = details?.blockContent?.bgAudio == "1", sharedPreferences = sharedPreferences)
                                    && currentDuration != -1L
                                ) {
                                    currentDurationBackgroundPlayerService = currentDuration

                                    // If play item of playlist, when change item, must call get detail. Because, this is a playlist logic.
                                    if (it[pos].isItemOfPlaylist) {
                                        viewModel.saveId(id = it[pos].vodId)
                                        userProfile.updateEpisodeIndex(index = 0)
                                        viewModel.dispatchIntent(GetDetail(id = it[pos].vodId, isPlayerCalled = false))
                                    }

                                    viewModel.saveCurrentEpisode(episode = it[pos])
                                    viewModel.dispatchIntent(OnPlayerChanged(episode = it[pos]))
                                    viewModel.triggerPlayerChanged(it[pos])
                                    vodPlaybackLogger.updateNewEpisodeItem(it[pos])
                                }
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            // TODO: Check product team

                        }
                    }

                }

                override fun onEpisodeChangeInBackground(pos: Int) {
                    if (vodPlayerInfo.isPlayingTrailer) return

                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            viewModel.getDataDetail()?.blockEpisode?.episodes?.let {
                                if (pos >= 0 && pos < it.size
                                    && (PlayerUtils.checkRulePlayBAAndBAForPiP(isApiEnableBackgroundAudio = details?.blockContent?.bgAudio == "1", sharedPreferences = sharedPreferences)
                                            || PlayerUtils.checkRulePlayPiP(sharedPreferences = sharedPreferences))
                                ) {
                                    // If play item of playlist, when change item, must call get detail. Because, this is a playlist logic.
                                    if (it[pos].isItemOfPlaylist) {
                                        viewModel.saveId(id = it[pos].vodId)
                                        userProfile.updateEpisodeIndex(index = 0)
                                    }
                                    viewModel.saveCurrentEpisode(episode = it[pos])
                                    notifyPlayerChange(episode = it[pos])
                                    vodPlaybackLogger?.updateNewEpisodeItem(it[pos])
                                }
                            }
                        }
                        PlayerView.PlayingType.Cast -> {}
                    }
                }

                override fun onVideoSizeChanged(videoSize: VideoSize) {
                    sendTrackingChangeResolution()
                    userClickChangeBitrate = false
                }

                override fun onTracksInfoChanged() {
                    // Logic save tracks history
                    updateLogicSaveTrack()
                    //
                }

                override fun onActionSeek(duration: Long) {
                    Logger.d("$TAG onActionSeek")
                    if(!isSendLogSeek514){
                        // case : first time load bookmarks or play new episode
                        isSendLogSeek514 = true
                    }
                    else {
                        timeExecuteActionSeek = System.currentTimeMillis()
                        typeActionSeek = "Seek"
                    }
                }
                override fun onSeekNext(duration: Long) {
                    timeExecuteActionSeek = System.currentTimeMillis()
                    typeActionSeek = "SkipForward"
                }

                override fun onSeekPrevious(duration: Long) {
                    timeExecuteActionSeek = System.currentTimeMillis()
                    typeActionSeek = "SkipBack"
                }

                override fun onReport() {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            if (sharedPreferences.userLogin()) {
                                navigateToReportPlayerVod()
                            } else {
                                // parentFragment?.parentFragment?.navigateToLoginWithParams(navigationId = R.id.action_vod_detail_fragment_to_player_report_dialog_fragment, isDirect = false)
                                parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = false)
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }

                    }

                }

                override fun onAdsShow() {
                    super.onAdsShow()
                    checkToDismissFragmentDialogInPlayer()
                }

                override fun onShowBuyPackageGuide(buyPackageGuide: BuyPackageGuide) {
                    super.onShowBuyPackageGuide(buyPackageGuide)
                    viewModel.dispatchIntent(TriggerShowBuyPackageGuide(buyPackageGuide))
                }

                override fun onPreviewPlayCompleted() {
                    viewModel.dispatchIntent(TriggerPreviewPlayCompleted)
                }

                override fun onPictureInPictureModeChanged(isInPictureInPictureMode: Boolean) {
                    if (!isInPictureInPictureMode) {
                        // Handle Player Retry
                        val isPlayerRetryProcessing = playerPiPRetryHandler.isProcessing()
                        if (isPlayerRetryProcessing && _binding?.player?.isPlaying() != true) {
                            when (val processData = playerPiPRetryHandler.getProcessData()) {
                                is PlayerPiPRetryHandler.PlayerRetryData -> {
                                    playerRetryHandlerListener.onShowPlayerError(
                                        shouldCountDown = processData.shouldCountDown,
                                        countdownTimeMs = playerPiPRetryHandler.getCurrentTickMs(),
                                        code = processData.code,
                                        responseCode = processData.responseCode
                                    )
                                }
                                is PlayerPiPRetryHandler.PlayerGetStreamRetryData -> {
                                    playerGetStreamRetryListener.onShowGetStreamError(
                                        shouldCountDown = processData.shouldCountDown,
                                        countdownTimeMs = playerPiPRetryHandler.getCurrentTickMs(),
                                        errorMessage = processData.errorMessage,
                                    )
                                }
                            }
                        }
                        playerPiPRetryHandler.stopRetryFlow()

                        // Bookmark
                        updateBookMark()
                    }
                    // Hide popup
                    if (isInPictureInPictureMode) {
                        checkToDismissFragmentDialogInPlayer()
                    }
                }

                override fun onClickBuyPackage() {
                    viewModel.dispatchIntent(TriggerBuyPackage)
                }
            })
            // Player Events
            setPlayerEventsListener(object : IPlayer.IPlayerCallback {
                var startBufferTime = 0L
                override fun onBandwidth(message: String) {
                    Logger.d("$TAG onBandwidth")
                    //
                    userRealtimePlayingTracker.playerEvents.onBandwidth(message = message)
                    playerRetryHandler.playerEvents.onBandwidth(message = message)
                }

                override fun startBuffering() {
                    super.startBuffering()
                    if (_binding != null) {
                        vodPlaybackLogger.logStartBuffering(
                            logStreamInfo = getStreamInfoForLogger(),
                            userTimeInfo = getUserTimeInfoForLogger(),
                            appSource = details?.blockContent?.appId ?: ""
                        )
                        startBufferTime = System.currentTimeMillis()
                    }
                }

                override fun endBuffering() {
                    super.endBuffering()
                    if (_binding != null) {
                        vodPlaybackLogger.logEndBuffering(
                            logStreamInfo = getStreamInfoForLogger(),
                            bufferLength = (System.currentTimeMillis() - startBufferTime).toString(),
                            userTimeInfo = getUserTimeInfoForLogger(),
                            appSource = details?.blockContent?.appId ?: ""
                        )
                    }
                }

                override fun onBuffering() {
                    Logger.d("$TAG onBuffering")
                    //
                    userRealtimePlayingTracker.playerEvents.onBuffering()
                    playerRetryHandler.playerEvents.onBuffering()
                }

                override fun onEnd() {
                    Logger.d("$TAG onEnd")
                    //
                    userRealtimePlayingTracker.playerEvents.onEnd()
                    playerRetryHandler.playerEvents.onEnd()
                }

                override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
                    Logger.d("$TAG onError")
                    if (_binding != null) {
                        isPlayerErrorByInternet = code == 2001
                        <EMAIL>(resetInstreamAd = false)
                        <EMAIL>()

                        // Update bookmark
                        updateBookMark()
                    }

                    if(code in 6000..6006) {
                        vodPlaybackLogger.logDrmKeyLoadedFailed(
                            drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                            streamInfo = getStreamInfoForLogger()
                        )
                    }
                    //
                    userRealtimePlayingTracker.playerEvents.onError(code, name, detail, error403, responseCode)
                    playerRetryHandler.playerEvents.onError(code, name, detail, error403, responseCode)
                }

                override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
                    Logger.d("$TAG onError")
                    if (_binding != null) {
                        <EMAIL>(resetInstreamAd = false)
                        <EMAIL>()

                        // Update bookmark
                        updateBookMark()
                    }
                    //
                    userRealtimePlayingTracker.playerEvents.onErrorBehindInLive(code, name, detail)
                    playerRetryHandler.playerEvents.onErrorBehindInLive(code, name, detail)
                }

                override fun onErrorCodec(
                    code: Int,
                    name: String,
                    detail: String,
                    responseCode: Int,
                    isDrm: Boolean,
                    codec: IPlayer.CodecType
                ) {
                    Logger.d("$TAG onErrorCodec")
                    userRealtimePlayingTracker.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                    playerRetryHandler.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                    updateBookMark()
                }

                override fun onError6006(code: Int, name: String, detail: String) {
                    Logger.d("$TAG onError6006")
                    userRealtimePlayingTracker.playerEvents.onError6006(code = code, name = name, detail = detail)
                    playerRetryHandler.playerEvents.onError6006(code = code, name = name, detail = detail)

                    vodPlaybackLogger.logDrmKeyLoadedFailed(
                        drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                        streamInfo = getStreamInfoForLogger()
                    )
                }

                override fun onError6006WhenPreview(code: Int, name: String, detail: String, responseCode: Int) {
                    Logger.d("$TAG onError6006WhenPreview")
                    userRealtimePlayingTracker.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                    playerRetryHandler.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)

                    vodPlaybackLogger.logDrmKeyLoadedFailed(
                        drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                        streamInfo = getStreamInfoForLogger()
                    )
                }

                override fun onPrepare() {
                    Logger.d("$TAG onPrepare")
                    if (_binding != null) {
                        binding.loading.gone()
                        TrackingUtil.saveStreamProfile("")
                        viewModel.savePrepareSourceTimeInMs(System.currentTimeMillis())
                        if (viewModel.getVipRequired()?.first == false || binding.player.getPlayerConfig().isPlayPreview) {
                            vodPlaybackLogger.logPlayAttempt(getStreamInfoForLogger(), viewModel.getIsStreamRetry() || playerRetryHandler.getIsAutoRetry() || isContentTheSameWithPrevious())
                        }
                        vodPlaybackLogger.updateStreamProfile("")
                        when (PlayerUtils.getPlayingType()) {
                            PlayerView.PlayingType.Local -> {
                                playInstreamAd(currentDuration(), forcePlayAfterTime = false)
                                startLogoAds()
                            }
                            PlayerView.PlayingType.Cast -> {}
                        }
                        //
                        if (_binding?.player?.getPlayerConfig()?.isPlayPreview == true) {
                            TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
                        }
                    }
                    //
                    userRealtimePlayingTracker.playerEvents.onPrepare()
                    //
                    playerRetryHandler.playerEvents.onPrepare()

                    //player speed
                    MainApplication.INSTANCE.getPlayerSpeed().let {
                        binding.player.setPlaybackSpeed(it)
                    }
                }

                override fun onReady() {
                    // Bookmark
                    handlePlayBookmark()
                    // Check flow can play
                    checkPlayerCanPlayWhenReady()
                    //
                    // Logic save tracks history
                    updateLogicSaveTrack()
                    //
                    setupUserRealtimePlayingTracker()
                    //
                    setupAgeRestrictionHandler()
                    //
                    setupSituationWarningHandler()
                    //
                    userRealtimePlayingTracker.playerEvents.onReady()
                    playerRetryHandler.playerEvents.onReady()
                    totalDuration = binding.player.totalDuration()
                    currentDuration = binding.player.currentDuration()

                    if ((viewModel.getVipRequired()?.first == false && _binding != null) || binding.player.getPlayerConfig().isPlayPreview) {
                        sendTrackingFirstFrame()
                    }

                    if(inforMobile.itemId != viewModel.getId())
                        sendTrackingStartMovie()
                    else sendTrackingResume()
                }

                override fun onStart() {
                    //
                    userRealtimePlayingTracker.playerEvents.onStart()
                    playerRetryHandler.playerEvents.onStart()

                    timeExecuteActionSeek?.let { timeStartSeek ->
                        vodPlaybackLogger.logSeekComplete(
                            seekType = typeActionSeek ?: "",
                            seekTimeMs = (System.currentTimeMillis() - timeStartSeek),
                            seekToSec = (binding.player.currentDuration() / 1000),
                            durationSec = getTotalDuration(),
                            status = if (binding.player.getPlayerConfig().isPlayPreview) LogStreamInfo.LogPlaybackStatus.Preview else LogStreamInfo.LogPlaybackStatus.None
                        )

                        timeExecuteActionSeek = null
                        typeActionSeek = null
                    }
                }

                override fun onFetchBitrateSuccess(bitrates: ArrayList<IPlayer.Bitrate>) {
                    Logger.d("$TAG => OnFetchBitrateSuccess: $bitrates")
                    mappingBitrate(playerDataBitrate = bitrates)
                    processChangeBitrate()

                    //
                    userRealtimePlayingTracker.playerEvents.onFetchBitrateSuccess(bitrates)
                    playerRetryHandler.playerEvents.onFetchBitrateSuccess(bitrates)
                }

                override fun onFetchBitrateAll(
                    bitrates: ArrayList<IPlayer.Bitrate>,
                    audioTracks: List<PlayerControlView.Data.Track>?
                ) {
                    val data = bitrates.joinToString(";") { data -> data.dataTracking() }
                    TrackingUtil.saveStreamProfile(data)
                    vodPlaybackLogger.updateStreamProfile(data)
                    Logger.d(data)
                }

                override fun onPause() {
                    //
                    userRealtimePlayingTracker.playerEvents.onPause()
                    playerRetryHandler.playerEvents.onPause()
                }

                override fun onPlay() {
                    //
                    userRealtimePlayingTracker.playerEvents.onPlay()
                    playerRetryHandler.playerEvents.onPlay()
                }

                override fun onResume() {
                    //
                    userRealtimePlayingTracker.playerEvents.onResume()
                    playerRetryHandler.playerEvents.onResume()
                }

                override fun onStop() {
                    //
                    userRealtimePlayingTracker.playerEvents.onStop()
                    playerRetryHandler.playerEvents.onStop()

                    // Check to send log stop movie
                    Logger.d("$TAG onStop -- ${isContentTheSameWithPrevious()}")
                }

                override fun onRelease() {
                    //
                    userRealtimePlayingTracker.playerEvents.onRelease()
                    playerRetryHandler.playerEvents.onRelease()

                    Logger.d("$TAG onRelease -- ${isContentTheSameWithPrevious()}")
                }

                override fun onSeek() {
                    _binding?.let {
                        if (binding.player.getPlayerConfig().isPlayPreview) {
                            if (viewModel.isPauseAfterSeek()) {
                                binding.player.pause(force = true)
                                viewModel.saveIsPauseAfterSeek(false)
                            }
                        }
                    }
                }

                override fun onDrmKeysLoaded() {
                    Logger.d("$TAG onDrmKeysLoaded")
                    vodPlaybackLogger.logDrmKeyLoadedSuccess(
                        loadedTime = System.currentTimeMillis() - viewModel.getPrepareSourceTimeInMs(),
                        drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                        streamInfo = getStreamInfoForLogger(),
                    )
                }
            })
            // Player State Validation
            setPlayerStateValidation(object : IPlayerStateValidation {
                override fun checkPlayerCanPlay(): Boolean {
                    return viewModel.getPlayerCanPlayContent()
                }
            })
        }
    }

    private fun nextMovieNavigation(relatedVod: Details.RelatedVod) {
        TrackingUtil.idRelated = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id ?: "" else viewModel.getId()
        if (relatedVod.relatedType == Details.RelatedVodType.VOD_PLAYLIST) {
            viewModel.dispatchIntent(GetNextPlaylist(playlistId = relatedVod.id))
            viewModel.saveIsPlaylistContent(isPlaylist = true)
        } else if (relatedVod.relatedType == Details.RelatedVodType.VOD){
            viewModel.saveIsPlaylistContent(isPlaylist = false)
            viewModel.triggerPlayVod(id = relatedVod.id, type = Utils.VOD_PLAY_TYPE_NEXT_MOVIE)
        }
    }

    override fun observeState() {
        super.observeState()
        vodAdsViewModel.state.observe(viewLifecycleOwner) { it.toAdsUI() }

    }

    private fun bindEventInternetListener() {
        MainApplication.INSTANCE.networkDetector.observe(this) {
            it?.let { hasInternet ->
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        if (isPlayerErrorByInternet && hasInternet) {
                            tryRetry()
                        }
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
                isPlayerErrorByInternet = false

                if (!hasInternet) {
                    updateBookMark()
                }
            }
        }
    }

    private fun bindEventFragmentResult() {
        parentFragment?.run {
            setFragmentResultListener(Utils.OPTION_DIALOG_BITRATE_KEY) { _, bundle ->
                val bitrateId = bundle.getString(Utils.OPTION_DIALOG_BITRATE_ID_KEY, "")
                val bitratePos = bundle.getInt(Utils.OPTION_DIALOG_BITRATE_POSITION_KEY, 0)
                if (bitrateId.isNullOrEmpty()) {
                    binding.player.play()
                    return@setFragmentResultListener
                }
                userClickChangeBitrate = true

                val oldBitrate = userProfile.getBitrateId()
                userProfile.updateBitrateId(id = bitrateId)
                userProfile.updateBitrateIndex(index = bitratePos)
                updateBookMark()

                //
                sharedPreferences.saveBitrateVod(bitrateId = bitrateId)
                //
                if (!processChangeBitrate()) {
                    stopTvcAds()
                    stopLogoAds()
                    getStream(episode = getEpisodeToPlay(), delayToPlay = false, isGetBookmark = false) // change bitrate
                } else {
                    binding.player.play()
                }

                // Tracking
                sendTrackingChangeVideoQuality(oldBitrate = oldBitrate, curBitrate = bitrateId)
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_SUBTITLE_KEY) { _, bundle ->
                val subtitleId = bundle.getString(Utils.OPTION_DIALOG_SUBTITLE_ID_KEY, "")
                if (subtitleId.isNullOrEmpty()) {
                    binding.player.play()
                    return@setFragmentResultListener
                }
                viewModel.getTracks()?.findTrackById(id = subtitleId, type = TrackType.TEXT.ordinal)?.let {
                    // Save setting
                    sharedPreferences.saveSubVod(it.id)

                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.changeTrack(track = it)
                            binding.player.play()
                        }
                        PlayerView.PlayingType.Cast -> {
                            viewModel.getDataDetail()?.let { detail ->
                                pairingConnection.sendEventChangeTrack(id = detail.blockContent.id, refId = detail.blockContent.refId, selectId = it.id, type = "sub")
                            }
                        }
                    }

                        // Tracking
                        sendTrackingChangeSubtitlesAudio(value = it.name, isChangeAudio = false)
                    }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_AUDIO_TRACK_KEY) { _, bundle ->
                val audioTrackName = bundle.getString(Utils.OPTION_DIALOG_AUDIO_TRACK_ID_KEY, "")
                if (audioTrackName.isNullOrEmpty()) {
                    binding.player.play()
                    return@setFragmentResultListener
                }
                viewModel.getTracks()?.findTrackByName(name = audioTrackName, type = TrackType.AUDIO.ordinal)?.let {
                    // Save setting
                    sharedPreferences.saveAudioVod(it.name)

                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.changeTrack(track = it)
                            binding.player.play()
                        }
                        PlayerView.PlayingType.Cast -> {
                            viewModel.getDataDetail()?.let { detail ->
                                pairingConnection.sendEventChangeTrack(id = detail.blockContent.id, refId = detail.blockContent.refId, selectId = it.id, type = "audio")
                            }
                        }
                    }

                    // Tracking
                    sendTrackingChangeSubtitlesAudio(value = it.name, isChangeAudio = true)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_EXPAND_KEY) { _, bundle ->
                val id = bundle.getInt(Utils.OPTION_DIALOG_EXPAND_ID_KEY, -1)
                val pos = bundle.getInt(Utils.OPTION_DIALOG_EXPAND_POS_KEY, 0)
                binding.player.play()
                if (id != -1) {
                    binding.player.setResizeMode(resizeMode = id, position = pos)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_SPEED_KEY) { _, bundle ->
                val speed = bundle.getFloat(Utils.OPTION_DIALOG_SPEED_ID_KEY, -1f)
                if(speed != -1f) {
                    binding.player.setPlaybackSpeed(speed)
                    MainApplication.INSTANCE.savePlayerSpeed(speed = speed)
                }
                binding.player.play()
                return@setFragmentResultListener

            }



            setFragmentResultListener(Utils.PAIRING_DIALOG_TYPE) { _, bundle ->
                val type = bundle.getInt(Utils.PAIRING_DIALOG_TYPE_KEY, 0)

                when (type) {
                    0 -> {
                        try {
                            pairingConnection.let {
                                it.getSelectDevice()?.run {
                                    when (this) {
                                        is FBoxDeviceInfoV2 -> { // Only logic for BoxC
                                            if (this.response.state == "running") {
                                                it.connect(this)
                                            } else {
                                                pairingScanner.awake(deviceInfo = this, lifecycleScope = lifecycleScope, callback = object : FScannerAwakeListener {
                                                    override fun awakeDeviceCallBack(deviceInfo: DeviceInfo?, isSuccess: Boolean, isRunning: Boolean) {
                                                        if (isRunning) {
                                                            if (deviceInfo is FBoxDeviceInfoV2) {
                                                                it.setSelectDevice(data = FBoxDeviceInfoV2(device = <EMAIL>, response = deviceInfo.response))
                                                                it.getSelectDevice()?.let { selectDevice ->
                                                                    it.connect(selectDevice)
                                                                }
                                                            }
                                                        } else {
                                                            it.showToast(message = binding.root.context.getString(R.string.pairing_control_waiting_connection, pairingConnection.getReceiverName()))
                                                            it.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_error))
                                                        }
                                                    }
                                                }
                                                )
                                            }
                                        }
                                        else -> {
                                            it.connect(this)
                                        }
                                    }

                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    1 -> {}
                    2 -> {}
                    else -> {}
                }
            }
            setFragmentResultListener(Utils.PAIRING_CONTROL_NAVIGATE_TYPE) { _, bundle ->
                val type = bundle.getString(Utils.PAIRING_CONTROL_NAVIGATE_TYPE_KEY, "")
                if (!type.isNullOrBlank()) {
                    try {
                        pairingConnection.getCastingItemMapped()?.let {
                            if (it.id != viewModel.getDataDetail()?.blockContent?.id) {
                                if (ItemType.valueOf(type) == ItemType.VOD) {
                                    viewModel.triggerPlayVod(id = it.id)
                                }
                            }
                        }
                    } catch (_: Exception) { }
                }
            }
            setFragmentResultListener(Utils.OPTION_DIALOG_USER_REPORT_KEY) { _, bundle ->
                val isReported = bundle.getBoolean(Utils.OPTION_DIALOG_USER_REPORT_STATUS, false)
                val message = bundle.getString(Utils.OPTION_DIALOG_USER_REPORT_MESSAGE, "")
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.play()
                        if(isReported) {
                            viewModel.dispatchIntent(TriggerShowMsgUserReport(
                                isReported = isReported, message = message))
                        }
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
            }

            setFragmentResultListener(Utils.DEEPLINK_NOT_SUPPORTED_CONFIRM_EVENT) { _, _ ->
                val navController = (activity as? HomeActivity)?.navHostFragment?.navController
                val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
                navigation?.navigate(NavHomeMainDirections.actionGlobalToHomeMainFragment())

            }
        }
    }

    private fun observeData() {
        viewModel.initPlayer.observe(viewLifecycleOwner) {
            it?.run {
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)
                Timber.d("trangtest isOffline : ${this} || getId ${viewModel.getId()}")
                getDetails(
                    id = viewModel.getId(),
                    fileHash = viewModel.getFileHash(),
                    isOffline = this
                )
            }
        }
        viewModel.initOfflinePlayer.observe(viewLifecycleOwner) {
            it?.run {
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)
                getOfflineDetails(id = viewModel.getId(), fileHash = viewModel.getFileHash())
            }
        }
        viewModel.playVod.observe(viewLifecycleOwner) {
            it?.run {
                stopTvcAds(resetPrerollAd = true)
                stopLogoAds()
                // Game
                stopGameEmoji()
                // Log ping

                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.stop(force = true)
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
                // Update bookmark local
                updateBookmarkLocal()

                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)
                viewModel.saveId(id = it.first)
                when (it.second) {
                    Utils.VOD_PLAY_TYPE_SESSION,
                    Utils.VOD_PLAY_TYPE_RELATED-> {
                        TrackingUtil.screen = TrackingUtil.screenGeneral
                    }
                    Utils.VOD_PLAY_TYPE_NEXT_MOVIE,
                    Utils.VOD_PLAY_TYPE_NEXT_VOD_PLAYLIST -> {
                        TrackingUtil.screen = TrackingUtil.screenRelated
                    }
                }
                TrackingUtil.resetIsRecommend()
                getDetails(id = it.first, isPlayTrailer = it.second == Utils.VOD_PLAY_TYPE_TRAILER)
            }
        }
        viewModel.playEpisode.observe(viewLifecycleOwner) { episode ->
            viewModel.saveClickOnItemTimeInMs(System.currentTimeMillis())
            episode?.run {
//                if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast && !pairingConnection.isSessionRunning) {
//                    return@run
//                }

                stopTvcAds()
                stopLogoAds()
                // Game
                stopGameEmoji()
                // Log ping

                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.stop(force = true)
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
                // Update bookmark local
                updateBookmarkLocal()
                isResetPlayingPosition = false
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)
                findEpisodePosition(episode)?.let { userProfile.updateEpisodeIndex(index = it) }
                userProfile.updateEpisodeId(id = episode.id)
                // Reset bitrate when change video
                userProfile.updateBitrateId(sharedPreferences.bitrateVod())
                userProfile.updateBitrateIndex(0)
                if (viewModel.isPlaylistContent()) {
                    viewModel.saveId(id = this.vodId)
                    userProfile.updateEpisodeIndex(index = 0) // Play a film in playlist (only 1 episode -> index always = 0)
                    getDetails(vodId)
                } else {
                    getStream(episode = episode, delayToPlay = false, isSendLog = true, fromChangeEpisode = Pair(true, 1)) // Click on episode
                    requestGameEmojiData(episode = episode, episodeIndex = userProfile.getEpisodeIndex(), details = details)
                }
            }
        }
        viewModel.playTrailer.observe(viewLifecycleOwner) { episode ->
            viewModel.saveClickOnItemTimeInMs(System.currentTimeMillis())
            episode?.run {
                stopTvcAds()
                stopLogoAds()
                // Game
                stopGameEmoji()
                // TODO: Check case cast

                // Update bookmark local
                updateBookmarkLocal()

                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.stop(force = true)
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)

                // Reset bitrate when change video
                userProfile.updateBitrateId(sharedPreferences.bitrateVod())
                userProfile.updateBitrateIndex(0)
                TrackingUtil.resetIsRecommend()
                getStream( // Trigger play trailer
                    episode = episode,
                    delayToPlay = false,
                    isPlayingTrailer = true,
                    isSendLog = true,
                    fromChangeEpisode = Pair(true, 1)
                )
                requestGameEmojiData(
                    episode = episode,
                    episodeIndex = userProfile.getEpisodeIndex(),
                    details = details
                )
            }
        }
        viewModel.preparePlayer.observe(viewLifecycleOwner) { preparePlayerState ->
            if (details != null) {
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {

                        preparePlayerState?.let {

                            setupPlayerRetryHandler(channelId = it.episode.id, streamId =  it.episode.autoProfile)

                            binding.player.preparePlayerVOD(
                                playerConfig = PlayerConfigBuilder()
                                    .setEnableReportPlayer(details?.blockContent?.enableReport?:false)
                                    .setSupportAudioMode(isAudioMode = it.stream.isAudio, backgroundAudioOverlay = it.stream.audioBackgroundImageUrl)
                                    .build(),
                                userProfile = userProfile,
                                //Todo: Check crash
                                details = details!!,
                                episode = it.episode,
                                data = it.stream,
                                delayToPlay = false,
                                isPlayingTrailer = it.isPlayingTrailer,
                                playlistName = if (it.episode.isItemOfPlaylist) viewModel.getPlaylist()?.title?: "" else "",
                                isResetPlayingPosition = this.isResetPlayingPosition ?: false,
                                onEvents = object : PlayerHandler.OnEvents {
                                    override fun showError(reason: String) {
                                        Logger.d("$TAG showError $reason")
                                        showWarningDialog(message = reason)
                                    }

                                    override fun navigateToRequiredVip(
                                        planId: String,
                                        fromSource: String,
                                        idToPlay: String,
                                        vipBackground: String,
                                        title: String,
                                        description: String,
                                        btnActive: String,
                                        btnSkip: String,
                                        trailerUrl: String,
                                    ) {
                                        navigateToRequiredBuyPackage(
                                            title = title,
                                            message = description,
                                            titlePosition = btnActive,
                                            titleNegative = btnSkip,
                                            packageId = planId,
                                            isDirect = false
                                        )
                                    }

                                    override fun navigateToRequireLogin(message: String, idToPlay: String) {
                                        parentFragment?.parentFragment?.navigateToLoginWithParams(
                                            title = message,
                                            idToPlay = idToPlay,
                                            isDirect = true
                                        )
                                    }

                                    override fun sendTrackingPingPlayCcu(
                                        actionType: CommonInfor.PingStreamActionType,
                                        message: String,
                                        showFingerprint: Boolean,
                                        type: String
                                    ) {
                                    }
                                },
                                onCastSessionEvents = object : PlayerHandler.OnCastSessionEvents {
                                    override fun checkCastSession(): Boolean {
                                        return pairingConnection.isConnected
                                    }
                                },
                                drmKey = it.drmKey?.drmKeyBase64?.fromBase64Default()
                            )

                            this.isResetPlayingPosition = false
                        }
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
            }
        }
        viewModel.prepareOfflinePlayer.observe(viewLifecycleOwner) { data ->
            data?.let {
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)
                binding.player.updatePlayerEpisodeForOffline(it.data.listChapters.sortedBy { it.chapterIdx })
                val collection = it.data
                val currentItem = collection.listChapters.first { item ->
                    item.chapterId == it.currentId
                }
                saveUserHistoryForOffline(currentItem)
                binding.player.preparePlayerOfflineVOD(
                    playerConfig = PlayerConfigBuilder()
                        .setEnableReportPlayer(false)
                        .setEnableSharePlayer(false)
                        .setSupportAudioMode(isAudioMode = currentItem.isAudioOnly, backgroundAudioOverlay = currentItem.backgroundUrl?.localUrl() ?: currentItem.backgroundUrl ?: "")
                        .build(),
                    currentItem = currentItem,
                    title = collection.title +
                            if (collection.isSeries)
                                " - " + currentItem.chapterName
                            else
                                ""
                )
                //log stop video
                val lastItem = viewModel.getCurDataOffline()
                lastItem?.let {
                    saveTrackingOffline(logId = "52", event = "StopMovie", data = it, viewModel.getDataMovieOffline())
                    viewModel.saveCurDataOffline(null)
                }
                //log start video
                saveTrackingOffline(logId = TrackingConstants.EVENT_LOG_ID_FIRST_FRAME, event = "StartMovie", data =  currentItem, it.data)
                viewModel.saveCurDataOffline(currentItem)
                viewModel.saveDataMovieOffline(it.data)
                this.isResetPlayingPosition = false
            }
        }
        viewModel.playRequiredVipTrailer.observe(viewLifecycleOwner) { url ->
            url?.run {
                // Reset bitrate when change video
                userProfile.updateBitrateId(sharedPreferences.bitrateVod())
                userProfile.updateBitrateIndex(0)

                viewModel.currentEpisode()?.let {
                    binding.player.onShowThumb(isShow = false)
                    binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Live)
                    binding.player.preparePlayerVipTrailer(
                        playerConfig = PlayerConfigBuilder()
                            .setEnableReportPlayer(details?.blockContent?.enableReport?:false)
                            .build(),
                        url = url,
                        linkToShare = details?.blockContent?.webUrl ?: ""
                    )
                    binding.player.setBuyPackageGuide(null)
                    viewModel.saveBuyPackageGuide(null)
                }

                // Pairing Control
                pairingConnection.resetRemoteData()
                //

            } ?: run {
                Timber.d("********Link null")
            }
        }

        viewModel.playPreview.observe(viewLifecycleOwner) {
            it?.let { previewData ->
                if (viewModel.hasPreviewLink(previewData)) {
                    //
                    val playingSession = System.currentTimeMillis()
                    Logger.d("trangtest === playingSession $playingSession")
                    trackingInfo.updatePlayingSession(playingSession)
                    vodPlaybackLogger.apply {
                        updatePlayingSession(playingSession)
                    }
                    //
                    viewModel.currentEpisode()?.let { curEpisode ->
                        binding.player.onShowThumb(isShow = false)
                        binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Vod)
                        binding.player.preparePlayerPreview(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(details?.blockContent?.enableReport?:false)
                                .setIsPlayPreview(isPlayPreview = curEpisode.isPreview)
                                .build(),
                            vodPreviewData = previewData,
                            userProfile = userProfile,
                            details = details,
                            episode = curEpisode,
                            playlistName = if (curEpisode.isItemOfPlaylist) viewModel.getPlaylist()?.title?: "" else "",
                            onEvents = object : PlayerHandler.OnEvents {
                                override fun showError(reason: String) {
                                    showWarningDialog(message = reason)
                                }

                                override fun navigateToRequiredVip(planId: String, fromSource: String, idToPlay: String, vipBackground: String, title: String, description: String, btnActive: String, btnSkip: String, trailerUrl: String,) {
                                    navigateToRequiredBuyPackage(title = title, message = description, titlePosition = btnActive, titleNegative = btnSkip, packageId = planId, isDirect = false)
                                }

                                override fun navigateToRequireLogin(message: String, idToPlay: String) {
                                    parentFragment?.parentFragment?.navigateToLoginWithParams(title = message, idToPlay = idToPlay, isDirect = true)
                                }

                                override fun sendTrackingPingPlayCcu(actionType: CommonInfor.PingStreamActionType, message: String, showFingerprint: Boolean, type: String) {
                                }
                            }
                        )
                        binding.player.setBuyPackageGuide(null)
                        viewModel.saveBuyPackageGuide(null)
                    }

                    // PairingControl
                    pairingConnection.disconnect()
                    //
                }
            }
        }
        MqttConnectManager.INSTANCE.messageArrived()?.let {
            try {
                it.observe(viewLifecycleOwner) { messages ->
                    if (messages.isNotEmpty()) {
                        val latestMessage = messages[0]
                        val publisher = latestMessage.mapToPublisher()
                        publisher?.let { pub ->
                            if (pub.action == MqttUtil.ACTION_LIMIT_CCU) {
                                MqttConnectManager.INSTANCE.sendLogLimitCCU(pub, latestMessage.topic)
                            }
                        }
                    }
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    private fun saveUserHistoryForOffline(currentItem: VideoTaskItem) {
        userHistory = History(
            movieId = currentItem.movieId,
            episodeId = currentItem.chapterId,
            episodeDuration = currentItem.duration.toString(),
            startPosition = 0
        )
    }

    override fun onResume() {
        super.onResume()
        if (PlayerUtils.getPlayingType() is PlayerView.PlayingType.Cast) {
            syncDetailUIForCast()
        }
    }

    override fun onPause() {
        isSendLogSeek514 = false
        totalDuration = binding.player.totalDuration()
        updateBookMark()
        // Update bookmark local
        updateBookmarkLocal()

        //
        checkToDismissFragmentDialogInPlayer()
        //
        super.onPause()
        ///log id pause
    }

    override fun onStop() {
        // Tracking
        if(this.parentFragment?.isRemoving == true) {

            //MQTT
            publishEndToTopic()

            sendTrackingStopMovie()
            if (binding.player.isPlayOffline()) {
                viewModel.getCurDataOffline()?.let {
                    Logger.d("trangtest === sendTrackingHeartBeat offline ")
                    saveTrackingOffline(logId = "52", event = "StopMovie", data = viewModel.getCurDataOffline(), collection = viewModel.getDataMovieOffline())
                }
            }
        }else sendTrackingPause()
        super.onStop()
    }

    override fun onDestroyView() {
        // Tracking
        TrackingUtil.keyword = ""
        TrackingUtil.resetIsRecommend()
        removeTrackingHeartBeat()
        stopCountDownTimerRetry()
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                binding.player.stop(force = true)
            }
            PlayerView.PlayingType.Cast -> {
                binding.player.stopPlayerLocal(isClearRequest = true)
            }
        }
        //
        binding.player.setPlayerEventsListener(listener = null)
        binding.player.setPlayerStateValidation(listener = null)
        //
        removeIntervalUpdateProgress()
        _binding = null
        logoAdsListener = null
        GlobalEvent.unRegisterEvent(DeeplinkConstants.DEEPLINK__NAVIGATE__START, playEventListener)
        GlobalEvent.unRegisterEvent(DeeplinkConstants.DEEPLINK__NAVIGATE__STOP, pauseEventListener)

        // Pairing Control
        removePairingControlListener()
        // User realtime playing
        userRealtimePlayingTracker.removeRealtimePlayingTrackingCallback(listener = userRealtimePlayingTrackerListener)
        // Age restriction handler
        ageRestrictionHandler.destroy()
        ageRestrictionHandler.removeListener()
        //
        situationWarningHandler.destroy()
        situationWarningHandler.removeListener()
        viewLifecycleOwner.lifecycle.removeObserver(situationWarningHandler)
        //
        // Player error handler
        removePlayerRetryHandlerListener()
        // Player get stream error handler
        removePlayerGetStreamRetryListener()
        //
        viewModel.cancelGetStreamJob()
        //
        //
        if (TrackingUtil.getPreviewProcessItem().first == viewModel.getId()
            && TrackingUtil.getPreviewProcessItem().second == viewModel.currentEpisode()?.id
            ) {
            // Don't reset playing session
        } else {
            trackingInfo.updatePlayingSession(0)
            Logger.d("trangtest === playingSession 0")
            vodPlaybackLogger.apply {
                updatePlayingSession(0)
            }
            TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
        }

        //
        super.onDestroyView()

        if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER)) {
            MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(
                Constants.TOOLTIP_SETTING_PLAYER,
                shouldShow = false
            )
        }
    }

    override fun bindData() {

    }

    override fun bindOrientationStateChange(newConfig: Configuration) {
        super.bindOrientationStateChange(newConfig)
        if (_binding != null) {
            binding.player.configurationChanged(newConfig)
        }
    }


    override fun VodDetailViewModel.VodDetailState.toUI() {
        when (this) {
            is Loading -> {
                when (data) {
                    is GetHistoryById,
                    is GetHistoryByIndex -> {
                        userHistory = null
                    }
                    else -> {}
                }
            }
            is ResultDetail -> {
                vodPlaybackLogger.updateNewVodItem(
                    details = details,
                    playlistId = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id ?: "" else "",
                    refPlaylistId = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.refId ?: "" else ""
                    )

                if (viewModel.isPlaylistContent()) {
                    viewModel.getPlaylist()?.videos?.let { data.blockEpisode.episodes = it }
                }
                details = data

                // Clear player request when change vod
                // Case: Change vod when it's not a playlist. Because, when next episode in playlist, must be get detail
                if (!viewModel.isPlaylistContent()) {
                    if (!(this.intent is GetDetail && this.intent.isPlayerCalled)) {
                        binding.player.clearRequest()
                    }
                }
                //

                //
                clearAgeRestriction()
                userRealtimePlayingTracker.clearData()
                //
                situationWarningHandler.stopAll()
                //

                //
                if (this.intent is GetDetail && this.intent.isPlayerCalled) {
                    resetGetStreamRetryStep()
                }
            }
            is ResultStream -> {
                isSendLogSeek514 = false
                Timber.e("$TAG ResultStream $data")
                //
                resetGetStreamRetryStep()
                resetVodPreviewPlayerInfo()

                // save stream retry status
                viewModel.saveIsStreamRetry(isStreamRetry = intent.isRetry)

                // Tracking
                vodPlaybackLogger.updateStreamInfo(stream = data)
                if (isSendLog) {
                    sendTrackingStopMovie()
                    isNeedUpdatePlayingSession = false

                    if (TrackingUtil.getPreviewProcessItem().first == viewModel.getId()
                        && TrackingUtil.getPreviewProcessItem().second == viewModel.currentEpisode()?.id
                        ) {
                        // Don't reset playing session
                    } else {
                        val playingSession = System.currentTimeMillis()
                        Logger.d("trangtest === playingSession $playingSession")
                        trackingInfo.updatePlayingSession(playingSession)
                        vodPlaybackLogger.apply {
                            updatePlayingSession(playingSession)
                        }
                        TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
                    }
                    sendTrackingEnterDetail(event = "EnterDetail")
                }
                //
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
//                        binding.player.onShowThumb(isShow = false)
                    }
                    PlayerView.PlayingType.Cast ->{
                        binding.player.onShowThumb(isShow = true, url = details?.blockContent?.horizontalImage?.ifBlank { details?.blockContent?.verticalImage ?: "" } ?: "")
                    }
                }
                vodPlayerInfo.apply {
                    this.stream = data
                    this.episode = <EMAIL>
                    this.isPlayingTrailer = <EMAIL>
                    this.drmKey = <EMAIL>
                }
                binding.player.apply {
                    setInComingRequest(
                        request = buildVodRequest(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(details?.blockContent?.enableReport?:false)
                                .setSupportAudioMode(isAudioMode = data.isAudio, backgroundAudioOverlay = data.audioBackgroundImageUrl)
                                .build(),
                            userProfile = userProfile,
                            delayToPlay = false,
                            stream = data,
                            episode = <EMAIL>,
                            drmKey = drmKey?.drmKeyBase64?.fromBase64Default()
                        ),
                        isRunningBackgroundAudio = details?.blockContent?.bgAudio == "1",
                        vodDetail = viewModel.getDataDetail(),
                        currentEpisode = viewModel.currentEpisode(),
                        userProfile = userProfile,
                        isPlayingTrailer = isPlayingTrailer,
                        playlistName = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) viewModel.getPlaylist()?.title?: "" else "",
                    )
                    val guide = data.suggestBuyPackageInstreamAds.mapToBuyPackageGuide()
                    setBuyPackageGuide(guide)
                    viewModel.saveBuyPackageGuide(guide)
                }
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
//                        trackingAds.updateContentEpisodeId(this.episode.realEpisodeId)
                        playPrerollAd()

                        //MQTT
                        publishStartToTopic(pingStart = data.pingMqtt)
                    }
                    PlayerView.PlayingType.Cast -> {
                        // Case: Receive signal change channel from remote.

                        // Case: Change local -> send signal to remote
                        binding.player.preparePlayerVODCast(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(false)
                                .setSupportAudioMode(isAudioMode = data.isAudio, backgroundAudioOverlay = data.audioBackgroundImageUrl)
                                .setEnableReportPlayer(details?.blockContent?.enableReport?:false)
                                .build(),
                            userProfile = userProfile,
                            details = details!!,
                            episode = episode,
                            data = data,
                            delayToPlay = false,
                            isPlayingTrailer = isPlayingTrailer,
                            playlistName = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) viewModel.getPlaylist()?.title?: "" else "",
                            isResetPlayingPosition = isResetPlayingPosition ?: false,
                            onEvents = object : PlayerHandler.OnEvents {}
                        )
                        if (isSendEventToRemote) {
                            val contentId = viewModel.getDataDetail()?.blockContent?.id ?: ""
                            if (fromChangeEpisode.first && pairingConnection.isSessionRunning && contentId == pairingConnection.getCastingItem()?.id) {
                                sendNextChapterMessage(indexAction = fromChangeEpisode.second.toString())
                            } else {
                                sendPlayContentMessage()
                            }
                        }
                        //
                    }
                }

            }
            is ResultIntroCreditDataForCast -> {
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {}
                    PlayerView.PlayingType.Cast -> {
                        updateIntroCreditData(episode = episode, stream = data)
                    }
                }
            }
            is ResultHistoryById -> {
                userHistory = data
                vodPlaybackLogger.updateBookmark(data)
            }
            is ResultHistoryByIndex -> {
                userHistory = data
                vodPlaybackLogger.updateBookmark(data)
            }
            is ResultListNextVideo -> {
                if (_binding != null && data.isNotEmpty()) {
                    binding.player.updateListNextVideo(data = data)
                }
            }
            is ResultTriggerStopPlayer -> {
                stopPlayer()
            }
            is ResultPlayerState -> {
                _binding.let {
                    if (this.isPlay) {
                        binding.player.play(force = true)
                    } else {
                        binding.player.pause(force = true)
                    }
                }
            }

            is ResultPausePreview -> {
                _binding?.let {
                    if(binding.player.getPlayerConfig().isPlayPreview) {
                        binding.player.pause(force = true)
                    }
                }
            }
            is ResultSaveLocalPreviewHistory -> {
                _binding.let {
                    viewModel.saveLocalPreviewHistory(
                        history = History(
                            movieId = viewModel.getId(),
                            episodeId = viewModel.currentEpisode()?.id ?: "",
                            startPosition = binding.player.currentDuration() / 1000L
                        )
                    )
                }
            }
            is ResultTriggerPlayerLayout -> {
                if (context.isTablet()) {
                    adjustPlayerLayoutTablet(isScale = isScale)
                } else {
                    adjustPlayerLayout(isScale = isScale)
                }
            }
            is ResultSwitchPlayerMode -> {
                if (_binding != null) {
                    if (this.modeFullscreen) {
                        binding.player.enterFullscreenMode()
                    } else {
                        binding.player.exitFullscreenMode()
                    }
                }
            }
            is ResultPlayerControlVisibility -> {
                if (_binding != null) {
                    binding.player.setPlayerUIViewVisible(isVisible = isVisible)
                }
            }
            is ResultPlayerProgressBarAndLockButtonVisibility -> {
                if (_binding != null) {
                    binding.player.setPlayerProgressAndLockVisible(isVisible = isVisible)
                }
            }

            //endregion
            is ResultGameVod -> {
                Timber.tag("tamlog-emoji").w("VodPlayerFragment ResultGameVod $data")
                if (GameEmojiUtils.validGameEmojiV2(viewModel,data)) {
                    startGameEmoji(data.data)
                    viewModel.savePlayerScalableOnGame(data.data.isScale)
                    viewModel.saveGameVod(data.data)
                } else {
                    stopGameEmoji()
                    viewModel.savePlayerScalableOnGame(false)
                }
            }
            is ResultNextVodPlaylist -> {
                if (data.videos.isNotEmpty()) {
                    viewModel.savePlaylist(playlist = data)
                    val firstItem = data.videos.first()
                    viewModel.triggerPlayVod(id = firstItem.vodId, type = Utils.VOD_PLAY_TYPE_NEXT_VOD_PLAYLIST)
                }
            }
            is Error -> {
                Logger.d("$TAG Error: $message - $data")
                when (data) {
                    is GetStream -> {
                        //
                        notifyGetStreamError(errorMessage = this.message)
                    }
                    is GetDetail -> {
                        showWarningDialog(message = message)
                        trackingProxy.sendEvent(
                            InforMobile(
                                infor = trackingInfo,
                                logId = TrackingConstants.EVENT_APP_ERROR,
                                event = AppErrorType.ERROR,
                                appId = TrackingUtil.currentAppId,
                                appName = TrackingUtil.currentAppName,
                                errorCode = AppErrorConstants.GET_VOD_DETAIL_CODE,
                                errorMessage = this.message,
                                itemName = AppErrorConstants.GET_VOD_DETAIL_MESSAGE,
                                issueId = TrackingUtil.createIssueId(),
                                position = TrackingUtil.position
                            )
                        )
                    }
                    is GetGameVod -> {
                        stopGameEmoji()
                        viewModel.savePlayerScalableOnGame(false)
                    }
                    is GetIntroCreditDataForCast -> {
                        updateIntroCreditData(episode = data.episode, stream = null)
                    }
                    else -> {}
                }
            }

            is ErrorByInternet -> {
                when (data) {
                    is GetStream -> {
                        isPlayerErrorByInternet = true
                        showWarningDialog(message = this.message)
                    }
                    is GetDetail -> {
                        showWarningDialog(message = message)
                    }
                    else -> {}
                }

                //
                resetGetStreamRetryStep()
            }
            is ErrorRequiredLogin -> {
                Logger.d("$TAG ErrorRequiredLogin: ${data}")
                when (data) {
                    is GetStream -> {
                        resetGetStreamRetryStep()
                        resetVodPlayerInfo()
                        requiredLogin?.let {
                            if(viewModel.hasPreview()
                                && (viewModel.hasPreviewLink(it))
                            ) {
                                vodPreviewPlayerInfo.apply {
                                    urlDash = it.urlDash ?: ""
                                    urlDashH265 = it.urlDashH265 ?: ""
                                    urlDashDolbyVision = it.urlDashDolbyVision
                                    urlDashH265Hdr10Plus = it.urlDashH265Hdr10Plus
                                    urlDashH265Hdr = it.urlDashH265Hdr
                                    urlDashH265Hlg = it.urlDashH265Hlg
                                    urlDashAv1 = it.urlDashAv1
                                    urlDashVp9 = it.urlDashVp9
                                }
                                playPrerollAd()

                                // Don't use bookmark when play preview
                                userHistory = userHistory?.copy(startPosition = 0L)

                                //MQTT
                                publishStartToTopic(contentType = MqttContentType.PreviewVOD, pingStart = requiredLogin.pingMqtt)
                            }
                        }
                    }

                    else -> {}
                }
            }
            is ErrorRequiredVip -> {
                Logger.d("$TAG ErrorRequiredVip: ${data}")
                if (data is GetStream) {
                    // Tracking
                    isSendLogSeek514 = false
                    //MQTT
                    publishEndToTopic()
                    
                    sendTrackingStopMovie()
                    if (TrackingUtil.getPreviewProcessItem().first == viewModel.getId()
                        && TrackingUtil.getPreviewProcessItem().second == viewModel.currentEpisode()?.id
                    ) {
                        // Don't reset playing session
                    } else {
                        trackingInfo.updatePlayingSession(0)
                        Logger.d("trangtest === playingSession 0")
                        vodPlaybackLogger.apply {
                            updatePlayingSession(0)
                        }
                        TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
                    }
                    sendTrackingEnterDetail(event = "RequestPackage")
                    //
                    resetGetStreamRetryStep()
                    //
                    resetVodPlayerInfo()
                    //
                    requiredVip?.let {

                        if(viewModel.hasPreview()
                            && (viewModel.hasPreviewLink(it))
                        ) {
                            val isAutoPlay = if (data.nextActionEvent != null) {
                                if (data.nextActionEvent.action == NextActionEvent.Action.GO_TO_PAYMENT) {
                                    val isNavigateToPayment = data.nextActionEvent.data.getBoolean(Constants.PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_VALUE, false)
                                    !isNavigateToPayment
                                } else true
                            } else true
                            vodPreviewPlayerInfo.apply {
                                this.urlDash = it.urlDash ?: ""
                                this.urlDashH265 = it.urlDashH265 ?: ""
                                this.urlDashDolbyVision = it.urlDashDolbyVision
                                this.urlDashH265Hdr10Plus = it.urlDashH265Hdr10Plus
                                this.urlDashH265Hdr = it.urlDashH265Hdr
                                this.urlDashH265Hlg = it.urlDashH265Hlg
                                this.urlDashAv1 = it.urlDashAv1
                                this.urlDashVp9 = it.urlDashVp9
                                this.isAutoPlay = isAutoPlay
                            }
                            playPrerollAd()

                            // Don't use bookmark when play preview
                            userHistory = userHistory?.copy(startPosition = 0L)

                            //MQTT
                            publishStartToTopic(contentType = MqttContentType.PreviewVOD, pingStart = it.pingMqtt)
                        }
                    }
                }
            }

            is Done -> {
                binding.loading.gone()
                if (intent is GetDetail) {
                    if (intent.isPlayerCalled) {
                        Timber.e("$TAG GetDetail initTvcAds")
                        initTvcAds(trackingAds)
                        initLogoAds()

                        // Game Emoji
                        initGameEmoji()

                        val editData = details?.blocks?.toMutableList()
                        editData?.removeAll { it is Details.BlockEpisode && it.episodes.size <= 1 }
                        if (editData.isNullOrEmpty()) {
                            //showWarningDialog(message = "Dữ liệu rỗng")
                        } else if(MultiProfileUtils.isCurrentProfileKid(sharedPreferences) && (details?.blockContent?.isKid != true)) {
                            val navController = (activity as? HomeActivity)?.navHostFragment?.navController
                            val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
                            navigation?.navigate(NavHomeMainDirections.actionGlobalToDeeplinkNotSupportedDialog(
                                titlePositive = getString(R.string.multi_profile_kid_not_supported_content_title_confirm)
                            ))

                        } else {
                            userProfile.resetData()
                            if (intent.isPlayingTrailer) {
                                details?.blockTrailer?.trailers?.let {
                                    if (it.isNotEmpty()) {
                                        getStream( // Done get vod detail
                                            episode = it.first(),
                                            delayToPlay = false,
                                            isPlayingTrailer = true,
                                            isSendLog = true
                                        )
                                        requestGameEmojiData(
                                            episode = it.first(),
                                            episodeIndex = userProfile.getEpisodeIndex(),
                                            details = details
                                        )
                                    }
                                }
                            } else {
                                // case: change episode in playlist when ads out stream is playing -> force stop if content playing
                                if (viewModel.isPlaylistContent()) {
                                    when (PlayerUtils.getPlayingType()) {
                                        PlayerView.PlayingType.Local -> {
                                            binding.player.stop(force = true)
                                        }
                                        PlayerView.PlayingType.Cast -> {}
                                    }
                                    viewModel.saveId(intent.id)
                                }
                                val episode = getEpisodeToPlay(checkUserHistory = true)
                                getStream(episode = episode, delayToPlay = false, isSendLog = true) // Done get vod detail
                                requestGameEmojiData(
                                    episode = episode,
                                    episodeIndex = userProfile.getEpisodeIndex(),
                                    details = details
                                )
                            }
                        }
                    }
                } else if (intent is GetDetailOffline) {
                    if (intent.isPlayerCalled) {
                        binding.loading.gone()
                    }
                }
            }
            else -> Unit
        }
    }

    private fun publishStartToTopic(contentType: MqttContentType = MqttContentType.VOD, pingStart: Boolean = false) {
        if (mqttPublisher != null) {
            publishToTopic(MqttUtil.ACTION_END)
        }
        if (pingStart) {
            publishToTopic(MqttUtil.ACTION_START, contentType)
        }
    }
    private fun publishEndToTopic() {
        publishToTopic(MqttUtil.ACTION_END)
    }

    private fun publishToTopic(action: String, contentType: MqttContentType = MqttContentType.VOD) {
        if (action == MqttUtil.ACTION_END) {
            mqttPublisher?.let { publisher ->
                MqttConnectManager.INSTANCE.publishToTopic(
                    publisher = publisher.copy(action = action, createdTime = System.currentTimeMillis()),
                    type = publisher.itemType.id,
                    typeId = publisher.itemId
                )
                mqttPublisher = null
            } ?: kotlin.run { return }
        } else if (action == MqttUtil.ACTION_START) {
            mqttPublisher = Publisher(
                action = action,
                createdTime = System.currentTimeMillis(),
                isRetry = 0,
                uid = sharedPreferences.userId(),
                contract = "",
                netMode = NetworkUtils.getNetworkMode(),
                appVer = BuildConfig.VERSION_NAME,
                profileId = sharedPreferences.profileId(),
                contentType = contentType.value.toString(),
                playlistId = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id ?: "" else "",
                chapterId = viewModel.currentEpisode()?.id ?: "",
                episodeId = viewModel.currentEpisode()?.realEpisodeId ?: "",
                itemId = viewModel.getId(),
                refPlaylistId = if(viewModel.isPlaylistContent()) viewModel.getPlaylist()?.refId ?: "" else "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refEpisodeId = TrackingUtil.contentPlayingInfo.refEpisodeId,
            ).apply {
                itemType = ItemType.VOD
                MqttConnectManager.INSTANCE.publishToTopic(
                    publisher = this,
                    type = itemType.id,
                    typeId = itemId
                )
            }
        }
    }

    private fun handlePlayBookmark() {
        // If change stream by choose other bitrate => Not apply logic bookmark in this case
        if (_binding != null && viewModel.getVipRequired()?.first == false) {
            // Preview
            userHistory = mapLocalPreviewHistory()

            if (currentDurationBackgroundPlayerService != -1L) {
                binding.player.seek(duration = currentDurationBackgroundPlayerService)
                currentDurationBackgroundPlayerService = -1L
            } else {
                startTime = userHistory?.run { startPosition.toInt() } ?: 0
                if (viewModel.currentEpisode()?.isResetPosition == true) {
                    if(binding.player.currentDuration() != 0L) {
                        binding.player.seek(duration = 0)
                    }
                    //
                    viewModel.currentEpisode()?.isResetPosition = false
                    isResetPlayingPosition = false
                } else {
                    userHistory?.run {
                        val position =
                            if (startPosition >= binding.player.totalDuration()) 0 else (startPosition * 1000)
                        if (MainApplication.INSTANCE.appConfig.watchingManualHandle == "1") {
                            if (isPlayingAtCreditRange(currentTime = position)) {
                                binding.player.seek(duration = 0)
                            } else {
                                if (<EMAIL>) {
                                    <EMAIL> = false
                                    if(binding.player.currentDuration() != 0L) {
                                        binding.player.seek(duration = 0)
                                    }
                                } else {
                                    if(binding.player.currentDuration() != position) {
                                        binding.player.seek(duration = position)
                                    }
                                }
                            }
                        } else {
                            if (<EMAIL>) {
                                <EMAIL> = false
                                if(binding.player.currentDuration() != 0L) {
                                    binding.player.seek(duration = 0)
                                }
                            } else {
                                if (binding.player.currentDuration() != position) {
                                    binding.player.seek(duration = position)
                                }
                            }
                        }
                    } ?: kotlin.run {
                        // Get bookmark error -> play from startPosition
                        if(binding.player.currentDuration() != 0L) {
                            binding.player.seek(duration = 0)
                        }
                    }
                }
            }

            binding.player.onShowThumb(isShow = false)
        }

    }

    private fun checkPlayerCanPlayWhenReady() {
        val canPlay = viewModel.getPlayerCanPlayContent()
        if (!canPlay) {
            binding.player.pause(force = true)
        }
    }

    private fun resetVodPlayerInfo() {
        vodPlayerInfo.apply {
            this.stream = null
            this.episode = null
            this.userProfile = UserProfile()
            this.userHistory = null
        }
    }

    private fun resetVodPreviewPlayerInfo() {
        vodPreviewPlayerInfo.apply {
            this.urlDashDolbyVision = ""
            this.urlDashH265Hdr10Plus = ""
            this.urlDashH265Hdr = ""
            this.urlDashH265Hlg = ""
            this.urlDashAv1 = ""
            this.urlDashVp9 = ""
            this.urlDashH265 = ""
            this.urlDash = ""
            this.isAutoPlay = true
        }
    }

    private fun tabletPlayerLayout() {
        view?.run {
            val width = context.getDisplayWidth()
            val height = context.getDisplayHeight()
            var realWidth = if (width > height) width else height
            realWidth = (realWidth * (Constants.PLAYER_SCALED_RATIO)).toInt()
            layoutParams = ConstraintLayout.LayoutParams(
                realWidth,
                (realWidth / Constants.PLAYER_RATIO).toInt()
            )
        }
    }


    private fun resetPlayerLayout() {
        view?.run {
            val width = context.getDisplayWidth()
            val height = context.getDisplayHeight()
            val realWidth = if (width < height) width else height
            layoutParams = ConstraintLayout.LayoutParams(
                realWidth,
                (realWidth / Constants.PLAYER_RATIO).toInt()
            )
        }
    }

    private fun updateBookMark() {
        // Update user history
        userHistory = userHistory?.copy(
            episodeId = viewModel.currentEpisode()?.id ?: "0",
            startPosition = if (binding.player.getPlayerConfig().isPlayPreview) {
                binding.player.currentDuration() / 1000
            } else {
                if(binding.player.isPlaying() || binding.player.getPlayer()?.isPause() == true) {
                    binding.player.currentDuration() / 1000
                } else {
                    currentDuration / 1000
                }
            }
        )
        userHistory?.let {
            vodPlaybackLogger.updateBookmark(userHistory = it)
        }
    }

    //region Process -> prepare to something (getStartedEpisodeIndex, getStartedBitrateIndex, getStream ...) before get stream.
    private fun mappingBitrate(playerDataBitrate: ArrayList<IPlayer.Bitrate>) {
        val cacheListBitrateApi = mutableListOf<Details.Episode.Bitrate>()
        cacheListBitrateApi.addAll(viewModel.currentEpisode()?.apiBitrates ?: listOf())

        val processBitrateList = mutableListOf<Details.Episode.Bitrate>().apply {
            addAll(cacheListBitrateApi.map {
                playerDataBitrate.firstOrNull { bitrate -> bitrate.name == it.id }?.let { _ ->
                    it.copy()
                } ?: Details.Episode.Bitrate(
                    id = it.name,
                    name = it.name + "p",
                    requiredPayment = true, // can't found from API map
                )
            }.filter { bitrate -> !bitrate.requiredPayment })

            // Always add auto profile
            cacheListBitrateApi.firstOrNull { bitrate -> bitrate.type() == "auto_default" }?.let { autoBitrate ->
                add(autoBitrate)
            }
        }
        viewModel.currentEpisode()?.appBitrate?.apply {
            clear()
            addAll(processBitrateList)
        }

        Logger.d("$TAG => MappingBitrate => => Bitrates: ${viewModel.currentEpisode()?.appBitrate}")
    }

    private fun processChangeBitrate(): Boolean {
        _binding?.let {
            viewModel.currentEpisode()?.let { currentEpisode ->
                val bitrateId = currentEpisode.appBitrate.findBitrateId()
                //
                binding.player.playerData.bindBitrateFromVod(data = currentEpisode)
                //
                val result = binding.player.changeBitrate(
                    playerRequestId = playerRequestId(),
                    key = bitrateId
                )
                Logger.d("$TAG => ProcessChangeBitrate => BitrateId: $bitrateId => Bitrates: ${binding.player.playerData.bitrates} => Result: $result")
                return result
            } ?: return true
        } ?: return true
    }

    private fun playerRequestId(): String {
        return viewModel.currentEpisode()?.id ?:""
    }

    /**
     * Reset bitrates when change content
     */
    private fun PlayerControlView.Data.resetBitrates() {
        bitrates = listOf()
    }
    /**
     * Bind all bitrates to display for user
     */
    private fun PlayerControlView.Data.bindBitrateFromVod(data: Details.Episode) {
        //
        fun List<Details.Episode.Bitrate>?.getBitrateIndex(): Int {
            if (this.isNullOrEmpty()) return -1
            val userBitrateIndex = this.indexOfFirst { it.id == userProfile.getBitrateId() }
            return if (userBitrateIndex >= 0 && userBitrateIndex < this.size) userBitrateIndex
            else this.size - 1
        }
        //
        if (data.appBitrate.isEmpty()) this.bitrates = emptyList()
        else {
            bitrates = data.appBitrate.map { bitrate ->
                PlayerControlView.Data.Bitrate(
                    id = bitrate.id,
                    name = bitrate.name,
                    iconVip = "",
                    type = bitrate.type()
                )
            }
            bitrateIndex = data.appBitrate.getBitrateIndex()
        }
    }

    private fun getEpisodeToPlay(checkUserHistory: Boolean = false): Details.Episode? {
        // Case: Playlist
        details?.blockEpisode?.episodes?.forEach { item ->
            if (item.isItemOfPlaylist && item.vodId == viewModel.getId()) return item
        }
        //
        var episode =
            findEpisode(episodeIndex = getStartedEpisodeIndex(checkUserHistory = checkUserHistory))
        if (episode == null) { // Play trailer if episode empty
            episode =
                findTrailer(trailerIndex = getStartedTrailerIndex(checkUserHistory = checkUserHistory))
        }
        return episode
    }

    private fun getStartedEpisodeIndex(checkUserHistory: Boolean = false): Int {
        val episodes = details?.blockEpisode?.episodes
        if (!episodes.isNullOrEmpty()) {
            if (checkUserHistory) {
                userHistory?.let {
                    val index = try {
                        it.episodeId.toInt()
                    } catch (ex: Exception) {
                        0
                    }
                    userProfile.updateEpisodeIndex(index = index)
                    userProfile.updateStartPosition(position = it.startPosition)
                }
            }
            if (userProfile.getEpisodeIndex() < 0 || userProfile.getEpisodeIndex() >= episodes.size) {
                userProfile.updateEpisodeIndex(index = 0)
            }
        } else {
            userProfile.updateEpisodeIndex(index = -1)
        }
        return userProfile.getEpisodeIndex()
    }

    private fun getStartedTrailerIndex(checkUserHistory: Boolean = false): Int {
        val trailers = details?.blockTrailer?.trailers
        if (!trailers.isNullOrEmpty()) {
            if (checkUserHistory) {
                userHistory?.let {
                    val index = try {
                        it.episodeId.toInt()
                    } catch (ex: Exception) {
                        0
                    }
                    userProfile.updateEpisodeIndex(index = index)
                    userProfile.updateStartPosition(position = it.startPosition)
                }
            }
            if (userProfile.getEpisodeIndex() < 0 || userProfile.getEpisodeIndex() >= trailers.size) {
                userProfile.updateEpisodeIndex(index = 0)
            }
        } else {
            userProfile.updateEpisodeIndex(index = -1)
        }
        return userProfile.getEpisodeIndex()
    }

    private fun findTrailer(trailerIndex: Int): Details.Episode? {
        val trailers = details?.blockTrailer?.trailers
        return if (trailers == null || (trailerIndex < 0 || trailerIndex >= trailers.size)) {
            userProfile.updateEpisodeId(id = "")
            null
        } else {
            userProfile.updateEpisodeId(id = trailers[trailerIndex].id)
            trailers[trailerIndex]
        }
    }

    private fun List<Details.Episode.Bitrate>.findBitrateId(): String {
        val userBitrateIndex = this.indexOfFirst { it.id == userProfile.getBitrateId() }
        if (userBitrateIndex >= 0 && userBitrateIndex < this.size) {
            userProfile.updateBitrateId(id = this[userBitrateIndex].id)
            userProfile.updateBitrateIndex(index = userBitrateIndex)
        } else {
            userProfile.updateBitrateId(id = this[this.size - 1].id)
            userProfile.updateBitrateIndex(index = this.size - 1)
        }
        return userProfile.getBitrateId()
    }

    private fun findEpisode(episodeIndex: Int): Details.Episode? {
        val episodes = details?.blockEpisode?.episodes
        return if (episodes == null || (episodeIndex < 0 || episodeIndex >= episodes.size)) {
            userProfile.updateEpisodeId(id = "")
            null
        } else {
            userProfile.updateEpisodeId(id = episodes[episodeIndex].id)
            episodes[episodeIndex]
        }
    }

    private fun findEpisodePosition(episode: Details.Episode): Int? {
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        val index = listEpisode.getIndexOf(episode)

        if ((index != -1) && (index in listEpisode.indices)) {
            return index
        }
        return null
    }

    private fun UserProfile.resetData() {
        this.updateBitrateId(sharedPreferences.bitrateVod())
        this.updateBitrateIndex(0)
        this.updateEpisodeId("")
        this.updateEpisodeIndex(0)
        this.updateStartPosition(0)
    }

    private fun getStream(
        episode: Details.Episode?,
        delayToPlay: Boolean,
        resetStartPositionFromUserProfile: Boolean = false, // using for replay
        isPlayingTrailer: Boolean = false,
        isSendLog: Boolean = false,
        isGetBookmark: Boolean = true,
        //region Pairing Control
        fromChangeEpisode: Pair<Boolean, Int> = Pair(false, 0), // Pair<fromChangeEpisode, action_source> | action_source: 0: from player | 1: from list episode/trailer
        isSendEventToRemote: Boolean = true,
        //endregion
        delay: Long = 0L,
        isRetry: Boolean = false
    ) {
        //
        if (checkHaveChangeContent(vodId = viewModel.getId(), episode = episode)) {
            stopPlayer()
        }
        //
        isResetPlayingPosition?.let { episode?.isResetPosition = it }
        episode?.let {
            viewModel.saveCurrentEpisode(it)
            vodPlaybackLogger.updateNewEpisodeItem(it)
        }
        notifyPlayerChange(episode = episode)
        isPlayTrailer = isPlayingTrailer

        // Pairing control
        if (pairingConnection.isConnected && (viewModel.currentEpisode()?.isTrailer == 1 || isPlayingTrailer)) {
//            pairingConnection.setSessionRunning(isRunning = false)
            binding.player.updateIsSupportCast(isSupport = false)
        }
        //

        if (episode == null) {
            //navigateToWarning(message = getString(R.string.all__error__empty_data), requestKey = GetStream::class.java.safeName())
        } else {
            episode.also { currentEpisode ->
                val nextActionEvent = viewModel.getNextActionEvent()
                // Get Stream
                getStream(
                    id = viewModel.getId(),
                    episode = currentEpisode,
                    bitrateId = currentEpisode.autoProfile,
                    delayToPlay = delayToPlay,
                    isPlayingTrailer = isPlayingTrailer,
                    isSendLog = isSendLog,
                    isGetBookmark = isGetBookmark,
                    fromChangeEpisode = fromChangeEpisode,
                    isSendEventToRemote = isSendEventToRemote,
                    nextActionEvent = nextActionEvent,
                    delay = delay,
                    isRetry = isRetry
                )
                // Reset next action event
                viewModel.saveNextActionEvent(action = null)
                //

                // Logic -> Next Video (Call API)
                if (isLastEpisode(episode = currentEpisode)) {
                    val isPlaylist = viewModel.isPlaylistContent()
                    var videoId = if (isPlaylist) {
                        viewModel.getPlaylist()?.id ?: ""
                    } else {
                        viewModel.getId()
                    }
                    val structureId = try {
                        viewModel.getDataDetail()?.blockContent?.structureIds?.first() ?: ""
                    } catch (ex: Exception) {
                        ""
                    }
                    viewModel.dispatchIntent(
                        GetListNextVideo(
                            videoId = videoId,
                            structureId = structureId,
                            isPlaylist = isPlaylist
                        )
                    )
                }
                //
            }
        }
    }

    private fun getHistory(id: String) {
        viewModel.dispatchIntent(GetHistoryById(id = id, userId = sharedPreferences.userId()))
    }

    private fun getHistoryByIndex(id: String, episodeId: String) {
        viewModel.dispatchIntent(
            GetHistoryByIndex(
                id = id,
                userId = sharedPreferences.userId(),
                episodeId = episodeId
            )
        )
    }

    private fun getDetails(
        id: String,
        fileHash: String = "",
        isPlayTrailer: Boolean = false,
        isOffline: Boolean = false
    ) {
        if (!isOffline) binding.loading.visible()
        viewModel.saveLoginRequired(isRequired = false, requiredLogin = null)

        viewModel.dispatchIntent(
            if (isOffline) {
                Timber.d("LogSelect getDetailOffline")
                GetDetailOffline(id = id, isAirline = isAirlineLayout(), isPlayerCalled = true)
            } else {
                GetDetail(id = id, isPlayerCalled = true, isPlayingTrailer = isPlayTrailer)
            }
        )
    }

    private fun getStream(
        id: String,
        episode: Details.Episode,
        bitrateId: String,
        delayToPlay: Boolean,
        isPlayingTrailer: Boolean,
        isSendLog: Boolean,
        isGetBookmark: Boolean,
        fromChangeEpisode: Pair<Boolean, Int>,
        isSendEventToRemote: Boolean,
        nextActionEvent: NextActionEvent?,
        delay: Long = 0L,
        isRetry: Boolean = false
    ) {
        runOnUiThread {
            if (isGetBookmark) binding.player.onShowThumb(isShow = true, url = details?.blockContent?.horizontalImage?.ifBlank { details?.blockContent?.verticalImage ?: "" } ?: "")
        }
        viewModel.saveVipRequired(isRequired = false, requiredVip = null)

        // Get bookmark
        if (isGetBookmark) {
            getHistoryByIndex(id = id, episodeId = episode.id)
        }

        if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast && !isSendEventToRemote) { // Get Intro Credit Data For Cast
            viewModel.dispatchIntent(GetIntroCreditDataForCast(id = id, episode = episode, bitrateId = bitrateId))
        } else {

            viewModel.dispatchIntent(
                GetStream(
                    id = id,
                    episode = episode,
                    bitrateId = bitrateId,
                    delayToPlay = delayToPlay,
                    isPlayingTrailer = isPlayingTrailer,
                    isSendLog = isSendLog,
                    fromChangeEpisode = fromChangeEpisode,
                    isSendEventToRemote = isSendEventToRemote,
                    nextActionEvent = nextActionEvent,
                    delay = delay,
                    isRetry = isRetry
                )
            )
        }
    }
    private fun getOfflineDetails(id: String, fileHash: String = "") {
        viewModel.saveLoginRequired(isRequired = false, requiredLogin = null)

        viewModel.dispatchIntent(
            GetDetailOffline(
                id = id,
                isAirline = isAirlineLayout(),
                isPlayerCalled = true
            )
        )
    }

    private fun notifyPlayerChange(episode: Details.Episode?) {
        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.dispatchIntent(OnPlayerChanged(episode = episode))
            viewModel.triggerPlayerChanged(episode)
        }
    }

    private fun checkHaveChangeContent(vodId: String, episode: Details.Episode?): Boolean {
        return vodId != viewModel.getId() || episode?.id != viewModel.currentEpisode()?.id
    }

    private fun stopPlayer() {
        _binding?.let {
            it.player.stop(force = true)
            it.player.stopServices()
            it.player.playerData.resetBitrates()
        }
        //
        clearAgeRestriction()
    }

    private fun showPlayerErrorWithRetryDialog(message: String) {
        if (playerRetryDialog != null) {
            playerRetryDialog?.dismissAllowingStateLoss()
            playerRetryDialog = null
        }
        if (playerRetryDialog == null) {
            playerRetryDialog = AlertInfoDialog().apply {
                setTextTitle(title = <EMAIL>(R.string.notification))
                setMessage(text = message)
                setUserId(text = sharedPreferences.userId())
                setDeviceName(text = "${Util.manufacturer()} ${Util.model()}")
                setMac(text = Util.deviceId(<EMAIL>()))
                setVersion(text = "iZiOS ${BuildConfig.VERSION_NAME}")
                setTime(
                    text = DateTimeUtils.addTimeWithCurrentLocationAndFormat(
                        currentMilliseconds = System.currentTimeMillis(),
                        timeFormat = "dd/MM/yyyy - HH:mm"
                    )
                )
                setType(text = kotlin.run {
                    if (viewModel.isPlaylistContent()) "Playlist" else {
                        if (_binding != null) {
                            if (binding.player.isPlayOffline()) "VOD Offline" else "VOD"
                        } else "VOD"
                    }
                })
                setMessageContent(text = kotlin.run {
                    if (viewModel.isPlaylistContent()) {
                        <EMAIL>(R.string.vod_title_pattern, viewModel.getPlaylist()?.title ?: "", viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "")
                    } else {
                        if(viewModel.getDataDetail()?.blockContent?.episodeType == 0){
                            viewModel.getDataDetail()?.blockContent?.titleVietnam ?: ""
                        }else{
                            if (viewModel.currentEpisode() == null) viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "" else <EMAIL>(R.string.vod_title_pattern, viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "", viewModel.currentEpisode()?.titleVietnam ?: "")
                        }
                    }
                })
                setTextExit(text = <EMAIL>(R.string.all_exit))
                setTextConfirm(text = <EMAIL>(R.string.all_retry))
                setListener(object : AlertDialogListener {
                    override fun onExit() {
                        stopCountDownTimerRetry()
                    }

                    override fun onConfirm() {
                        tryRetry()
                        stopCountDownTimerRetry()
                    }
                })
            }
            playerRetryDialog?.show(childFragmentManager, "PlayerErrorWithRetryDialog")
        }
    }

    private fun showPlayerErrorDialog(message: String) {
        AlertInfoDialog().apply {
            setTextTitle(title = <EMAIL>(R.string.notification))
            setMessage(text = message)
            setUserId(text = sharedPreferences.userId())
            setDeviceName(text = "${Util.manufacturer()} ${Util.model()}")
            setMac(text = Util.deviceId(<EMAIL>()))
            setVersion(text = "iZiOS ${BuildConfig.VERSION_NAME}")
            setTime(
                text = DateTimeUtils.addTimeWithCurrentLocationAndFormat(
                    currentMilliseconds = System.currentTimeMillis(),
                    timeFormat = "dd/MM/yyyy - HH:mm"
                )
            )
            setType(text = kotlin.run {
                if (viewModel.isPlaylistContent()) "Playlist" else {
                    if (_binding != null) {
                        if (binding.player.isPlayOffline()) "VOD Offline" else "VOD"
                    } else "VOD"
                }
            })
            setMessageContent(text = kotlin.run {
                if (viewModel.isPlaylistContent()) {
                    <EMAIL>(R.string.vod_title_pattern, viewModel.getPlaylist()?.title ?: "", viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "")
                } else {
                    if(viewModel.getDataDetail()?.blockContent?.episodeType == 0){
                        viewModel.getDataDetail()?.blockContent?.titleVietnam ?: ""
                    }else{
                        if (viewModel.currentEpisode() == null) viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "" else <EMAIL>(R.string.vod_title_pattern, viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "", viewModel.currentEpisode()?.titleVietnam ?: "")
                    }
                }
            })
            setTextExit(text = <EMAIL>(R.string.all_exit))
            setTextConfirm(text = <EMAIL>(R.string.all_retry))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    tryRetry()
                }
            })
        }.show(childFragmentManager, "PlayerErrorDialog")
    }

    //region Logic Next movie
    private fun isLastEpisode(episode: Details.Episode): Boolean {
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        return listEpisode.getIndexOf(episode) == listEpisode.size - 1
    }
    //endregion

    //region Tracking
    private fun initTrackingHeartBeat() {
        removeTrackingHeartBeat()
        if (handlerTrackingHeartBeat == null) {
            Looper.getMainLooper()?.run {
                handlerTrackingHeartBeat = Handler(this)
            }
        }
        handlerTrackingHeartBeat?.run {
            post(runnableTrackingHeartBeat)
        }
    }

    private fun removeTrackingHeartBeat() {
        handlerTrackingHeartBeat?.removeCallbacks(runnableTrackingHeartBeat)
        handlerTrackingHeartBeat = null
    }

    private fun sendTrackingHeartBeat() {
        if (binding.player.isPlaying()) {
            Logger.d("trangtest === sendTrackingHeartBeat runnable")
            sendHeartBeat()
        }

        handlerTrackingHeartBeat?.run {
            postDelayed(runnableTrackingHeartBeat, 60000)
        }
    }

    private fun sendHeartBeat(duration: String = getTotalDuration()) {

        Logger.d("VodPlayerFragment sendHeartBeat: ${(binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: ""} vs ${(binding.player.request()?.url)}")

        if(inforMobile.logId != "51" || pairingConnection.isConnected) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        val canPingHeartBeat = (viewModel.getVipRequired()?.first == false && _binding != null) || binding.player.getPlayerConfig().isPlayPreview
        if (canPingHeartBeat) {
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "111",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = if (binding.player.getPlayerConfig().isPlayPreview) "PingPreview" else if (viewModel.currentEpisode()?.isTrailer != 1) "PingVOD" else "PingTrailer",
                    event = "Ping",
                    chapterId = viewModel.currentEpisode()?.id ?: "",
                    EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                    itemId = viewModel.getId(),
                    itemName = details?.blockContent?.titleVietnam ?: "",
                    url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                    publishCountry = details?.blockContent?.nation ?: "",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                    startTime = startTime.toString(),
                    elapsedTimePlaying = calculateElapsedTimePlaying(),
                    realTimePlaying = getRealTimePlayingForLog(),
                    duration = duration,
                    credit = vodPlayerInfo.stream?.timeEndContent?.toString() ?: "0",
                    subMenuId = TrackingUtil.blockId,
                    videoQuality = findBitrateNameById(bitrateId = userProfile.getBitrateId()),
                    audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                    subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                    playlistID = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id
                        ?: "" else "",
                    isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                    totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                    isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                    CDNName = "",
                    Bitrate = binding.player.getTrackingBitrate(),
                    Resolution = binding.player.getVideoSize(),
                    drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                    bandwidth =  binding.player.getTrackingBandWith(),
                    streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                    streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                    dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                    streamProfile = TrackingUtil.getStreamProfile(),
                    totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                    hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}",
                    position = TrackingUtil.position,
                    idRelated = TrackingUtil.idRelated,
                    isRecommend = TrackingUtil.isRecommend,
                    appSource = details?.blockContent?.appId ?: ""
                )
            )
        }
    }
    private fun sendHeartBeatStop(duration: String = getTotalDuration()) {
        if(pairingConnection.isConnected) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        val canPingHeartBeat = (viewModel.getVipRequired()?.first == false && _binding != null) || binding.player.getPlayerConfig().isPlayPreview
        if (canPingHeartBeat) {
            trackingProxy.sendEvent(inforMobile.copy(
                logId = "111",
                screen = if (binding.player.getPlayerConfig().isPlayPreview) "PingPreview" else if (viewModel.currentEpisode()?.isTrailer != 1) "PingVOD" else "PingTrailer",
                event = "Ping",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                credit = vodPlayerInfo.stream?.timeEndContent?.toString() ?: "0",
                duration = duration,
                audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                elapsedTimePlaying = (currentDuration / 1000).toString(),
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                Resolution = binding.player.getVideoSize(),
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                realTimePlaying = userRealtimePlayingTracker.getRealTimePlaying().toString(),
                videoQuality = userProfile.getBitrateId(),
                bandwidth =  binding.player.getTrackingBandWith(),
                streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),
                Bitrate = binding.player.getTrackingBitrate(),
                
                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}"
            ))
        }
    }


    private fun sendTrackingChangeResolution() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "113",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "ChangeResolution",
                event = "ChangeResolution",
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                bandwidth = binding.player.getTrackingBandWith(),
                isManual = if (userClickChangeBitrate) "1" else "0",
                chapterId = viewModel.currentEpisode()?.id ?: "",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                itemId = viewModel.getId(),
                itemName = details?.blockContent?.titleVietnam ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                publishCountry = details?.blockContent?.nation ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = startTime.toString(),
                elapsedTimePlaying = calculateElapsedTimePlaying(),
                duration = (binding.player.totalDuration() / 1000L).toString(),
                realTimePlaying = getRealTimePlayingForLog(),
                credit = vodPlayerInfo.stream?.timeEndContent?.toString() ?: "0",
                subMenuId = TrackingUtil.blockId,
                videoQuality = findBitrateNameById(bitrateId = userProfile.getBitrateId()),
                audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                playlistID = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id
                    ?: "" else "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                CDNName = "",
                Bitrate = binding.player.getTrackingBitrate(),
                Resolution = binding.player.getVideoSize(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                position = TrackingUtil.position
            )
        )
    }

    private fun saveTrackingOffline(
        logId: String,
        event:String,
        data:VideoTaskItem?,
        collection:CollectionVideoTaskItem?
    ) {
        data?.apply {
            val dataSend = InforMobile(
                infor = trackingInfo,
                logId = logId,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "D2G",
                event = event,
                EpisodeID = data.chapterId,
                itemId = collection?.movieId?: "",
                itemName = collection?.title?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = startTime.toString(),
                duration = getTotalDuration(),
                elapsedTimePlaying = (currentDuration / 1000).toString(),
                realTimePlaying = if(logId == "52") userRealtimePlayingTracker.getRealtimePlaying().toString()
                                  else getRealTimePlayingForLog(),
                videoQuality = data.profileId ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                bandwidth =  binding.player.getTrackingBandWith(),
                streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),
                totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
            if(NetworkUtils.isNetworkAvailable()){
                trackingProxy.sendEvent(dataSend)
                trackingProxy.sendEvent(dataSend.copy(logId = "111", event = "Ping"))
            }else{
                TrackingUtil.arrayLogOffline.add(dataSend)
                TrackingUtil.arrayLogOffline.add(dataSend.copy(logId = "111", event = "Ping"))
                sharedPreferences.setLogD2G(Gson().toJson(TrackingUtil.arrayLogOffline))
            }
        }

    }

    private fun sendTrackingFirstFrame() {
        // LogId: "520"
        val adsStartTime = viewModel.getPreAdsStartTimeInMs()
        val adsEndTime = viewModel.getPreAdsEndTimeInMs()
        vodPlaybackLogger.logStartFirstFrame(
            logStreamInfo = getStreamInfoForLogger(),
            loadType = if (viewModel.getIsStreamRetry() || isContentTheSameWithPrevious()) LoadType.Retry else LoadType.Initial,
            startFromAds = viewModel.getIsPreAds(),
            timeFromClick = if (viewModel.getClickOnItemTimeInMs() != 0L) System.currentTimeMillis() - viewModel.getClickOnItemTimeInMs() else 0,
            timeFromPrepareSource = System.currentTimeMillis() - viewModel.getPrepareSourceTimeInMs(),
            timePreAds = if (adsStartTime != 0L && adsEndTime != 0L && adsStartTime <= adsEndTime)
                adsEndTime - adsStartTime else 0
        )
        viewModel.saveClickOnItemTimeInMs(0)

        //Ads
        viewModel.saveIsPreAds(false)
        viewModel.savePreAdsStartTimeInMs(0)
        viewModel.savePreAdsEndTimeInMs(0)

    }

    private fun isContentTheSameWithPrevious(): Boolean {
        return inforMobile.itemId == viewModel.getId() && inforMobile.chapterId == viewModel.currentEpisode()?.id
    }

    private fun sendTrackingStartMovie(){
        if(checkNeedSendLogStart()) {
            sendTrackingStopMovie() //stop phim trước đó nếu c
            if(isNeedUpdatePlayingSession){
                if (TrackingUtil.getPreviewProcessItem().first == viewModel.getId()
                    && TrackingUtil.getPreviewProcessItem().second == viewModel.currentEpisode()?.id
                ) {
                    // Don't reset playing session
                } else {
                    val playingSession = System.currentTimeMillis()
                    Logger.d("trangtest === playingSession $playingSession")
                    trackingInfo.updatePlayingSession(playingSession)
                    vodPlaybackLogger.apply {
                        updatePlayingSession(playingSession)
                    }
                    TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
                }

            }
            isNeedUpdatePlayingSession = true
            initTrackingHeartBeat()
            inforMobile = InforMobile(
                infor = trackingInfo,
                logId = "51",
                appId = TrackingUtil.currentAppId,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = if (isPlayTrailer) "StartTrailer" else "StartMovie",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                chapterId = viewModel.currentEpisode()?.id ?: "",
                itemId = viewModel.getId(),
                itemName = details?.blockContent?.titleVietnam ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                directors = if (details?.blockContent?.directors?.isNotEmpty() == true) details?.blockContent?.directors?.first()
                    ?: "" else "",
                publishCountry = details?.blockContent?.nation ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = this.startTime.toString(),
                subMenuId = TrackingUtil.blockId,
                duration = (totalDuration / 1000L).toString(),
                elapsedTimePlaying = "",
                realTimePlaying = getRealTimePlayingForLog(),
                keyword = if (TrackingUtil.screen == TrackingUtil.screenSearch) TrackingUtil.keyword else "",
                idRelated = TrackingUtil.idRelated,
                videoQuality = userProfile.getBitrateId(),
                playlistID = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id
                    ?: "" else "",
                businessPlan = details?.blockContent?.payment?.id ?: "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend,
                appSource = details?.blockContent?.appId ?: ""
            )
            trackingProxy.sendEvent(inforMobile)
            Logger.d("trangtest === sendTrackingHeartBeat start")
            sendHeartBeat()
        }
    }
    private fun sendTrackingStopMovie(){ //phim stop -> chuyển tập, chọn phim khác, back ra khoi detailvod
        if(inforMobile.logId != "51" || pairingConnection.isConnected) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        Logger.d("trangtest === sendTrackingHeartBeat stop")
        //log Adjust
        AdjustAllEvent.sendVideoViewEvent(
            watchDuration = userRealtimePlayingTracker.getRealTimePlaying().toString(),
            isComplete = if(binding.player.getPlayer()?.isEnd() == true) "true" else "false",
            abandonedReason = AbandonedReason.user_initiated
        )

        sendHeartBeatStop()
        trackingProxy.sendEvent(inforMobile.copy(
            logId = "52",
            status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
            event = if (inforMobile.event == "StartTrailer") "StopTrailer" else "StopMovie",
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
            elapsedTimePlaying = (currentDuration / 1000).toString(),
            realTimePlaying = userRealtimePlayingTracker.getRealTimePlaying().toString(),
            videoQuality = userProfile.getBitrateId(),

            videoCodec = sharedPreferences.videoCodecInfo(),
            videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
            audioCodec = sharedPreferences.audioCodecInfo(),

            deviceFingerprint = sharedPreferences.deviceFingerprint(),
            urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
            streamProfile = TrackingUtil.getStreamProfile(),
            streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: ""
        ))
        TrackingUtil.resetPosition()
        inforMobile = InforMobile()
    }
    private fun sendTrackingPause(){
        if(inforMobile.logId != "51" || pairingConnection.isConnected) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        trackingProxy.sendEvent(inforMobile.copy(
            logId = "53",
            status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
            event = if (isPlayTrailer) "PauseTrailer" else "PauseMovie",
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
            elapsedTimePlaying = (currentDuration / 1000).toString(),
            realTimePlaying = getRealTimePlayingForLog(),
            videoQuality = userProfile.getBitrateId(),
        ))
    }
    private fun sendTrackingResume(){
        if(inforMobile.logId != "51" || pairingConnection.isConnected) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        trackingProxy.sendEvent(inforMobile.copy(
            logId = "54",
            status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
            event = if (isPlayTrailer) "ResumeTrailer" else "ResumeMovie",
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
            elapsedTimePlaying = (currentDuration / 1000).toString(),
            realTimePlaying = getRealTimePlayingForLog(),
            videoQuality = userProfile.getBitrateId(),
        ))
    }
    private fun sendTrackingNextPrev(logId:String, event:String){
        if(pairingConnection.isConnected) return // đang cast thì ko gửi log
        if(checkNeedSendLogStart()) {
            sendTrackingStopMovie() //stop phim trước đó nếu có
            isNeedUpdatePlayingSession = false
            if (TrackingUtil.getPreviewProcessItem().first == viewModel.getId()
                && TrackingUtil.getPreviewProcessItem().second == viewModel.currentEpisode()?.id
            ) {
                // Don't reset playing session
            } else {
                trackingInfo.updatePlayingSession(System.currentTimeMillis())
                Logger.d("trangtest === playingSession ${System.currentTimeMillis()}")
                vodPlaybackLogger.apply {
                    updatePlayingSession(createPlayingSession())
                }
                TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
            }
            trackingProxy.sendEvent(InforMobile(
                infor = trackingInfo,
                logId = logId,
                event = event,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                chapterId = viewModel.currentEpisode()?.id ?: "",
                itemId = details?.blockContent?.id?: "",
                itemName = details?.blockContent?.titleVietnam ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                directors = if (details?.blockContent?.directors?.isNotEmpty() == true) details?.blockContent?.directors?.first()
                    ?: "" else "",
                publishCountry = details?.blockContent?.nation ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = this.startTime.toString(),
                subMenuId = TrackingUtil.blockId,
                duration = (totalDuration / 1000L).toString(),
                keyword = if (TrackingUtil.screen == TrackingUtil.screenSearch) TrackingUtil.keyword else "",
                idRelated = TrackingUtil.idRelated,
                videoQuality = userProfile.getBitrateId(),
                playlistID = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id
                    ?: "" else "",
                businessPlan = details?.blockContent?.payment?.id ?: "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                position = TrackingUtil.position
            ))
        }
    }

    private fun checkNeedSendLogStart():Boolean{
        if(binding.player.isPlayOffline()) return false //nếu đang play offline
        if (binding.player.getPlayerConfig().isPlayPreview) return true
        if (viewModel.getVipRequired()?.first == true || _binding == null) return false //nếu bắt mua gói
        return true
    }

    private fun sendTrackingEnterDetail(event:String){
        if(pairingConnection.isConnected) return
        sendTracking(logId = "512", event = event)
    }
    private fun sendTracking(
        logId: String,
        screen: String = if (pairingConnection.isConnected) "Casting" else TrackingUtil.screen,
        event: String,
        errorCode: String = "",
        errorMessage: String = "",
        issueId: String = "",
    ) {
        if(binding.player.isPlayOffline()) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = logId,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                appSource = details?.blockContent?.appId ?: "",
                screen = screen,
                event = event,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                chapterId = viewModel.currentEpisode()?.id ?: "",
                itemId = viewModel.getId(),
                itemName = details?.blockContent?.titleVietnam ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                directors = if (details?.blockContent?.directors?.isNotEmpty() == true) details?.blockContent?.directors?.first()
                    ?: "" else "",
                publishCountry = details?.blockContent?.nation ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = this.startTime.toString(),
                subMenuId = TrackingUtil.blockId,
                duration = (totalDuration / 1000L).toString(),
                realTimePlaying = if(logId == "512") "" else getRealTimePlayingForLog(), //log 512 ko gửi realtimeplaying
                keyword = if(screen == TrackingUtil.screenSearch) TrackingUtil.keyword else "",
                idRelated = TrackingUtil.idRelated,
                errorCode = errorCode,
                errorMessage = errorMessage,
                errUrl = (binding.player.getPlayer() as? ExoPlayerProxy)?.tracking?.urlError ?: "",
                errHeader = (binding.player.getPlayer() as? ExoPlayerProxy)?.tracking?.responseHeaderWhenError ?: "",
                videoQuality = userProfile.getBitrateId(),
                playlistID = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id
                    ?: "" else "",
                businessPlan = details?.blockContent?.payment?.id ?: "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                 drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                position = TrackingUtil.position,
                issueId = issueId,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }
    @SuppressLint("SimpleDateFormat")
    private fun sendTrackingActionSeek(
        logId: String,
        screen: String = TrackingUtil.screen,
        event: String,
        errorCode: String = "",
        errorMessage: String = "",
        duration: String
    ) {
        if(binding.player.isPlayOffline() || pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = logId,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = screen,
                event = event,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                chapterId = viewModel.currentEpisode()?.id ?: "",
                itemId = viewModel.getId(),
                itemName = details?.blockContent?.titleVietnam ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                directors = if (details?.blockContent?.directors?.isNotEmpty() == true) details?.blockContent?.directors?.first()
                    ?: "" else "",
                publishCountry = details?.blockContent?.nation ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = this.startTime.toString(),
                subMenuId = TrackingUtil.blockId,
                duration = (totalDuration / 1000L).toString(),
                elapsedTimePlaying = duration,
                realTimePlaying = getRealTimePlayingForLog(),
                keyword = if(screen == TrackingUtil.screenSearch) TrackingUtil.keyword else "",
                idRelated = TrackingUtil.idRelated,
                errorCode = errorCode,
                errorMessage = errorMessage,
                videoQuality = userProfile.getBitrateId(),
                playlistID = if (viewModel.isPlaylistContent()) viewModel.getPlaylist()?.id
                    ?: "" else "",
                businessPlan = details?.blockContent?.payment?.id ?: "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: ""
            )
        )
    }

    private fun sendTrackingChangeVideoQuality(oldBitrate: String, curBitrate: String) {
        AdjustAllEvent.sendQualityChangeEvent(findBitrateNameById(bitrateId = curBitrate))
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "416",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "ChangeVideoQuality",
                event = "ChangeVideoQuality",
                itemId = viewModel.getId(),
                chapterId = viewModel.currentEpisode()?.id ?: "",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                publishCountry = details?.blockContent?.nation ?: "",
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                itemName = findBitrateNameById(bitrateId = curBitrate),
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                subMenuId = TrackingUtil.blockId,
                videoQuality = findBitrateNameById(bitrateId = oldBitrate),
                businessPlan = details?.blockContent?.payment?.id ?: "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                realTimePlaying = getRealTimePlayingForLog(),

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun sendTrackingChangeSubtitlesAudio(value: String, isChangeAudio: Boolean) {
        if(pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "518",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                appSource = viewModel.getDataDetail()?.blockContent?.appId ?: "",

                screen = TrackingUtil.screen,
                event = if (isChangeAudio) "ChangeAudio" else "ChangeSubtitles",
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                itemId = viewModel.getId(),
                itemName = value,
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                subMenuId = TrackingUtil.blockId,
                businessPlan = details?.blockContent?.payment?.id ?: "",
                isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                totalEpisode = details?.blockContent?.addedEpisodeTotal ?: "",
                isLinkDRM = details?.blockContent?.isVerimatrix?.toString() ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId ?: "",
                audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                videoQuality = userProfile.getBitrateId(),

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun findBitrateNameById(bitrateId: String): String {
        viewModel.currentEpisode()?.let {
            if (it.appBitrate.isEmpty()) {
                return ""
            } else {
                val result = it.appBitrate.firstOrNull { bitrate -> bitrate.id == bitrateId }
                if (result != null) {
                    return result.name
                } else {
                    return ""
                }
            }
        } ?: kotlin.run { return "" }
    }

    private fun calculateElapsedTimePlaying(): String {
        return if (_binding != null) {
            val position = binding.player.currentDuration()
            if (position != 0L) (position / 1000L).toString() else (currentDuration / 1000L).toString()
        } else (currentDuration / 1000L).toString()
    }

    private fun getTotalDuration(): String {
        return if(_binding != null) {
            val totalD = binding.player.totalDuration()
            if(totalD != 0L) (totalD / 1000L).toString() else (totalDuration / 1000L).toString()
        } else (totalDuration / 1000L).toString()
    }

    private fun getStreamInfoForLogger(): LogStreamInfo {
        return LogStreamInfo(
            bitrate = binding.player.getTrackingBitrate(),
            bandwidth = binding.player.getTrackingBandWith(),
            streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
            resolution = binding.player.getVideoSize(),
            videoQuality = userProfile.getBitrateId(),

            audioName = binding.player.currentSelectedAudioTrack?.name ?: "",
            audioBandwidth = binding.player.debugViewData.audioInfo.getBitrateRawValue(),

            subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",

            durationInSecond = (binding.player.totalDuration() / 1000L),

            url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
            urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
            status = if (binding.player.getPlayerConfig().isPlayPreview) LogStreamInfo.LogPlaybackStatus.Preview else LogStreamInfo.LogPlaybackStatus.None,
        )
    }

    private fun getUserTimeInfoForLogger(): LogUserTimeInfo {
        return LogUserTimeInfo(
            elapsedTimePlaying = calculateElapsedTimePlaying(),
            realtimePlaying = getRealTimePlayingForLog(),
        )
    }

    //endregion


    //region Logic Bookmark

    /***
     * 1)
     * - Film has credit -> watching in credit range -> ping log 111 = 0
     * - Film no credit:
     *      + Duration <= 10 minutes: -> watching in last 10 seconds -> ping log 111 = 0
     *      + Duration > 10 minutes: -> watching in last 60 seconds -> ping log 111 = 0
     *
     * 2)
     * - Next film by click next button/auto next: reset start position = 0
     * - Next film by click at list chapter: play in bookmark position
     */
    private fun isPlayingAtCreditRange(currentTime: Long): Boolean {
        vodPlayerInfo.stream?.run {
            val currentEndContentMillis = timeEndContent * 1000
            return if (currentEndContentMillis > 0) { // Film has credit
                currentTime >= currentEndContentMillis
            } else { // Film no credit
                if (binding.player.totalDuration() <= 600000) { // 10 minutes
                    currentTime >= binding.player.totalDuration() - 10000
                } else {
                    currentTime >= binding.player.totalDuration() - 60000
                }
            }
        }
        return false
    }

    private fun updateBookmarkLocal() {
        val currentEpisode = viewModel.currentEpisode()
        currentEpisode?.timeWatchedLocal = calculateElapsedTimePlaying().toLong() ?: 0L

        Timber.d("done updateBookmarkLocal currentEpisode: $currentEpisode")
    }
    //endregion

    //region Logic save tracks history
    private fun updateLogicSaveTrack() {
        if (_binding != null) {
            val tracks = binding.player.playerData.tracks ?: listOf()
            val listSubtitle = tracks.filter { item -> (item.type == TrackType.TEXT.ordinal || ((item.id == "Tắt phụ đề" || item.name == "Tắt phụ đề") && item.type == 10002))}
            val listAudio = tracks.filter { item -> (item.type == TrackType.AUDIO.ordinal) }

            listSubtitle.findLast { it.isSelected }?.let { selectedSub ->
                tracks.findTrackById(id = sharedPreferences.subVod(), type = TrackType.TEXT.ordinal)?.let { historyTrack ->
                    if (selectedSub.id != historyTrack.id) {
                        binding.player.changeTrack(historyTrack)
                    }
                }
            }

            listAudio.findLast { it.isSelected }?.let { selectedAudio ->
                tracks.findTrackByName(name = sharedPreferences.audioVod(), type = TrackType.AUDIO.ordinal)?.let { historyTrack ->
                    if (selectedAudio.name != historyTrack.name) {
                        binding.player.changeTrack(historyTrack)
                    }
                }
            }
        }
    }
    //endregion

    // region Ads


    private fun VodAdsViewModel.VodAdsState.toAdsUI() {
        when (this) {
            is VodAdsViewModel.VodAdsState.OnInteractivePopupShow -> {
                if(source != OmniProductView.Source.OUTSTREAM_VOD) {
                    stopTvcAds(resetPrerollAd = false, resetInstreamAd = true)
                    pauseLogoAds()
                }
            }

            VodAdsViewModel.VodAdsState.RestartAds -> {
                playInstreamAd(binding.player.currentDuration(), forcePlayAfterTime = true)
                resumeLogoAds()
            }
            else -> {}
        }
    }


    private fun layoutCanRequestAds(): Boolean {
        return !isAirlineLayout() && viewModel.screenProvider() != PageId.HboPageId.id
    }

    private fun vodCanRequestAds(details: Details?): Boolean {
        if (details == null) return false
        return details.blockContent.sourceProvider != Constants.SOURCE_PROVIDER_HBO_GO
                && details.blockContent.enableAds == 1
    }

    // region Tvc Ads
    private fun initTvcAds(adsTrackingProxy: AdsTrackingProxy) {
        if (layoutCanRequestAds()
            && vodCanRequestAds(details)
            && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)
        ) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client
            binding.player.initAdsController(adsTrackingProxy)
            binding.player.tvcAdsListener = tvcAdsListener
        }
    }

    private fun playPrerollAd() {
//        Timber.tag("tamlog").d("playPrerollAd ${details?.blockContent?.enableAds}")
        if (layoutCanRequestAds()
            && vodCanRequestAds(details)
            && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)
            && !vodAdsViewModel.isInteractiveOpened() // only when interactive not open
        ) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client

            viewModel.saveIsPreAds(true)
            binding.player.playPrerollAds(
                websiteUrl = AdsUtils.vodUrl(
                    details?.blockContent?.webUrl ?: "",
                    userProfile.getEpisodeIndex()
                ),
                contentId = viewModel.getId(),
                contentListStructureId = details?.blockContent?.structureIds,

                )
            viewModel.savePreAdsStartTimeInMs(System.currentTimeMillis())
        } else {
            tvcAdsListener.onPrerollCompleted()
        }
    }

    private fun playInstreamAd(currentPosition: Long? = -1, forcePlayAfterTime: Boolean) {
        if (layoutCanRequestAds()
            && vodCanRequestAds(details)
            && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)
            && !vodAdsViewModel.isInteractiveOpened() // only when interactive not open
            && activity?.checkAppInBackground() != true
        ) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client
            binding.player.playInstreamAdsVOD(
                websiteUrl = AdsUtils.vodUrl(
                    websiteUrl = details?.blockContent?.webUrl ?: "", userProfile.getEpisodeIndex()
                ),
                vodId = viewModel.getId(),
                vodListStructureId = details?.blockContent?.structureIds,
                currentPositionMill = currentPosition,
                forcePlayAfterTime =forcePlayAfterTime
            )
        }
    }

    private fun stopTvcAds(resetPrerollAd: Boolean = false, resetInstreamAd: Boolean = true) {
        binding.player.stopTvcAds(resetPrerollAd, resetInstreamAd)
    }

    private val tvcAdsListener by lazy {
        object : AdsTvcListener() {
            override fun onPrerollCompleted() {
                Timber.tag("tamlog").w("onPrerollCompleted")
                vodAdsViewModel.dispatchIntent(
                    VodAdsViewModel.VodAdsIntent.TriggerCloseInteractiveAdsPopupOnAdsDone(source = OmniProductView.Source.OUTSTREAM_VOD)
                )

                if (viewModel.hasPreview() && (viewModel.hasPreviewLink(vodPreviewPlayerInfo))) {
                    viewModel.triggerPlayPreview(vodPreviewPlayerInfo)
                } else {
                    // Case: Content have background audio, but not prepare player -> move to background. After, go to foreground
                    // if will prepare again -> wrong current duration
                    if (details?.blockContent?.bgAudio == "1") {
                        if(activity?.checkAppInBackground() != true) {
                            triggerPreparePlayer(vodPlayerInfo)
                        }
                    } else {
                        triggerPreparePlayer(vodPlayerInfo)
                    }

                }
                viewModel.savePreAdsEndTimeInMs(System.currentTimeMillis())

            }

            override fun onOutStreamInteractivePopupLoaded(dataJson: String, userInteract: Boolean) {
                Timber.tag("tamlog").w("onOutStreamInteractivePopupLoaded $dataJson $userInteract")
                vodAdsViewModel.dispatchIntent(
                    VodAdsViewModel.VodAdsIntent.TriggerOpenInteractiveAdsPopup(
                        jsonData = dataJson,
                        source = OmniProductView.Source.OUTSTREAM_VOD,
                        userInteract = userInteract
                    )
                )
                if(userInteract && binding.player.isFullscreen()) {
                    viewModel.dispatchIntent(SwitchPlayerMode(modeFullscreen = false))
                }

            }
        }
    }

    private fun triggerPreparePlayer(vodInfo: VodPlayerInfo) {
        vodInfo.run {
            if (episode != null && stream != null) {
                viewModel.triggerPreparePlayer(
                    episode = episode!!,
                    stream = stream!!,
                    delayToPlay = false,
                    isPlayingTrailer = isPlayingTrailer,
                    drmKey = drmKey,
                )
            }
        }
    }

    // endregion Tvc Ads

    // region Logo Ads

    private fun initLogoAds() {
        Timber.tag("tamlog").d("initLogoAds ${details?.blockContent?.enableAds}")
        if (layoutCanRequestAds()
            && vodCanRequestAds(details)
            && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)
        ) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client
            binding.player.initLogoAdsController()
            binding.player.logoAdsListener = logoAdsListener
        }
    }

    private fun startLogoAds() {
        if (layoutCanRequestAds()
            && vodCanRequestAds(details)
            && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)
            && activity?.checkAppInBackground() != true
        ) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client

            val autoStartLogo =  !vodAdsViewModel.isInteractiveOpened() // only auto start logo when interactive not open

            binding.player.startLogoAds(
                websiteUrl = AdsUtils.vodUrl(
                    details?.blockContent?.webUrl ?: "", userProfile.getEpisodeIndex()
                ),
                vodId = viewModel.getId(),
                vodListStructureId = details?.blockContent?.structureIds,
                autoStartLogo = autoStartLogo
            )
        }
    }

    private fun stopLogoAds() {
        binding.player.stopLogoAds()
    }

    private fun pauseLogoAds() {
        binding.player.pauseLogoAds()
    }

    private fun resumeLogoAds() {
        binding.player.resumeLogoAds()

    }

    private var logoAdsListener: AdsLogoListener? = null

    private fun initLogoAdsListener(): AdsLogoListener {
        return object : AdsLogoListener(viewLifecycleOwner, trackingInfo) {
            override fun onLogoSendInteractive(data: String, userInteract: Boolean) {
                Timber.tag("tam-logo").i("VodPlayer onLogoSendInteractive")
                vodAdsViewModel.dispatchIntent(
                    VodAdsViewModel.VodAdsIntent.TriggerOpenInteractiveAdsPopup(
                        jsonData = data,
                        source = OmniProductView.Source.LOGO_INSTREAM_VOD,
                        userInteract = userInteract
                    )
                )

            }

            override fun onLogoClicked(landingPage: String?, useDirectBrowser: Boolean?) {
                Timber.tag("tam-logo").i("VodPlayer onLogoClicked")
                super.onLogoClicked(landingPage, useDirectBrowser)
                if (binding.player.isFullscreen()) {
                    viewModel.dispatchIntent(SwitchPlayerMode(modeFullscreen = false))
                }
            }

            override fun onLogoStart() {
                super.onLogoStart()
            }

            override fun onLogoStop() {
                super.onLogoStop()
                vodAdsViewModel.dispatchIntent(
                    VodAdsViewModel.VodAdsIntent.TriggerCloseInteractiveAdsPopupOnAdsDone(source = OmniProductView.Source.LOGO_INSTREAM_VOD)
                )


            }
        }
    }
    // endregion Logo Ads
    // endregion Ads


    //region Adjust -> Player Layout
    private fun adjustPlayerLayout(isScale: Boolean) {
        if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (isScale) {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(
                        0,
                        0
                    )
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = Constants.PLAYER_SCALED_RATIO
                    layoutParams = lp
                }
            } else {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(
                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                        ConstraintLayout.LayoutParams.MATCH_PARENT
                    )
                    layoutParams = lp
                }
            }
        }
    }

    private fun adjustPlayerLayoutTablet(isScale: Boolean) {
        if (isScale) {
            if (viewModel.isFullScreen.value?.first == true && viewModel.isFullScreen.value?.second == true) {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(0, 0)
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = Constants.PLAYER_SCALED_RATIO
                    layoutParams = lp
                }
            }
        } else {
            view?.run {
                if (viewModel.isFullScreen.value?.second == true) { // landscape
                    if (viewModel.isFullScreen.value?.first == true) { // fullscreen
                        layoutParams = ConstraintLayout.LayoutParams(
                            ConstraintLayout.LayoutParams.MATCH_PARENT,
                            ConstraintLayout.LayoutParams.MATCH_PARENT
                        )
                    } else {
                        tabletPlayerLayout()
                    }
                } else { // portrait
                    if (viewModel.isFullScreen.value?.first == true) {
                        layoutParams = ConstraintLayout.LayoutParams(
                            ConstraintLayout.LayoutParams.MATCH_PARENT,
                            ConstraintLayout.LayoutParams.MATCH_PARENT
                        )
                    } else {
                        resetPlayerLayout()
                    }
                }
            }
        }
    }
    //endregion

    //region Game
    private fun vodCanRequestGame(details: Details?): Boolean {
        if (details == null) return false
        return details.blockContent.isGameVod == "1"
    }

    // region Game Emoji

    private fun initGameEmoji() {
        Timber.tag("tamlog-emoji").d("initGameEmoji ${details?.blockContent?.isGameVod}")
        if (vodCanRequestGame(details)) {
            binding.player.initGameEmojiController()
            binding.player.gameEmojiListener = object : GameEmojiListener {
                override fun onStartGame(url: String, dataJson: String) {
                    Timber.tag("tamlog-emoji").i("VodPlayer onStartGame")
                    viewModel.dispatchIntent(TriggerStartGameEmoji(url, dataJson))
                }

                override fun onStopGame() {
                    Timber.tag("tamlog-emoji").i("VodPlayer onStartGame")
                    viewModel.dispatchIntent(TriggerCloseGameEmoji)

                }
            }

            // lock player controller for game
            viewModel.dispatchIntent(SetPlayerProgressBarAndLockButtonVisibility(isVisible = false))
        }

    }

    private fun requestGameEmojiData(
        episode: Details.Episode?,
        episodeIndex: Int,
        details: Details?
    ) {
        if (episode == null || details == null) {
            return
            //navigateToWarning(message = getString(R.string.all__error__empty_data), requestKey = GetStream::class.java.safeName())
        }
        val gameEpisodeId = try {
            "tap-" + (episode.id.toInt() + 1).toString()
        } catch (e: Exception) {
            "tap-" + (episodeIndex + 1)
        }
//        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetGameVod(id = "6308413381870d3bab965f1d", gameEpisodeId = gameEpisodeId))
        viewModel.dispatchIntent(
            GetGameVod(
                id = details.blockContent.id,
                gameEpisodeId = gameEpisodeId,
                userId = sharedPreferences.userId()
            )
        )

    }

    private fun startGameEmoji(data: GameVOD.GameVODData) {
        binding.player.startGameEmoji(data)
    }

    private fun stopGameEmoji() {
        binding.player.stopGameEmoji()
        viewModel.saveGameVod(null)
    }

    // endregion Game Emoji
    // endregion Game



    // region Pairing Control
    private fun safeUpdateUI(block: () -> Unit = {}) {
        _binding?.let {
            block()
        }
    }
    /**
     * type: 0 -> Device Bottom Sheet
     * 1 -> Pairing with pin code
     * 2 -> Device Management
     * 3 -> Popup Remote Control
     */
    private fun navigateToPairingControl(type: Int) {
        if (isAirlineLayout()) {
            val navController = (activity as? AirlineActivity)?.navHostFragment?.navController
            val navigation = if(navController?.graph?.id == R.id.nav_airline) { navController } else { null }
            navigation?.navigateSafe(NavAirlineDirections.actionGlobalToPairingControl(type = type))
        } else {
            val navController = (activity as? HomeActivity)?.navHostFragment?.navController
            val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
            navigation?.navigateSafe(NavHomeMainDirections.actionGlobalToPairingControl(type = type))
        }
    }

    //region Report Player
    private fun startToReportPlayer(isOffline: Boolean = false){
        val playerReportDetails = PlayerReportUtils.ReportPlayerVod.mappingDataReportDetailVod(
            details = details,
            curEpisode =  viewModel.currentEpisode()
        )
        if(isOffline){
            parentFragment?.findNavController()?.navigateSafe( directions = VodDetailOfflineFragmentV2Directions.actionVodDetailOffineFragmentToPlayerReportDialogFragment(),
                extendArgs = Bundle().apply { putSerializable(Constants.PLAYER_REPORT_DETAIL_KEY, playerReportDetails)})
        }
        else{
            parentFragment?.findNavController()?.navigateSafe( directions = VodDetailFragmentDirections.actionVodDetailFragmentToPlayerReportDialogFragment(),
                extendArgs = Bundle().apply {
                    putSerializable(Constants.PLAYER_REPORT_DETAIL_KEY, playerReportDetails)
                })
        }
    }
    private fun showMsgInternetUserReport(){
        binding.player.play()
        viewModel.dispatchIntent(TriggerShowMsgUserReport(
            isReported = false,
            message = <EMAIL>(R.string.report_vod_error_no_internet_message)
        ))
    }
    private fun navigateToReportPlayerVod() {
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                binding.player.pause(force = true)
                if (findNavController().currentDestination?.id == R.id.vod_detail_fragment) {
                    if (NetworkUtils.isNetworkAvailable()){
                        startToReportPlayer()
                    }
                    else {
                        showMsgInternetUserReport()
                    }
                } else if (findNavController().currentDestination?.id == R.id.vodDetailOfflineFragmentV2) {
                    if (NetworkUtils.isNetworkAvailable())
                        startToReportPlayer(isOffline = true)
                    else{
                        showMsgInternetUserReport()
                    }
                }
            }
            PlayerView.PlayingType.Cast -> {
                parentFragment?.findNavController()
                    ?.navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToPlayerReportDialogFragment())
            }
        }
    }
    //endregion
    private fun sendPlayContentMessage() {
        if (viewModel.currentEpisode()?.isTrailer == 1) {
            safeUpdateUI {
//                pairingConnection.setSessionRunning(isRunning = false)
                binding.player.updateIsSupportCast(isSupport = false)
            }
            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                navHostFragment = activity?.findNavHostFragment(),
                onStopCastAndNavigate = {
                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                }
            )
            return
        }
        if (isAirlineLayout()) {
//            pairingConnection.setSessionRunning(isRunning = false)
            binding.player.updateIsSupportCast(isSupport = false)
            VodDialogPopupHelper.needShowDialogPopup(VodDialogPopupHelper.PriorityDialog(VodDialogPopupHelper.Priority.HIGH) {
                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                    navHostFragment = activity?.findNavHostFragment(),
                    onStopCastAndNavigate = {
                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                    }
                )
            })
            return
        }
        pairingConnection.getCastingItemMapped()?.let {
            if (viewModel.getDataDetail()?.blockContent?.id == it.id) {
                when (pairingConnection.getCastingItemState()) {
                    CastingItemState.CAN_CAST -> {
                        if (pairingConnection.isSessionRunning) {
                            triggerGetStatusWatching()
                            startIntervalUpdateProgress()
                            if (_binding != null) {
                                binding.player.triggerInitCheckSkipIntroAndCredits()
                            }
                            return
                        }
                    }
                    CastingItemState.NOT_SUPPORT -> {
                        safeUpdateUI {
//                            pairingConnection.setSessionRunning(isRunning = false)
                            binding.player.updateIsSupportCast(isSupport = false)
                        }
                        MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                            navHostFragment = activity?.findNavHostFragment(),
                            onStopCastAndNavigate = {
                                MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                            }
                        )
                        return
                    }
                    else -> { }
                }
            }
        }

        if (viewModel.getVipRequired()?.first == false) {
            safeUpdateUI { binding.player.updateIsSupportCast(isSupport = true) }
            viewModel.getDataDetail()?.let { details ->
                // Update bookmark
                if (currentDuration > 0L) {
                    updateBookMark()
                }

                pairingConnection.sendEventPlayContent(
                    id = details.blockContent.id,
                    highlightId = details.blockContent.id,
                    refId = details.blockContent.refId,
                    appId = details.blockContent.appId,
                    title = details.blockContent.titleVietnam,
                    image = details.blockContent.horizontalImage,
                    indexChapter = viewModel.currentEpisode()?.id ?: "0",
                    isPlay = "1",
                    isLive = "0",
                    timeWatched = userHistory?.startPosition?.toString() ?: "0", // Send current duration
                    type = "vod",
                    token = sharedPreferences.accessToken(),
                    userId = sharedPreferences.userId(),
                    userPhone = sharedPreferences.userPhone(),
                    username = sharedPreferences.displayName(),
                    deepLink = details.blockContent.webUrl,
                    isFollow = viewModel.getFollow(),
                    currentAutoProfile = viewModel.currentEpisode()?.autoProfile ?: "",
                    autoProfiles = kotlin.run {
                        try {
                            details.blockEpisode.episodes.map { episode -> episode.autoProfile }
                        } catch (e: Exception) {
                            listOf()
                        }
                    },
                    bookmarkInfo = BookmarkInfo(
                        bookmark = BookmarkItem(
                            logId = "111",
                            appId = TrackingUtil.currentAppId,
                            appName = TrackingUtil.currentAppName,
                            screen = if (binding.player.getPlayerConfig().isPlayPreview) "PingPreview" else if (viewModel.currentEpisode()?.isTrailer != 1) "PingVOD" else "PingTrailer",
                            subMenuId = TrackingUtil.blockId,
                            isLastEpisode = viewModel.currentEpisode()?.isLatest ?: "",
                            totalEpisode = details.blockContent.addedEpisodeTotal,
                            audio = "",
                            subtitle = "",
                            platform = "Android - " + platform.name,
                            device = "Mobile",
                            deviceId = kotlin.run {
                              try {
                                  Util.deviceId(requireContext())
                              } catch (e: Exception) {
                                  ""
                              }
                            },
                            deviceName = Util.getDeviceName(),
                            modelName = Util.model(),
                            deviceManufacture = Util.manufacturer(),
                            userId = sharedPreferences.userId(),
                            FPTPlayVersion = BuildConfig.VERSION_NAME,
                            environment = if (com.xhbadxx.projects.module.core.BuildConfig.API_TYPE == 3) "Product" else "Stag",
                            osVersion = Util.os(),
                            userPhone = sharedPreferences.userPhone(),
                            refItemId = TrackingUtil.contentPlayingInfo.refId,
                            refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                            refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                        ),
                        domain = "https://logs-ott.fbox.fpt.vn/topics/mobile-ott/",
                        episode = details.blockEpisode.episodes.mapIndexed { index, episode -> EpisodeItem(id = episode.realEpisodeId, index = index.toString(), timeWatched = episode.timeWatched, duration = episode.duration) }
                    )
                )
            }
        } else {
            safeUpdateUI {
//                pairingConnection.setSessionRunning(isRunning = false)
                binding.player.updateIsSupportCast(isSupport = false)
            }
        }
    }

    private fun sendNextChapterMessage(indexAction: String) {
        if (viewModel.currentEpisode()?.isTrailer == 1) {
            safeUpdateUI {
//                pairingConnection.setSessionRunning(isRunning = false)
                binding.player.updateIsSupportCast(isSupport = false)
            }
            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                navHostFragment = activity?.findNavHostFragment(),
                onStopCastAndNavigate = {
                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                }
            )
            return
        }
        if (isAirlineLayout()) {
//            pairingConnection.setSessionRunning(isRunning = false)
            binding.player.updateIsSupportCast(isSupport = false)
            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                navHostFragment = activity?.findNavHostFragment(),
                onStopCastAndNavigate = {
                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                }
            )
            return
        }
        viewModel.getDataDetail()?.let { details ->
            safeUpdateUI { binding.player.updateIsSupportCast(isSupport = true) }
            pairingConnection.sendEventChangeChapter(
                id = details.blockContent.id,
                refId = details.blockContent.refId,
                indexChapter = viewModel.currentEpisode()?.id ?: "0",
                timeWatched = userHistory?.startPosition?.toString() ?: viewModel.currentEpisode()?.timeWatched ?: "0",
                indexAction = indexAction
            )
        }
    }


    private fun addPairingControlListener() {
        pairingConnection.apply {
            addConnectionListener(connectionListener)
            addCommunicationListener(communicationListener)
        }
    }

    private fun removePairingControlListener() {
        pairingConnection.apply {
            removeConnectionListener(connectionListener)
            removeCommunicationListener(communicationListener)
        }
    }


    private val connectionListener = object : FConnectionManager.FConnectionListener {
        override fun onConnectError(errorCode: Int, message: String) {
            removeIntervalUpdateProgress()
            if (errorCode != 2) {
                currentDuration = pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                updateBookMark()
                getStream(episode = viewModel.currentEpisode(), delayToPlay = false, isGetBookmark = false) // Cast connection error
            }
            runOnUiThread {
                binding.player.onShowThumb(isShow = false)
                pairingConnection.showToast(message = if (errorCode == 2) binding.root.context.getString(R.string.pairing_cast_description_connect_error_disable_cast, pairingConnection.getReceiverName().truncateString()) else binding.root.context.getString(R.string.pairing_cast_title_connect_error))
                //
                triggerStartAds()
                //
            }
        }

        override fun onConnectSuccess(message: String) {
            runOnUiThread {
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_success, pairingConnection.getReceiverName()))
                sendPlayContentMessage()
                binding.player.run {
                    stopServices()
                    stopPlayerLocal(force = true, isClearRequest = true)
                    onShowThumb(isShow = true, url = viewModel.getDataDetail()?.blockContent?.horizontalImage?.ifBlank { viewModel.getDataDetail()?.blockContent?.verticalImage ?: "" } ?: "")
                }

                //
                triggerStopAds()
                //
            }

            // Send tracking
            sendTracking(logId = "516", event = "CastToDevice")
        }

        override fun onDisconnectError(message: String) {
        }

        override fun onDisconnectSuccess(message: String) {
            runOnUiThread {
                removeIntervalUpdateProgress()
                currentDuration = if (viewModel.currentEpisode()?.isTrailer == 1) { 0L } else {
                    pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                }
                updateBookMark()
                viewModel.currentEpisode()?.isResetPosition = viewModel.currentEpisode()?.isTrailer == 1
                getStream(episode = viewModel.currentEpisode(), delayToPlay = false, isGetBookmark = false) // Cast Disconnect

                binding.player.run {
                    onShowThumb(isShow = false)
                }
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_disconnect_success, pairingConnection.getReceiverName()))
                //
                triggerStartAds()
            }
        }
    }


    private val communicationListener = object : FConnectionManager.FCommunicationListener {
        override fun onMessage(message: ObjectReceiver) {
            runOnUiThread {
                when (message) {
                    is FBoxObjectReceiver -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }

                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            if (result == "-1") { // Box Iptv (ref_id=-1) => Not stop session, continue play current content (if exist)
                                                //
                                                hideWarningDialog()
                                                //
                                                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                    navHostFragment = activity?.findNavHostFragment(),
                                                    message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message_with_error_code, "#CP001"),
                                                    onCancel = {
                                                        findNavController().popBackStack()
                                                    },
                                                    onStopCastAndNavigate = {
                                                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                    },
                                                    allowCancelByOutside = false
                                                )
                                            } else {
                                                if (!pairingConnection.isProcessHandleException()) {
                                                    //
                                                    hideWarningDialog()
                                                    //
                                                    safeUpdateUI {
                                                        pairingConnection.setSessionRunning(isRunning = false)
                                                        binding.player.updateIsSupportCast(isSupport = false)
                                                    }
                                                    MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                        navHostFragment = activity?.findNavHostFragment(),
                                                        message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                        onStopCastAndNavigate = {
                                                            MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                        }
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveChangeChapter -> {
                                // Call getStream, but don't send event to remote
                                (message.data as? ReceiveChangeChapter)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)

                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveGetStatusWatching -> {
                                (message.data as? ReceiveGetStatusWatching)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            // Check have change episode, when app from background -> foreground
                                            if (pairingConnection.getRemoteData()?.episodeIndex != viewModel.currentEpisode()?.id) {
                                                startIntervalUpdateProgress()
                                                onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)
                                                if (_binding != null) {
                                                    binding.player.triggerInitCheckSkipIntroAndCredits()
                                                }
                                            }

                                            if (isDetectDuration) return@run
                                            if (Utils.convertStringToLong(data?.duration, 0L) > 0L) {
                                                isDetectDuration = true

                                                if (_binding != null) {
                                                    binding.player.triggerUpdateShowIntroCredit()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveSeekTime -> {
                                (message.data as? ReceiveSeekTime)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            if (_binding != null) {
                                                binding.player.triggerUpdateShowIntroCredit()
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            //
                                            currentDuration = pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                                            updateBookMark()
                                            //
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            //
                                            when (data?.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    hideWarningDialog()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else -> {}
                        }
                    }
                    is FSamsungObjectReceiver,
                    is FSamsungObjectReceiverExternal -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }

                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            //
                                            hideWarningDialog()
                                            //
                                            safeUpdateUI {
                                                pairingConnection.setSessionRunning(isRunning = false)
                                                binding.player.updateIsSupportCast(isSupport = false)
                                            }
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            is ReceiveChangeChapter -> {
                                // Call getStream, but don't send event to remote
                                (message.data as? ReceiveChangeChapter)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)  // Update timeWatched

                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveGetStatusWatching -> {
                                (message.data as? ReceiveGetStatusWatching)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            // Check have change episode, when app from background -> foreground
                                            if (pairingConnection.getRemoteData()?.episodeIndex != viewModel.currentEpisode()?.id) {
                                                startIntervalUpdateProgress()
                                                onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)
                                                if (_binding != null) {
                                                    binding.player.triggerInitCheckSkipIntroAndCredits()
                                                }
                                            }

                                            if (isDetectDuration) return@run
                                            if (Utils.convertStringToLong(data?.duration, 0L) > 0L) {
                                                isDetectDuration = true

                                                if (_binding != null) {
                                                    binding.player.triggerUpdateShowIntroCredit()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveSeekTime -> {
                                (message.data as? ReceiveSeekTime)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        startIntervalUpdateProgress()
                                        if (isSessionRunning()) {
                                            if (_binding != null) {
                                                binding.player.triggerUpdateShowIntroCredit()
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            //
                                            currentDuration = pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                                            updateBookMark()
                                            //
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            //
                                            when (data?.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    hideWarningDialog()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    is FAndroidTVObjectReceiver -> {
                        when (message.data) {
                            is FAndroidTVPlayContentResponse -> {
                                (message.data as? FAndroidTVPlayContentResponse)?.run {
                                    if (_binding != null) {
                                        binding.player.triggerInitCheckSkipIntroAndCredits()
                                    }

                                    isDetectDuration = false
                                }
                            }
                            is FAndroidTVChangeChapterResponse -> {
                                (message.data as? FAndroidTVChangeChapterResponse)?.run {
                                    if (_binding != null) {
                                        binding.player.triggerInitCheckSkipIntroAndCredits()
                                    }

                                    isDetectDuration = false
                                }
                            }
                            is FAndroidTVUpdateProgressResponse -> {
                                (message.data as? FAndroidTVUpdateProgressResponse)?.run {
                                    pairingConnection.getRemoteData()?.remotePlayer?.let {

                                        if (requestCheckShowIntroCredit) {
                                            if (isSessionRunning()) {
                                                if (_binding != null) {
                                                    requestCheckShowIntroCredit = false
                                                    binding.player.triggerUpdateShowIntroCredit()
                                                    binding.player.triggerInitCheckSkipIntroAndCredits()
                                                }
                                            }
                                        }

                                        if (it.currentDuration > 0L && it.totalDuration > 0L && (it.totalDuration - 1000L..it.totalDuration + 1000L).contains(it.currentDuration)) {
                                            triggerShowNextRecommendation()
                                        }

                                        if (!isDetectDuration) {
                                            if (isSessionRunning()) {
                                                if (it.totalDuration > 0L) {
                                                    isDetectDuration = true

                                                    if (_binding != null) {
                                                        binding.player.triggerUpdateShowIntroCredit()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is FAndroidTVMetadataUpdated -> {
                                (message.data as? FAndroidTVMetadataUpdated)?.let {
                                    if (it.episodeId.toString() != viewModel.currentEpisode()?.id && isSessionRunning() && isMyContentId(it.id)) {
                                        // Don't need handle,because FAndroidTVCustomChangeChapterResponse processed this case
//                                        onChangeChapterFromRemote(
//                                            indexChapter = it.episodeId.toString(),
//                                            timeWatched = null
//                                        ) // Update timeWatched
//
//                                        if (_binding != null) {
//                                            binding.player.triggerInitCheckSkipIntroAndCredits()
//                                        }
//                                        //
//                                        pairingConnection.resetRemoteData()
                                    }
                                }
                            }
                            is FAndroidTVCustomMessageResponse -> {
                                (message.data as? FAndroidTVCustomMessageResponse)?.let {
                                    if (isSessionRunning()) {
                                        if (it.message == "seek") {
                                            requestCheckShowIntroCredit = true
                                        }
                                    } else {
                                        //
                                        currentDuration = pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                                        updateBookMark()
                                        //
                                        removeIntervalUpdateProgress()
                                        if (shouldShowStopSessionToast()) {
                                            runOnUiThread {
                                                pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                            }
                                        }
                                    }
                                }
                            }
                            is FAndroidTVCustomPlayContentResponse -> {
                                (message.data as? FAndroidTVCustomPlayContentResponse)?.run {
                                    if (result.isFailure()) {
                                        if (isSessionRunning()) {
                                            //
                                            hideWarningDialog()
                                            //
                                            safeUpdateUI {
                                                pairingConnection.setSessionRunning(isRunning = false)
                                                binding.player.updateIsSupportCast(isSupport = false)
                                            }
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            is FAndroidTVCustomChangeChapterResponse -> {
                                (message.data as? FAndroidTVCustomChangeChapterResponse)?.run {
                                    if (result.isSuccess() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)  // Update timeWatched

                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }
                                    }
                                }
                            }
                            is FAndroidTVCustomExceptionMessageResponse -> {
                                (message.data as? FAndroidTVCustomExceptionMessageResponse)?.run {
                                    if (result.isSuccess()) {
                                        if (isSessionRunning()) {
                                            //
                                            when (data?.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    hideWarningDialog()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is FAndroidTVCustomStopSessionResponse -> {
                                (message.data as? FAndroidTVCustomStopSessionResponse)?.run {
                                    if (result.isSuccess()) {
                                        if (!isSessionRunning()) {
                                            //
                                            currentDuration = pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                                            updateBookMark()
                                            //
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    is FAndroidTVObjectReceiverExternal -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }

                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            //
                                            hideWarningDialog()
                                            //
                                            safeUpdateUI {
                                                pairingConnection.setSessionRunning(isRunning = false)
                                                binding.player.updateIsSupportCast(isSupport = false)
                                            }
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            is ReceiveChangeChapter -> {
                                // Call getStream, but don't send event to remote
                                (message.data as? ReceiveChangeChapter)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                            onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)

                                            if (_binding != null) {
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveGetStatusWatching -> {
                                (message.data as? ReceiveGetStatusWatching)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            // Check have change episode, when app from background -> foreground
                                            if (pairingConnection.getRemoteData()?.episodeIndex != viewModel.currentEpisode()?.id) {
                                                startIntervalUpdateProgress()
                                                onChangeChapterFromRemote(indexChapter = data?.indexChapter, timeWatched = data?.timeWatched)
                                                if (_binding != null) {
                                                    binding.player.triggerInitCheckSkipIntroAndCredits()
                                                }
                                            }

                                            if (isDetectDuration) return@run
                                            if (Utils.convertStringToLong(data?.duration, 0L) > 0L) {
                                                isDetectDuration = true

                                                if (_binding != null) {
                                                    binding.player.triggerUpdateShowIntroCredit()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveSeekTime -> {
                                (message.data as? ReceiveSeekTime)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        startIntervalUpdateProgress()
                                        if (isSessionRunning()) {
                                            if (_binding != null) {
                                                binding.player.triggerUpdateShowIntroCredit()
                                                binding.player.triggerInitCheckSkipIntroAndCredits()
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            //
                                            currentDuration = pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
                                            updateBookMark()
                                            //
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            //
                                            when (data?.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    hideWarningDialog()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else -> {}
                        }
                    }
                    else -> {}
                }
            }
        }

        override fun onSendMessageCallback(
            messageType: Int,
            isSuccess: Boolean,
            errorCode: Int,
            errorMessage: String
        ) {

        }
    }


    //region Handle progress countdown
    private var numGetTrackRequest = 0
    private var maxTrackRequest = 3


    private var handlerProgress: Handler? = null
    private var runnableProgress = Runnable {
        intervalUpdateProgress()
    }

    private fun startIntervalUpdateProgress() {
        isDetectDuration = false
        numGetTrackRequest = 0
        removeIntervalUpdateProgress()
        if (handlerProgress == null) {
            Looper.getMainLooper()?.run {handlerProgress = Handler(this) }
        }
        handlerProgress?.post(runnableProgress)
    }
    private fun intervalUpdateProgress() {
        handlerProgress?.run {
            pairingConnection.getRemoteData()?.let {
                if (it.remotePlayer.state == RemotePlayerState.PLAY) {
                    pairingConnection.setRemoteData(data = it.apply {
                        remotePlayer.currentDuration += 1000L

                        if (remotePlayer.currentDuration > 0L && remotePlayer.totalDuration > 0L && (remotePlayer.totalDuration - 1000L..remotePlayer.totalDuration + 1000L).contains(remotePlayer.currentDuration)) {
                            triggerShowNextRecommendation()
                            removeIntervalUpdateProgress()
                        }
                    })
                }

                if (it.remotePlayer.tracks == null
                    && numGetTrackRequest < maxTrackRequest
                    && (it.remotePlayer.currentDuration % (5 * 1000L) == 0L))
                {
                    numGetTrackRequest += 1
                    triggerGetStatusWatching()
                }
                else if (it.remotePlayer.currentDuration > 0L && it.remotePlayer.currentDuration % (30 * 1000L) == 0L) {
                    triggerGetStatusWatching()
                }
            } ?: kotlin.run {
                pairingConnection.sendEventGetBoxInfo()
            }
            postDelayed(runnableProgress, 1000)
        }
    }

    private fun removeIntervalUpdateProgress() {
        handlerProgress?.removeCallbacks(runnableProgress)
        handlerProgress = null
    }

    private fun triggerGetStatusWatching() {
        viewModel.getDataDetail()?.let { details ->
            pairingConnection.sendEventGetStatusWatching(id = details.blockContent.id, refId = details.blockContent.refId)
        }
    }

    private fun triggerShowNextRecommendation() {
        runOnUiThread {
            _binding?.let { binding.player.triggerShowNextRecommendation() }
        }
    }
    //endregion

    private fun onChangeChapterFromRemote(indexChapter: String?, timeWatched: String?) {

        <EMAIL>()
        <EMAIL>()
        <EMAIL>()

        val position = Utils.convertStringToInt(indexChapter, 0)
//        val timeWatched = Utils.convertStringToLong(indexChapter, 0L)
        //TODO: Update bookmark position
        userProfile.updateEpisodeIndex(index = position)
        userProfile.updateEpisodeId(id = position.toString())

        getStream(episode = findEpisode(position), delayToPlay = false, isSendEventToRemote = false) // Change chapter from remote
    }

    private fun updateIntroCreditData(episode: Details.Episode, stream: Stream?) {
        binding.player.onShowThumb(isShow = true, url = details?.blockContent?.horizontalImage?.ifBlank { details?.blockContent?.verticalImage ?: "" } ?: "")
        vodPlayerInfo.apply {
            this.episode = episode
            this.isPlayingTrailer = false
        }
        binding.player.preparePlayerVODCast(
            playerConfig = PlayerConfigBuilder()
                .setEnableReportPlayer(false)
                .setSupportAudioMode(isAudioMode = stream?.isAudio ?: false, backgroundAudioOverlay = stream?.audioBackgroundImageUrl ?: "")
                .setEnableReportPlayer(details?.blockContent?.enableReport?:false)
                .build(),
            userProfile = userProfile,
            details = details,
            episode = episode,
            data = stream,
            delayToPlay = false,
            isPlayingTrailer = false,
            playlistName = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) viewModel.getPlaylist()?.title?: "" else "",
            isResetPlayingPosition = isResetPlayingPosition ?: false,
            object : PlayerHandler.OnEvents {}
        )
    }

    private fun isMyContentId(remoteId: String?) : Boolean {
        if (remoteId == null) return false
        return remoteId == viewModel.getDataDetail()?.blockContent?.id
    }

    private fun syncDetailUIForCast() {
        notifyPlayerChange(episode = viewModel.currentEpisode())
    }

    /**
     * Trigger stop all ads when cast connect
     */
    private fun triggerStopAds() {
        stopLogoAds()
        stopTvcAds()
    }

    /**
     * Trigger startads when cast connect
     */
    private fun triggerStartAds() {
        // Don't need do anything. Because when triggerStopAds, we don't stop instream ads. It will
        // auto trigger play in prepare
    }
    // endregion Pairing Control

    //region Handle User Realtime Playing
    private fun setupUserRealtimePlayingTracker() {
        if(_binding?.player?.isPlayOffline() == true) {
            userRealtimePlayingTracker.setData(contentId = viewModel.getCurDataOffline()?.movieId ?: "", extraId = (viewModel.getCurDataOffline()?.chapterIdx ?: 0).toString())
        } else {
            if (_binding?.player?.getPlayerConfig()?.isPlayPreview == true) {
                val vodId = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) { viewModel.currentEpisode()?.vodId.orEmpty() } else { viewModel.getId() }
                userRealtimePlayingTracker.setData(contentId = vodId, extraId = viewModel.currentEpisode()?.id.orEmpty())
            } else {
                if (viewModel.getVipRequired()?.first == false) {
                    val vodId = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) { viewModel.currentEpisode()?.vodId.orEmpty() } else { viewModel.getId() }
                    userRealtimePlayingTracker.setData(contentId = vodId, extraId = viewModel.currentEpisode()?.id.orEmpty())
                }
            }
        }
    }

    private val userRealtimePlayingTrackerListener = object : UserRealtimePlayingTracker.UserRealtimePlayingTrackerListener {
        override fun onRealtimePlayingChanged(timeWatched: Long) {
            _binding?.let {
                currentDuration = binding.player.currentDuration()
                totalDuration = binding.player.totalDuration()
            }
            Logger.d("$TAG onRealtimePlayingChanged => Time watched : $timeWatched | CurrentDuration: $currentDuration")
        }

        override fun onRealtimePlayingAtCheckpoint(timeWatched: Long, checkPoint: Long) {}
        override fun getCheckpointForRealtimeTracking(): List<Long> {
            return listOf()
        }
    }
    //endregion

    //region Age Restriction
    private fun setupAgeRestrictionHandler() {
        _binding?.run {
            if (player.isPlayOffline()) {
                val currentDataOffline = viewModel.getCurDataOffline()
                ageRestrictionHandler.initAgeRestrictionsData(
                    vodId = currentDataOffline?.movieId ?: "",
                    episodeId = (currentDataOffline?.chapterIdx ?: 0).toString(),
                    contentDuration = _binding?.player?.totalDuration() ?: 0L,
                    value = currentDataOffline?.maturityValue ?: "",
                    prefix = currentDataOffline?.maturityPrefix ?: "",
                    position = currentDataOffline?.maturityPosition ?: "",
                    advisories = currentDataOffline?.maturityAdvisories ?: "",
                    startTime = Utils.convertStringToLong(text = MainApplication.INSTANCE.appConfig.maturityRating.startTime, 3L) * 1000,
                    duration = Utils.convertStringToLong(text = MainApplication.INSTANCE.appConfig.maturityRating.duration, 10L) * 1000
                )
            } else {
                val vodId = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) { viewModel.currentEpisode()?.vodId.orEmpty() } else { viewModel.getId() }
                ageRestrictionHandler.initAgeRestrictionsData(
                    vodId = vodId,
                    episodeId = viewModel.currentEpisode()?.id.orEmpty(),
                    contentDuration = _binding?.player?.totalDuration() ?: 0L,
                    value = details?.maturityRating?.value ?: "",
                    prefix = details?.maturityRating?.prefix ?: "",
                    position = details?.maturityRating?.position ?: "",
                    advisories = details?.maturityRating?.advisories ?: "",
                    startTime = Utils.convertStringToLong(text = MainApplication.INSTANCE.appConfig.maturityRating.startTime, 3L) * 1000,
                    duration = Utils.convertStringToLong(text = MainApplication.INSTANCE.appConfig.maturityRating.duration, 10L) * 1000
                )
            }
        }
    }

    private val ageRestrictionHandlerListener = object : AgeRestrictionHandler.AgeRestrictionListener {
        override fun onShow(checkPoint: Long, value: String, position: String, advisories: String, duration: Long) {
            _binding?.player?.showAgeRestriction(checkPoint, value, position, advisories, duration)
            Logger.d("$TAG => AgeRestrictionHandlerListener => onShow => checkpoint: $checkPoint")
        }

        override fun onStop() {
            Logger.d("$TAG => AgeRestrictionHandlerListener => onStop")
            clearAgeRestriction()
        }
    }

    private fun clearAgeRestriction() {
        _binding?.let {
            it.player.hideAgeRestriction()
            it.player.clearAgeRestrictionData()
        }
    }
    //endregion

    //region Situation Warning Content Handler
    private fun setupSituationWarningHandler() {
        _binding?.let { viewBinding ->
            if (viewBinding.player.isPlayOffline()) {
                situationWarningHandler.initData(data = viewModel.getCurDataOffline()?.warnings?.map {
                    Stream.WarningScenario(content = it.content, from = it.from, to = it.to)
                } ?: listOf())
            } else {
                situationWarningHandler.initData(data = vodPlayerInfo.stream?.warnings ?: listOf())
            }
        }
    }

    private val situationWarningListener = object : SituationWarningHandler.SituationWarningListener {
        override fun getPlayCurrentDuration(): Long {
            return _binding?.player?.currentDuration() ?: 0L
        }
        override fun onShow(content: Stream.WarningScenario) {
            _binding?.let {
                Logger.d("$TAG situationWarningListener onShow = ${content}")
                it.player.showSituationWarning(content = content.content)
            }
        }

        override fun onHide() {
            _binding?.let {
                Logger.d("$TAG situationWarningListener onHide")
                it.player.hideSituationWarning()
            }
        }
    }
    //endregion

    fun getRealTimePlayingForLog(): String{
        val vodId = if (viewModel.currentEpisode()?.isItemOfPlaylist == true) { viewModel.currentEpisode()?.vodId.orEmpty() } else { viewModel.getId() }
        return userRealtimePlayingTracker.getRealTimePlaying(contentId = vodId, extraId = viewModel.currentEpisode()?.id.orEmpty()).toString()
    }

    //region Handle Player Error
    private fun setupPlayerRetryHandler(channelId: String, streamId: String) {
        playerRetryHandler.setupData(channelId = channelId, streamId = streamId)
    }

    private fun setPlayerRetryHandlerListener() {
        playerRetryHandler.setListener(listener = playerRetryHandlerListener)
    }

    private fun removePlayerRetryHandlerListener() {
        playerRetryHandler.removeListener()
    }

    private val playerRetryHandlerListener = object : PlayerRetryHandler.PlayerRetryHandlerListener {
        override fun getRealtimePlayingMs(): Long {
            return userRealtimePlayingTracker.getRealtimePlaying() * 1000L
        }

        override fun onRetryGetStream() {
            tryRetry()
        }

        override fun onShowPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
            showPlayerError(shouldCountDown = shouldCountDown, countdownTimeMs = countdownTimeMs, code = code, responseCode = responseCode)
        }

        override fun onSendTrackingPlayerError(
            isAutoRetry: Boolean,
            screen: String,
            errorCode: String,
            errorMessage: String
        ) {
            sendTrackingPlayerError(isAutoRetry = isAutoRetry, screen = screen, errorCode = errorCode, errorMessage = errorMessage)
        }

        override fun onClearAudioUserHistory() {
            sharedPreferences.saveAudioVod("")
        }
    }

    private fun showPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
        if (activity.isInPiPMode()) {
            playerPiPRetryHandler.startRetryFlow(
                processData = PlayerPiPRetryHandler.PlayerRetryData(
                    shouldCountDown = shouldCountDown,
                    countdownTimeMs = countdownTimeMs,
                    code = code,
                    responseCode = responseCode
                ),
                onCompleted = {
                    tryRetry()
                }
            )
        } else {
            if (shouldCountDown) {
                showPlayerErrorWithRetryDialog(message = getErrorMessage(code, responseCode))
                stopCountDownTimerRetry()
                startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
            } else {
                showPlayerErrorDialog(message = getErrorMessage(code, responseCode))
            }
        }
    }

    //region Retry Task
    private fun tryRetry(delay: Long = 0L) {
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                binding.player.stop(force = true)
            }
            PlayerView.PlayingType.Cast -> {}
        }
        viewModel.saveRetryStreamInfo(viewModel.createRetryStreamInfo(viewModel.getDataDetail()?.id ?: "", viewModel.currentEpisode()?.id ?: ""))
        getStream( // retry
            episode = getEpisodeToPlay(),
            delayToPlay = false,
            isSendLog = false,
            isGetBookmark = false,
            delay = delay,
            isRetry = true
        )
    }

    private fun getErrorMessage(code: Int, responseCode: Int): String {
        val message =
            MainApplication.INSTANCE.appConfig.msgPlayerError.ifBlank {
                <EMAIL>().getString(R.string.msg_player_error)
            }
        return if(responseCode != UnValidResponseCode) {
            "$message (Mã lỗi ${code}-${responseCode})"

        } else {
            "$message (Mã lỗi $code)"
        }
    }

    private fun startCountDownTimerRetry(timeCountDown: Long) {
        if (countDownTimerRetry == null) {
            countDownTimerRetry = object : CountDownTimer(timeCountDown * 1000 + 1000, 1000) {
                override fun onTick(millis: Long) {
                    if (playerRetryDialog?.isShow() == true) {
                        playerRetryDialog?.updateTextConfirm("${<EMAIL>(R.string.all_retry)} (${millis / 1000})")
                    }
                }

                override fun onFinish() {
                    stopCountDownTimerRetry()
                    playerRetryDialog?.dismissAllowingStateLoss()
                    tryRetry()
                }
            }
            countDownTimerRetry?.start()
        }
    }

    private fun stopCountDownTimerRetry() {
        if (countDownTimerRetry != null) {
            countDownTimerRetry?.cancel()
            countDownTimerRetry = null
        }
    }
    //endregion

    //region Tracking player retry
    private fun sendTrackingPlayerError(
        isAutoRetry: Boolean,
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        sendTracking(
            logId = TrackingConstants.EVENT_LOG_ID_ERROR_STREAM_VOD,
            screen = screen,
            event = "PlaybackError",
            errorCode = errorCode,
            errorMessage = errorMessage
        )
        if (!isAutoRetry) {
            sendTrackingShowPopupRetry(screen, errorCode, errorMessage)
        }
    }

    private fun sendTrackingShowPopupRetry(
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        sendTracking(
            logId = "17",
            screen = screen,
            event = "Error",
            errorCode = errorCode,
            errorMessage = errorMessage,
            issueId = TrackingUtil.createIssueId(),
        )
    }
    //endregion

    //endregion

    //region Handle Get Stream Error
    private fun setPlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = playerGetStreamRetryListener)
    }

    private fun removePlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = null)
    }

    private fun resetGetStreamRetryStep() {
        playerGetStreamRetryHandler.resetGetStreamRetryStep()
    }

    private fun notifyGetStreamError(errorMessage: String) {
        playerGetStreamRetryHandler.notifyGetStreamError(errorMessage = errorMessage)
    }

    private val playerGetStreamRetryListener = object : PlayerGetStreamRetryHandler.PlayerGetStreamRetryListener {
        override fun onRetryGetStream() {
            tryRetry(delay = PlayerGetStreamRetryHandler.FIRST_TIME_DELAY_TIMES_MS)
        }

        override fun onShowGetStreamError(
            shouldCountDown: Boolean,
            countdownTimeMs: Long,
            errorMessage: String
        ) {
            if (activity.isInPiPMode()) {
                playerPiPRetryHandler.startRetryFlow(
                    processData = PlayerPiPRetryHandler.PlayerGetStreamRetryData(
                        shouldCountDown = shouldCountDown,
                        countdownTimeMs = countdownTimeMs,
                        errorMessage = errorMessage
                    ),
                    onCompleted = {
                        tryRetry()
                    }
                )
            } else {
                if (shouldCountDown) {
                    showPlayerErrorWithRetryDialog(message = errorMessage)
                    stopCountDownTimerRetry()
                    startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
                } else {
                    showPlayerErrorDialog(message = errorMessage)
                }
            }
        }

        override fun onSendTrackingGetStreamError(
            isAutoRetry: Boolean,
            screen: String,
            errorMessage: String
        ) {
            sendTracking(
                logId = "17",
                screen = screen,
                event = "Error",
                errorCode = AppErrorConstants.GET_VOD_STREAM_CODE,
                errorMessage = errorMessage,
                issueId = TrackingUtil.createIssueId(),
            )
        }

    }

    //endregion

    //region Preview
    private fun mapLocalPreviewHistory() : History? {
        if (!binding.player.getPlayerConfig().isPlayPreview) {
            val localPreviewHistory = viewModel.getLocalPreviewHistory()
            viewModel.saveLocalPreviewHistory(history = null)
            if (viewModel.getId() == localPreviewHistory?.movieId) {
                return if (userHistory?.startPosition != 0L) {
                    userHistory
                } else if (userHistory?.episodeId == localPreviewHistory.episodeId) {
                    userHistory?.copy(episodeId = localPreviewHistory.episodeId, startPosition = localPreviewHistory.startPosition)
                } else {
                    userHistory
                }
            }
        }
        return userHistory
    }
    //endregion

}