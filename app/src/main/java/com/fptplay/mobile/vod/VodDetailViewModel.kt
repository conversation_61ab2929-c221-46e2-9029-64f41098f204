package com.fptplay.mobile.vod

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.database.DataBase
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.global.SourcePlayObject
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.models.NextActionEvent
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.comment_v2.data.CommentInfo
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.handler.drm.DrmApi
import com.fptplay.mobile.vod.data.BuyPackageGuide
import com.fptplay.mobile.vod.data.VodPreviewPlayerInfo
import com.tear.modules.player.util.PlayerControlView
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.History
import com.xhbadxx.projects.module.domain.entity.fplay.common.PlayerSpeed
import com.xhbadxx.projects.module.domain.entity.fplay.common.Status
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.drm.DrmKey
import com.xhbadxx.projects.module.domain.entity.fplay.drm.Ping
import com.xhbadxx.projects.module.domain.entity.fplay.drm.PingStreamV2
import com.xhbadxx.projects.module.domain.entity.fplay.game.gamevod.GameVOD
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.vod.*
import com.xhbadxx.projects.module.domain.repository.fplay.*
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject
import kotlin.system.measureTimeMillis

@HiltViewModel
class VodDetailViewModel @Inject constructor(
    private val savedState: SavedStateHandle,
    private val vodRepository: VodRepository,
    private val drmRepository: DrmRepository,
    private val commonRepository: CommonRepository,
    private val momentRepository: MomentRepository,
    private val sharedPreferences: SharedPreferences,
) : BaseViewModel<VodDetailViewModel.VodDetailIntent, VodDetailViewModel.VodDetailState>(), DrmApi {

    private val getStreamScope = CoroutineScope(Dispatchers.IO)
    private var getStreamJob: Job? = null

    private var detail: Details? = null
    var ratingData:RatingData? = null
        private set
    private var collectionVideoOffline: CollectionVideoTaskItem? = null
    private var dataCurOffline: VideoTaskItem? = null
    private var actor: List<People>? = null
    private var playerBitrates : List<PlayerControlView.Data.Bitrate>? = null
    private var playerTracks : List<PlayerControlView.Data.Track>? = null
    private var isVipRequired : Pair<Boolean, RequiredVip?>? = null
    private var isLoginRequired : Triple<Boolean, RequiredLogin?, String> ?= null // (isRequired, requiredLogin, message)
    private var videoPlaylist: VodPlaylist?= null
    private var currentEpisode: Details.Episode? = null
    private var gameVodData: GameVOD.GameVODData?= null
    private var listMoment: List<StructureItem>? = null
    var listQualityDownload: MutableList<String> = ArrayList()
    private var localPreviewHistory: History? = null

    private var nextActionEvent: NextActionEvent? = null

    private var isPauseAfterSeek = false

    private var fileHash: String = ""

    private var _isFullScreen = MutableLiveData<Pair<Boolean, Boolean>?>() // Pair<isFullscreen, isLandscape>
    val isFullScreen get() = _isFullScreen

    private var _initPlayer = MutableLiveData<Boolean?>()
    val initPlayer get() = _initPlayer

    private var _initOfflinePlayer = MutableLiveData<Boolean?>()
    val initOfflinePlayer get() = _initOfflinePlayer

    private var _playVod = MutableLiveData<Pair<String, String>?>() // <id, type> ......... Type: isPlayTrailer, session, related
    val playVod get() = _playVod

    private var _playerChanged = MutableLiveData<Details.Episode?>()
    val playerChanged get() = _playerChanged

    private var _playEpisode = MutableLiveData<Details.Episode?>()
    val playEpisode get() = _playEpisode

    private var _playTrailer = MutableLiveData<Details.Episode?>()
    val playTrailer get() = _playTrailer

    private var _playRequiredVipTrailer = MutableLiveData<String?>()
    val playRequiredVipTrailer get() = _playRequiredVipTrailer

    private var _playPreview = MutableLiveData<VodPreviewPlayerInfo?>()
    val playPreview get() = _playPreview

    private var _preparePlayer = MutableLiveData<VodDetailState.PreparePlayer?>()
    val preparePlayer get() = _preparePlayer

    private var _prepareOfflinePlayer = MutableLiveData<VodDetailState.PrepareOfflinePlayer?>()
    val prepareOfflinePlayer get() = _prepareOfflinePlayer

    private var _downloadClick = MutableLiveData<VodDetailState.OnDownloadClick?>()
    val downloadClick get() = _downloadClick

    private var _updateDownloadState = MutableLiveData<VodDetailState.OnDownloadState?>()
    val updateDownloadState get() = _updateDownloadState

    private var _updateDownloadProcess = MutableLiveData<VodDetailState.OnDownloadProcess?>()
    val updateDownloadProcess get() = _updateDownloadProcess

    private var savablePlayerSpeed: Float = 1f

    private var buyPackageGuide: BuyPackageGuide? = null

    override fun dispatchIntent(intent: VodDetailIntent) {
        safeLaunch {
            when (intent) {
                is VodDetailIntent.GetDetail -> {
                    getVodInfoInParallel(intent).collect {
                        _state.value = it
                    }
                }
                is VodDetailIntent.GetDetailOffline -> {
                    VideoDownloadManager.instance.getListChapterWithWarningInMovie(intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.ResultDetailOffline(
                                isCached = isCached,
                                data = data,
                                currentId = intent.id
                            )
                        }
                    }
                }
                is VodDetailIntent.GetStream -> {
                    getStreamJob?.cancel()
                    if (intent.delay != 0L) {
                        getStreamJob = getStreamScope.launch {
                            delay(intent.delay)
                            getStream(intent = intent)
                        }
                    } else {
                        getStream(intent = intent)
                    }
                }
                is VodDetailIntent.CheckForDownload -> {
                    val videoId = intent.details.blockContent.id
                    val chapterId = intent.episode.id

                    val collection = DataBase.videoDownloadDB.collectionAndChaptersDao().getDownloadVideoByMovieId(videoId)
                    val isCollectionExists = collection != null
                    val isChapterExists =
                        if (collection != null)
                            collection.listChapters.find { chapter -> chapter.chapterId == "${videoId}-${chapterId}" } != null
                        else
                            false
                    if (isCollectionExists && isChapterExists || isChapterExists) {
                        _state.value = VodDetailState.ResultCheckEpisodeFailed
                    } else {
                        vodRepository.checkVodDownload(getId(),intent.episode.id).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                VodDetailState.ResultCheckForDownload(isCached,data,intent.details,intent.episode,intent.episodeIndex,intent.useExternalStorage,isCollectionExists)
                            }
                        }
                    }
                }
                is VodDetailIntent.GetDownloadStage -> {
                    VideoDownloadManager.instance.getDownloadChaptersByMovieId(intent.videoId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            var downloadStage = 0
                            var percent = 0f
                            if (data != null) {
                                val chapter = data.listChapters.find { chapter -> chapter.chapterId == "${intent.videoId}-${intent.episode.id}" }
                                chapter?.let { chapter ->
                                    downloadStage = chapter.downloadStage ?: VideoTaskState.DEFAULT
                                    percent = if(chapter.downloadProgress >= 1f) chapter.downloadProgress else 0f
                                }
                            } else {
                                downloadStage = VideoTaskState.DEFAULT
                            }
                            VodDetailState.ResultDownloadStage(intent.episode, downloadStage,percent)
                        }
                    }
                }
                is VodDetailIntent.CheckDownloadReadyForAirline -> {
                    VideoDownloadManager.instance.getDownloadChaptersByMovieId(intent.videoId).collect { collector ->
                        _state.value = collector.reduce(intent = intent) { _, data ->
                            var downloadStage = 0
                            if (data != null) {
                                val chapter = data.listChapters.find { chapter -> chapter.chapterId == "${intent.videoId}-${intent.episode.id}" }
                                chapter?.let {
                                    downloadStage = it.downloadStage ?: VideoTaskState.DEFAULT
                                }
                            } else {
                                downloadStage = VideoTaskState.DEFAULT
                            }
                            VodDetailState.ResultCheckDownloadReadyForAirline(intent.episode, downloadStage)
                        }
                    }
                }
                is VodDetailIntent.GetHistoryById -> {
                    if (SourcePlayObject.SourcePlayVOD.getLocalBookmark() != null) {
                        _state.value = VodDetailState.ResultHistoryById(isCached = false, data = SourcePlayObject.SourcePlayVOD.getLocalBookmark()!!)
                        SourcePlayObject.SourcePlayVOD.clearLocalBookmark()
                    } else {
                        vodRepository.getBookmarkChapter(vodId = intent.id, "-1").collect {
                            _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultHistoryById(isCached = isCached, data = data) }
                        }
                    }
                }
                is VodDetailIntent.GetHistoryByIndex -> {
                    vodRepository.getBookmarkChapter(vodId = intent.id, chapterId = intent.episodeId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultHistoryByIndex(isCached = isCached, data = data) }
                    }
                }
                is VodDetailIntent.GetVodPeopleSuggest -> {
                    vodRepository.getVodPeople(id = intent.id, page = intent.page, perPage = intent.perPage, sort = intent.sort, requestType = intent.requestType).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultVodPeopleSuggest(isCached = isCached, data = data) }
                    }
                }
                is VodDetailIntent.GetVodAddFollowSuggest -> {
                    vodRepository.addFollow(type = intent.type, id = intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultVodAddFollowSuggest(isCached = isCached, data = data) }
                    }
                }
                is VodDetailIntent.GetVodDeleteFollowSuggest -> {
                    vodRepository.deleteFollow(type = intent.type, id = intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultVodDeleteFollowSuggest(isCached = isCached, data = data) }
                    }
                }
                is VodDetailIntent.GetVodCheckFollowSuggest -> {
                    vodRepository.checkFollow(type = intent.type, id = intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultVodCheckFollowSuggest(isCached = isCached, data = data) }
                    }
                }
                is VodDetailIntent.GetListNextVideo -> {
                    vodRepository.getNextMovie(videoId = intent.videoId, structureId = intent.structureId, pageSize = intent.pageSize, isPlaylist = intent.isPlaylist).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultListNextVideo(isCached = isCached, data = data)}
                    }
                }
                is VodDetailIntent.DetailDataChanged -> {
                    _state.value = VodDetailState.ResultDetailDataChange
                }
                is VodDetailIntent.OnPlayerChanged -> {
                    _state.value = VodDetailState.ResultOnPlayerChanged(episode = intent.episode)
                }
                is VodDetailIntent.GetAllDownloadVideos -> {
                    if(intent.isAirline){
                        VideoDownloadManager.instance.getAllForAirlineDownloadVideos(intent.isAirline).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                val listData = ArrayList<CollectionVideoTaskItem>()
                                data.map { collectionAndChapters ->
                                    listData.addAll(collectionAndChapters.toListCollectionVideoTaskItem())
                                }
                                VodDetailState.GetAllDownloadVideosResult(
                                    isCached = isCached,
                                    data = listData
                                )
                            }
                        }
                    } else {
                        VideoDownloadManager.instance.getDownloadChaptersByMovieId(intent.movieId).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                VodDetailState.GetAllDownloadVideosResult(
                                    isCached = isCached,
                                    data = data?.toListCollectionVideoTaskItem() ?: listOf()
                                )
                            }
                        }
                    }
                }
                is VodDetailIntent.GetDownloadChaptersByMovieId -> {
                    VideoDownloadManager.instance.getDownloadChaptersByMovieId(intent.movieId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.GetDownloadChaptersResult(
                                isCached = isCached,
                                data = data?.toVideoTaskItem() ?: listOf()
                            )
                        }
                    }
                }
                is VodDetailIntent.CancelDownloadByChapterId -> {
                    VideoDownloadManager.instance.getDownloadChapterById(intent.chapterId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VideoDownloadManager.instance.deleteVideoTask(data.listChapters[0].chapterId, true)
                            VodDetailState.Done()
                        }
                    }
                }
                is VodDetailIntent.DownloadByChapterId -> {
                    VideoDownloadManager.instance.getDownloadChapterById(intent.chapterId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.DownloadByChapterResult(isCached,data.listChapters[0])
                        }
                    }
                }
                is VodDetailIntent.NotifyAddDownloadItem -> {
                    _state.value = VodDetailState.AddDownloadTaskItemForVodTrack(intent.videoTaskItem)
                }
                is VodDetailIntent.GetPlaylist -> {
                    vodRepository.getPlaylist(intent.playlistId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.ResultVodPlaylist(isCached, data)
                        }
                    }
                }
                is VodDetailIntent.GetNextPlaylist -> {
                    vodRepository.getPlaylist(intent.playlistId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.ResultNextVodPlaylist(isCached, data)
                        }
                    }
                }

                is VodDetailIntent.TriggerPlayerLayout -> {
                    _state.value = VodDetailState.ResultTriggerPlayerLayout(isScale = intent.isScale)
                }

                is VodDetailIntent.SetPlayerControlVisibility -> {
                    _state.value = VodDetailState.ResultPlayerControlVisibility(isVisible = intent.isVisible)
                }

                is VodDetailIntent.SetPlayerProgressBarAndLockButtonVisibility -> {
                    _state.value = VodDetailState.ResultPlayerProgressBarAndLockButtonVisibility(isVisible = intent.isVisible)
                }

                is VodDetailIntent.SwitchPlayerMode -> {
                    _state.value = VodDetailState.ResultSwitchPlayerMode(modeFullscreen = intent.modeFullscreen)
                }

                is VodDetailIntent.CheckExtendForDownload -> {
                    vodRepository.checkVodDownload(intent.item.movieId,intent.item.chapterIdx.toString()
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.ResultCheckExtendForDownload(isCached,data,intent.item)
                        }
                    }
                }
                is VodDetailIntent.GetGameVod -> {
                    commonRepository.getGameVOD(intent.id, intent.gameEpisodeId, intent.userId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.ResultGameVod(isCached, data)
                        }
                    }
                }
                is VodDetailIntent.TriggerStartGameEmoji -> {
                    _state.value = VodDetailState.StartGameEmoji(url = intent.url, jsonData = intent.jsonData)
                }
                is VodDetailIntent.TriggerShowRankGame -> {
                    _state.value = VodDetailState.ShowRankGameEmoji(url = intent.url)
                }
                is VodDetailIntent.TriggerCloseGameEmoji -> {
                    _state.value = VodDetailState.CloseGameEmoji
                }
                is VodDetailIntent.GetListMoment -> {
                    momentRepository.getMomentStructureItem(
                        type = intent.type,
                        blockId = intent.structureId,
                        blockType = intent.blockType,
                        pageIndex = intent.page,
                        pageSize = intent.perPage,
                        watchingVersion = intent.watchingVersion,
                        customData = intent.customData,
                        relatedId = intent.relatedId
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            VodDetailState.ResultListMoment(isCached, data)
                        }
                    }
                }
                //region Pairing Control
                is VodDetailIntent.GetIntroCreditDataForCast -> {
                    vodRepository.getStream(id = intent.id, episodeId = intent.episode.id, bitrateId = intent.bitrateId, dataType = "").collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> VodDetailState.ResultIntroCreditDataForCast(isCached = isCached, episode = intent.episode, data = data) }
                    }
                }
                is VodDetailIntent.TriggerShowMsgUserReport->{
                    _state.value = VodDetailState.ResultTriggerMsgUserReport(
                        isReported = intent.isReported, message = intent.message
                    )
                }
                //endregion

                is VodDetailIntent.TriggerStopPlayer -> _state.value = VodDetailState.ResultTriggerStopPlayer

                is VodDetailIntent.FetchDownloadLink -> {
                    vodRepository.checkVodDownload(intent.item.movieId,intent.item.chapterIdx.toString()).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                           VodDetailState.ResultFetchDownloadLink(isCached,data,intent.item)
                        }
                    }
                }
                is VodDetailIntent.GetDrmKeyOffline -> {
                    drmRepository.getDrmOfflineKey(intent.uid).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            VodDetailState.ResultDrmKeyOffline(isCached, data)
                        }
                    }
                }
                is VodDetailIntent.SaveDrmKeyOffline -> {
                    drmRepository.insert(intent.drmKey).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            VodDetailState.ResultSaveDrmKey(isCached, data)
                        }
                    }
                }

                is VodDetailIntent.TriggerPreviewPlayCompleted -> _state.value =
                    VodDetailState.ResultPreviewPlayCompleted

                is VodDetailIntent.TriggerPreviewTimeUpPopupClose -> _state.value =
                    VodDetailState.ResultPreviewTimeUpPopupClose

                is VodDetailIntent.TriggerSaveLocalPreviewHistory -> _state.value =
                    VodDetailState.ResultSaveLocalPreviewHistory

                is VodDetailIntent.TriggerPlayerState -> _state.value =
                    VodDetailState.ResultPlayerState(isPlay = intent.isPlay)

                is VodDetailIntent.TriggerPausePreview -> _state.value = VodDetailState.ResultPausePreview

                is VodDetailIntent.GetRating -> {
                    vodRepository.getRatingVod(itemId = intent.itemId, refId = intent.refId).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            ratingData = data
                            VodDetailState.ResultRatingData(isCached, data)
                        }
                    }
                }
                is VodDetailIntent.UserRating -> {
                    vodRepository.userRating(itemId = intent.itemId, refId = intent.refId, appId = intent.appId, rating = intent.rating).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            VodDetailState.ResultUserRating(isCached, data)
                        }
                    }
                }

                is VodDetailIntent.TriggerShowBuyPackageGuide -> {
                    _state.value = VodDetailState.ShowBuyPackageGuide(intent.buyPackageGuide)
                }

                is VodDetailIntent.TriggerBuyPackage -> {
                    _state.value = VodDetailState.ResultTriggerBuyPackage
                }

                is VodDetailIntent.TriggerConsumeBuyPackageAction -> {
                    _state.value = VodDetailState.Idle
                }
            }

        }
    }

    private suspend fun getStream(intent: VodDetailIntent.GetStream) {
        vodRepository.getStream(
            id = intent.id,
            episodeId = intent.episode.id,
            bitrateId = intent.bitrateId,
            dataType = ""
        ).zip(
            drmRepository.getDrmOfflineKey(
                PlayerUtils.getVodDrmUid(
                    intent.id,
                    intent.episode.id
                )
            )
        ) { stream, drmKey ->
            when (stream) {
                is Result.Success -> {
                    if (drmKey.isSuccess()) {
                        VodDetailState.ResultStream(
                            isCached = false,
                            episode = intent.episode,
                            data = stream.successData,
                            delayToPlay = intent.delayToPlay,
                            isPlayingTrailer = intent.isPlayingTrailer,
                            isSendLog = intent.isSendLog,
                            fromChangeEpisode = intent.fromChangeEpisode,
                            isSendEventToRemote = intent.isSendEventToRemote,
                            drmKey = drmKey.data,
                            intent = intent
                        )
                    } else {
                        VodDetailState.ResultStream(
                            isCached = false,
                            episode = intent.episode,
                            data = stream.successData,
                            delayToPlay = intent.delayToPlay,
                            isPlayingTrailer = intent.isPlayingTrailer,
                            isSendLog = intent.isSendLog,
                            fromChangeEpisode = intent.fromChangeEpisode,
                            isSendEventToRemote = intent.isSendEventToRemote,
                            drmKey = null,
                            intent = intent
                        )
                    }
                }

                else -> {
                    stream.reduce(intent = intent) { isCached, data ->
                        VodDetailState.ResultStream(
                            isCached = isCached,
                            episode = intent.episode,
                            data = data,
                            delayToPlay = intent.delayToPlay,
                            isPlayingTrailer = intent.isPlayingTrailer,
                            isSendLog = intent.isSendLog,
                            fromChangeEpisode = intent.fromChangeEpisode,
                            isSendEventToRemote = intent.isSendEventToRemote,
                            drmKey = null,
                            intent = intent
                        )
                    }
                }
            }
        }.collect {
            viewModelScope.launch(Dispatchers.Main) {
                _state.value = it
            }
        }
    }

    private suspend fun getVodInfoInParallel(intent: VodDetailIntent.GetDetail) = flow {
        var details: Details? = null
        var bookmarkInfo: VodBookmarkInfo? = null
        var history: History? = null
        var error: VodDetailState.Error? = null
        val measureTime = measureTimeMillis {
            emit(VodDetailState.Loading(intent))
            withTimeoutOrNull(10_000) {
                try {
                    val deffereds: ArrayList<Deferred<Unit>> = arrayListOf()
                    deffereds.add(async {
                        vodRepository.getDetailV2(intent.id, "").process (successCallback = {
                            details = it
                        }, errorCallback = {
                            error = VodDetailState.Error(message = it, data = intent)
                        })
                    })
                    deffereds.add(async {
                        vodRepository.getBookmarkVod(intent.id).process({
                            bookmarkInfo = it
                        })
                    })

                    deffereds.add(async {
                        vodRepository.getBookmarkChapter(vodId = intent.id, chapterId = "-1").process ({
                            history = it
                        })
                    })

                    deffereds.forEach { it.await() }
                    if(error != null) {
                        emit(VodDetailState.Error(error!!.message, intent))
                    } else {
                        emit(VodDetailState.ResultHistoryById(isCached = false, data = history))
                        bookmarkInfo?.listChapter?.forEach { history ->
                            details?.blockEpisode?.episodes?.find { it.id == history.episodeId}?.run {
                                this.timeWatched = history.startPosition.toString()
                            }
                        }

                        emit(
                            VodDetailState.ResultDetail(isCached = false, data = details?.apply {
                                blockContent.playerSpeed = MainApplication.INSTANCE.getPlayerSpeed()
                            } ?: Details(), intent = intent)
                        )
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }

            } ?: emit(VodDetailState.Error("Lấy dữ liệu không thành công", intent))
        }
        Timber.d("***** Measure time get vod info: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
        emit(VodDetailState.Done(intent))
    }

    private suspend fun <T> Flow<Result<T>>.process(successCallback: (T) -> Unit, errorCallback: ((String) -> Unit)? = null) {
        this.collect {
            if(it is Result.Success) {
                successCallback(it.successData)
            } else if(it is Result.Error) {
                errorCallback?.invoke(it.message)
            }
        }
    }

    override fun resetState() {
//        _isFullScreen.value = null
        _initPlayer.value = null
        _initOfflinePlayer.value = null
        _playVod.value = null
        _playEpisode.value = null
        _playTrailer.value = null
//        _playRequiredVipTrailer.value = null
        _preparePlayer.value = null
        _prepareOfflinePlayer.value = null
        _downloadClick.value = null
    }

    fun cancelGetStreamJob() {
        getStreamJob?.cancel()
    }

    //region Save args
    fun saveActorId(id: String) = savedState.set("actorId", id)
    fun getActorId(): String = savedState.get("actorId") ?: ""

    fun saveIsPreAds(isPreAds: Boolean) = savedState.set("isPreAds", isPreAds)
    fun getIsPreAds(): Boolean = savedState.get("isPreAds") ?: false

    fun savePreAdsStartTimeInMs(time: Long) = savedState.set("preAdsStartTimeInMs", time)
    fun getPreAdsStartTimeInMs(): Long = savedState.get("preAdsStartTimeInMs") ?: 0
    fun savePreAdsEndTimeInMs(time: Long) = savedState.set("preAdsEndTimeInMs", time)
    fun getPreAdsEndTimeInMs(): Long = savedState.get("preAdsEndTimeInMs") ?: 0

    fun saveIsStreamRetry(isStreamRetry: Boolean) = savedState.set("isStreamRetry", isStreamRetry)
    fun getIsStreamRetry(): Boolean = savedState.get("isStreamRetry") ?: false

    fun saveClickOnItemTimeInMs(time: Long) = savedState.set("clickOnItemTimeInMs", time)
    fun getClickOnItemTimeInMs(): Long = savedState.get("clickOnItemTimeInMs") ?: 0

    fun savePrepareSourceTimeInMs(time: Long) = savedState.set("prepareSourceTimeInMs", time)
    fun getPrepareSourceTimeInMs(): Long = savedState.get("prepareSourceTimeInMs") ?: 0

    fun saveDataDetail(data: Details?) {
        detail = data
        saveDetailDataForTrackingLog()
        if(data == null) ratingData = null
    }
    fun getDataDetail(): Details? { return detail }

    fun saveDataMovieOffline(data: CollectionVideoTaskItem?) { collectionVideoOffline = data }
    fun getDataMovieOffline(): CollectionVideoTaskItem? { return collectionVideoOffline }

    fun saveCurDataOffline(data: VideoTaskItem?) { dataCurOffline = data }
    fun getCurDataOffline(): VideoTaskItem? { return dataCurOffline }

    val idChange = MutableLiveData("")
    fun saveId(id: String) {
        idChange.postValue(id)
        savedState.set("id", id)
    }
    fun getId() = savedState.get<String>("id") ?: ""

    fun saveCurrentPlayerBitrates(bitrates: List<PlayerControlView.Data.Bitrate>?){ playerBitrates = bitrates }
    fun getCurrentPlayerBitrates() = playerBitrates

    fun saveTracks(tracks: List<PlayerControlView.Data.Track>?){ playerTracks = tracks }
    fun getTracks() = playerTracks

    fun saveVipRequired(isRequired: Boolean, requiredVip: RequiredVip? = null) { isVipRequired = Pair(isRequired, requiredVip)}
    fun getVipRequired() = isVipRequired

    fun saveLoginRequired(isRequired: Boolean, requiredLogin: RequiredLogin ?= null, message: String = "") { isLoginRequired = Triple(isRequired, requiredLogin, message)}
    fun getLoginRequired() = isLoginRequired

    fun saveCurrentEpisode(episode: Details.Episode?) {
        if (currentEpisode?.id != episode?.id || currentEpisode?.vodId != episode?.vodId) {
            Logger.d("VodDetailViewModel -> SaveCurrentEpisode -> Id: ${episode?.id} | Title: ${episode?.titleVietnam}")
            currentEpisode = episode
            saveEpisodeDataForTrackingLog()
        }
    }
    fun currentEpisode() = currentEpisode

    fun saveFileHash(hash: String) { fileHash = hash }
    fun getFileHash() = fileHash

    fun saveScreenProvider(screenProvider: String) {
        savedState.set("screenProvider", screenProvider)
    }
    fun screenProvider() = savedState.get<String>("screenProvider") ?: ""

    fun savePlaylist(playlist: VodPlaylist?) { videoPlaylist = playlist }
    fun getPlaylist() = videoPlaylist

    fun saveIsPlaylistContent(isPlaylist: Boolean) = savedState.set("isPlaylistContent", isPlaylist)
    fun isPlaylistContent() =  savedState.get<Boolean>("isPlaylistContent") ?: false

    fun savePlayerScalableOnGame(isScale: Boolean) = savedState.set("isPlayerScalable", isScale)
    fun isPlayerScalableOnGame() = savedState.get<Boolean>("isPlayerScalable") ?: false

    fun saveGameVod(gameData: GameVOD.GameVODData?) {
        this.gameVodData = gameData
    }
    fun currentGameVod() = gameVodData

    fun  saveMomentDetail(data: List<StructureItem>?) { listMoment = data }
    fun  getMomentDetail(): List<StructureItem>? { return listMoment}
    //endregion

    //region Player Options Dialog
    fun savePlayerOptionsType(type: String) { savedState.set("playerOptionsType", type)}
    fun getPlayerOptionsType() = savedState.get<String>("playerOptionsType") ?: ""

    fun savePlayerOptionsCurItem(idCurItem: String) { savedState.set("playerOptionsCurItem", idCurItem)}
    fun getPlayerOptionsCurItem() = savedState.get<String>("playerOptionsCurItem") ?: ""
    //endregion

    //region save retry stream info
    /**
     * Save retry stream info
     * @param retryStreamInfo String
     *
     */
    fun saveRetryStreamInfo(retryStreamInfo: String) { savedState.set("retryStreamInfo", retryStreamInfo)}
    fun isStreamRetry(retryStreamInfo: String) = savedState.get<String>("retryStreamInfo") ?: ""
    fun createRetryStreamInfo(vodId: String, episodeId: String): String = "$vodId-$episodeId"
    //endregion

    //region Player Download
    fun saveDownloadUrl(type: String) = savedState.set("playerDownloadUrl", type)
    fun getDownloadUrl() = savedState.get<String>("playerDownloadUrl") ?: ""
    //endregion

    //region PLayer Rules
    fun setPlayerCanPlayContent(isCanPlay: Boolean) { savedState.set("player_can_play", isCanPlay) }
    fun getPlayerCanPlayContent() = savedState.get<Boolean>("player_can_play") ?: true
    //endregion

    fun saveListQualityDownload(listQualityDownload: MutableList<String>) {
        this.listQualityDownload = listQualityDownload
    }
    private fun saveDetailDataForTrackingLog(){
        detail?.let {
            TrackingUtil.setIdContentAndRefId(idContent = it.blockContent.id, refId = it.blockContent.refId)
        }?: kotlin.run {
            TrackingUtil.resetDataPlaying()
        }

    }
    private fun saveEpisodeDataForTrackingLog(){
        currentEpisode?.let {
            if(isPlaylistContent()){
                TrackingUtil.setRefEpisodeId(epiId = it.realEpisodeId, refEpisode = it.refEpisodeId, true)
            }else{
                TrackingUtil.setRefEpisodeId(epiId = it.realEpisodeId, refEpisode = it.refEpisodeId, false)
            }
        }
    }

    fun saveFollow(value: String) { savedState.set("vod_follow", value) }
    fun getFollow() = savedState.get<String>("vod_follow") ?: "0"

    fun hasPreview() = currentEpisode()?.isPreview ?: false

    fun saveLocalPreviewHistory(history: History?) { localPreviewHistory = history }
    fun getLocalPreviewHistory() = localPreviewHistory

    fun saveNextActionEvent(action: NextActionEvent?) { nextActionEvent = action }
    fun getNextActionEvent() = nextActionEvent

    fun saveIsPauseAfterSeek(isPause: Boolean) { isPauseAfterSeek = isPause }
    fun isPauseAfterSeek() = isPauseAfterSeek

    fun saveBuyPackageGuide(guide: BuyPackageGuide?) {buyPackageGuide = guide}
    fun getBuyPackageGuide() = buyPackageGuide

    fun triggerFullScreen(isFull: Boolean, isLandscapeMode: Boolean) {
        _isFullScreen.postValue(Pair(isFull, isLandscapeMode))
    }

    fun triggerInitPlayer(isOffline: Boolean = false) {
        _initPlayer.postValue(isOffline)
    }

    fun triggerInitOfflinePlayer(isOffline: Boolean = false) {
        _initOfflinePlayer.postValue(isOffline)
    }

    fun triggerPlayVod(id: String, type: String = "") {
        _playVod.postValue(Pair(id, type))
    }

    fun triggerPlayerChanged(episode: Details.Episode?) {
        _playerChanged.postValue(episode)
    }

    fun triggerPlayEpisode(data: Details.Episode) {
        _playEpisode.postValue(data)
    }

    fun triggerPlayTrailer(data: Details.Episode) {
        _playTrailer.postValue(data)
    }

    fun triggerPlayRequiredVipTrailer(url: String?) {
        _playRequiredVipTrailer.postValue(url)
    }

    fun triggerPreparePlayer(episode: Details.Episode, stream: Stream, delayToPlay: Boolean, isPlayingTrailer: Boolean, drmKey: DrmKey?) {
        _preparePlayer.postValue(
            VodDetailState.PreparePlayer(
                episode = episode,
                stream = stream,
                delayToPlay = delayToPlay,
                isPlayingTrailer = isPlayingTrailer,
                drmKey = drmKey
            )
        )
    }
    fun triggerPlayPreview(data: VodPreviewPlayerInfo?) {
        _playPreview.postValue(data)
    }

    fun triggerPrepareOfflinePlayer(data: CollectionVideoTaskItem, currentId: String) {
        _prepareOfflinePlayer.postValue(
            VodDetailState.PrepareOfflinePlayer(
                data = data,
                currentId = currentId
            ))
    }

    fun triggerDownloadClick(episode: Details.Episode, episodeIndex: Int, currentStatus: Any) {
        _downloadClick.postValue(
            VodDetailState.OnDownloadClick(
                episode = episode,
                episodeIndex = episodeIndex,
                currentStatus = currentStatus
            ))
    }

    fun triggerUpdateDownloadState(chapterIdx: String, progress: String = "", size: String = "", state : Int) {
        _updateDownloadState.setValue(
            VodDetailState.OnDownloadState(
                chapterIdx = chapterIdx,
                progress = progress,
                size = size,
                state = state)
        )
    }

    fun triggerUpdateDownloadProcess(chapterIdx: String, progress: String = "", size: String = "", state : Int) {
        _updateDownloadProcess.setValue(
            VodDetailState.OnDownloadProcess(
                chapterIdx = chapterIdx,
                progress = progress,
                size = size,
                state = state)
        )
    }

    fun getListPlayerSpeed(): List<PlayerSpeed> {
        return commonRepository.getPlayerSpeed().map { it.apply {
            isSelected = (it.speed == MainApplication.INSTANCE.getPlayerSpeed())
        } }
    }

    // region Check Preview Link
    fun hasPreviewLink(requiredLogin: RequiredLogin): Boolean {
        return requiredLogin.urlDashDolbyVision.isNotBlank()
                || requiredLogin.urlDashH265Hdr10Plus.isNotBlank()
                || requiredLogin.urlDashH265Hdr.isNotBlank()
                || requiredLogin.urlDashH265Hlg.isNotBlank()
                || requiredLogin.urlDashAv1.isNotBlank()
                || requiredLogin.urlDashVp9.isNotBlank()
                || requiredLogin.urlDashH265.isNotBlank()
                || requiredLogin.urlDash.isNotBlank()
    }

    fun hasPreviewLink(requiredVip: RequiredVip): Boolean {
        return requiredVip.urlDashDolbyVision.isNotBlank()
                || requiredVip.urlDashH265Hdr10Plus.isNotBlank()
                || requiredVip.urlDashH265Hdr.isNotBlank()
                || requiredVip.urlDashH265Hlg.isNotBlank()
                || requiredVip.urlDashAv1.isNotBlank()
                || requiredVip.urlDashVp9.isNotBlank()
                || requiredVip.urlDashH265.isNotBlank()
                || requiredVip.urlDash.isNotBlank()
    }

    fun hasPreviewLink(vodPreviewPlayerInfo: VodPreviewPlayerInfo): Boolean {
        return vodPreviewPlayerInfo.urlDashDolbyVision.isNotBlank()
                || vodPreviewPlayerInfo.urlDashH265Hdr10Plus.isNotBlank()
                || vodPreviewPlayerInfo.urlDashH265Hdr.isNotBlank()
                || vodPreviewPlayerInfo.urlDashH265Hlg.isNotBlank()
                || vodPreviewPlayerInfo.urlDashAv1.isNotBlank()
                || vodPreviewPlayerInfo.urlDashVp9.isNotBlank()
                || vodPreviewPlayerInfo.urlDashH265.isNotBlank()
                || vodPreviewPlayerInfo.urlDash.isNotBlank()
    }

    // endregion

    override fun <T> Result<T>.reduce(
        intent: VodDetailIntent?,
        successFun: (Boolean, T) -> VodDetailState
    ): VodDetailState {
        return when (this) {
            is Result.Init -> VodDetailState.Loading(data = intent)
            is Result.Success -> { successFun(this.isCached, this.successData) }
            is Result.UserError.RequiredLogin -> {
                if (intent is VodDetailIntent.GetVodCheckFollowSuggest) {
                    VodDetailState.Error(this.message, data = intent)
                } else {
                    VodDetailState.ErrorRequiredLogin(
                        message = this.message,
                        data = intent,
                        requiredLogin = this.requiredLogin
                    )
                }
            }
            is Result.UserError.RequiredVip -> VodDetailState.ErrorRequiredVip(
                message = this.message,
                data = intent,
                requiredVip = this.requiredVip
            )
            is Result.Error.Intenet -> VodDetailState.ErrorByInternet(message = this.message, data = intent)
            is Result.Error -> VodDetailState.Error(message = this.message, data = intent)
            Result.Done -> VodDetailState.Done(intent = intent)
        }
    }
    sealed class VodDetailState : ViewState {
        object Init : VodDetailState()
        data class Loading(val data: VodDetailIntent?= null) : VodDetailState()
        data class ResultDetail(val isCached: Boolean, val data: Details, val intent: VodDetailIntent? = null) : VodDetailState()
        data class ResultRatingData(val isCached: Boolean, val data: RatingData) : VodDetailState()
        data class ResultUserRating(val isCached: Boolean, val data: UserRating) : VodDetailState()
        data class ResultStream(
            val isCached: Boolean,
            val episode: Details.Episode,
            val data: Stream,
            val delayToPlay: Boolean,
            val isPlayingTrailer: Boolean,
            val isSendLog: Boolean,
            val fromChangeEpisode: Pair<Boolean, Int>,
            val isSendEventToRemote: Boolean,
            val drmKey: DrmKey? = null,
            val intent: VodDetailIntent.GetStream,
        ) : VodDetailState()
        data class ResultDetailOffline(val isCached: Boolean, val data: CollectionVideoTaskItem,val currentId : String) : VodDetailState()
        data class ResultHistoryById(val isCached: Boolean, val data: History?) : VodDetailState()
        data class ResultHistoryByIndex(val isCached: Boolean, val data: History) : VodDetailState()
        data class ResultVodPeopleSuggest(val isCached: Boolean, val data: List<People>) : VodDetailState()
        data class ResultVodAddFollowSuggest(val isCached: Boolean, val data: Status) : VodDetailState()
        data class ResultVodDeleteFollowSuggest(val isCached: Boolean, val data: Status) : VodDetailState()
        data class ResultVodCheckFollowSuggest(val isCached: Boolean, val data: Status) : VodDetailState()
        data class ResultVodPlaylist(val isCached: Boolean, val data: VodPlaylist): VodDetailState()
        data class ResultNextVodPlaylist(val isCached: Boolean, val data: VodPlaylist): VodDetailState()
        data class ResultListNextVideo(val isCached: Boolean, val data: List<Details.RelatedVod>): VodDetailState()
        data class Error(val message: String, val data: VodDetailIntent? = null) : VodDetailState()
        data class ErrorRequiredLogin(val message: String, val data: VodDetailIntent ?= null, val requiredLogin: RequiredLogin?) : VodDetailState()
        data class ErrorRequiredVip(val message: String, val data: VodDetailIntent ?= null, val requiredVip: RequiredVip?) : VodDetailState()
        data class ErrorByInternet(val message: String, val data: VodDetailIntent ?= null) : VodDetailState()
        data class Done(val intent: VodDetailIntent? = null) : VodDetailState()
        data class PreparePlayer(val episode: Details.Episode, val stream: Stream, val delayToPlay: Boolean, val isPlayingTrailer: Boolean, val drmKey: DrmKey?): VodDetailState()
        data class PrepareOfflinePlayer(val data: CollectionVideoTaskItem, val currentId: String): VodDetailState()
        object ResultDetailDataChange : VodDetailState()
        data class ResultOnPlayerChanged(val episode: Details.Episode?) : VodDetailState()
        data class OnDownloadClick(val episode: Details.Episode, val episodeIndex: Int, val currentStatus: Any): VodDetailState()
        data class OnDownloadState(val chapterIdx: String, val progress: String, val size: String,val state : Int): VodDetailState()
        data class OnDownloadProcess(val chapterIdx: String, val progress: String, val size: String,val state : Int): VodDetailState()
        data class ResultDownloadStage(val episode: Details.Episode, val downloadStage: Int, val percent : Float): VodDetailState()
        data class ResultCheckDownloadReadyForAirline(val episode: Details.Episode, val downloadStage: Int): VodDetailState()
        object ResultCheckEpisodeFailed : VodDetailState()
        data class GetAllDownloadVideosResult(val isCached: Boolean, val data: List<CollectionVideoTaskItem>?) : VodDetailState()
        data class GetDownloadChaptersResult(val isCached: Boolean, val data: List<VideoTaskItem>) : VodDetailState()
        data class DownloadByChapterResult(val isCached: Boolean, val data: VideoTaskItem) : VodDetailState()
        data class AddDownloadTaskItemForVodTrack(val data: VideoTaskItem) : VodDetailState()
        data class ResultTriggerPlayerLayout(val isScale: Boolean) : VodDetailState()
        data class ResultSwitchPlayerMode(val modeFullscreen: Boolean) : VodDetailState()
        data class ResultCheckForDownload(val isCached: Boolean, val vodDownloadInfo: VodDownloadInfo,val vod: Details,val episode: Details.Episode,val episodeIndex: Int,val useExternalStorage: Boolean = false,val hasCollectionInDb: Boolean) : VodDetailState()
        data class ResultCheckExtendForDownload(val isCached: Boolean, val vodDownloadInfo: VodDownloadInfo,val item: VideoTaskItem) : VodDetailState()
        data class ResultPlayerControlVisibility(val isVisible: Boolean) : VodDetailState()
        data class ResultPlayerProgressBarAndLockButtonVisibility(val isVisible: Boolean) : VodDetailState()
        data class ResultGameVod(val isCached: Boolean, val data: GameVOD) :VodDetailState()
        data class StartGameEmoji(val url: String, val jsonData: String): VodDetailState()
        data class ResultListMoment(val isCached: Boolean, val data: List<StructureItem>): VodDetailState()
        data class ShowRankGameEmoji(val url: String): VodDetailState()
        object CloseGameEmoji: VodDetailState()

        //region Pairing Control
        data class ResultIntroCreditDataForCast(val isCached: Boolean, val episode: Details.Episode, val data: Stream) : VodDetailState()
        //endregion

        //region Report Player
        data class ResultTriggerMsgUserReport(val isReported: Boolean, val message: String) : VodDetailState()
        //endregion
        object ResultTriggerStopPlayer: VodDetailState()
        data class ResultFetchDownloadLink(val isCached: Boolean, val vodDownloadInfo: VodDownloadInfo, val item: VideoTaskItem) : VodDetailState()

        //region Preview
        object ResultPreviewPlayCompleted: VodDetailState()
        object ResultPreviewTimeUpPopupClose: VodDetailState()
        object ResultSaveLocalPreviewHistory: VodDetailState()
        data class ResultPlayerState(val isPlay: Boolean): VodDetailState()
        object ResultPausePreview: VodDetailState()
        //endregion

        data class ResultDrmKeyOffline(val isCached: Boolean, val data: DrmKey) : VodDetailState()
        data class ResultSaveDrmKey(val isCached: Boolean, val isSuccess: Boolean) : VodDetailState()

        data class ShowBuyPackageGuide(val buyPackageGuide: BuyPackageGuide): VodDetailState()
        object ResultTriggerBuyPackage: VodDetailState()
        object Idle : VodDetailState()
    }

    sealed class VodDetailIntent : ViewIntent {
        data class GetDetail (val id: String, val isPlayerCalled: Boolean, val isPlayingTrailer: Boolean = false) : VodDetailIntent()
        data class GetRating(val itemId:String, val refId: String) : VodDetailIntent()
        data class UserRating(val itemId: String, val refId: String, val appId: String, val rating: String) : VodDetailIntent()
        data class GetStream(
            val id: String,
            val episode: Details.Episode,
            val bitrateId: String,
            val delayToPlay: Boolean,
            val isPlayingTrailer: Boolean,
            val isSendLog: Boolean,
            val fromChangeEpisode: Pair<Boolean, Int>,
            val isSendEventToRemote: Boolean,
            val delay: Long = 0L,
            val nextActionEvent: NextActionEvent? = null,
            val isRetry: Boolean = false
        ) : VodDetailIntent()
        data class GetDetailOffline(val id: String, val isAirline: Boolean, val isPlayerCalled: Boolean) : VodDetailIntent()
        data class GetHistoryById(val id: String, val userId: String) : VodDetailIntent()
        data class GetHistoryByIndex(val id: String, val episodeId: String, val userId: String) : VodDetailIntent()
        data class GetVodPeopleSuggest(val id: String,val page: Int, val perPage: Int, val sort: Int, val requestType: Int) : VodDetailIntent()
        data class GetVodAddFollowSuggest(val id: String,val type: String) : VodDetailIntent()
        data class GetVodDeleteFollowSuggest(val id: String,val type: String) : VodDetailIntent()
        data class GetVodCheckFollowSuggest(val id: String,val type: String) : VodDetailIntent()
        data class GetPlaylist(val playlistId: String): VodDetailIntent()
        data class GetNextPlaylist(val playlistId: String): VodDetailIntent()
        data class GetListNextVideo(val videoId: String, val structureId: String, val pageSize: Int = 1, val isPlaylist: Boolean) : VodDetailIntent()
        object DetailDataChanged : VodDetailIntent()
        data class OnPlayerChanged(val episode: Details.Episode?) : VodDetailIntent()
        data class GetDownloadStage(val videoId: String, val episode: Details.Episode): VodDetailIntent()
        data class CheckDownloadReadyForAirline(val videoId: String, val episode: Details.Episode): VodDetailIntent()
        data class GetAllDownloadVideos(val isAirline : Boolean,val movieId: String) : VodDetailIntent()
        data class GetDownloadChaptersByMovieId(val movieId: String) : VodDetailIntent()
        data class CancelDownloadByChapterId(val chapterId: String) : VodDetailIntent()
        data class DownloadByChapterId(val chapterId: String) : VodDetailIntent()
        data class NotifyAddDownloadItem(val videoTaskItem: VideoTaskItem) : VodDetailIntent()
        data class TriggerPlayerLayout(val isScale: Boolean) : VodDetailIntent()

        /**
         * @param modeFullscreen: If you want to enter fullscreen, pass "true" to param, otherwise pass "false" -> exit fullscreen
         */
        data class SwitchPlayerMode(val modeFullscreen: Boolean) : VodDetailIntent()
        data class CheckForDownload(val details: Details,val episode: Details.Episode,val episodeIndex: Int,val useExternalStorage: Boolean = false) : VodDetailIntent()
        data class CheckExtendForDownload(val item: VideoTaskItem) : VodDetailIntent()
        data class SetPlayerControlVisibility(val isVisible: Boolean) : VodDetailIntent()
        data class SetPlayerProgressBarAndLockButtonVisibility(val isVisible: Boolean) : VodDetailIntent()
        data class GetGameVod(val id: String, val gameEpisodeId: String, val userId: String): VodDetailIntent()
        data class TriggerStartGameEmoji(val url: String, val jsonData: String): VodDetailIntent()
        data class TriggerShowRankGame(val url: String): VodDetailIntent()
        object TriggerCloseGameEmoji: VodDetailIntent()
        data class GetListMoment(
            val structureId: String,
            val type: String,
            val blockType: String,
            val userId: String,
            val page: Int,
            val perPage: Int,
            val watchingVersion: String? = null,
            val customData: String,
            val relatedId: String,
        ) : VodDetailIntent()

        //region Pairing Control
        data class GetIntroCreditDataForCast(val id: String, val episode: Details.Episode, val bitrateId: String): VodDetailIntent()
        //endregion

        //region ReportPlayer
        data class TriggerShowMsgUserReport(val isReported: Boolean = false , val message: String): VodDetailIntent()
        //endregion

        object TriggerStopPlayer: VodDetailIntent()
        data class FetchDownloadLink(val item: VideoTaskItem) : VodDetailIntent()

        // region DRM offline
        data class SaveDrmKeyOffline(val drmKey: DrmKey): VodDetailIntent()
        data class GetDrmKeyOffline(val uid: String): VodDetailIntent()
        //endregion

        //region Preview
        object TriggerPreviewPlayCompleted: VodDetailIntent()
        object TriggerPreviewTimeUpPopupClose: VodDetailIntent()
        object TriggerSaveLocalPreviewHistory: VodDetailIntent()
        data class TriggerPlayerState(val isPlay: Boolean): VodDetailIntent()
        object TriggerPausePreview: VodDetailIntent()
        //endregion
        object TriggerBuyPackage: VodDetailIntent()
        object TriggerConsumeBuyPackageAction: VodDetailIntent()

        data class TriggerShowBuyPackageGuide(val buyPackageGuide: BuyPackageGuide) : VodDetailIntent()
    }

    override suspend fun pingPlay(id: String, session: String, lastSession: String, encryptData: Boolean, type: String, eventId: String): Flow<Result<PingStreamV2>> {
        return drmRepository.pingPlay(id = id, session = session, lastSession = lastSession, encryptData = encryptData, type = type, eventId = eventId)
    }

    override suspend fun pingPlay(id: String, type: String, eventId: String): Flow<Result<Ping>> {
        return drmRepository.pingPlay(id = id, type = type, eventId = eventId)
    }

    override suspend fun pingPause(id: String, type: String, eventId: String): Flow<Result<Ping>> {
        return drmRepository.pingPause(id = id, type = type, eventId = eventId)
    }

    override suspend fun pingPlayHbo(operatorId: String, sessionId: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingPlayHbo(operatorId = operatorId, sessionId = sessionId)
    }

    override suspend fun pingPlayHboByToken(token: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingPlayHbo(token = token)
    }

    override suspend fun pingEndHbo(token: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingEndHbo(token = token)
    }

    override suspend fun refreshToken(operatorId: String, sessionId: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.refreshTokenHbo(operatorId = operatorId, sessionId = sessionId)
    }

    override suspend fun getVodStream(id: String, episodeId: String, bitrateId: String): Flow<Result<Stream>> {
        return drmRepository.getVodStream(id = id, episodeId = episodeId, bitrateId = bitrateId, dataType = "")
    }
}
