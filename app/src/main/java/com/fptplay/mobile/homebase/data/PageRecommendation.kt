package com.fptplay.mobile.homebase.data

import androidx.annotation.Keep
import com.xhbadxx.projects.module.domain.entity.fplay.home.RecommendBlockPosition

@Keep
data class PageRecommendation(
    val pageId: String = "",
    var pageRecommendationData: PageRecommendationData ?= null
)

@Keep
data class PageRecommendationData(
    val pageRecommendationTimestamp: Long = 0L,
    val pageRecommendationValue: List<RecommendBlockPosition>? = null
)
