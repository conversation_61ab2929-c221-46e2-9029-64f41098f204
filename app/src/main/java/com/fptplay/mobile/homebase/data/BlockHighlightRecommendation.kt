package com.fptplay.mobile.homebase.data

import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem

data class BlockHighlightRecommendation constructor(
    val blockKey: String = "",
    var blockRecommendationData: BlockHighlightRecommendationData = BlockHighlightRecommendationData()
) {
    data class BlockHighlightRecommendationData(
        val blockRecommendationTimestamp: Long = 0L,
        val blockRecommendationValue: List<StructureItem>? = null
    )
}
