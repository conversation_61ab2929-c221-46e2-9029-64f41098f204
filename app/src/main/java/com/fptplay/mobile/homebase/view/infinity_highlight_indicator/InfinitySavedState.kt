package com.fptplay.mobile.homebase.view.infinity_highlight_indicator

import android.os.Parcel
import android.os.Parcelable
import android.view.View.BaseSavedState
import org.jetbrains.annotations.NotNull

internal class InfinitySavedState : BaseSavedState {
    var count: Int = 0
    var selectedIndex = 0

    constructor(superState: Parcelable) : super(superState)

    private constructor(`in`: Parcel) : super(`in`) {
        this.count = `in`.readInt()
        this.selectedIndex = `in`.readInt()
    }

    override fun writeToParcel(out: Parcel, flags: Int) {
        super.writeToParcel(out, flags)
        out.writeInt(this.count)
        out.writeInt(this.selectedIndex)
    }

    companion object {
        @JvmField
        @NotNull
        val CREATOR: Parcelable.Creator<InfinitySavedState> = object : Parcelable.Creator<InfinitySavedState> {
            override fun createFromParcel(`in`: Parcel): InfinitySavedState {
                return InfinitySavedState(`in`)
            }

            override fun newArray(size: Int): Array<InfinitySavedState?> {
                return arrayOfNulls(size)
            }
        }
    }
}