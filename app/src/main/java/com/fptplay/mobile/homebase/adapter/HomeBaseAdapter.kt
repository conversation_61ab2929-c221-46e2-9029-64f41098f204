package com.fptplay.mobile.homebase.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.drowsyatmidnight.haint.android_banner_sdk.tvc_banner.model.AdPosition
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.block.BlockHandler
import com.fptplay.mobile.common.adapter.block.highlight.BlockEntity
import com.fptplay.mobile.common.classes.HighLightOrientation
import com.fptplay.mobile.common.classes.ScrollHighLightToNextPosition
import com.fptplay.mobile.common.classes.ScrollHighLightToPosition
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.databinding.BlockAdsTvcBannerBinding
import com.fptplay.mobile.databinding.BlockButtonShareBinding
import com.fptplay.mobile.features.ads.banner.tip_guideline.TipGuidelineLifecycleObserver
import com.fptplay.mobile.features.ads.banner.tip_guideline.TvcBannerActionListener
import com.fptplay.mobile.features.ads.banner.tip_guideline.TipGuidelineViewHolder
import com.fptplay.mobile.features.ads.banner.tip_guideline.TvcBannerLifecycleObserver
import com.fptplay.mobile.features.ads.banner.tip_guideline.TvcBannerViewHolder
import com.fptplay.mobile.homebase.viewholder.ButtonShareViewHolder
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.AdData
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class HomeBaseAdapter(val lifecycleOwner: LifecycleOwner? = null, val sharedPreferences: SharedPreferences? = null, val isEnableWatchingProgress: () -> Boolean = { true }) :
    BaseAdapter<Structure, RecyclerView.ViewHolder>() {

    private val blockHandler: BlockHandler by lazy { BlockHandler() }
    private val listViewHolder = arrayListOf<RecyclerView.ViewHolder>()
    private val mapTipGuidelineLifecycleObserver by lazy {
        HashMap<TipGuidelineViewHolder, TipGuidelineLifecycleObserver>()
    }
    private val mapTvcBannerLifecycleObserver by lazy {
        HashMap<TvcBannerViewHolder, TvcBannerLifecycleObserver>()
    }
    var tvcBannerActionListener: TvcBannerActionListener? = null
    var onShareButtonClicked: ((data: Structure?) -> Unit)? = null
    private var highLightPosition = -1

    override fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return if(oldItem is Structure && newItem is Structure) {
            (oldItem.id == newItem.id)
        } else {
            false
        }
    }

    override fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject): Boolean {
        return if(oldItem is Structure && newItem is Structure) {
            when(oldItem.style) {
                BlockStyle.HighLight -> oldItem.items == newItem.items
                else -> {
                    oldItem.name == newItem.name &&
                            oldItem.shortDescription == newItem.shortDescription &&
                            oldItem.background == newItem.background &&
                            oldItem.items == newItem.items
                }
            }
        } else {
            false
        }
    }

    override fun getItemViewType(position: Int): Int {
        val item = differ.currentList[position]
        return when (item.contentType) {
            Structure.ContentType.Content -> item.style.ordinal
            Structure.ContentType.Ads -> 1_000
            Structure.ContentType.ButtonShare -> 2_000
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            1_000 -> {
                TipGuidelineViewHolder(
                    BlockAdsTvcBannerBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    ),
                    tvcBannerActionListener
                )

            }
            BlockStyle.AdsTvcBanner.ordinal -> {
                TvcBannerViewHolder(
                    BlockAdsTvcBannerBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    ),
                    tvcBannerActionListener
                )
            }

            BlockStyle.HighLight.ordinal -> {
                blockHandler.onCreateViewHolder(type = BlockHandler.Type.HighLight, parent)
            }

            2_000 -> {
                ButtonShareViewHolder(BlockButtonShareBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ), onShare = onShareButtonClicked)
            }

            BlockStyle.HorizontalHighlight.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.HorizontalHighlight,
                parent
            )

            BlockStyle.VodDetail.ordinal -> blockHandler.onCreateViewHolder(type = BlockHandler.Type.VodDetail, parent)

            BlockStyle.AutoExpand.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.AutoExpand,
                parent
            )

            BlockStyle.Category.ordinal -> blockHandler.onCreateViewHolder(type = BlockHandler.Type.Category, parent)

            BlockStyle.NumericRank.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.NumericRank,
                parent
            )

            BlockStyle.VerticalSliderSmall.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.VerticalSliderSmall,
                parent
            )

            BlockStyle.VerticalSliderMedium.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.VerticalSliderMedium,
                parent
            )

            BlockStyle.HorizoltalSlider.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.HorizontalSlider,
                parent
            )

            BlockStyle.HorizontalSliderSmall.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.HorizontalSlider, // same layout with HorizontalSlider
                parent
            )

            BlockStyle.FeatureHorizontalSlider.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.FeaturedHorizontalSlider,
                parent
            )

            BlockStyle.HorizontalSliderWithBackground.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.HorizontalWithBackground,
                parent
            )

            BlockStyle.HorizontalSliderHyperlink.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.HorizontalSliderHyperlink,
                parent
            )

            BlockStyle.SportSideBySideTable.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.SportSideBySide,
                parent
            )

            BlockStyle.VerticalSliderVideo.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.VerticalSliderVideo,
                parent
            )
            BlockStyle.VerticalSliderVideoWithBackground.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.VerticalSliderVideoWithBackground,
                parent
            )
            BlockStyle.SquareSlider.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.SquareSlider,
                parent
            )
            BlockStyle.GameHorizontalHighlight.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.GameHorizontalHighlight,
                parent
            )
            BlockStyle.GameHorizontalSquare.ordinal -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.GameHorizontalSquare,
                parent
            )

            else -> blockHandler.onCreateViewHolder(
                type = BlockHandler.Type.FeaturedHorizontalSlider,
                parent
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val currentItem = differ.currentList[position]
        when (holder) {
            is ButtonShareViewHolder -> {
                holder.bind(currentItem)
            }
            is BlockHandler.BlockHighLightViewHolder -> {
                lifecycleOwner?.lifecycle?.addObserver(holder)
                holder.setCurrentPosition(highLightPosition)
                holder.bind(currentItem.items)
                listViewHolder.add(holder)
            }
            is BlockHandler.BlockViewHolder -> {
                lifecycleOwner?.lifecycle?.addObserver(holder)
                listViewHolder.add(holder)
                val itemExcessLimit = if(currentItem.redirect.viewMoreLimit < 0) {
                    // have no viewMoreLimit, check with config limit
                    currentItem.items.size > MainApplication.INSTANCE.appConfig.numItemOfPage
                } else {
                    // have viewMoreLimit, check with viewMoreLimit
                    currentItem.items.size > currentItem.redirect.viewMoreLimit
                }
                holder.bind(
                    data = BlockEntity(
                        blockId = currentItem.id,
                        blockStyle = currentItem.style.id,
                        iType = currentItem.itype,
                        header = currentItem.name,
                        subHeader = currentItem.shortDescription,
                        background = currentItem.background,
                        items = if (currentItem.items.size > MainApplication.INSTANCE.appConfig.numItemOfPage) currentItem.items.subList(
                            0,
                            MainApplication.INSTANCE.appConfig.numItemOfPage
                        ) else currentItem.items,
                        hasViewMore =
                            currentItem.itype != Constants.STRUCTURE_ITEM_TYPE_FPT_PLAY_SHOP
                                    && currentItem.redirect.type.isNotBlank()
                                    && (currentItem.redirect.id.isNotBlank() || currentItem.redirect.type != "page")
                                    && itemExcessLimit
                                        ,
//                        hasViewMore = currentItem.redirect.id.isNotBlank(),
                        viewMoreText = currentItem.redirect.text.ifBlank { MainApplication.INSTANCE.appConfig.textViewMore },
                        viewMoreIcon = currentItem.redirect.viewMoreIcon,
                        viewMoreType = currentItem.redirect.type,
                        viewMoreId = currentItem.redirect.id ?: "",
                        customData = currentItem.customData,
                        //
                        isEnableWatchingProgress = isEnableWatchingProgress()
                    )
                )
            }
            is BlockHandler.BlockSportSideBySideViewHolder -> holder.bind(currentItem)

            is TipGuidelineViewHolder -> {
                holder.bind(currentItem)
                if (lifecycleOwner != null) {
//                    holder.showAdsBanner(lifecycleOwner)
                    if (!mapTipGuidelineLifecycleObserver.containsKey(holder)) {
                        val tipGuidelineLifecycleObserver = TipGuidelineLifecycleObserver(holder)
                        mapTipGuidelineLifecycleObserver[holder] = tipGuidelineLifecycleObserver
                        lifecycleOwner.lifecycle.addObserver(tipGuidelineLifecycleObserver)
                        Timber.tag("tamlog")
                            .w("add TipGuidelineLifecycleObserver onBindViewHolder ${mapTipGuidelineLifecycleObserver.size}")
                    }
                }


            }

            is TvcBannerViewHolder -> {
                holder.bind(currentItem, sharedPreferences = sharedPreferences)
                if (lifecycleOwner != null) {
//                    holder.showAdsBanner(lifecycleOwner)
                    if (!mapTvcBannerLifecycleObserver.containsKey(holder)) {
                        Timber.tag("tam-ads")
                            .w("add TvcBannerViewHolder onBindViewHolder ${holder.absoluteAdapterPosition} BEFORE ${mapTvcBannerLifecycleObserver.size}")
                        val tvcBannerLifecycleObserver = TvcBannerLifecycleObserver(holder)
                        mapTvcBannerLifecycleObserver[holder] = tvcBannerLifecycleObserver
                        lifecycleOwner.lifecycle.addObserver(tvcBannerLifecycleObserver)
                        Timber.tag("tam-ads")
                            .w("add TvcBannerViewHolder onBindViewHolder AFTER ${mapTvcBannerLifecycleObserver.size}")
                    } else {
                        holder.showAdsBanner(lifecycleOwner)
                    }
                }
            }

            is BlockHandler.UnknownViewHolder -> {
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) super.onBindViewHolder(holder, position, payloads)
        else {
            when (holder) {
                is BlockHandler.BlockViewHolder -> {
                    holder.bindItems(data = (payloads[0] as List<BaseObject>))
                }
                is BlockHandler.BlockHighLightViewHolder -> {
                    for (obj in payloads) {
                        when (obj) {
                            is HighLightOrientation -> {
                                holder.bindOrientation(obj,highLightPosition)
                            }
                            is ScrollHighLightToNextPosition -> {
                                holder.scrollToNextPosition()
                            }
                            is ScrollHighLightToPosition -> {
                                holder.scrollToPosition(obj.position, obj.isSmooth)
                            }
                            else -> {
                                holder.bind(data = obj as List<StructureItem>)
                            }
                        }
                    }
                }
            }
        }
    }

    fun stopAllTimer() {
        try {
            listViewHolder.forEach {
                when (it) {
                    is BlockHandler.BlockViewHolder -> {
                        it.stopTimer()
                        lifecycleOwner?.lifecycle?.removeObserver(it)
                    }
                    is BlockHandler.BlockHighLightViewHolder -> {
                        it.stopTimer()
                        lifecycleOwner?.lifecycle?.removeObserver(it)
                    }
                }
            }
        } finally {
            listViewHolder.clear()
        }
    }

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        when (holder) {
//            is BlockHandler.BlockHighLightViewHolder -> {
//                holder.stopTimer()
//                lifecycleOwner?.lifecycle?.removeObserver(holder)
//                listViewHolder.remove(holder)
//            }
//            is BlockHandler.BlockViewHolder -> {
//                holder.stopTimer()
//                lifecycleOwner?.lifecycle?.removeObserver(holder)
//                listViewHolder.remove(holder)
//            }
            is TipGuidelineViewHolder -> {
                try {
                    if (mapTipGuidelineLifecycleObserver.containsKey(holder)) {
                        val lifecycleObserver = mapTipGuidelineLifecycleObserver[holder]
                        mapTipGuidelineLifecycleObserver.remove(holder)
                        lifecycleObserver?.let {
                            lifecycleOwner?.lifecycle?.removeObserver(it)
                        }
                        Timber.tag("tamlog").w("remove TipGuidelineLifecycleObserver onViewRecycled ${mapTipGuidelineLifecycleObserver.size}")
                    }
                } catch (e: Exception) {

                }
            }
            is TvcBannerViewHolder -> {
                try {
//                    holder.closeAdsBanner()
                    if (mapTvcBannerLifecycleObserver.containsKey(holder)) {
                        val lifecycleObserver = mapTvcBannerLifecycleObserver[holder]
                        mapTvcBannerLifecycleObserver.remove(holder)
                        lifecycleObserver?.let {
                            lifecycleOwner?.lifecycle?.removeObserver(it)
                        }
                        Timber.tag("tam-ads").w("remove   TvcBannerLifecycleObserver onViewRecycled ${mapTvcBannerLifecycleObserver.size}")
                    }
                } catch (e: Exception) {

                }
            }
        }
    }

    override fun onViewDetachedFromWindow(holder: RecyclerView.ViewHolder) {

        when (holder) {
            is TipGuidelineViewHolder -> {
                try {
                    if (mapTipGuidelineLifecycleObserver.containsKey(holder)) {
                        val lifecycleObserver = mapTipGuidelineLifecycleObserver[holder]
                        mapTipGuidelineLifecycleObserver.remove(holder)
                        lifecycleObserver?.let {
                            lifecycleOwner?.lifecycle?.removeObserver(it)
                        }
                        Timber.tag("tam-ads").w("remove TipGuidelineLifecycleObserver onViewDetachedFromWindow ${mapTipGuidelineLifecycleObserver.size}")
                    }
                } catch (e: Exception) {

                }
            }
        }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
    }

    //region Common
    fun insertDataToBlock(data: List<Structure>) {
        if (data.size == differ.currentList.size) {
            data.forEachIndexed { index, item ->
                differ.currentList[index].items = item.items
                notifyItemChanged(index, item.items)
            }
        }
    }

    fun refreshAndRemoveIfNeeded(item: Structure, dataSize: Int, index: Int) {
        if (dataSize < 1) {
            removeIndex(index)
        } else {
            changeDataAt(index, item)
        }
    }

    fun refreshStructureItem(item: Structure, dataSize: Int, index: Int) {
        data().getOrNull(index)?.let {
            it.items = item.items
            it.name = item.name
            notifyItemChanged(index)
        }
    }

    fun refresh(data: List<Structure>, callback: Runnable? = null) {
        // refresh data ads
        refreshAdsBlocks()
        bind(data, callback)
    }


    fun bindWithItems(data: List<Structure>, callback: Runnable? = null) {
        bind(data, callback)
    }

    private fun refreshAdsBlocks() {
        val listTipGuideKey: ArrayList<TipGuidelineViewHolder> = arrayListOf<TipGuidelineViewHolder>().apply {
            addAll(mapTipGuidelineLifecycleObserver.keys)
        }
        for(tipGuidelineViewHolder in listTipGuideKey) {
            tipGuidelineViewHolder.closeAdsBanner()
            val lifecycleObserver = mapTipGuidelineLifecycleObserver[tipGuidelineViewHolder]
            mapTipGuidelineLifecycleObserver.remove(tipGuidelineViewHolder)
            lifecycleObserver?.let {
                lifecycleOwner?.lifecycle?.removeObserver(it)
            }

        }

        val listTVCBannerKey: ArrayList<TvcBannerViewHolder> = arrayListOf<TvcBannerViewHolder>().apply {
            addAll(mapTvcBannerLifecycleObserver.keys)
        }
        for(tvcBannerViewHolder in listTVCBannerKey) {
            tvcBannerViewHolder.closeAdsBanner()
            val lifecycleObserver = mapTvcBannerLifecycleObserver[tvcBannerViewHolder]
            mapTvcBannerLifecycleObserver.remove(tvcBannerViewHolder)
            lifecycleObserver?.let {
                lifecycleOwner?.lifecycle?.removeObserver(it)
            }

        }

    }

    fun onBlockEvent(eventsListener: IEventListener<BaseObject>) {
        blockHandler.blockEventsListener = eventsListener
    }

    fun onEventsItem(eventsListener: IEventListener<BaseObject>) {
        blockHandler.itemEventsListener = eventsListener
    }

    fun onEventLinePositionsItem(eventsListener: BlockHandler.ItemViewParentEventsListener) {
        blockHandler.itemEventsParentListener = object : BlockHandler.ItemViewParentEventsListener {
            override fun getPositionLineItemView(pos: Int) {
                eventsListener.getPositionLineItemView(pos)
            }

            override fun getPositionFocusItem(
                posLine: Int,
                posFirstClicked: Int,
                posLastClicked: Int,
                data: BaseObject?
            ) {
                eventsListener.getPositionFocusItem(posLine, posFirstClicked, posLastClicked, data)
                data?.let {
                    if (it is StructureItem) {
                        if (it.blockStyle == BlockStyle.HighLight) {
                            highLightPosition = posLastClicked
                        }
                    }

                }
            }

            override fun itemScrollStateChange(blockType: BlockHandler.Type, state: Int) {
                eventsListener.itemScrollStateChange(blockType, state)
            }
        }
    }

    fun setHighLightPosition(position: Int) {
        highLightPosition = position
    }

    fun updateAdPosition(adPosition: Int, adPositionData: AdPosition?) {
        val adBlockPosition = differ.currentList.find {
            it.adPosition == adPosition && it.style == BlockStyle.AdsTvcBanner
        }?.apply {
            adData = adPositionData?.let {
                AdData(
                    url = it.data?.url,
                    options = it.data?.options,
                    isFirstTimeInit = true,
                    position = adPosition

                )
            } ?: AdData(
                url = null,
                options = null,
                isFirstTimeInit = true,
                position = adPosition
            )
        }

        if (adBlockPosition != null) {
            notifyItemChanged(differ.currentList.indexOf(adBlockPosition))
        }
    }

    fun getActualPositionForHighlight(position: Int, linePosition: Int) : Int {
        val listItem = differ.currentList[linePosition].items
        return if (listItem.isNotEmpty()) position % listItem.size else -1
    }
 }