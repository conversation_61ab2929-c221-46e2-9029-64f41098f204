package com.fptplay.mobile.homebase.view.infinity_highlight_indicator

import timber.log.Timber
import kotlin.math.max
import kotlin.math.min

internal class InfinityHighlightDotManager(
    count: Int,
    private val dotSize: Int,
    private val dotSpacing: Int,
    private val dotBound: Int,
    private val dotSizes: Map<Byte, Int>,
    private val sizeThreshold: Int,
    private val targetScrollListener: TargetScrollListener? = null
) {

    internal var dots: ByteArray = ByteArray(count)
    internal var selectedIndex = 0

    private var scrollAmount = 0

    init {

        if (count > 0) {
            dots[0] = InfinityHighlightIndicator.BYTE_SELECTED
        }

        val thresholdVisible = sizeThreshold - 2
//        Timber.d("*****ThreadHoldVisible = $thresholdVisible")
        if (count <= thresholdVisible ) {
            (1 until count).forEach { i -> dots[i] = InfinityHighlightIndicator.BYTE_3 }
        } else {

            (1..thresholdVisible).forEach { i ->
//                dots[i] = InfinityHighlightIndicator.BYTE_5
                dots.safeSet(i, InfinityHighlightIndicator.BYTE_3)
            }
//            dots[thresholdVisible - 1] = InfinityHighlightIndicator.BYTE_4
            dots.safeSet(thresholdVisible + 1, InfinityHighlightIndicator.BYTE_1)
            if (count > thresholdVisible) {
                // small dot for more in case number of dot are bigger than threshold
//                dots[treshold_visible] = InfinityHighlightIndicator.BYTE_2
                dots.safeSet(thresholdVisible, InfinityHighlightIndicator.BYTE_2)
            }
            (thresholdVisible + 2 until count).forEach { i ->
//                dots[i] = 0
                dots.safeSet(i, 0)
            }
        }
    }

    internal fun dots() = dots.joinToString("")

    fun dotSizeFor(size: Byte) = dotSizes[size] ?: 0

    fun goToNext() {
        if (selectedIndex >= dots.size - 1) {
            return
        }

        ++selectedIndex

//        Timber.d("*****goToNext: ${dots.size} - $sizeThreshold")
        if (dots.size < sizeThreshold) {
            goToNextSmall()
        } else {
            goToNextLarge()
        }
    }

    fun goToPrevious() {
//        Timber.d("*****goToPrevious: ${dots.size} - $sizeThreshold")
        if (selectedIndex == 0) {
            return
        }

        --selectedIndex

        if (dots.size < sizeThreshold) {
            goToPreviousSmall()
        } else {
            goToPreviousLarge()
        }
    }

    fun goToBegin() {
        //move to the beginning
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_3)
        selectedIndex = 0
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_SELECTED)
        val lastVisibleIndex = min(sizeThreshold, dots.size)
        if (dots.size >= sizeThreshold) {
            (selectedIndex + 1 until lastVisibleIndex).forEach {
                if (it < lastVisibleIndex - 2) {
                    dots.safeSet(it, InfinityHighlightIndicator.BYTE_3)
                } else {
                    dots.safeSet(lastVisibleIndex - 2, InfinityHighlightIndicator.BYTE_2)
                    dots.safeSet(lastVisibleIndex - 1, InfinityHighlightIndicator.BYTE_1)
                }
            }
        }
        (lastVisibleIndex + 1 until dots.size)
            .takeWhile { dots[it] != 0.toByte() }
            .forEach { dots[it] = 0 }

        scrollAmount = 0
        targetScrollListener?.scrollToTarget(scrollAmount)
    }

    fun goToEnd() {
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_3)
        selectedIndex = dots.size - 1
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_SELECTED)
        //move to the end
        val firstVisibleIndex = max(dots.size - sizeThreshold, 0)
        if (dots.size >= sizeThreshold) {
            (selectedIndex - 1 downTo firstVisibleIndex).forEach {
                if (it > firstVisibleIndex + 1) {
                    dots.safeSet(it, InfinityHighlightIndicator.BYTE_3)
                } else {
                    dots.safeSet(firstVisibleIndex + 1, InfinityHighlightIndicator.BYTE_2)
                    dots.safeSet(firstVisibleIndex, InfinityHighlightIndicator.BYTE_1)
                }
            }
        }
        if (firstVisibleIndex > 0) {
            (firstVisibleIndex - 1 downTo 0)
                .takeWhile { dots[it] != 0.toByte() }
                .forEach { dots[it] = 0 }
        }
        scrollAmount = firstVisibleIndex * (dotSize + dotSpacing)
        targetScrollListener?.scrollToTarget(scrollAmount)
    }

    private fun goToNextSmall() {
//        Timber.d("*****goToNextSmall: $selectedIndex - ${dots.size} - $sizeThreshold")
//        dots[selectedIndex] = InfinityHighlightIndicator.BYTE_SELECTED
//        dots[selectedIndex - 1] = InfinityHighlightIndicator.BYTE_5
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_SELECTED)
        dots.safeSet(selectedIndex - 1, InfinityHighlightIndicator.BYTE_3)
    }

    private fun goToNextLarge() {
//        Timber.d("*****goToNextLarge: $selectedIndex - ${dots.size} - $sizeThreshold")
        // swap 6 and 5
//        dots[selectedIndex] = InfinityHighlightIndicator.BYTE_SELECTED
//        dots[selectedIndex - 1] = InfinityHighlightIndicator.BYTE_5
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_SELECTED)
        dots.safeSet(selectedIndex - 1, InfinityHighlightIndicator.BYTE_3)

        val thresholdVisible = min(sizeThreshold, dots.size) - 2 // last index in frame
        var isLastTwoDotInFrame = true
        if(selectedIndex >= thresholdVisible) {
            (selectedIndex - 1 downTo selectedIndex - (thresholdVisible - 2)).forEach {
                // for each of dot that supposed to be in frame
//                Timber.tag("tamlog").w("check $it ${dots[it]}")
                if (dots[it] != InfinityHighlightIndicator.BYTE_3) {
                    isLastTwoDotInFrame = false
                    return@forEach
                }
            }
            if(isLastTwoDotInFrame) {
//                Timber.d("*****isLastTwoDotInFrame: $selectedIndex - ${dots.size} - $sizeThreshold")
                // move to the last two dot in frame, change the first dot in frame to byte 2
//                val secondIndexInFrame = selectedIndex - (thresholdVisible - 2)
//                dots.safeSet(secondIndexInFrame, InfinityHighlightIndicator.BYTE_4)
                val firstIndexInFrame = if(selectedIndex == dots.size - 1) {
                    selectedIndex - thresholdVisible - 1
                } else if(selectedIndex == dots.size - 2){
                    selectedIndex - thresholdVisible
                } else {
                    selectedIndex - (thresholdVisible - 1)

                }
                Timber.d("*****isLastTwoDotInFrame: $firstIndexInFrame")
                dots.safeSet(firstIndexInFrame, InfinityHighlightIndicator.BYTE_1)
                dots.safeSet(firstIndexInFrame + 1, InfinityHighlightIndicator.BYTE_2)

                // change the one before that to byte 0 -> invisible
                (firstIndexInFrame - 1 downTo 0)
                    .takeWhile { dots[it] != 0.toByte() }
                    .forEach { dots[it] = 0 }
            }
        }
        // BYTE_SELECTED must be around BYTE_3 or higher
        if (selectedIndex + 1 < dots.size && dots[selectedIndex + 1] < InfinityHighlightIndicator.BYTE_2) {
            Timber.d("*****Setup  2 dots behind: $selectedIndex")
            dots[selectedIndex + 1] = InfinityHighlightIndicator.BYTE_2
            // set the next one to 1 if any
            if (selectedIndex + 3 == dots.size) {
                Timber.d("*****Setup last 2 dots")
                dots[selectedIndex + 1] = InfinityHighlightIndicator.BYTE_3
                dots[selectedIndex + 2] = InfinityHighlightIndicator.BYTE_3
            }else if (selectedIndex + 2 < dots.size && dots[selectedIndex + 2] < InfinityHighlightIndicator.BYTE_1) {
                dots[selectedIndex + 2] = InfinityHighlightIndicator.BYTE_1
            }
        }
        Timber.tag("tamlog").d("dots ${dots.contentToString()}")

        // Scroll to keep the selected dot within bound
        val endBound = selectedIndex * (dotSize + dotSpacing) + dotSize
        val isLastTwoDot = dots.size - selectedIndex <= 2
        if (endBound > dotBound && isLastTwoDotInFrame && !isLastTwoDot) {
            scrollAmount = endBound - dotBound
            targetScrollListener?.scrollToTarget(scrollAmount)
        }
        Timber.tag("tamlog").d("endBound $endBound - $dotBound - $scrollAmount")

    }

    private fun goToPreviousSmall() {
//        Timber.d("*****goToPreviousSmall: $selectedIndex - ${dots.size} - $sizeThreshold")
        dots.safeSet(selectedIndex, InfinityHighlightIndicator.BYTE_SELECTED)
        dots.safeSet(selectedIndex + 1, InfinityHighlightIndicator.BYTE_3)
    }

    private fun goToPreviousLarge() {
//        Timber.d("*****goToPreviousLarge: $selectedIndex - ${dots.size} - $sizeThreshold")
        // swap 6 and 5
        dots[selectedIndex] = InfinityHighlightIndicator.BYTE_SELECTED
        dots[selectedIndex + 1] = InfinityHighlightIndicator.BYTE_3

        // no more than 3 5's in a row backward
        val thresholdVisible = min(sizeThreshold, dots.size)  - 2
        var isFirstTwoDotInFrame = true
        if(selectedIndex <= dots.size - 1 - (thresholdVisible)) {
            // move to the first two dot in frame, change the last dot in frame to byte 4
            (selectedIndex + 1 until (selectedIndex + thresholdVisible - 1)).forEach {
                // for each of dot that supposed to be in frame
                Timber.tag("tamlog").w("check $it ${dots[it]}")
                if (dots[it] != InfinityHighlightIndicator.BYTE_3) {
                    isFirstTwoDotInFrame = false
                    return@forEach
                }
            }
            if(isFirstTwoDotInFrame) {
//                Timber.d("*****isFirstTwoDotInFrame: $selectedIndex - ${dots.size} - $sizeThreshold")
                val lastIndexInFrame = if (selectedIndex == 0) {
                    selectedIndex + thresholdVisible + 1
                } else if(selectedIndex == 1) {
                    selectedIndex + thresholdVisible
                }
                else {
                    selectedIndex + (thresholdVisible - 1)
                }
//                Timber.d("*****lastIndexInFrame: $lastIndexInFrame")
                dots.safeSet(lastIndexInFrame, InfinityHighlightIndicator.BYTE_1)
                dots.safeSet(lastIndexInFrame - 1, InfinityHighlightIndicator.BYTE_2)
                // change the one before that to byte 0 -> invisible
                (lastIndexInFrame + 1 until dots.size)
                    .takeWhile { dots[it] != 0.toByte() }
                    .forEach { dots[it] = 0 }
            }
        }
        if (selectedIndex - 2 >= -1 && dots[selectedIndex - 1] < InfinityHighlightIndicator.BYTE_3) {
            if(selectedIndex - 2 > 0) {
                dots.safeSet(selectedIndex - 1, InfinityHighlightIndicator.BYTE_2)
                dots.safeSet(selectedIndex - 2, InfinityHighlightIndicator.BYTE_1)
            } else if(selectedIndex - 2 == 0) {
                dots[selectedIndex - 2] = InfinityHighlightIndicator.BYTE_3
                dots[selectedIndex - 1] = InfinityHighlightIndicator.BYTE_3
            }
        }

        Timber.tag("tamlog").v("dots ${dots.contentToString()} : isFirstTwoDotInFrame $isFirstTwoDotInFrame")

        val isFirstTwoDot = selectedIndex < 2
        // Scroll to keep the selected dot within bound
        // minus 2 because scroll when at first two dot in frame
        val startBound = (selectedIndex - 2) * (dotSize + dotSpacing)
        if (startBound < scrollAmount && isFirstTwoDotInFrame && !isFirstTwoDot) {
            scrollAmount =  startBound
            targetScrollListener?.scrollToTarget(scrollAmount)
        }

    }

    private fun ByteArray.safeSet(index: Int, value: Byte) {
        if(index in this.indices) {
            set(index, value)
        }
    }
    private fun ByteArray.safeGet(index: Int): Byte {
        return if(index in this.indices) {
            get(index)
        } else {
            0
        }
    }

    interface TargetScrollListener {
        fun scrollToTarget(target: Int)
    }

}