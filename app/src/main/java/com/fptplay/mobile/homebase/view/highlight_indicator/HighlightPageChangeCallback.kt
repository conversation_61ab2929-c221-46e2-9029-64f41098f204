package com.fptplay.mobile.homebase.view.highlight_indicator

import androidx.viewpager2.widget.ViewPager2
import timber.log.Timber

class HighlightPageChangeCallback(private val indicator: HighlightIndicator): ViewPager2.OnPageChangeCallback() {

    override fun onPageScrollStateChanged(state: Int) {}

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

    override fun onPageSelected(position: Int) {
        indicator.selectPosition(position)
    }

}