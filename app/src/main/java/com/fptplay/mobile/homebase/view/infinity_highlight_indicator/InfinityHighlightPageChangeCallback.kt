package com.fptplay.mobile.homebase.view.infinity_highlight_indicator

import androidx.viewpager2.widget.ViewPager2

class InfinityHighlightPageChangeCallback(private val indicator: InfinityHighlightIndicator): ViewPager2.OnPageChangeCallback() {

    override fun onPageScrollStateChanged(state: Int) {}

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

    override fun onPageSelected(position: Int) {
        indicator.selectPosition(position)
    }

}