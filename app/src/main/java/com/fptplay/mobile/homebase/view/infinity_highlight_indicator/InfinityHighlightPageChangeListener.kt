package com.fptplay.mobile.homebase.view.infinity_highlight_indicator

import androidx.viewpager.widget.ViewPager
import com.fptplay.mobile.homebase.view.infinity_highlight_indicator.InfinityHighlightIndicator

class InfinityHighlightPageChangeListener(private val indicator: InfinityHighlightIndicator) : ViewPager.OnPageChangeListener {
    private var selectedPage = 0

    override fun onPageScrollStateChanged(state: Int) {}

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

    override fun onPageSelected(position: Int) {
        if (position != selectedPage) {
            when {
                this.selectedPage < position -> indicator.swipeNext()
                else -> indicator.swipePrevious()
            }
        }
        selectedPage = position
    }
}
