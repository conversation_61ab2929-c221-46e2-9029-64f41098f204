package com.fptplay.mobile.homebase.view.highlight_indicator

import androidx.viewpager.widget.ViewPager

class HighlightPageChangeListener(private val indicator: HighlightIndicator) : ViewPager.OnPageChangeListener {
    private var selectedPage = 0

    override fun onPageScrollStateChanged(state: Int) {}

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

    override fun onPageSelected(position: Int) {
        if (position != selectedPage) {
            when {
                this.selectedPage < position -> indicator.swipeNext()
                else -> indicator.swipePrevious()
            }
        }
        selectedPage = position
    }
}
