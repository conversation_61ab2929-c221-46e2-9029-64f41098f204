package com.fptplay.mobile.homebase

import android.os.Bundle
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.PageId
import com.fptplay.mobile.common.utils.PageProvider
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure

class HomeBaseImplFragment: HomeBaseFragment() {
    companion object {
        private const val SCREEN_PROVIDER = "screen_provider"
        fun newInstance(screenProvider: String, reloadTime: Int): HomeBaseImplFragment {
            val bundle = Bundle()
            bundle.putString(SCREEN_PROVIDER, screenProvider)
            bundle.putInt(Constants.RELOAD_PAGE, reloadTime)
            val f = HomeBaseImplFragment().apply { pageId = PageId.OtherPageId(screenProvider) }
            f.arguments = bundle
            return f
        }
    }

    private var screenProvider: String = ""
    override val handleBackPressed = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
            arguments?.run {
                screenProvider = getString(SCREEN_PROVIDER, "")
            }
    }

    override fun getStructure(id: String) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetStructure(
                pageProvider = PageProvider.Other(screenProvider),
                userLogin = sharedPreferences.userLogin(),
                revision = MainApplication.INSTANCE.revision
            )
        )
    }

    override fun getStructureItem(
        data: List<Pair<Int, Structure>>,
        userId: String,
        page: Int,
        perPage: Int
    ) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetClusterItem(
                pageProvider = PageProvider.Other(screenProvider),
                data = data,
                userId = sharedPreferences.userId(),
                page = page,
                perPage = perPage
            )
        )
    }

    private fun updateUIStructureItem(index: Int, structure: Structure) {
//        Timber.d("*** Execute update item $index - ${structure.items.size}")
        homeBaseAdapter.refreshStructureItem(index = index, dataSize = structure.items.size, item = structure)
        setCategoryStructure(structure)
        logRecommendBlockItem(structure = structure)
    }

    override fun getStructureLocal() {
        viewModel.dispatchIntent(HomeBaseViewModel.HomeBaseIntent.GetStructureLocal(pageProvider = PageProvider.Other(screenProvider), homeBaseAdapter.data().filter { it.contentType != Structure.ContentType.Ads }))
    }

    override fun getStructureItemLocal(data: List<Pair<Int, Structure>>) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetClusterItemLocal(
                pageProvider = PageProvider.Other(screenProvider),
                data = data
            )
        )
    }

    override fun processStateToUi(state: HomeBaseViewModel.HomeBaseState) {
        when (state) {
            is HomeBaseViewModel.HomeBaseState.StructureOtherResult -> {
                processStructureResult(state.data)
                sendTrackingBlock511(state.data)
            }

            is HomeBaseViewModel.HomeBaseState.ResultOtherClusterStructureItem -> {
                processStructureItemResult(state.data)
                if(showButtonShare) {
                    processButtonShare(
                        Structure(
                            contentType = Structure.ContentType.ButtonShare,
                            name = metaName ?: "",
                            id = pageId?.id ?: ""
                        )
                    )
                }
            }

            is HomeBaseViewModel.HomeBaseState.ResultStructureItem -> {
//                Timber.d("*** Execute result: ${state.data.first}")
                updateUIStructureItem(index = state.data.first, state.data.second)
            }

            else -> {}
        }
    }

}