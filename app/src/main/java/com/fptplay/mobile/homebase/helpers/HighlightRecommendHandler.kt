package com.fptplay.mobile.homebase.helpers

import com.fptplay.mobile.homebase.data.BlockHighlightRecommendation
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class HighlightRecommendHandler(val sharedPreferences: SharedPreferences) {
    private val cacheTimeMs = sharedPreferences.getHighlightRecommendTimeCacheInSecond() * 1000

    val highlightRecommendBlocks: ArrayList<BlockHighlightRecommendation> = ArrayList()

    private fun getBlockRecommendKey(blockId: String): String {
        return "$blockId/${sharedPreferences.userId()}/${sharedPreferences.profileId()}/${sharedPreferences.profileType()}"
    }

    fun updateHighlightRecommendBlock(
        blockId: String,
        blockRecommendationData: BlockHighlightRecommendation.BlockHighlightRecommendationData
    ) {
        for (block in highlightRecommendBlocks) {
            if (block.blockKey == getBlockRecommendKey(blockId)) {
                block.blockRecommendationData = blockRecommendationData
                return
            }
        }
        highlightRecommendBlocks.add(BlockHighlightRecommendation(getBlockRecommendKey(blockId), blockRecommendationData))
    }

    fun getHighlightRecommendBlock(blockId: String): BlockHighlightRecommendation? {
        for (block in highlightRecommendBlocks) {
            if (block.blockKey == getBlockRecommendKey(blockId)) {
                if (isBlockCacheValid(block)) {
                    return block
                } else {
                    highlightRecommendBlocks.remove(block)
                    return null
                }
            }
        }
        return null
    }

    fun getHighlightRecommendBlockWithFlow(blockId: String): Flow<Result<BlockHighlightRecommendation>> =
        flow {
            emit(Result.Init())
            val block = getHighlightRecommendBlock(blockId)
            if (block != null) {
                emit(Result.Success(block))
            } else {
                emit(Result.Error.General("Block not found"))
            }
            emit(Result.Done)
        }

    private fun isBlockCacheValid(block: BlockHighlightRecommendation): Boolean {
        return System.currentTimeMillis() - block.blockRecommendationData.blockRecommendationTimestamp < cacheTimeMs
    }

    fun mergeOriginalDataWithRecommend(originalData: List<StructureItem>, recommendData: List<StructureItem>, requiredDataSize: Int): List<StructureItem> {
        if(recommendData.isEmpty()) return originalData

        // Get only 31 items from recommend data
        val subRecommend = if(recommendData.size > requiredDataSize) {
            recommendData.subList(0, requiredDataSize)
        } else {
            recommendData
        }

        // Copy original data to result
        val result: ArrayList<StructureItem> = arrayListOf<StructureItem>().apply { addAll(originalData) }

        // Separate pinned and non-pinned items
        val pinnedList: ArrayList<StructureItem> = arrayListOf()
        val nonPinnedList: ArrayList<StructureItem> = arrayListOf()

        for(item in originalData) {
            if (item.pin) {
                pinnedList.add(item)
            } else {
                nonPinnedList.add(item)
            }
        }

        // Merge recommend data with original data
        val recommendAfterEdit = ((subtractLists(subRecommend, pinnedList)) + (nonPinnedList)).distinctBy { it.id }.toMutableList()

        if(recommendAfterEdit.isEmpty())
            return result
        else {

            originalData.forEachIndexed { index, item ->
                if(recommendAfterEdit.isEmpty()) {
                    return@forEachIndexed
                } else {
                    if(!item.pin) {
                        result[index] = recommendAfterEdit.removeAt(0)
                    }
                }
            }

            // Add more recommend items if the original data is less than 31 items
            if(result.size < requiredDataSize) {
                val quantity = requiredDataSize - result.size
                if(recommendAfterEdit.size > quantity) {
                    result.addAll(recommendAfterEdit.subList(0, quantity))
                } else {
                    result.addAll(recommendAfterEdit)
                }
            }
        }

        return result
    }

    private fun subtractLists(listA: List<StructureItem>, listB: List<StructureItem>): List<StructureItem> {
        val idsToExclude = listB.map { it.id }.toSet()
        return listA.filter { item -> item.id !in idsToExclude }
    }
}