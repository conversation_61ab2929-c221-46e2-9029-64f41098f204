package com.fptplay.mobile.homebase.view.infinity_highlight_indicator

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Parcelable
import android.util.AttributeSet
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.view.animation.Interpolator
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.fptplay.mobile.R
import timber.log.Timber
import kotlin.math.max
import kotlin.math.min

open class InfinityHighlightIndicator @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr), InfinityHighlightDotManager.TargetScrollListener {

    private lateinit var dotSizes: IntArray
    private lateinit var dotAnimators: Array<ValueAnimator>

    private val defaultPaint = Paint().apply { isAntiAlias = true }
    private val selectedPaint = Paint().apply { isAntiAlias = true }

    private val dotSize: Int
    private val dotSizeMap: Map<Byte, Int>
    private val dotBound: Int
    private val dotSpacing: Int
    private val animDuration: Long
    private val animInterpolator: Interpolator
    private var centered: Boolean = true
    private val customInitalPadding: Int

    private var dotManager: InfinityHighlightDotManager? = null
    private var scrollAmount: Int = 0
    private var scrollAnimator: ValueAnimator? = null
    private var initialPadding: Int = 0

    private lateinit var scrollListener: RecyclerView.OnScrollListener
    private lateinit var pageChangeListener: ViewPager.OnPageChangeListener
    private lateinit var pageChangeCallback: InfinityHighlightPageChangeCallback

    private var selectedPage = 0
    var count: Int = 0
        set(value) {
            selectedPage = 0
            dotManager = InfinityHighlightDotManager(
                value,
                dotSize,
                dotSpacing,
                dotBound,
                dotSizeMap,
                SIZE_THRESHOLD,
                this)

            dotSizes = IntArray(value)
            dotManager?.let { it.dots.forEachIndexed { index, dot -> dotSizes[index] = it.dotSizeFor(dot) } }
            dotAnimators = Array(value) { ValueAnimator() }

            initialPadding = when {
                !centered -> 0
                customInitalPadding != -1 -> customInitalPadding
                else -> when (value) {
                    in 0..4 -> (dotBound + (4 - value) * (dotSize + dotSpacing) + dotSpacing) / 2
                    else -> 2 * (dotSize + dotSpacing)
                }
            }

            field = value
            requestLayout()
        }

    init {
        val ta = getContext().obtainStyledAttributes(attrs, R.styleable.HighlightIndicator)
        dotSizeMap = mapOf(
            BYTE_SELECTED to ta.getDimensionPixelSize(R.styleable.HighlightIndicator_piSize1, 4.dp),
            BYTE_3 to ta.getDimensionPixelSize(R.styleable.HighlightIndicator_piSize2, 4.dp),
            BYTE_2 to ta.getDimensionPixelSize(R.styleable.HighlightIndicator_piSize5, 4.dp),
            BYTE_1 to ta.getDimensionPixelSize(R.styleable.HighlightIndicator_piSize6, 3f.dp)
        )
        dotSize = dotSizeMap.values.maxOrNull() ?: 0
//        Timber.d("*****dotSize: $dotSize")
        dotSpacing = ta.getDimensionPixelSize(R.styleable.HighlightIndicator_piDotSpacing, 4.dp)
//        Timber.d("*****dotSpacing: $dotSpacing")
        centered = ta.getBoolean(R.styleable.HighlightIndicator_piCentered, true)
        dotBound = (SIZE_THRESHOLD - 2) * (dotSize + dotSpacing) // scroll at 2 last dots
//        Timber.d("*****dotBound: $dotBound")
        customInitalPadding = ta.getDimensionPixelSize(R.styleable.HighlightIndicator_piInitialPadding, -1)

        animDuration = ta.getInteger(
            R.styleable.HighlightIndicator_piAnimDuration, DEFAULT_ANIM_DURATION
        ).toLong()
        defaultPaint.color = ta.getColor(
            R.styleable.HighlightIndicator_piDefaultColor,
            ContextCompat.getColor(getContext(), R.color.highlight_indicator_default_pi_color))
        selectedPaint.color = ta.getColor(
            R.styleable.HighlightIndicator_piSelectedColor,
            ContextCompat.getColor(getContext(), R.color.highlight_indicator_focus_pi_color))
        animInterpolator = AnimationUtils.loadInterpolator(context, ta.getResourceId(
            R.styleable.HighlightIndicator_piAnimInterpolator,
            R.anim.highlight_indicator_pi_default_interpolator))
        ta.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // FIXME: add support for `match_parent`
//        setMeasuredDimension(10 * (dotSize + dotSpacing) + dotBound + initialPadding, dotSize)
        val numDotVisible = min(count, SIZE_THRESHOLD)
//        Timber.d("*****size: $count - $SIZE_THRESHOLD")
        setMeasuredDimension(numDotVisible * (dotSize + dotSpacing), dotSize)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        var paddingStart = 0
        val (start, end) = getDrawingRange()

        paddingStart += (dotSize + dotSpacing) * start
        val pi1Paint = Paint().apply {
            color = Color.RED
        }
        val pi2Paint = Paint().apply {
            color = Color.GREEN
        }
        val pi3Paint = Paint().apply {
            color =  Color.CYAN
        }

        val pi4Paint = Paint().apply {
            color =  Color.MAGENTA
        }
        val pi5Paint = Paint().apply {
            color =   Color.YELLOW
        }

        (start until end).forEach {
            canvas?.drawCircle(
                paddingStart + dotSize / 2f - scrollAmount,
                dotSize / 2f,
                dotSizes[it] / 2f,
                when (dotManager?.dots?.get(it)) {
                    BYTE_SELECTED -> selectedPaint
//                    BYTE_1 -> pi1Paint
//                    BYTE_2 -> pi2Paint
//                    BYTE_3 -> pi3Paint
//                    BYTE_4 -> pi4Paint
//                    BYTE_5 -> pi5Paint

                    else -> defaultPaint
                })
            paddingStart += dotSize + dotSpacing
        }
    }

    override fun onSaveInstanceState(): Parcelable? {
        val superState = super.onSaveInstanceState()
        if (superState == null) {
            return superState
        }

        val infinitySavedState = InfinitySavedState(superState)
        infinitySavedState.count = this.count
        infinitySavedState.selectedIndex = this.dotManager?.selectedIndex ?: 0
        return infinitySavedState
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        if (state !is InfinitySavedState) {
            super.onRestoreInstanceState(state)
            return
        }

        super.onRestoreInstanceState(state.superState)

        this.count = state.count
        for (i in 0 until state.selectedIndex) {
            swipeNext()
        }
    }

    override fun scrollToTarget(target: Int) {
        scrollAnimator?.cancel()
        scrollAnimator = ValueAnimator.ofInt(scrollAmount, target).apply {
            duration = animDuration
            interpolator = DEFAULT_INTERPOLATOR
            addUpdateListener { animation ->
                scrollAmount = animation.animatedValue as Int
                invalidate()
            }
            start()
        }
    }

    infix fun attachTo(recyclerView: RecyclerView) {
        if (::scrollListener.isInitialized) {
            recyclerView.removeOnScrollListener(scrollListener)
        }
        Timber.tag("tamlog").d("set count at attachTo RecyclerView")
        scrollListener = InfinityScrollListener(this)
        recyclerView.addOnScrollListener(scrollListener)
        scrollToTarget(0)
    }

    infix fun attachTo(viewPager: ViewPager) {
        if (::pageChangeListener.isInitialized) {
            viewPager.removeOnPageChangeListener(pageChangeListener)
        }
        pageChangeListener = InfinityHighlightPageChangeListener(this)
        viewPager.addOnPageChangeListener(pageChangeListener)
        scrollToTarget(0)
    }

    infix fun attachTo(viewPager: ViewPager2) {
        if (::pageChangeCallback.isInitialized) {
            viewPager.unregisterOnPageChangeCallback(pageChangeCallback)
        }
        pageChangeCallback = InfinityHighlightPageChangeCallback(this)
        viewPager.registerOnPageChangeCallback(pageChangeCallback)
        scrollToTarget(0)
    }

    fun selectPosition(position: Int) {
        val infinityPos = if (count > 0) position % count else 0
        val spacing = infinityPos - selectedPage

        when(spacing) {
            1 -> {
                swipeNext()
                this.selectedPage++
            }
            -1 -> {
                swipePrevious()
                this.selectedPage--
            }
            count -1 -> {
                swipeEnd()
                this.selectedPage = count -1
            }
            -(count - 1) -> {
                swipeBegin()
                this.selectedPage = 0
            }
            0 -> {
                swipeBegin()
                this.selectedPage = 0
            }
            else -> {
                while (infinityPos != selectedPage) {
                    when {
                        this.selectedPage < infinityPos -> {
                            swipeNext()
                            this.selectedPage++
                        }
                        else -> {
                            swipePrevious()
                            this.selectedPage--
                        }
                    }
                }
            }
        }
    }

    private fun swipeBegin() {
        dotManager?.goToBegin()
        animateDots()
    }

    private fun swipeEnd() {
        dotManager?.goToEnd()
        animateDots()
    }

    fun swipePrevious() {
        dotManager?.goToPrevious()
        animateDots()
    }

    fun swipeNext() {
        dotManager?.goToNext()
        animateDots()
    }

    private fun animateDots() {
        dotManager?.let {
            val (start, end) = getDrawingRange()
            (start until end).forEach { index ->
                dotAnimators[index].cancel()
                dotAnimators[index] = ValueAnimator.ofInt(dotSizes[index], it.dotSizeFor(it.dots[index]))
                    .apply {
                        duration = animDuration
                        interpolator = DEFAULT_INTERPOLATOR
                        addUpdateListener { animation ->
                            if (index in dotSizes.indices) {
                                dotSizes[index] = animation.animatedValue as Int
                                invalidate()
                            }
                        }
                    }
                dotAnimators[index].start()
            }
        }
    }

    private fun getDrawingRange(): Pair<Int, Int> {
        val start = max(0, (dotManager?.selectedIndex ?: 0) - MOST_VISIBLE_COUNT)
        val end = min(dotManager?.dots?.size ?: 0,
            (dotManager?.selectedIndex ?: 0) + MOST_VISIBLE_COUNT
        )

        return Pair(start, end)
    }

    companion object {
        const val BYTE_SELECTED = 6.toByte()
        const val BYTE_3 = 3.toByte()
        const val BYTE_2 = 3.toByte()
        const val BYTE_1 = 2.toByte()

        private const val MOST_VISIBLE_COUNT = 10
        private const val DEFAULT_ANIM_DURATION = 200

        private val DEFAULT_INTERPOLATOR = DecelerateInterpolator()

        const val SIZE_THRESHOLD = 5

    }
}