package com.fptplay.mobile.homebase.view.infinity_highlight_indicator

import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.common.extensions.findFirstVisibleItem
import com.fptplay.mobile.common.extensions.findViewAtPosition
import com.fptplay.mobile.common.extensions.getVisibleWidthOnScreen

internal class InfinityScrollListener(private val indicator: InfinityHighlightIndicator) : RecyclerView.OnScrollListener() {
    private var oldPos = -1
    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        val firstPosition = recyclerView.findFirstVisibleItem()
        when (newState) {
            RecyclerView.SCROLL_STATE_DRAGGING -> {
                updateSelectPos(firstPosition)
            }
            RecyclerView.SCROLL_STATE_IDLE  -> {
                val itemView = recyclerView.findViewAtPosition(firstPosition)
                val visiblePercent = itemView.getVisibleWidthOnScreen() * 1.0 / recyclerView.width
                if (visiblePercent > 0.5) {
                    updateSelectPos(firstPosition)
                }
            }
        }
    }

    private fun updateSelectPos(position: Int) {
        if (oldPos != position) {
            indicator.selectPosition(position)
            oldPos = position
        }
    }
}
