package com.fptplay.mobile.homebase

import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.*
import android.widget.FrameLayout
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.drowsyatmidnight.haint.android_banner_sdk.model.BannerRequestParams
import com.drowsyatmidnight.haint.android_banner_sdk.tvc_banner.OnGetTvcBannerCompleted
import com.drowsyatmidnight.haint.android_banner_sdk.tvc_banner.TvcBannerProxy
import com.drowsyatmidnight.haint.android_banner_sdk.tvc_banner.model.AdHtmlResponse
import com.drowsyatmidnight.haint.android_banner_sdk.tvc_banner.model.AdPosition
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.block.BlockHandler
import com.fptplay.mobile.common.adapter.block.BlockItemAdapter
import com.fptplay.mobile.common.adapter.block.highlight.BlockEntity
import com.fptplay.mobile.common.adapter.block.sport.SportBlockEntity
import com.fptplay.mobile.common.adapter.block.sport.SportTableType
import com.fptplay.mobile.common.extensions.*
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.global.GlobalEventListener
import com.fptplay.mobile.common.global.SourceRemoveObject
import com.fptplay.mobile.common.classes.HighLightOrientation
import com.fptplay.mobile.common.classes.ScrollHighLightToNextPosition
import com.fptplay.mobile.common.classes.ScrollHighLightToPosition
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.HomeBaseFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.adjust.SourcePage
import com.fptplay.mobile.features.ads.banner.tip_guideline.TvcBannerActionListener
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.floating_button.FiresBaseFloatingButton
import com.fptplay.mobile.features.floating_button.FloatingButton
import com.fptplay.mobile.features.floating_button.FloatingButtonView
import com.fptplay.mobile.features.floating_button.IEventListenerFirestoreFloatingButton
import com.fptplay.mobile.features.home.HomeFragment
import com.fptplay.mobile.features.home.HomeMainFragment
import com.fptplay.mobile.features.pladio.entry_point.FirebasePladioEntryPoint
import com.fptplay.mobile.features.pladio.entry_point.IEventListenerFirestorePladioEntryPoint
import com.fptplay.mobile.features.pladio.entry_point.PladioEntryPoint
import com.fptplay.mobile.features.pladio.entry_point.PladioEntryPointView
import com.fptplay.mobile.features.sport.tournament.schedule_and_result.SportTournamentScheduleAndResultFragment
import com.fptplay.mobile.features.sport.util.ShareScheduleSportUtils
import com.fptplay.mobile.homebase.adapter.HomeBaseAdapter
import com.fptplay.mobile.homebase.helpers.DataLogRecommendBlockItem
import com.fptplay.mobile.player.utils.afterMeasured
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.exo.ExoPlayerView
import com.tear.modules.player.util.IPlayer
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureMeta
import com.xhbadxx.projects.module.domain.entity.fplay.sport.SportScheduleAndResultItemV2
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.math.max
import kotlin.math.min


@AndroidEntryPoint
open class HomeBaseFragment :
    BaseFragment<HomeBaseViewModel.HomeBaseState, HomeBaseViewModel.HomeBaseIntent>() {
    open var pageId: PageId = PageId.HomePageId
    override val hasEdgeToEdge = true

    open var metaData: StructureMeta? = null
    open var metaTitle:String? = null
    open var metaName: String? = null
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor

    override val viewModel: HomeBaseViewModel by viewModels()

    private var _binding: HomeBaseFragmentBinding? = null
    protected val binding get() = _binding!!

    val structures: MutableList<Structure> by lazy { mutableListOf() }
    private var selectedItem: BaseObject? = null
    protected open val homeBaseAdapter: HomeBaseAdapter by lazy { HomeBaseAdapter(this, sharedPreferences) }
    private var structureId: String? = ""

    private var lastExecuteTime: Long = 0
    private var autoReloadTime: Long = 0L

    private var timer: Timer? = null
    private var reloadTimerTask: TimerTask? = null

    private var lastOrientation = Configuration.ORIENTATION_UNDEFINED
    //floating_button
    private val firesBaseFloatingButton: FiresBaseFloatingButton by lazy { FiresBaseFloatingButton() }
    //pladio_entry_point
    private val firebasePladioEntryPoint: FirebasePladioEntryPoint by lazy { FirebasePladioEntryPoint() }

    private val dataLogRecommendBlockHighlightKeeper by lazy { DataLogRecommendBlockItem() }

    private var dataSubscribe:StructureItem? = null

    private val reloadEventListener by lazy {
        object : GlobalEventListener {
            @Synchronized
            override fun onEventReceive(data: Any?) {
                if (System.currentTimeMillis() - lastExecuteTime > 999) {
                    lastExecuteTime = System.currentTimeMillis()
                    Timber.d("*****Reloaddddddd local")
                    getStructureLocal()
                }
            }

        }
    }
    private val removeEventItemListener by lazy {
        object: GlobalEventListener {
            @Synchronized
            override fun onEventReceive(data: Any?) {
                checkRemoveItemIfNeeded(data)
            }
        }
    }
    private val eventGlobalStopTrailerListener by lazy {
        object : GlobalEventListener {
            @Synchronized
            override fun onEventReceive(data: Any?) {
                stopPlayer()
                allowAddPlayer = false
                allowAddPlayerPos = -1
            }
        }
    }
    // Player
    private var player: IPlayer? = null
    private var playerCallbackAutoExpand: IPlayer.IPlayerCallback? = null
    private var playerView: ExoPlayerView? = null
    private var posLineClicked = -1
    private var allowAddPlayerPos = -1
    private var allowAddPlayer = false
    private var listLastItemClickedState: HashMap<Int, Int>? = null
    private var isMute = true
    var showButtonShare = false
//    private val volumeObserver by lazy { VolumeObserver(context, Handler(Looper.getMainLooper())) }

    //endregion
    private val SCROLL_DELAY = 5600L  //delay time: 5000ms // transition time: 600ms
    private var timerScroll : Timer? = null
    private var timerScrollTask : TimerTask? = null
    //region AutoScroll
    private fun startAutoScroll() {
        if (_binding != null) {
            timerScroll = Timer()
            timerScrollTask = object : TimerTask() {
                override fun run() {
                    scrollHighLightToNextPosition()
                }
            }
            timerScroll?.schedule(timerScrollTask, SCROLL_DELAY, SCROLL_DELAY)
        }
    }
    private fun scrollHighLightToNextPosition() {
        scrollHighLightToPosition()
    }
    private fun scrollHighLightToPosition(position: Int = -1, isSmooth: Boolean = false) {
        try {
            if (_binding != null) {
                runOnUiThread {
                    if (homeBaseAdapter.data().isNotEmpty()) {
                        val highlightData = homeBaseAdapter.data().first()
                        if(highlightData.style == BlockStyle.HighLight) {
                            homeBaseAdapter.notifyItemChanged(0,
                                if (position > -1)
                                    ScrollHighLightToPosition(position, isSmooth)
                                else
                                    ScrollHighLightToNextPosition
                            )
                        } else {
                            homeBaseAdapter.data().forEachIndexed { index, structure ->
                                if(structure.style == BlockStyle.HighLight) {
                                    homeBaseAdapter.notifyItemChanged(index,
                                        if (position > -1)
                                            ScrollHighLightToPosition(position, isSmooth)
                                        else
                                            ScrollHighLightToNextPosition
                                    )
                                    return@forEachIndexed
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun stopAutoScroll() {
        timerScrollTask?.cancel()
        timerScroll?.cancel()
        timerScroll?.purge()
        timerScrollTask = null
        timerScroll = null
    }
    //endregion

    // region Ads variable

    private val tvcBannerProxy by lazy {
        TvcBannerProxy(requireContext()).apply {
            onGetTvcBannerCompleted = tvcBannerRequestListener
        }
    }

    private val tvcBannerRequestListener by lazy {
        TvcBannerRequestListener()
    }
    // endregion Ads variable


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(checkBeforePlayUtil)
        checkBeforePlayUtil.setScreenProvider(pageId.id)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = HomeBaseFragmentBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Timber.d("*****OnDestroyview $pageId")
        firesBaseFloatingButton.stopListenFireStore()
        firebasePladioEntryPoint.stopListenFireStore()
        binding.rcvMain.clearOnScrollListeners()
//        context?.contentResolver?.unregisterContentObserver(volumeObserver)
        timer?.cancel()
        timer?.purge()
        timer = null
        stopAutoScroll()
        GlobalEvent.unRegisterEvent(GlobalEvent.REMOVE_ENDED_EVENT, reloadEventListener)
        GlobalEvent.unRegisterEvent(GlobalEvent.REMOVE_ENDED_EVENT_ITEM, removeEventItemListener)
        GlobalEvent.unRegisterEvent(GlobalEvent.REMOVE_COUNT_DOWN_TIME_EVENT,eventGlobalStopTrailerListener)
        binding.rcvMain.adapter = null
        _binding = null
        player?.request = null
        player = null
        playerView = null
        playerCallbackAutoExpand = null
    }


    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkBeforePlayUtil)
    }

    override fun onStop() {
        super.onStop()
        stopAutoScroll()
    }

    //endregion
    //region Handle exit app
    override fun backHandler() {
        checkExit()
    }
    private var backCount = 0
    private var lastBackTime = 0L
    private fun checkExit() {
        backCount += 1
        if (backCount == 1) {
            Toast.makeText(context, getString(R.string.press_back_2_times_to_exit_app), Toast.LENGTH_SHORT).show()
            lastBackTime = System.currentTimeMillis()
        } else if (backCount > 1) {
            if (System.currentTimeMillis() - lastBackTime < 1000L) {
                activity?.finish()
            } else {
                Toast.makeText(context, getString(R.string.press_back_2_times_to_exit_app), Toast.LENGTH_SHORT).show()
                lastBackTime = System.currentTimeMillis()
                backCount = 1
            }
        }
    }
    //endregion Handle exit app

    //region implement by inherited class
    open fun getStructure(id: String = "") {
        // viewModel.setLastListItemClickedState(null)
    }

    open fun getStructureLocal(){}

    open fun getStructureItem(
        data: List<Pair<Int, Structure>>,
        userId: String,
        page: Int,
        perPage: Int
    ) {
    }
    open fun getStructureItemLocal(data: List<Pair<Int, Structure>>) {}

    open fun reloadStructureItem(
        structureId: String,
        blockType: String,
        type: String,
        customData: String
    ) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetStructureItem(
                structureId = structureId,
                userId = sharedPreferences.userId(),
                page = 1,
                perPage = MainApplication.INSTANCE.appConfig.numItemOfPage + 1,
                blockType = blockType,
                type = type,
                watchingVersion = if(type == Constants.WATCHING_TYPE) Constants.WATCHING_VERSION else null,
                customData = customData
            )
        )
    }
    open fun processStateToUi(state: HomeBaseViewModel.HomeBaseState) {}
    open fun processMetaData(metaData: StructureMeta) {
        this.metaData = metaData
        metaTitle = metaData.metaTitle
        metaName = metaData.name
        showButtonShare = metaData.isShare
        setPageTitle(metaData.name)
    }
    private fun setPageTitle(title: String?) {
        (parentFragment as? HomeMainFragment)?.setPageTitle(title ?: "")
    }

    open fun isHeaderWithoutToolbar(): Boolean {
        return when(metaData?.pageStyle) {
            Structure.PageStyle.IncludedToolBar -> false
            Structure.PageStyle.NoToolBar -> true
            else -> false
        }
    }
    //endregion

    override fun onStart() {
        super.onStart()
        checkShowInternetView()
        startAutoScroll()
    }

    //region bind screen
    override fun bindData() {
        (parentFragment as? HomeMainFragment)?.hidePageError()
        Timber.d("***run bind data: ${structures.size}")
        if (shouldReloadStructure()) {
            getStructure()
        } else {
            checkRemoveItemIfNeeded(SourceRemoveObject.getIdToRemove())
        }
        GlobalEvent.registerEvent(GlobalEvent.REMOVE_ENDED_EVENT, reloadEventListener)
        GlobalEvent.registerEvent(GlobalEvent.REMOVE_ENDED_EVENT_ITEM, removeEventItemListener)
        GlobalEvent.registerEvent(GlobalEvent.REMOVE_COUNT_DOWN_TIME_EVENT, eventGlobalStopTrailerListener)
    }

    private fun isAdsBannerBlock(position: Int): Boolean {
        return (
                position in structures.indices
                        && structures[position].style == BlockStyle.AdsTvcBanner
                )
    }

    private fun isHighlightMobileBlock(position: Int): Boolean {
        return (
                context?.isTablet() == false
                        && position in structures.indices
                        && structures[position].style == BlockStyle.HighLight
                )
    }

    override fun bindComponent() {
        if ((parentFragment as? HomeMainFragment)?.isAppBarShowing() == false) {
            (parentFragment as? HomeMainFragment)?.showAppBar(true)
        }
        setPageTitle(metaName)
        binding.rcvMain.apply {
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            //    setItemViewCacheSize(structures.size?:10)
            adapter = homeBaseAdapter
            addItemDecoration(
                object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        val position = parent.getChildAdapterPosition(view)
                        if (position == 0) {
                            if (structures.isNotEmpty() && structures[position].style != BlockStyle.HighLight) {
                                outRect.top = context.resources.getDimensionPixelSize(R.dimen.home_app_bar_height)
                            } else {
                                outRect.top = 0
                            }

                            if (isAdsBannerBlock(position) || isHighlightMobileBlock(position)) {

                                outRect.bottom = 0
                            } else {
                                outRect.bottom = resources.getDimensionPixelSize(R.dimen.block_margin_bottom)
                            }
                        } else {
                            if (isAdsBannerBlock(position)) {
                                outRect.top = 0
                                outRect.bottom = 0
                            } else {
                                outRect.bottom = resources.getDimensionPixelSize(R.dimen.block_margin_bottom)
                            }
                        }
                    }
                }
            )
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        val firstPosVisible = recyclerView.findFirstVisibleItem()
                        val endPostVisible = recyclerView.findLastVisibleItem()
                        scanBlockShouldHavePlayer(firstPosVisible, endPostVisible)
                    }
                }

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    processScroll(dy, recyclerView)
                }
            })
        }
        homeBaseAdapter.onEventLinePositionsItem(object : BlockHandler.ItemViewParentEventsListener {
            override fun getPositionLineItemView(pos: Int) {
                posLineClicked = pos
            }
            override fun getPositionFocusItem(
                posLine: Int,
                posFirstClicked: Int,
                posLastClicked: Int,
                data: BaseObject?
            ) {
                if (posLastClicked != 0) {
                    updateStateStructureItemClicked(posLine, posLastClicked)
                }
                if (data is StructureItem) {
                    if (data.blockStyle == BlockStyle.HighLight && data.itype != ItemType.Fake) {
                        if (timerScroll == null) {
                            startAutoScroll()
                        }
                    }
                }
            }

            override fun itemScrollStateChange(blockType: BlockHandler.Type, state: Int) {
                if (blockType == BlockHandler.Type.HighLight) {
                    when (state) {
                        ViewPager.SCROLL_STATE_IDLE -> {
                            // ViewPager no scrolling
                            if (timerScroll == null) {
                                startAutoScroll()
                            }
                        }
                        ViewPager.SCROLL_STATE_DRAGGING -> {
                            // ViewPager scrolling by dragging
                            stopAutoScroll()
                        }
                        ViewPager.SCROLL_STATE_SETTLING -> {
                            // ViewPager is scrolling due to inertia
                        }
                    }
                }
            }

        })
        homeBaseAdapter.onEventsItem(object : IEventListener<BaseObject> {
            override fun onClickView(position: Int, view: View?, data: BaseObject) {
                if (data is BlockEntity && (view?.id == R.id.tv_view_more || view?.id == R.id.iv_view_more)) {
                    navigateViewMore(data, position)
                } else if (data is StructureItem && view?.id == R.id.btn_book) {
                    dataSubscribe = data
                    val structure = structures.find { it.name == data.metaName } //update tạm thời, sau này nên bổ sung id vào data sau đó find = id
                    TrackingUtil.setDataTracking(
                        screenValue = data.blockStyle.toString(),
                        nameBlockVal = data.metaName,
                        indexBlockVal = structures.indexOf(structure),
                        isRecommend = data.isRecommend
                    )
                    if(!data.isSubscribe) {
                        bookItem(type = "vod", itemId = data.id)
                    }else{
                        unBookItem(type = "vod", itemId = data.id)
                    }
                } else if (data is StructureItem && view?.id == R.id.iv_sound_control) {
                    changePlayerSoundControl()
                }
                if (data is SportBlockEntity) {
                    navigateToSportViewMore(data)
                }
            }

            override fun onClickedItem(position: Int, data: BaseObject) {
                selectedItem = data
                structureId = if (posLineClicked != -1 && posLineClicked < structures.size) homeBaseAdapter.item(posLineClicked)?.id ?: "" else ""
                sendLog(data)
                updateStateStructureItemClicked(posLineClicked, position)
                val actualPosition = if (data is StructureItem) {
                    if (data.blockStyle == BlockStyle.HighLight) {
                        homeBaseAdapter.getActualPositionForHighlight(position, posLineClicked)
                    } else {
                        position
                    }
                } else {
                    ""
                }
                TrackingUtil.position = actualPosition.toString()
                checkBeforePlayUtil.navigateToSelectedContent(data)
            }

            // This interface is called only from an AutoExpand item
            override fun onSelectedItem(
                position: Int,
                data: BaseObject?,
                viewHolder: RecyclerView.ViewHolder?
            ) {
                Logger.d("trangtest homeBaseAdapter.onEventsItem onSelectedItem pos = $position")
                if(position >= 0) {
                    if (allowAddPlayer && data is StructureItem && viewHolder is BlockItemAdapter.BlockItemViewHolder) {
                        if (data.isTrailer && data.bitrateId.isNotBlank() && data.trailerId.isNotBlank()) {
                            renewPlayer()
                            startPlayerForAutoExpand(data, viewHolder)
                        } else {
                            stopPlayer()
                        }
                    }
                } else {
                    stopPlayer()
                }
            }
        })

        firesBaseFloatingButton.openFloatingButton()
        firebasePladioEntryPoint.openPladioEntryPoint()

        configFloatingButton(pageId)
        configPladioEntryPoint(pageId)
    }

    open fun checkShowInternetView() {
        val isShow = packageValidationViewModel.isShowInternetView
        if(parentFragment is HomeMainFragment) {
            if(isShow && !NetworkUtils.isNetworkAvailable()) {
                (parentFragment as? HomeMainFragment)?.hideNoInternetView()
                (parentFragment as? HomeMainFragment)?.showNoInternetView()
            } else {
                (parentFragment as? HomeMainFragment)?.hideNoInternetView()
            }
        } else {
            if(isShow && !NetworkUtils.isNetworkAvailable()) {
                hideNoInternetView()
                showNoInternetView()
            } else {
                hideNoInternetView()
            }
        }
    }

    // region Handle Floating Button
    private fun configFloatingButton(pageId: PageId) {
        firesBaseFloatingButton.callbackEventFirestoreFloatingButton(object : IEventListenerFirestoreFloatingButton {
            override fun openFloatingButton(listObjectFloatingButton: ArrayList<FloatingButton>) {
                getDataFloatingButton(listObjectFloatingButton, pageId)
            }
        })
    }

    private fun getDataFloatingButton(listFloatingButton: List<FloatingButton>, pageId: PageId) {
        try {
            binding.viewFloatingButton.removeAllViews()
            for (floatingButton in listFloatingButton) {
                floatingButton.let {
                    val stringKeyClosed = "${pageId.id}${it.url}"
                    val listFloatingClose = MainApplication.INSTANCE.getListFloatingButton()
                    if (floatingButton.status == Constants.FLOATING_BUTTON_ON) {
                        if (!listFloatingClose.contains(stringKeyClosed)) {
                            if (it.pageKey == pageId.id) {
                                setUpViewFloatingButton(it)
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setUpViewFloatingButton(stringIndexFloatingButton: FloatingButton) {
        val floatingButtonView = FloatingButtonView(requireContext()).apply {
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT)
        }
        floatingButtonView.setListener(
            onClick = {
                sendTrackingClickFloatingButton(stringIndexFloatingButton)
                DeeplinkUtils.parseDeepLinkAndExecute(stringIndexFloatingButton.url)
            },
            onClose = {
                MainApplication.INSTANCE.setListFloatingButton(stringFloatingButton = pageId.id + stringIndexFloatingButton.url)
                binding.viewFloatingButton.apply {
                    removeView(floatingButtonView)
                }
            }
        )
        floatingButtonView.setImage(url = stringIndexFloatingButton.icon)
        binding.viewFloatingButton.apply {
            addView(floatingButtonView)
            //
            setupFloatingButtonPosition(data = stringIndexFloatingButton, parentView = this, floatingButtonView = floatingButtonView)
            //
        }
    }

    private fun setupFloatingButtonPosition(data: FloatingButton, parentView: View, floatingButtonView: FloatingButtonView) {
        try {
            floatingButtonView.x = 0f
            floatingButtonView.y = 0f
            var x = Utils.convertStringToFloat(data.coordinates.xPercent, 100f)
            var y = Utils.convertStringToFloat(data.coordinates.yPercent, 100f)
            if (x !in 0f.. 100f) { x = 100f }
            if (y !in 0f.. 100f) { y = 100f }
            parentView.afterMeasured {
                floatingButtonView.afterMeasured {
                    context?.let {
                        val parentWidth: Int
                        val parentHeight: Int
                        when (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation) {
                            Configuration.ORIENTATION_LANDSCAPE -> {
                                parentWidth = max(parentView.width, parentView.height)
                                parentHeight = min(parentView.width, parentView.height)
                            }
                            else -> {
                                parentWidth = min(parentView.width, parentView.height)
                                parentHeight = max(parentView.width, parentView.height)
                            }
                        }
                        val newX = parentWidth * (x / 100f)
                        val newY = parentHeight * (y / 100f)
                        Logger.d("FloatingButton Parent wxh: $parentWidth x $parentHeight")
                        val xNormalize = if (newX + floatingButtonView.width > parentWidth) parentWidth - floatingButtonView.width else newX
                        val yNormalize = if (newY + floatingButtonView.height > parentHeight) parentHeight - floatingButtonView.height else newY
                        floatingButtonView.x = xNormalize.toFloat()
                        floatingButtonView.y = yNormalize.toFloat()
                    }
                }
            }
            floatingButtonView.post {
                floatingButtonView.setVisibility(true)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sendTrackingClickFloatingButton(data:FloatingButton){
        trackingProxy.sendEvent(
            infor = InforMobile(
                infor = trackingInfo,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                logId = TrackingConstants.EVENT_LOG_ID_MEGA_FUNCTION_CLICK,
                screen = "FloatingButton",
                event = "ButtonClick",
                itemId = data.url,
                url = data.url
            )
        )
    }
    // endregion

    // region Handle Pladio Entry Point
    private fun configPladioEntryPoint(pageId: PageId) {
        firebasePladioEntryPoint.callbackEventFirestoreFloatingButton(object : IEventListenerFirestorePladioEntryPoint {
            override fun showPladioEntryPoint(listObjectPladioEntryPoint: ArrayList<PladioEntryPoint>) {
                getDataPladioEntryPoint(listObjectPladioEntryPoint, pageId)
            }
        })
    }

    private fun getDataPladioEntryPoint(listPladioEntryPoint: List<PladioEntryPoint>, pageId: PageId) {
        try {
            binding.viewPladioEntryPoint.removeAllViews()
            for (pladioEntryPoint in listPladioEntryPoint) {
                val stringKeyClosed = "${pageId.id}${pladioEntryPoint.value}"
                val listFloatingClose = MainApplication.INSTANCE.getListPladioEntryPoint()
                if (pladioEntryPoint.status == Constants.FLOATING_BUTTON_ON) {
                    if (!listFloatingClose.contains(stringKeyClosed)) {
                        if (pladioEntryPoint.pageKey == pageId.id) {
                            setUpViewPladioEntryPointButton(pladioEntryPoint)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setUpViewPladioEntryPointButton(stringIndexPladioEntry: PladioEntryPoint) {
        val pladioEntryPointView = PladioEntryPointView(requireContext()).apply {
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT)
        }
        pladioEntryPointView.setListener(
            onClick = {
                sendTrackingClickPladioEntryPoint(stringIndexPladioEntry)
                if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected) {
                    showWarningDialog(
                        message = MainApplication.INSTANCE.getString(R.string.pairing_control_content_not_support_message),
                        textConfirm = MainApplication.INSTANCE.getString(R.string.alert_confirm),
                        textClose = MainApplication.INSTANCE.getString(R.string.back),
                        onClose = {},
                        onConfirm = {
                            findNavController().navigateSafe(NavHomeMainDirections.actionGlobalToNavPladio())
                        },
                        isOnlyConfirmButton = false
                    )
                } else {
                    findNavController().navigateSafe(NavHomeMainDirections.actionGlobalToNavPladio())
                }
            },
            onClose = {
                MainApplication.INSTANCE.setListPladioEntryPoint(stringPladioEntryPoint = pageId.id + stringIndexPladioEntry.value)
                binding.viewPladioEntryPoint.apply {
                    removeView(pladioEntryPointView)
                }
            }
        )
        pladioEntryPointView.setImage(url = stringIndexPladioEntry.icon)
        binding.viewPladioEntryPoint.apply {
            addView(pladioEntryPointView)
            //
            setupPladioEntryPointPosition(data = stringIndexPladioEntry, parentView = this, pladioEntryPointView = pladioEntryPointView)
            //
        }
    }

    private fun setupPladioEntryPointPosition(data: PladioEntryPoint, parentView: View, pladioEntryPointView: PladioEntryPointView) {
        try {
            pladioEntryPointView.x = 0f
            pladioEntryPointView.y = 0f
            var x = Utils.convertStringToFloat(data.coordinates.xPercent, 100f)
            var y = Utils.convertStringToFloat(data.coordinates.yPercent, 100f)
            if (context.isTablet()) {
                when (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation) {
                    Configuration.ORIENTATION_LANDSCAPE -> {
                        x = Utils.convertStringToFloat(data.coordinates.xTabletLandscapePercent, 100f)
                        y = Utils.convertStringToFloat(data.coordinates.yTabletLandscapePercent, 100f)
                    }
                    else -> {
                        x = Utils.convertStringToFloat(data.coordinates.xTabletPortraitPercent, 100f)
                        y = Utils.convertStringToFloat(data.coordinates.yTabletPortraitPercent, 100f)
                    }
                }
            }
            if (x !in 0f.. 100f) { x = 100f }
            if (y !in 0f.. 100f) { y = 100f }
            parentView.afterMeasured {
                pladioEntryPointView.afterMeasured {
                    context?.let {
                        val parentWidth: Int
                        val parentHeight: Int
                        when (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation) {
                            Configuration.ORIENTATION_LANDSCAPE -> {
                                parentWidth = max(parentView.width, parentView.height)
                                parentHeight = min(parentView.width, parentView.height)
                            }
                            else -> {
                                parentWidth = min(parentView.width, parentView.height)
                                parentHeight = max(parentView.width, parentView.height)
                            }
                        }
                        val newX = parentWidth * (x / 100f)
                        val newY = parentHeight * (y / 100f)
                        val xNormalize = if (newX + pladioEntryPointView.width > parentWidth) parentWidth - pladioEntryPointView.width else newX
                        val yNormalize = if (newY + pladioEntryPointView.height > parentHeight) parentHeight - pladioEntryPointView.height else newY
                        pladioEntryPointView.x = xNormalize.toFloat()
                        pladioEntryPointView.y = yNormalize.toFloat()
                    }
                }
            }
            pladioEntryPointView.post {
                pladioEntryPointView.setVisibility(true)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sendTrackingClickPladioEntryPoint(data: PladioEntryPoint){
        trackingProxy.sendEvent(
            infor = InforMobile(
                infor = trackingInfo,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                logId = TrackingConstants.EVENT_LOG_ID_MEGA_FUNCTION_CLICK,
                screen = "FloatingButton",
                event = "ButtonClick",
                itemId = data.value,
                url = data.value
            )
        )
    }
    // endregion Handle Pladio Entry Point
    override fun retryLoadPage() {
        getStructure()
    }

    private fun updateStateStructureItemClicked(posLineClicked: Int, posLastStructureItem: Int) {
        val stateLast: HashMap<Int, Int>? = listLastItemClickedState
        if (stateLast != null) {
            for (key in stateLast.keys) {
                if (key == posLineClicked) {
                    stateLast[key] = posLastStructureItem
                }
            }
        }
    }

    override fun bindEvent() {
        binding.srlMain.setOnRefreshListener {
            getStructure()
            tvcBannerProxy.refresh()
        }

        autoReloadTime = (arguments?.getInt(Constants.RELOAD_PAGE) ?: 0).toLong() * 1000
//        autoReloadTime = 30000
        Timber.tag("tamlog-reload").d("autoReloadTime ${javaClass} $autoReloadTime")
        Timber.d("*****Reload page $pageId with $autoReloadTime ms")
        if (autoReloadTime > 0) {
            timer = Timer()
            timer?.scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    getStructure()
                }
            }, autoReloadTime, autoReloadTime)
        }


        // register onVolume changed
//        volumeObserver.onVolumeChange = {
//            if (it > 0) {
//                unmuteVolume = it / 15f
//                unmute()
//            } else {
//                mute()
//            }
//        }
//        context?.applicationContext?.contentResolver?.registerContentObserver(
//            android.provider.Settings.System.CONTENT_URI, true, volumeObserver
//        )
        // end register onVolume changed

        parentFragment?.setFragmentResultListener(Constants.CHECK_REQUIRE_VIP) { key, bundle ->
            checkBeforePlayUtil.onFragmentResult(key, bundle)
        }

        setFragmentResultListener(Constants.EVENT_END_TIME_REMOVE_ITEM_KEY) { key, bundle ->
            val id = bundle.getString(Constants.EVENT_END_TIME_REMOVE_ITEM_VALUE, "")
            checkRemoveItemIfNeeded(id)
        }

        // tablet
        parentFragment?.setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            if (isSuccess) {
                invokeBookItemAction(bundle)
            }
        }
        // mobile
        setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            if (isSuccess) {
                invokeBookItemAction(bundle)
            }
        }
        if (listLastItemClickedState != null) {
            val stateLast = listLastItemClickedState
            binding.rcvMain.post {
                if (_binding != null) {
                    if (stateLast != null) {
                        for (key in stateLast.keys) {
                            if (key == 0) {
                                binding.rcvMain.findViewHolderForAdapterPosition(0)?.let { viewHolder ->
                                    // case : tablet not use viewPager2
                                    if (viewHolder is BlockHandler.BlockViewHolder) {
                                        val viewItem: View? = binding.rcvMain.layoutManager?.findViewByPosition(key)
                                        if (viewItem != null) {
                                            val rcvMainItem: RecyclerView? =
                                                viewItem.findViewById<RecyclerView>(R.id.rcv_item) ?: null
                                            rcvMainItem?.findViewHolderForAdapterPosition(0)?.let { item ->
                                                if (rcvMainItem != null) (rcvMainItem.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                                                    stateLast[key] ?: 0,
                                                    (rcvMainItem.width - item.itemView.width)/2
                                                )
                                            }
                                        }
                                    }
                                    // case warning : scroll to current position but position has been saved in HomeBaseAdapter so it is no longer used here !
                                    /*if(viewHolder is BlockHandler.BlockHighLightViewHolder){
                                        val viewItem: View? = binding.rcvMain.layoutManager?.findViewByPosition(key)
                                        if (viewItem != null) {
                                            val vpMain: ViewPager2? =
                                                viewItem.findViewById<ViewPager2>(R.id.vp_main) ?: null
                                            vpMain?.currentItem = stateLast[key] ?: 0
                                        }
                                    }*/
                                }
                            }else{
                                val viewItem: View? = binding.rcvMain.layoutManager?.findViewByPosition(key)
                                if (viewItem != null) {
                                    val rcvMainItem: RecyclerView? =
                                        viewItem.findViewById<RecyclerView>(R.id.rcv_item) ?: null
                                    rcvMainItem?.findViewHolderForAdapterPosition(0)?.let { item ->
                                        if (rcvMainItem != null) (rcvMainItem.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                                            stateLast[key] ?: 0,
                                            (rcvMainItem.width - item.itemView.width)/2
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        homeBaseAdapter.tvcBannerActionListener = object : TvcBannerActionListener() {
            override fun onClickAds(url: String?, useWebViewInApp: Boolean?) {
                Timber.tag("tam-ads").d("onClickAds $url")
                if (url != null) {
                    lifecycleScope.launch(Dispatchers.Main) {
                        DeeplinkUtils.parseDeepLinkAndExecute(
                            deeplink = url,
                            useWebViewInApp = useWebViewInApp ?: false,
                            trackingInfo = trackingInfo,
                            isDeeplinkCalledInApp = true,
                            callFromAds = true
                        )
                    }
                }
            }

            override fun clickInterative(dataJson: String?) {
                Timber.tag("tam-ads").d("onClickInteractiveAds $dataJson")
                if(dataJson != null)
                    viewModel.dispatchIntent(HomeBaseViewModel.HomeBaseIntent.TriggerOpenInteractiveAdsPopup(dataJson))
            }

            override fun requestBanner(adPosition: Int) {
                val screenName = if(metaName.isNullOrBlank()) viewModel.structureMeta?.name else metaName
                Timber.tag("tam-ads").i("requestBanner position $adPosition - screenName $screenName")
                if (AdsUtils.canRequestAds(context = requireContext(), sharedPreferences = sharedPreferences)) {

                    val bannerRequestParams = BannerRequestParams(
                        uuid = AdsUtils.uniqueUid(sharedPreferences),
                        screenName = screenName,
                        screenId = "",
                        isUseData = AdsUtils.useMobileData(requireContext()),
                        userType = AdsUtils.userType(sharedPreferences),
                        appVersion = AdsUtils.versionApp(),
                        userId = AdsUtils.userId(sharedPreferences),
                        adPosition = adPosition,
                        profileId = sharedPreferences.profileId(),
                        profileType = sharedPreferences.profileType(),
                        pageId = pageId.id

                    )
                    tvcBannerProxy.requestTvcBanner(
                        bannerRequestParams = bannerRequestParams,
                        coroutineScope = lifecycleScope
                    )
                }
            }
        }

    }
    @Synchronized
    private fun checkRemoveItemIfNeeded(data: Any?) {
        if(homeBaseAdapter.data().isNotEmpty() && data is String) {
            try {
                val highlightData = homeBaseAdapter.data().first()
                if(highlightData.items.firstOrNull()?.blockStyle == BlockStyle.HighLight) {
                    val newData = highlightData.copy(
                        items = arrayListOf<StructureItem>().apply {
                            highlightData.items.forEach { item -> if(item.highlightId != data) add(item.copy()) }
                        }
                    )
                    homeBaseAdapter.refreshAndRemoveIfNeeded(newData, newData.items.size, 0)
                    SourceRemoveObject.removeData()
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
                SourceRemoveObject.removeData()
            }
        }
    }

    //endregion
    //region process -> player
    private fun renewPlayer() {
        player?.stop(force = true)
        player?.request = null
        player = null
        player = ExoPlayerProxy(
            context = requireContext(),
            useCronetForNetworking = true,
            requireMinimumResolutionH265 = "",
            requireMinimumResolutionH265HDR = "",
            requireMinimumResolutionAV1 = "",
            requireMinimumResolutionVP9 = "",
            requireMinimumResolutionDolbyVision = "",
        )
        viewLifecycleOwner.lifecycle.apply {
            player?.let { addObserver(it as ExoPlayerProxy) }
        }
    }

    /**
     * Scan in recyclerview from fromPos position to toPos position
     * if fromPos -> toPos has an AutoExpand then trigger the
     * onItemSelected of the most visible item in that block
     * else stop the player if necessary
     */
    private fun scanBlockShouldHavePlayer(fromPos: Int, toPos: Int) {
        for (pos in fromPos..toPos) {
            if (checkHaveBlockToAddPlayer(pos)) {
                allowAddPlayer = true
                triggerPlayOnItem(pos)
                return
            }
        }
        stopPlayer()
        allowAddPlayer = false
        allowAddPlayerPos = -1
    }

    /**
     * Check if the given position can add player or not.
     *
     * @param pos: Position of block in Recyclerview
     * @return true if the block is AutoExpand and display more than 80% it's layout on screen
     * else return false
     */
    private fun checkHaveBlockToAddPlayer(pos: Int): Boolean {
        binding.rcvMain.findViewHolderForAdapterPosition(pos)?.let { viewHolder ->
            if (viewHolder is BlockHandler.BlockViewHolder && viewHolder.getType() == BlockHandler.Type.AutoExpand) {
                val displayPercent =
                    binding.rcvMain.getItemHeightDisplayOnScreenInPercentage(
                        pos
                    )
                Timber.d("---Display auto expand $displayPercent(%) at $pos")
                return (displayPercent > 80)
            }
        }
        return false
    }

    private fun startPlayerForAutoExpand(
        data: StructureItem?,
        viewHolder: BlockItemAdapter.BlockItemViewHolder
    ) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetStream(
                id = data?.id ?: "",
                episodeId = data?.trailerId ?: "",
                bitrateId = data?.bitrateId ?: ""
            )
        )
        if (playerView == null) {
            playerView = ExoPlayerView(requireContext()).apply {
                useControl(false)
                updateShowProgressWhenBuffering(false)
                isClickable = true
            }
        } else {
            playerView?.removeParent()
        }
        playerView?.let {
            viewHolder.bindPlayerView(view = it, isSoundOn = player?.getVolume() != 0f)
            player?.setInternalPlayerView(playerView = it)
            playerView?.hide()
            viewHolder.setSoundControlImage(null)
        }
        playerCallbackAutoExpand?.let { player?.removePlayerCallback(playerCallback = it) }
        playerCallbackAutoExpand = object : IPlayer.IPlayerCallback {
            override fun onPrepare() {
                viewHolder.hidePosterOverlayIfNeeded()
                if ((parentFragment as? HomeMainFragment)?.isFirstRenew == true) {
                    mute()
                    (parentFragment as? HomeMainFragment)?.isFirstRenew = false
                } else {
                    updateSoundControl()
                }
            }

            override fun onEnd() {
                viewHolder.setSoundControlImage(null)
                stopPlayer()
                if(requireContext().isTablet()) {
                    val networkType = Utils.checkNetWorkType(context = requireContext())
                    if(networkType != Utils.NetworkType.MOBILE  && NetworkUtils.isWifiEnabled()) {
                        if (allowAddPlayerPos >= 0) {
                            (binding.rcvMain.findViewHolderForAdapterPosition(allowAddPlayerPos) as BlockHandler.BlockViewHolder).autoNextTrailer()
                        }
                    }
                }
            }
            override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
                Timber.d("---onError")
                stopPlayer()
            }

            override fun onStop() {
                Timber.d("---onStop")
                viewHolder.setSoundControlImage(null)
            }
        }
        playerCallbackAutoExpand?.let { player?.addPlayerCallback(playerCallback = it) }
    }

    private fun changePlayerSoundControl() {
        player?.run {
            if (getVolume() == 0f) {
                unmute()
            } else {
                mute()
            }
        }
    }

    private var unmuteVolume = 0f
    private fun mute() {
        unmuteVolume = player?.getVolume() ?: 0f
        val isOk = player?.setVolume(0f)
        Timber.d("*****${isOk}")
        isMute = true
        GlobalEvent.pushEvent(GlobalEvent.SOUND_CONTROL, false)
    }

    private fun unmute() {
        player?.setVolume(unmuteVolume)
        isMute = false
        GlobalEvent.pushEvent(GlobalEvent.SOUND_CONTROL, true)
    }

    private fun updateSoundControl() {
        if (isMute) {
            mute()
        } else {
            unmute()
        }
    }

    private fun triggerPlayOnItem(pos: Int) {
        val viewholder : BlockHandler.BlockViewHolder = (binding.rcvMain.findViewHolderForAdapterPosition(pos) as BlockHandler.BlockViewHolder)
        viewholder.triggerOnItemSelectedIfPossible(
            isForceTrigger = allowAddPlayerPos != pos
        )

        //handle hide player and show poster on old item here
        allowAddPlayerPos = pos
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (requireContext().isTablet()) {
            lifecycleScope.launchWhenStarted {
                configurationChanged(MainApplication.INSTANCE.applicationContext.resources.configuration)
            }
        }
    }

    private fun configurationChanged(newConfig: Configuration?) {
        if (lastOrientation == newConfig?.orientation) return
        lastOrientation = newConfig?.orientation ?: Configuration.ORIENTATION_UNDEFINED
        val spanCount = when (newConfig?.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                if (context.isTablet()) 4 else 2
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                if (context.isTablet()) 3 else 2
            }
            else -> 2
        }
        updateRecycleViewContent(spanCount)
        updateBlockHighLight(lastOrientation)
        // Floating Buttons
        getDataFloatingButton(firesBaseFloatingButton.getFloatingButtons(), pageId)
        getDataPladioEntryPoint(firebasePladioEntryPoint.getPladioEntryPoints(), pageId)
    }

    private fun updateBlockHighLight(orientation: Int) {
        try {
            if (orientation == Configuration.ORIENTATION_LANDSCAPE || orientation == Configuration.ORIENTATION_PORTRAIT && homeBaseAdapter.data().isNotEmpty()) {
                val highlightData = homeBaseAdapter.data().first()
                if(highlightData.style == BlockStyle.HighLight) {
                    homeBaseAdapter.notifyItemChanged(0, HighLightOrientation(orientation))
                } else {
                    homeBaseAdapter.data().forEachIndexed { index, structure ->
                        if(structure.style == BlockStyle.HighLight) {
                            homeBaseAdapter.notifyItemChanged(index, HighLightOrientation(orientation))
                            return
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateRecycleViewContent(spanCount: Int) {
        if (_binding == null) return
        homeBaseAdapter.data().forEachIndexed { index, structure ->
            if(structure.style == BlockStyle.HorizontalSliderHyperlink) {
                getViewHolderForPosision(index, spanCount)
                return
            }
        }
    }

    private fun getViewHolderForPosision(posision: Int, spanCount: Int) {
        if(_binding != null) {
            binding.rcvMain.findViewHolderForAdapterPosition(posision)?.let {
                if (it is BlockHandler.BlockViewHolder) {
                    it.refreshRcvItemForGridStyle(spanCount)
                }
            }
        }
    }

    private fun stopPlayer() {
        player?.stop(force = true)
        player?.request = null
        playerCallbackAutoExpand?.let {
            player?.removePlayerCallback(playerCallback = it)
            playerCallbackAutoExpand = null
        }
        playerView?.removeParent()
    }

    //endregion

    //region log highlight recommend
    protected fun logRecommendBlockItem(structure: Structure) {
        if(structure.needSendLogRecommend) {
            val dataLog = buildListItemWithIndex(structure.items)
            if (dataLogRecommendBlockHighlightKeeper.shouldLog(blockId = structure.id, dataLog = dataLog)) {
                trackingProxy.sendEvent(
                    infor = InforMobile(
                        infor = trackingInfo,
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        logId = TrackingConstants.EVENT_LOG_ID_RECOMMEND_BLOCK_ITEM,
                        screen = TrackingConstants.EVENT_LOG_SCREEN_RECOMMEND_BLOCK_ITEM,
                        event = TrackingConstants.EVENT_LOG_EVENT_RECOMMEND_BLOCK_ITEM,
                        itemId = structure.id,
                        subMenuId = structure.name.ifBlank { "Recommend" },
                        itemName = dataLog,
                    )
                )
                dataLogRecommendBlockHighlightKeeper.saveLogItem(blockId = structure.id, dataLog = dataLog)
            }
        }
    }

    private fun buildListItemWithIndex(items: List<StructureItem>): String {
        var result = ""
        items.forEachIndexed { index, item ->
            val recommend = if(item.isRecommend) "1" else "0"
            result += "${index}_!${item.id}_!$recommend#;"
        }
        return result
    }

    //endregion

    override fun HomeBaseViewModel.HomeBaseState.toUI() {
        when (this) {
            is HomeBaseViewModel.HomeBaseState.Loading -> {
                Timber.d("Loading")
                when(this.data) {
                    is HomeBaseViewModel.HomeBaseIntent.GetStructure -> {
                        if(parentFragment is HomeMainFragment) {
                            (parentFragment as? HomeMainFragment)?.hideNoInternetView()
                            (parentFragment as? HomeMainFragment)?.hidePageError()
                        } else {
                            hideNoInternetView()
                            hidePageError()
                        }
                    }
                    is HomeBaseViewModel.HomeBaseIntent.GetPopup3GInformation -> {
                        processStateToUi(this)  // pass handle loading for 3G information to HomeFragment
                    }
                    else -> {

                    }
                }
            }
            is HomeBaseViewModel.HomeBaseState.ErrorRequiredLogin -> {
                when (this.data) {
                    is HomeBaseViewModel.HomeBaseIntent.BookItem -> {
                        val extendsArgs: Bundle = bundleOf(
                            Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_TYPE_KEY to data.type,
                            Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_ID_KEY to data.itemId,
                            Constants.HOME_BOOK_ITEM_VALUE to true
                        )
                        navigateToLoginWithParams(extendsArgs = extendsArgs)
                    }
                    is HomeBaseViewModel.HomeBaseIntent.UnBookItem -> {
                        val extendsArgs: Bundle = bundleOf(
                            Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_TYPE_KEY to data.type,
                            Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_ID_KEY to data.itemId,
                            Constants.HOME_BOOK_ITEM_VALUE to false
                        )
                        navigateToLoginWithParams(extendsArgs = extendsArgs)
                    }
                    else -> {
                    }
                }
            }

            is HomeBaseViewModel.HomeBaseState.ErrorNoInternet -> {
                when(this.data) {
                    is HomeBaseViewModel.HomeBaseIntent.GetStructure -> {
                        if(parentFragment is HomeMainFragment) {
                            (parentFragment as? HomeMainFragment)?.showNoInternetView()
                        } else {
                            showNoInternetView()
                        }
                        if (binding.srlMain.isRefreshing) {
                            binding.srlMain.isRefreshing = false
                        }
                    }
                    is HomeBaseViewModel.HomeBaseIntent.GetPopup3GInformation -> {
                        processStateToUi(this)  // pass handle error no internet for 3G information to HomeFragment
                    }
                    else -> {

                    }
                }
            }

            is HomeBaseViewModel.HomeBaseState.Error -> {
                if (this.data is HomeBaseViewModel.HomeBaseIntent.GetStructure) {
                    hideNoInternetView()
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = TrackingConstants.EVENT_APP_ERROR,
                            event = AppErrorType.ERROR,
                            appId = TrackingUtil.currentAppId,
                            appName = TrackingUtil.currentAppName,
                            errorCode = AppErrorConstants.GET_STRUCTURE_CODE,
                            errorMessage = this.message,
                            itemName = AppErrorConstants.GET_STRUCTURE_MESSAGE,
                            issueId = TrackingUtil.createIssueId(),
                        )
                    )
                    if (binding.srlMain.isRefreshing) {
                        binding.srlMain.isRefreshing = false
                    }
                    (parentFragment as? HomeMainFragment)?.showPageError(errorMessage = message)
                }
                if (this.data is HomeBaseViewModel.HomeBaseIntent.GetPopup3GInformation) {
                    processStateToUi(this)  // pass handle error for 3G information to HomeFragment
                }
            }

            is HomeBaseViewModel.HomeBaseState.ResultBookItem -> {
                if (this.data.status == 1) {
                    GlobalEvent.pushEvent(GlobalEvent.BOOK_ITEM_KEY, this.itemId)
                    if (this@HomeBaseFragment is HomeFragment) {
                        <EMAIL>()
                    }
                    sendLogSubscribed()
                }
            }
            is HomeBaseViewModel.HomeBaseState.ResultUnBookItem -> {
                if (this.data.status == 1) {
                    GlobalEvent.pushEvent(GlobalEvent.UN_BOOK_ITEM_KEY, this.itemId)
                    if (this@HomeBaseFragment is HomeFragment) {
                        <EMAIL>()
                    }
                    sendLogUnsubscribed()
                }
            }
            is HomeBaseViewModel.HomeBaseState.ResultStream -> {
                Timber.d("***player: $player -- url: ${data.url}")
                playerView?.visible()
                player?.prepare(
                    request = IPlayer.Request(
                        url = IPlayer.Request.Url(url = data.url),
                        clearRequestWhenOnStop = false,
                        headerRequestProperties = Utils.getHeaderForPlayerRequest(data.streamSession)
                    )
                )
            }
            is HomeBaseViewModel.HomeBaseState.ResultStructureMetaData -> {
                if (shouldProcess) processMetaData(data)
            }
            else -> {
                processStateToUi(this)
            }
        }
    }

    private fun sendLogSubscribed() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "59",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                event = "Subscribed" ,
                itemId = dataSubscribe?.id?:"",
                itemName = dataSubscribe?.title?:"",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                screen = TrackingUtil.screen,
                folder = TrackingUtil.blockId,
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.itemIndex,
                blocKPosition = TrackingUtil.blockIndex
            )
        )
        dataSubscribe = null
    }

    private fun sendLogUnsubscribed() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "59",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                event = "Unsubscribed",
                itemId = dataSubscribe?.id?:"",
                itemName = dataSubscribe?.title?:"",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                screen = TrackingUtil.screen,
                folder = TrackingUtil.blockId,
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.itemIndex,
                blocKPosition = TrackingUtil.blockIndex
            )
        )
        dataSubscribe = null
    }
    fun sendTrackingBlock511(data: List<Structure>){
        //Logger.d("trangtest data send block = ${getDataBlockForTracking(data)}")
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "511",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "ListBlockDisplay",
                event = "LoadBlockDisplay",
                itemName = getDataBlockForTracking(data),
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime()
            )
        )
    }
    private fun getDataBlockForTracking(data: List<Structure>):String{
        var result = ""
        data.forEachIndexed { index, structure ->
            val endIndex = if(structure.name.length >= 15) 15 else structure.name.length
            result += "${index}_!${structure.id}_!${structure.name.substring(startIndex = 0, endIndex = endIndex)}#;"
        }
        return result
    }

    //region process -> API Result
    protected fun processStructureResult(data: List<Structure>, isLocal: Boolean = false) {
        if (binding.srlMain.isRefreshing) {
            binding.srlMain.isRefreshing = false
        }
        stopAutoScroll()
        structures.clear()
        structures.addAll(data)
        if (structures.isNotEmpty()) {
            // Deep copy structure so can update items when load structure
            val cloneOfStructures = ArrayList<Structure>()
            structures.forEach { item -> cloneOfStructures.add(item.copy()) }
            posLineClicked = -1
            listLastItemClickedState = null
            val mapLastItemClickState: HashMap<Int, Int> = HashMap()
            cloneOfStructures.forEachIndexed { index, _ ->
                mapLastItemClickState[index] = 0
            }
            listLastItemClickedState = mapLastItemClickState
            homeBaseAdapter.setHighLightPosition(0)

            homeBaseAdapter.refresh(cloneOfStructures) {
                getHomeStructureItem(data, isLocal)
            }
        }
    }

    protected fun processStructureItemResult(data: List<Pair<Int, Structure>>, callback: Runnable? = null) {

        var balance = 0
        try {
            homeBaseAdapter.bindWithItems(data.mapNotNull {
//                Timber.tag("tam-ads").d("*****processStructureItemResult ${it.second}")
                if (it.second.style != BlockStyle.AdsTvcBanner &&it.second.items.isNullOrEmpty()) {
                    structures.removeAt(it.first - balance)
                    balance += 1
                    null
                } else {
                    try {
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                    it.second
                }
            })
        } finally {
            callback?.run()
        }
    }

    private fun getHomeStructureItem(structures: List<Structure>, isLocal: Boolean = false) {
        fun dataToFetch(): List<Pair<Int, Structure>> {
            val data = arrayListOf<Pair<Int, Structure>>()
            structures.forEachIndexed { index, structure ->
                data.add(Pair(index, structure))
            }
            return data
        }

        val data = dataToFetch()
        if (data.isNotEmpty()) {
            if (isLocal) {
                getStructureItemLocal(data = data)
            } else {
                getStructureItem(
                    data = data,
                    userId = sharedPreferences.userId(),
                    page = 1,
                    perPage = MainApplication.INSTANCE.appConfig.numItemOfPage + 1 // +1 for logic show text view more
                )
            }
        }
    }
    //endregion

    //region private fun
    private fun bookItem(type: String,itemId: String) {
        if (sharedPreferences.userLogin()) {
            viewModel.dispatchIntent(HomeBaseViewModel.HomeBaseIntent.BookItem(type= type,itemId = itemId))
        } else {
            val extendsArgs: Bundle = bundleOf(
                Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_TYPE_KEY to type,
                Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_ID_KEY to itemId,
                Constants.HOME_BOOK_ITEM_VALUE to true
            )
            navigateToLoginWithParams(extendsArgs = extendsArgs)
        }
    }
    private fun unBookItem(type: String,itemId: String) {
        if (sharedPreferences.userLogin()) {
            viewModel.dispatchIntent(HomeBaseViewModel.HomeBaseIntent.UnBookItem(type= type,itemId = itemId))

        } else {
            val extendsArgs: Bundle = bundleOf(
                Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_TYPE_KEY to type,
                Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_ID_KEY to itemId,
                Constants.HOME_BOOK_ITEM_VALUE to false
            )
            navigateToLoginWithParams(extendsArgs = extendsArgs)
        }
    }
    private fun sendLog(data: BaseObject) {
        if (data is StructureItem) {
            TrackingUtil.setDataTracking(
                screenValue = data.blockStyle.toString(),
                nameBlockVal = if (posLineClicked != -1) homeBaseAdapter.item(posLineClicked)?.name ?: "" else "",
                indexBlockVal = posLineClicked,
                isRecommend = data.isRecommend
            )
        }
        Logger.d("trangtest posLineClicked = $posLineClicked and name = ${homeBaseAdapter.item(posLineClicked)?.name}")
    }
    protected open fun setCategoryStructure(structure : Structure) {
        if (structure.itype.equals("category")) {
            (parentFragment as? HomeMainFragment)?.setStructure(structure)
        }
    }
    protected open fun navigateToPage(pageId: String, title: String = "") {
        findNavController()
            .navigate(
                NavHomeMainDirections.actionGlobalToCategoryDetail(
                    id = pageId,
                    title = title,
                    screenProvider = <EMAIL>
                )
            )

    }
    protected open fun navigateToListChannel(groupId: String) {
        findNavController().navigate(
            NavHomeMainDirections.actionGlobalToTv(shouldNavigate = true, requestFocusGroup = groupId)
        )
    }

    protected open fun navigateToViewMore(
        blockStyle: String,
        blockType: String,
        viewMoreId: String,
        header: String,
        subHeader: String,
        customData: String,
    ) {
        findNavController()
            .navigate(
                NavHomeMainDirections.actionGlobalToViewMoreFragment(
                    blockStyle = blockStyle,
                    blockType = blockType,
                    id = viewMoreId,
                    header = header,
                    subHeader = subHeader,
                    screenProvider = <EMAIL>,
                    customData = customData,
                    pageId = pageId.id
                )
            )
    }

    protected open fun navigateViewMore(data: BlockEntity, position: Int) {
        when (data.viewMoreType) {
            "page" -> {
                navigateToPage(data.viewMoreId, title = data.header ?: "")
            }
            "list-channel" -> {
                navigateToListChannel(data.blockId)
            }
            "default" -> {
                TrackingUtil.setDataTracking(
                    screenValue = data.blockStyle,
                    nameBlockVal = data.header ?: "",
                    indexBlockVal = position,
                    isRecommend = false
                )
                navigateToViewMore(
                    blockStyle = data.blockStyle,
                    blockType = data.iType,
                    viewMoreId = data.blockId,
                    header = data.header ?: "",
                    subHeader = data.subHeader ?: "",
                    customData = data.customData
                )
            }
            else -> {
                TrackingUtil.setDataTracking(
                    screenValue = data.blockStyle,
                    nameBlockVal = data.header ?: "",
                    indexBlockVal = position,
                    isRecommend = false
                )
                navigateToViewMore(
                    blockStyle = data.blockStyle,
                    blockType = data.viewMoreType,
                    viewMoreId = data.viewMoreId,
                    header = data.header ?: "",
                    subHeader = data.subHeader ?: "",
                    customData = data.customData
                )
            }
        }
    }

    protected open fun navigateToSportViewMore(data: SportBlockEntity) {
        if (data.tables.isNullOrEmpty()) return

        val titleForShareLink =
            try {
                if(data.tables.isNotEmpty()) ((data.tables.first() as? SportScheduleAndResultItemV2)?.title) ?: metaName else data.title
            } catch (ex: Exception) {
                ""
            }

        when (data.tableType) {
            SportTableType.SCHEDULE -> {
                findNavController().navigate(
                    NavHomeMainDirections.actionGlobalToSportTournamentScheduleAndResult(
                        data.blockId,
                        SportTournamentScheduleAndResultFragment.SCHEDULE_SCREEN,
                        title = titleForShareLink ?: ""
                    )
                )
            }
            SportTableType.RESULT -> {
                findNavController().navigate(
                    NavHomeMainDirections.actionGlobalToSportTournamentScheduleAndResult(
                        data.blockId,
                        SportTournamentScheduleAndResultFragment.RESULT_SCREEN,
                        title = titleForShareLink ?: ""
                    )
                )
            }
            SportTableType.RANKING -> {
                findNavController().navigate(
                    NavHomeMainDirections.actionGlobalToSportTournamentTeamRank(data.blockId,
                        title = titleForShareLink ?: ""
                    )
                )
            }

            SportTableType.OVERALL_SCHEDULE -> {
                findNavController()
                    .navigate(NavHomeMainDirections.actionGlobalToSportSchedule(data.blockId, titleForShareLink ?: metaTitle ?: ""))
            }
            else -> {}
        }
    }
    //endregion

    //region process screen scroll
    protected open fun processScroll(dy: Int, recyclerview: RecyclerView) {
        if (dy > 0) {
            (parentFragment as? HomeMainFragment)?.hideAppBar()
        }
        if (dy < 0) {
            (parentFragment as? HomeMainFragment)?.showAppBar(false)
            if (!recyclerview.canScrollVertically(-1)) (parentFragment as? HomeMainFragment)?.showAppBar(true)
        }
    }
    //endregion process screen scroll

    //region process -> ads
    protected fun structuresSize() = structures.size
    protected fun haveBlockHighLight(): Boolean {
        if (structures.isEmpty()) return false
        return structures[0].style == BlockStyle.HighLight || structures[0].style == BlockStyle.HorizontalHighlight
    }

    @Synchronized
    protected fun processTvcBannerResponse(adPositions: List<AdPosition>, structOffset: Int, topBannerIndex: Int) {
        if (structures.isEmpty()) {
            return
        }
        // Deep copy structure so can update items when load structure
        val arrStructures = ArrayList(structures.filter {
            it.contentType != Structure.ContentType.Ads
        })

        AdsUtils.insertAdsInStructures(
            structures = arrStructures,
            adPositionsSorted = adPositions,
            structOffset = structOffset,
            topBannerIndex = topBannerIndex
        )
        //Update structures
        structures.clear()
        structures.addAll(arrStructures)
        // Deep copy structure so can update items when load structure
        val cloneOfStructures = ArrayList<Structure>()
        structures.forEach { item -> cloneOfStructures.add(item.copy()) }
        homeBaseAdapter.bind(cloneOfStructures)
        viewModel.updateWatchingAndFollowPos(cloneOfStructures)
    }
    //endregion process -> ads

    //region process -> button share
    @Synchronized
    protected fun processButtonShare(data: Structure) {
        if (structures.isEmpty()) {
            return
        }
        // Deep copy structure so can update items when load structure
        val arrStructures = ArrayList(structures.filter {
            it.contentType != Structure.ContentType.ButtonShare
        })

        arrStructures.add(data)
        //Update structures
        structures.clear()
        structures.addAll(arrStructures)
        // Deep copy structure so can update items when load structure
        val cloneOfStructures = ArrayList<Structure>()
        structures.forEach { item -> cloneOfStructures.add(item.copy()) }
        homeBaseAdapter.onShareButtonClicked = {
            ShareScheduleSportUtils(activity).shareDeeplink(metaTitle ?: "",
                id = data.id
            )
        }
        homeBaseAdapter.bind(cloneOfStructures)
    }
    //endregion process -> ads
    private fun invokeBookItemAction(bundle: Bundle) {
        val extensionBundle = bundle.getBundle(Constants.EXTENDS_ARG_NAVIGATE_LOGIN_KEY) ?: Bundle()
        val typeBookItem = extensionBundle.getString(Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_TYPE_KEY, "vod")
        val itemId = extensionBundle.getString(Constants.HOME_BOOK_ITEM_NAVIGATE_LOGIN_ID_KEY, "")
        val isBookItem = extensionBundle.getBoolean(Constants.HOME_BOOK_ITEM_VALUE, true)
        if(itemId.isNotBlank()){
            if(isBookItem)
                bookItem(type = typeBookItem,itemId =itemId)
            else
                unBookItem(type = typeBookItem,itemId =itemId)
        }
    }

    private fun shouldReloadStructure(): Boolean {
        if(structures.isEmpty()) return true

        val adapterData = homeBaseAdapter.data()
        if(adapterData.isNotEmpty()) {
            adapterData.forEach { structure ->
                if(structure.items.isNotEmpty()) {
                    return structure.items.firstOrNull()?.itype == ItemType.Fake
                }
            }
        }

        return false
    }

    // region Ads
    inner class TvcBannerRequestListener : OnGetTvcBannerCompleted {
        override fun onGetListTvcBannerWithPositionFail(errorCause: String?) {
        }

        override fun onGetListTvcBannerWithPositionSuccess(listTipGuidelineSorted: List<AdPosition>) {
        }

        override fun onGetTvcBannerFail(errorCause: String?) {
        }

        override fun onGetTvcBannerSuccess(listTipGuideline: AdHtmlResponse) {
        }

        override fun onGetTvcBannerWithPositionSuccess(position: Int, adData: AdPosition) {
//            Timber.tag("tam-ads").d("onGetTvcBannerWithPositionSuccess $position: $adData")
            lifecycleScope.launch(Dispatchers.Main) {
                homeBaseAdapter.updateAdPosition(position, adData)
            }
        }

        override fun onGetTvcBannerWithPositionFail(position: Int?, errorCause: String?) {
        }

    }

    // endregion Ads
    open class Instance<T : Fragment>(private val cls: Class<T>) {
        fun newInstance(bundle: Bundle): T {
            return cls.newInstance().apply {
                arguments = bundle
            }
        }
    }
}