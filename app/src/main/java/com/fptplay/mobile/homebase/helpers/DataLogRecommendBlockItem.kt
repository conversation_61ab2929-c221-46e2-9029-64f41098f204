package com.fptplay.mobile.homebase.helpers

import com.tear.modules.tracking.TrackingProxy

class DataLogRecommendBlockItem {
    private val dataKeeper = HashMap<String, String>()

    fun saveLogItem(blockId: String, dataLog: String) {
        dataKeeper[blockId] = dataLog
    }

    /**
     * Check if the data log is different from the previous one => should log
     */
    fun shouldLog(blockId: String, dataLog: String): Boolean {
        return dataKeeper[blockId] != dataLog
    }
}