package com.fptplay.mobile.homebase

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.PageProvider
import com.fptplay.mobile.homebase.data.BlockHighlightRecommendation
import com.fptplay.mobile.homebase.data.PageRecommendation
import com.fptplay.mobile.homebase.data.PageRecommendationData
import com.fptplay.mobile.homebase.helpers.HighlightRecommendHandler
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.Information3GNetworkProviderResult
import com.xhbadxx.projects.module.domain.entity.fplay.common.Status
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.home.RecommendBlockPosition
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.*
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.HomeOs4Repository
import com.xhbadxx.projects.module.domain.repository.fplay.VodRepository
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.zip
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.system.measureTimeMillis


@HiltViewModel
class HomeBaseViewModel @Inject constructor(
    private val homeOs4Repository: HomeOs4Repository,
    private val vodRepository: VodRepository,
    private val commonRepository: CommonRepository,
    private val savedState: SavedStateHandle,
    private val sharedPreferences: SharedPreferences
) :
    BaseViewModel<HomeBaseViewModel.HomeBaseIntent, HomeBaseViewModel.HomeBaseState>() {

    companion object {
        private val PAGE_RECOMMENDATION_EXPIRE_TIME = TimeUnit.MINUTES.toMillis(30) // 30 minutes
    }

    private var clusterItemJob: Job? = null
    private var watchingStructure: Structure? = null
    private var followStructure: Structure? = null
    var yMoveFullScreen: Float = 0F
    var xMoveFullScreen: Float = 0F
    private var structMeta: StructureMeta? = null

    private val cachePageRecommendations = mutableListOf<PageRecommendation>()
    private val highlightRecommendHandler = HighlightRecommendHandler(sharedPreferences)

    override fun dispatchIntent(intent: HomeBaseIntent) {
        safeLaunch {
            when (intent) {
                is HomeBaseIntent.GetStructure -> {
                    val pageRecommendationKey = getPageRecommendationKey(pageId = intent.pageProvider.pageId.id, userId = sharedPreferences.userId(), profileId = sharedPreferences.profileId(), profileType = sharedPreferences.profileType())
                    if (isPageRecommendationValid(pageRecommendationKey)) {
                        Logger.d("A--getStructure -> Load from local")

                        homeOs4Repository.getStructure(pageId = intent.pageProvider.pageId.id).collect {
                            viewModelScope.launch(Dispatchers.Main) {
                                _state.value = it.reduce(intent = intent) { isCached, data ->
                                    structMeta = data.meta
                                    HomeBaseState.ResultStructureMetaData(
                                        isCached = isCached,
                                        data = data.meta,
                                        shouldProcess = intent.pageProvider is PageProvider.Other || intent.pageProvider is PageProvider.Sport || intent.pageProvider is PageProvider.HBO
                                    )
                                }
                                _state.value = it.reduce(intent = intent) { isCached, data ->
                                    val newStructure = mapStructureWithRecommendData(data.structure, getPageRecommendation(pageRecommendationKey)?.pageRecommendationValue)
                                    when (intent.pageProvider) {
                                        is PageProvider.Home -> {
                                            HomeBaseState.StructureResult(isCached = isCached, data = newStructure)
                                        }
                                        is PageProvider.Sport -> {
                                            HomeBaseState.StructureSportResult(isCached = isCached, data = newStructure)
                                        }
                                        is PageProvider.HBO -> {
                                            HomeBaseState.StructureHBOResult(isCached = isCached, data = newStructure)
                                        }
                                        is PageProvider.TV -> {
                                            HomeBaseState.StructureTVResult(isCached = isCached, data = newStructure)
                                        }
                                        is PageProvider.Other -> {
                                            HomeBaseState.StructureOtherResult(isCached = isCached, data = newStructure)
                                        }
                                        else -> {
                                            HomeBaseState.StructureOtherResult(isCached = isCached, data = newStructure)
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        Logger.d("A--getStructure -> Load from server")
                        removePageRecommendation(key = pageRecommendationKey)

                        // Load from server
                        homeOs4Repository.getStructure(pageId = intent.pageProvider.pageId.id).zip(
                            homeOs4Repository.getRecommendStructure(pageId = intent.pageProvider.pageId.id)
                        ) { structure, recommend ->
                            // Save to cached
                            if (recommend is Result.Success) {
                                if (!recommend.data.isNullOrEmpty()) {
                                    savePageRecommendation(
                                        key = pageRecommendationKey,
                                        data = PageRecommendationData(
                                            pageRecommendationTimestamp = System.currentTimeMillis(),
                                            pageRecommendationValue = recommend.data
                                        )
                                    )
                                }
                            }
                            createNewStructure(structure, recommend)
                        }.collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                structMeta = data.meta
                                HomeBaseState.ResultStructureMetaData(
                                    isCached = isCached,
                                    data = data.meta,
                                    shouldProcess = intent.pageProvider is PageProvider.Other || intent.pageProvider is PageProvider.Sport || intent.pageProvider is PageProvider.HBO
                                )
                            }
                            _state.value = it.reduce(intent = intent) { isCached, data ->

                                when (intent.pageProvider) {
                                    is PageProvider.Home -> {
                                        HomeBaseState.StructureResult(isCached = isCached, data = data.structure)
                                    }
                                    is PageProvider.Sport -> {
                                        HomeBaseState.StructureSportResult(isCached = isCached, data = data.structure)
                                    }
                                    is PageProvider.HBO -> {
                                        HomeBaseState.StructureHBOResult(isCached = isCached, data = data.structure)
                                    }
                                    is PageProvider.TV -> {
                                        HomeBaseState.StructureTVResult(isCached = isCached, data = data.structure)
                                    }
                                    is PageProvider.Other -> {
                                        HomeBaseState.StructureOtherResult(isCached = isCached, data = data.structure)
                                    }
                                    else -> {
                                        HomeBaseState.StructureOtherResult(
                                            isCached = isCached,
                                            data = data.structure
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                is HomeBaseIntent.GetStructureLocal -> _state.value = when (intent.pageProvider) {
                    is PageProvider.Home -> {
                        HomeBaseState.StructureResult(isCached = true, data = intent.dataLocal)
                    }
                    is PageProvider.Sport -> {
                        HomeBaseState.StructureSportResult(isCached = true, data = intent.dataLocal)
                    }
                    is PageProvider.HBO -> {
                        HomeBaseState.StructureHBOResult(isCached = true, data = intent.dataLocal)
                    }
                    is PageProvider.TV -> {
                        HomeBaseState.StructureTVResult(isCached = true, data = intent.dataLocal)
                    }
                    is PageProvider.Other -> {
                        HomeBaseState.StructureOtherResult(isCached = true, data = intent.dataLocal)
                    }
                    else -> {
                        HomeBaseState.StructureOtherResult(isCached = true, data = intent.dataLocal)
                    }
                }

                is HomeBaseIntent.GetClusterItem -> {
                    clusterItemJob?.cancel()
                    clusterItemJob = launch {
                        getStructureItems(intent) {
                            Timber.d("***** Done cluster job with ${it.data[0].second}")
                            _state.value = when (intent.pageProvider) {
                                is PageProvider.Home -> {
                                    saveWatchingAndFollowPosition(it.data.map { item -> item.second })
                                    HomeBaseState.ResultClusterStructureItem(isCached = false, data = it.data)
                                }
                                is PageProvider.Sport -> HomeBaseState.ResultSportClusterStructureItem(
                                    isCached = false,
                                    data = it.data
                                )
                                is PageProvider.TV -> HomeBaseState.ResultTVClusterStructureItem(
                                    isCached = false,
                                    data = it.data
                                )

                                is PageProvider.HBO -> HomeBaseState.ResultHboClusterStructureItem(
                                    isCached = false,
                                    data = it.data
                                )
                                is PageProvider.Other -> HomeBaseState.ResultOtherClusterStructureItem(
                                    isCached = false,
                                    data = it.data
                                )
                                else ->{
                                    HomeBaseState.ResultOtherClusterStructureItem(
                                        isCached = false,
                                        data = it.data
                                    )
                                }
                            }
                        }
                    }
                }

                is HomeBaseIntent.GetClusterItemLocal -> {
                    getStructureItemsLocal(intent) {
                        _state.value = when (intent.pageProvider) {
                            is PageProvider.Home -> {
                                saveWatchingAndFollowPosition(it.data.map { item -> item.second })
                                HomeBaseState.ResultClusterStructureItem(isCached = true, data = it.data)
                            }
                            is PageProvider.Sport -> HomeBaseState.ResultSportClusterStructureItem(
                                isCached = true,
                                data = it.data
                            )
                            is PageProvider.TV -> HomeBaseState.ResultTVClusterStructureItem(
                                isCached = true,
                                data = it.data
                            )

                            is PageProvider.HBO -> HomeBaseState.ResultHboClusterStructureItem(
                                isCached = true,
                                data = it.data
                            )
                            is PageProvider.Other -> HomeBaseState.ResultOtherClusterStructureItem(
                                isCached = true,
                                data = it.data
                            )
                            else -> HomeBaseState.ResultOtherClusterStructureItem(
                                isCached = true,
                                data = it.data
                            )
                        }
                    }
                }

                is HomeBaseIntent.TriggerOpenInteractiveAdsPopup -> {
                    _state.value = HomeBaseState.OpenInteractiveAdsPopup(jsonData = intent.jsonData)
                }
                is HomeBaseIntent.TriggerIdleInteractiveAds -> {
                    _state.value = HomeBaseState.Idle
                }
                is HomeBaseIntent.BookItem -> {
                    vodRepository.addFollow(type = intent.type, id = intent.itemId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            HomeBaseState.ResultBookItem(
                                isCached = isCached,
                                data = data,
                                itemId = intent.itemId
                            )
                        }
                    }
                }
                is HomeBaseIntent.UnBookItem -> {
                    vodRepository.deleteFollow(type = intent.type, id = intent.itemId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            HomeBaseState.ResultUnBookItem(
                                isCached = isCached,
                                data = data,
                                itemId = intent.itemId
                            )
                        }
                    }
                }
                is HomeBaseIntent.GetStream -> {
                    vodRepository.getStream(id = intent.id, episodeId = intent.episodeId, bitrateId = intent.bitrateId, dataType = "")
                        .collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                HomeBaseState.ResultStream(
                                    isCached = isCached,
                                    data = data
                                )
                            }
                        }
                }
                is HomeBaseIntent.GetStructureItem -> {
                    homeOs4Repository.getStructureItem(
                        type = intent.type,
                        blockId = intent.structureId,
                        blockType = intent.blockType,
                        pageIndex = intent.page,
                        pageSize = intent.perPage,
                        watchingVersion = intent.watchingVersion,
                        customData = intent.customData,
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            when (intent.type) {
                                Constants.FOLLOW_TYPE -> HomeBaseState.ResultFollowStructureItem(
                                    isCached = isCached,
                                    data = data
                                )
                                Constants.COMING_SOON -> HomeBaseState.ResultComingSoonStructureItem(
                                    isCached = isCached,
                                    data = data
                                )
                                else -> HomeBaseState.ResultHistoryVodStructureItem(isCached = isCached, data = data)
                            }
                        }
                    }
                }

                is HomeBaseIntent.GetHistoryVod -> {
                    homeOs4Repository.getHistoryVod(
                        userId = intent.userId,
                        page = intent.page,
                        perPage = intent.perPage
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            HomeBaseState.ResultHistoryVodStructureItem(
                                isCached = isCached,
                                data = data
                            )
                        }

                    }
                }
                is HomeBaseIntent.GetCategoryStructure -> {
                    _state.value = HomeBaseState.ResultCategoryStructure(isCached = false, data = intent.structure)
                }

                is HomeBaseIntent.GetPopup3GInformation -> {
                    commonRepository.get3GInformation(countryCode = intent.countryCode).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            HomeBaseState.ResultPopup3GInformation(
                                isCached = isCached,
                                data = data,
                                countryCode = intent.countryCode
                            )
                        }
                    }
                }
            }
        }
    }

    fun saveYMoveFullScreen(yMoveFullScreen: Float) { this.yMoveFullScreen = yMoveFullScreen}
    fun saveXMoveFullScreen(xMoveFullScreen: Float) { this.xMoveFullScreen = xMoveFullScreen}

    override fun <T> Result<T>.reduce(
        intent: HomeBaseIntent?,
        successFun: (Boolean, T) -> HomeBaseState
    ): HomeBaseState {
        return when (this) {
            is Result.Init -> HomeBaseState.Loading(intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.Error.Intenet -> HomeBaseState.ErrorNoInternet(data = intent)
            is Result.UserError.RequiredLogin -> HomeBaseState.ErrorRequiredLogin(this.message, data = intent)
            is Result.Error -> HomeBaseState.Error(message = this.message, data = intent)
            Result.Done -> HomeBaseState.Done(data = intent)
        }
    }



    private suspend fun getStructureItems(
        intent: HomeBaseIntent.GetClusterItem,
        block: (intent: HomeBaseIntent.GetClusterItem) -> Unit
    ) {
        val measureTime = measureTimeMillis {
            withContext(Dispatchers.IO) {
                try {
                    val arrDeferred: ArrayList<Deferred<Unit>> = arrayListOf()
                    val pairs = intent.data // Pair[Index, Structure]
                    withTimeoutOrNull(10_000L) {
                        pairs.forEach {
                            val index = it.first
                            val structure = it.second

                            val pageSize = when(structure.style) {
                                BlockStyle.HighLight -> intent.perPage - 1
                                BlockStyle.NumericRank -> 10
                                else -> intent.perPage
                            }
                            val watchingVersion = if(structure.itype == Constants.WATCHING_TYPE) "v1" else null

                            arrDeferred.add(async {
                                getBlockDataWithRecommend(
                                    structure,
                                    intent,
                                    pageSize,
                                    watchingVersion
                                ).process(index, structure, pageSize)
                            })
                        }
                        // Parallel the all requests
                        arrDeferred.forEach { it.await() }
                    }
                    pairs.map { item ->
                        if (item.second.items.isNotEmpty()) {
                            if (item.second.items[0].itype == ItemType.Fake) {
                                item.second.items = emptyList()
                            }
                        }
                    }
                    withContext(Dispatchers.Main) {
                        block(intent)
                    }
                }
                catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
            } // Process timeout or return null
            withContext(Dispatchers.Main) {
                clusterItemJob = null
                _state.value = HomeBaseState.Done()
            }
        }
        Logger.d("Measure time of GetStructureItem: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }

    private fun createNewStructure(
        structure: Result<StructureAndMeta>,
        recommend: Result<List<RecommendBlockPosition>?>
    ): Result<StructureAndMeta> {
        Logger.d("createNewStructure: ${structure::class.simpleName} - ${recommend::class.simpleName}")

        return if (structure is Result.Success && recommend is Result.Success) {
            val newStructureList = mapStructureWithRecommendData(structure.successData.structure, recommend.data)
            structure.copy(successData = structure.successData.copy(structure = newStructureList))
        } else if(structure is Result.Success) {
            structure.copy(successData = structure.successData.copy(structure = structure.successData.structure))
        } else {
            structure
        }
    }

    private fun mapStructureWithRecommendData(structure: List<Structure>, recommend: List<RecommendBlockPosition>?): List<Structure> {

        val newStructureList = arrayListOf<Structure>().apply {
            addAll(structure)
        }
        recommend?.forEach { recommendData ->
            newStructureList.find { it.id == recommendData.blockId }?.let { structureItem ->
                if (recommendData.position in structure.indices) {
                    newStructureList.remove(structureItem)
                    newStructureList.add(recommendData.position, structureItem)
                }
            }
        }
        return newStructureList
    }

    private fun getBlockDataWithRecommend(
        structure: Structure,
        intent: HomeBaseIntent.GetClusterItem,
        pageSize: Int = 10,
        watchingVersion: String? = null
    ): Flow<Result<List<StructureItem>>> {

        if(structure.needRecommend) {
            var cacheItems = highlightRecommendHandler.getHighlightRecommendBlock(structure.id)?.blockRecommendationData?.blockRecommendationValue
            if (!cacheItems.isNullOrEmpty()) {
                return homeOs4Repository.getStructureItem(
                    blockId = structure.id,
                    type = structure.itype ?: "",
                    blockType = structure.style.id,
                    pageIndex = intent.page,
                    pageSize = pageSize,
                    customData = structure.customData,
                    watchingVersion = watchingVersion,
                )
            } else {
                return homeOs4Repository.getStructureItem(
                    blockId = structure.id,
                    type = structure.itype ?: "",
                    blockType = structure.style.id,
                    pageIndex = intent.page,
                    pageSize = pageSize,
                    customData = structure.customData,
                    watchingVersion = watchingVersion,
                    pageId = intent.pageProvider.pageId.id
                ).zip(
                    homeOs4Repository.getRecommendHighlightBlockItems(
                        blockId = structure.id,
                        blockDataType = structure.itype ?: "",
                        blockStyle = structure.style.id,
                    )
                ) { structureItem, recommend ->
                    if (recommend is Result.Success) {
                        if (!recommend.data.isNullOrEmpty()) {
                            cacheItems = recommend.data
                            highlightRecommendHandler.updateHighlightRecommendBlock(
                                blockId = structure.id,
                                blockRecommendationData = BlockHighlightRecommendation.BlockHighlightRecommendationData(
                                    blockRecommendationTimestamp = System.currentTimeMillis(),
                                    blockRecommendationValue = recommend.data
                                )
                            )
                        }
                    }
                    structureItem
                }
            }
        } else {
            return homeOs4Repository.getStructureItem(
                blockId = structure.id,
                type = structure.itype ?: "",
                blockType = structure.style.id,
                pageIndex = intent.page,
                pageSize = pageSize,
                customData = structure.customData,
                watchingVersion = watchingVersion,
                pageId = intent.pageProvider.pageId.id,
                isBlockRecommend = structure.needSendLogRecommend
            )
        }
    }

    private suspend fun getStructureItemsLocal(
        intent: HomeBaseIntent.GetClusterItemLocal,
        block: (intent: HomeBaseIntent.GetClusterItemLocal) -> Unit
    ) {
        val measureTime = measureTimeMillis {
            withContext(Dispatchers.IO) {
                try {
                    val pairs = intent.data // Pair[Index, Structure]
                    pairs.forEach {
                        val index = it.first
                        val structure = it.second
                        processLocal(index = index, data = structure)
                    }
                    pairs.map { item ->
                        if (item.second.items.isNotEmpty()) {
                            if (item.second.items[0].itype == ItemType.Fake) {
                                item.second.items = emptyList()
                            }
                        }
                    }
                    withContext(Dispatchers.Main) {
                        block(intent)
                    }
                } catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
            } // Process timeout or return null
            withContext(Dispatchers.Main) {
                _state.value = HomeBaseState.Done()
            }
        }
        Logger.d("Measure time of GetStructureItem: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }

    private suspend fun Flow<Result<List<StructureItem>>>.process(index: Int, data: Structure, dataItemSize: Int) {
        this.filter { it is Result.Success || it is Result.Error }
            .collect {
                val structureItem = it.data ?: emptyList()
                val cacheItems =
                    highlightRecommendHandler.getHighlightRecommendBlock(data.id)?.blockRecommendationData?.blockRecommendationValue
                val metaName = structureItem.firstOrNull()?.metaName ?: ""
                data.name = metaName.ifBlank { data.name }

                data.items = if (cacheItems?.isNotEmpty() == true) {
                    highlightRecommendHandler.mergeOriginalDataWithRecommend(
                        originalData = structureItem,
                        recommendData = cacheItems,
                        requiredDataSize = dataItemSize
                    ).filterEndedItem()
                } else {
                    structureItem.filterEndedItem()
                }

                if (data.itype.equals("category")) {
                    saveStructureItemType(data.itype)
                    saveBlockId(data.id)
                    saveBlockType(data.style.id)
                    saveCustomData(data.customData)
                }

                safeLaunch {
//                    Timber.d("*** Item result: $index - $data")
                    _state.value = HomeBaseState.ResultStructureItem(
                        isCached = false,
                        data = Pair(index, data)
                    )
                }
            }
    }

    private fun processLocal(index: Int, data: Structure) {
        if (data.itype != Constants.WATCHING_TYPE
            && data.itype != Constants.FOLLOW_TYPE
            && data.itype != Constants.FOLLOW_CHANNEL_TYPE
            && data.itype != Constants.COMING_SOON) {
            data.items = data.items.filterEndedItem()
        }
    }

    private fun List<StructureItem>.filterEndedItem(): List<StructureItem> {
        return filter { Util.statusBetweenStartAndEndTime(it.beginTime, it.endTime) != 3 }
    }

    //region process channel follow
    fun saveFollowChannelPosition(data: List<Structure>) {
        saveFollowPos(-1)
        saveFollowStructure(null)
        data.filter { it.items.isNotEmpty() || it.itype == Constants.FOLLOW_CHANNEL_TYPE }
            .forEachIndexed { index, structure ->
                if (structure.itype == Constants.FOLLOW_CHANNEL_TYPE) {
                    saveFollowPos(index)
                    saveFollowStructure(structure)
                    return@forEachIndexed
                }
            }
//        Timber.d("***Save follow channel pos: $followPos")
    }

    //endregion process channel follow

    //region process watching and follow
    private fun saveWatchingAndFollowPosition(data: List<Structure>) {
        saveWatchingPos(-1)
        saveFollowPos(-1)
        saveWatchingStructure(null)
        saveFollowStructure(null)

        var followPosTemp = 0
        var watchingPosTemp = 0

        run loop@{
            data.forEach { item ->
                when {
                    item.itype == Constants.WATCHING_TYPE -> {
//                    Timber.d("***Before update ${item.itype}: $watchingPosTemp")
                        watchingPosTemp += 1
                        saveWatchingPos(watchingPosTemp)
                        saveWatchingStructure(item)
                        if (followPos == -1) {
                            followPosTemp += 1
                        } else {
                            return@loop
                        }
                    }
                    item.itype == Constants.FOLLOW_TYPE -> {
//                    Timber.d("***Before update ${item.itype}: $followPosTemp")
                        followPosTemp += 1
                        saveFollowPos(followPosTemp)
                        saveFollowStructure(item)
                        if (watchingPos == -1) {
                            watchingPosTemp += 1
                        } else {
                            return@loop
                        }
                    }
                    (item.items.isNotEmpty() || item.itype.lowercase() == "ads") -> {
//                        Timber.d("***Before update plus 1 ${item.itype}: $watchingPosTemp $followPosTemp")
                        followPosTemp += 1
                        watchingPosTemp += 1
                    }
                    (item.items.isEmpty() && item.itype.lowercase() != "ads") -> {
//                        Timber.d("***Before update minus 1 ${item.itype}: $watchingPosTemp $followPosTemp")
                        followPosTemp -= 1
                        watchingPosTemp -= 1
                    }
                }
            }
        }
        Timber.d("***Before update watching: $watchingPos follow: $followPos")
    }

    val watchingPos get() = savedState.get("watchingPos") ?: -1
    val followPos get() = savedState.get("followPos") ?: -1

    val structureType get() = savedState.get("structureType") ?: ""
    val blockType get() = savedState.get("blockType") ?: ""
    val blockId get() = savedState.get("blockId") ?: ""
    val customData get() = savedState.get("customData") ?: ""

    val watchingStruct get() = watchingStructure
    val followStruct get() = followStructure

    val structureMeta get() = structMeta
    private fun saveWatchingStructure(data: Structure?) {
        watchingStructure = data
    }

    private fun saveFollowStructure(data: Structure?) {
        followStructure = data
    }

    private fun saveWatchingPos(pos: Int) {
        savedState.set("watchingPos", pos)
    }

    private fun saveFollowPos(pos: Int) {
        savedState.set("followPos", pos)
    }

    fun clearWatchingAndFollowPos() {
        savedState.set("followPos", -1)
        savedState.set("followPos", -1)
        followStructure = null
        watchingStructure = null
    }

    fun saveStructureItemType(type:String){
        savedState.set("structureType",type)
    }
    fun saveBlockId(id:String){
        savedState.set("blockId",id)
    }
    fun saveBlockType(type:String){
        savedState.set("blockType",type)
    }
    fun saveCustomData(customData:String){
        savedState.set("customData",customData)
    }
    /**
     * Before adding Ads, these [watchingPos] and [followPos] were set (by calling [saveWatchingAndFollowPosition]).
     * This function is used for updated these 2 variables
     */
    fun updateWatchingAndFollowPos(structures: List<Structure>) {
        val differBetweenWatchingAndFollow = watchingPos - followPos
        run loop@{
            if (watchingPos > -1 && followPos > -1) {
                structures.forEachIndexed { index, item ->
                    if (item.itype == Constants.WATCHING_TYPE) {
                        saveWatchingPos(index)
                        saveFollowPos(index - differBetweenWatchingAndFollow)
                        return@loop
                    } else if (item.itype == Constants.FOLLOW_TYPE) {
                        saveFollowPos(index)
                        saveWatchingPos(index + differBetweenWatchingAndFollow)
                        return@loop
                    }
                }
            } else {
                if (watchingPos > -1) {
                    var needContinueProcess = true
                    structures.forEachIndexed { index, item ->
                        if (item.itype == Constants.WATCHING_TYPE) {
                            saveWatchingPos(index)
                            needContinueProcess = false
                            return@loop
                        }
                    }
                    if (needContinueProcess) {
                        try {
                            structures.filter { it.contentType != Structure.ContentType.Ads }[watchingPos].run {
                                saveWatchingPos(structures.indexOf(this) + 1)
                            }
                        } catch (ex: IndexOutOfBoundsException) {
                            ex.printStackTrace()
                        }
                    }
                }

                if (followPos > -1) {
                    Timber.d("***follow: $watchingPos - $differBetweenWatchingAndFollow")
                    saveFollowPos(watchingPos - differBetweenWatchingAndFollow)
                }
            }
        }

//        Timber.d("***After update watching: $watchingPos follow: $followPos")
    }
    //endregion process watching and follow

    sealed class HomeBaseState : ViewState {
        object Idle : HomeBaseState()
        data class Loading(val data: HomeBaseIntent? = null) : HomeBaseState()
        data class StructureResult(val isCached: Boolean, val data: List<Structure>) : HomeBaseState()
        data class StructureHBOResult(val isCached: Boolean, val data: List<Structure>) : HomeBaseState()
        data class StructureSportResult(val isCached: Boolean, val data: List<Structure>) : HomeBaseState()
        data class StructureTVResult(val isCached: Boolean, val data: List<Structure>) : HomeBaseState()
        data class StructureOtherResult(val isCached: Boolean, val data: List<Structure>) : HomeBaseState()
        data class ErrorRequiredLogin(val message: String, val data: HomeBaseIntent?) : HomeBaseState()

        data class ResultClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) :
            HomeBaseState()

        data class ResultHboClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) :
            HomeBaseState()

        data class ResultSportClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) :
            HomeBaseState()

        data class ResultTVClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) :
            HomeBaseState()

        data class ResultOtherClusterStructureItem(val isCached: Boolean, val data: List<Pair<Int, Structure>>) :
            HomeBaseState()

        data class ResultStructureItem(val isCached: Boolean, val data: Pair<Int, Structure>) : HomeBaseState()

        data class ResultBookItem(val isCached: Boolean, val data: Status, val itemId: String) : HomeBaseState()
        data class ResultUnBookItem(val isCached: Boolean, val data: Status, val itemId: String) : HomeBaseState()
        data class ResultStream(val isCached: Boolean, val data: Stream) : HomeBaseState()
        data class ResultStructureMetaData(val isCached: Boolean, val shouldProcess: Boolean, val data: StructureMeta) :
            HomeBaseState()

        data class Error(val message: String, val data: HomeBaseIntent? = null) : HomeBaseState()
        data class ErrorNoInternet(val data: HomeBaseIntent? = null) : HomeBaseState()
        data class Done(val data: HomeBaseIntent? = null) : HomeBaseState()
        data class OpenInteractiveAdsPopup(val jsonData: String) : HomeBaseState()
        data class ResultComingSoonStructureItem(val isCached: Boolean, val data: List<StructureItem>) : HomeBaseState()
        data class ResultFollowStructureItem(val isCached: Boolean, val data: List<StructureItem>) : HomeBaseState()
        data class ResultHistoryVodStructureItem(val isCached: Boolean, val data: List<StructureItem>) : HomeBaseState()
        data class ResultCategoryStructure(val isCached: Boolean, val data: Structure) : HomeBaseState()
        data class ResultPopup3GInformation(val isCached: Boolean, val data: Map<String, Information3GNetworkProviderResult>, val countryCode: String) : HomeBaseState()

    }

    sealed class HomeBaseIntent : ViewIntent {
        data class GetStructure(val pageProvider: PageProvider, val userLogin: Boolean, val revision: String) :
            HomeBaseIntent()
        data class GetStructureLocal(val pageProvider: PageProvider, val dataLocal: List<Structure>) :
            HomeBaseIntent()

        data class GetClusterItem(
            val pageProvider: PageProvider,
            val data: List<Pair<Int, Structure>>,
            val userId: String,
            val page: Int,
            val perPage: Int,
        ) : HomeBaseIntent()

        data class GetClusterItemLocal(
            val pageProvider: PageProvider,
            val data: List<Pair<Int, Structure>>,
        ) : HomeBaseIntent()

        data class TriggerOpenInteractiveAdsPopup(val jsonData: String) : HomeBaseIntent()
        object TriggerIdleInteractiveAds : HomeBaseIntent()
        data class BookItem(val type:String, val itemId:String) : HomeBaseIntent()
        data class UnBookItem(val type:String, val itemId:String) : HomeBaseIntent()
        data class GetStream(val id: String, val episodeId: String, val bitrateId: String) : HomeBaseIntent()
        data class GetStructureItem(
            val structureId: String,
            val type: String,
            val blockType: String,
            val userId: String,
            val page: Int,
            val perPage: Int,
            val watchingVersion: String? = null,
            val customData: String,
        ) : HomeBaseIntent()

        data class GetHistoryVod(val userId: String, val page: Int, val perPage: Int) : HomeBaseIntent()

        data class GetCategoryStructure(val structure : Structure) : HomeBaseIntent()

        data class GetPopup3GInformation(val countryCode: String) : HomeBaseIntent()
    }

    // region Cache Page Recommendation
    private fun getPageRecommendationKey(pageId: String, userId: String, profileId: String, profileType: String): String {
        return "$pageId/$userId/$profileId/$profileType"
    }

    private fun savePageRecommendation(key: String, data: PageRecommendationData) {
        synchronized(cachePageRecommendations) {
            val item = cachePageRecommendations.find { it.pageId == key }
            item?.let {
                it.pageRecommendationData = data
            } ?: kotlin.run {
                cachePageRecommendations.add(PageRecommendation(pageId = key, pageRecommendationData = data))
            }
        }
        Logger.d("A--savePageRecommendation -> cachePageRecommendations: $cachePageRecommendations")
        saveCachePageRecommendation(cachePageRecommendations)
    }

    private fun removePageRecommendation(key: String) {
        synchronized(cachePageRecommendations) {
            val newList = cachePageRecommendations.filterNot { it.pageId == key }
            cachePageRecommendations.clear()
            cachePageRecommendations.addAll(newList)
        }
        Logger.d("A--removePageRecommendation -> cachePageRecommendations: $cachePageRecommendations")
        saveCachePageRecommendation(cachePageRecommendations)
    }

    private fun getPageRecommendation(key: String): PageRecommendationData? {
        synchronized(cachePageRecommendations) {
            return cachePageRecommendations.firstOrNull { it.pageId == key }?.pageRecommendationData
        }
    }

    private fun isPageRecommendationValid(key: String): Boolean {
        //
        if (cachePageRecommendations.isEmpty()) {
            cachePageRecommendations.clear()
            cachePageRecommendations.addAll(getCachePageRecommendation())
        }

        Logger.d("A--isPageRecommendationValid -> catchPageRecommendations: $cachePageRecommendations")

        val now = System.currentTimeMillis()
        val data = getPageRecommendation(key)
        Logger.d("A--isPageRecommendationValid -> page_recommend_data: $data")
        if (data != null) {
            return now - data.pageRecommendationTimestamp < PAGE_RECOMMENDATION_EXPIRE_TIME
        }
        return false
    }

    private fun saveCachePageRecommendation(data: List<PageRecommendation>) {
        try {
            val gson = Gson()
            val json = gson.toJson(data)
            Logger.d("A--saveCachePageRecommendation -> json: $json")
            sharedPreferences.saveCachePageRecommendation(data = json)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getCachePageRecommendation(): List<PageRecommendation> {
        try {
            val gson = Gson()
            val json = sharedPreferences.getCachePageRecommendation()
            if (json.isNotBlank()) {
                val type = object : TypeToken<List<PageRecommendation>>() {}.type
                return gson.fromJson(json, type)
            } else {
                return mutableListOf()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return mutableListOf()
        }
    }


    // endregion
}