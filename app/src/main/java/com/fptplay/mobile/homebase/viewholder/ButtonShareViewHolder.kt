package com.fptplay.mobile.homebase.viewholder

import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.BlockButtonShareBinding
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure

class ButtonShareViewHolder(private val binding: BlockButtonShareBinding, private val onShare: ((data: Structure?) -> Unit)? = null): RecyclerView.ViewHolder(binding.root) {
    private var buttonData: Structure? = null
    fun bind(data: Structure) {
        buttonData = data
        binding.root.onClickDelay {
            onShare?.invoke(data)
        }
    }
}