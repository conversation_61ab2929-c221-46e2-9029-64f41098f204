package com.fptplay.mobile.homebase

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.constraintlayout.widget.Constraints
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.dial.model.DeviceInfo
import com.fptplay.dial.model.FBoxDeviceInfoV2
import com.fptplay.dial.scanner.interfaces.FScannerAwakeListener
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.navigateToScreen
import com.fptplay.mobile.common.extensions.runOnUiThread
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.global.GlobalEventObserver
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.StringUtils.truncateString
import com.fptplay.mobile.databinding.HomeAppBarBinding
import com.fptplay.mobile.databinding.HomeAppBarWithoutIconsBinding
import com.fptplay.mobile.databinding.HomeToolBarBinding
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureMeta
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


@AndroidEntryPoint
open class HomeBaseDetailFragment : HomeBaseFragment() {
    val safeArgs: HomeBaseDetailFragmentArgs? by navArgs()
    override val handleBackPressed = false

    private var _appbarBinding: HomeAppBarBinding? = null
    private val appbarBinding get() = _appbarBinding!!
    private var _headerViewBinding: HomeToolBarBinding? = null
    private val headerViewBinding get() = _headerViewBinding!!
    private var toolBarContainer: View? = null
    private var _appbarWithoutIconsBinding: HomeAppBarWithoutIconsBinding? = null
    private val appbarWithoutIconsBinding get() = _appbarWithoutIconsBinding!!
    private var pageTitle = ""
    private var isShowTitle = false
    private val globalObserver by lazy { GlobalEventObserver() }
    private var idApp:String? = null
    private var oldAppName = ""
    private var oldAppId = ""

    override var pageId: PageId
        get() = PageId.OtherPageId(safeArgs?.id ?: "")
        set(value) {}

    // Pairing Control
    private val pairingScanner by lazy { MainApplication.INSTANCE.pairingScannerHelper }
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    //

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Logger.d("trangtest onViewCreated")
        processAddHeaderAndBackground(metaData = metaData ?: StructureMeta(
            name = safeArgs?.title ?: "",
            metaTitle = safeArgs?.description ?: "",
        ))
        checkBeforePlayUtil.setScreenProvider(safeArgs?.screenProvider ?: "")
    }
    override fun bindComponent() {
        super.bindComponent()
        val deeplinkData = arguments?.getBundle(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME)
        if(deeplinkData != null) {
            DeeplinkUtils.handleDeeplinkInPageIzios (deeplinkData, this)
        }
    }
    override fun bindEvent() {
        super.bindEvent()
        viewLifecycleOwner.lifecycle.addObserver(globalObserver)
        globalObserver.addGlobalEventListener(GlobalEvent.NOTIFICATION_RECEIVED) {
            hasNewNotification()
        }
        setFragmentResultListener(Constants.CATEGORY_OF_CATEGORIES_BUNDLE_KEY) {_,bundle ->
            val style = bundle.getString(Constants.CATEGORY_OF_CATEGORIES_STYLE_KEY, "")
            val type = bundle.getString(Constants.CATEGORY_OF_CATEGORIES_TYPE_KEY, "")
            val id = bundle.getString(Constants.CATEGORY_OF_CATEGORIES_ID_KEY, "")
            findNavController().navigate(NavHomeMainDirections.actionGlobalToViewMoreFragment(
                blockStyle = style,
                blockType = type,
                id = id,
                pageId = pageId.id
            ))
        }
        setFragmentResultListener(Constants.CHECK_REQUIRE_VIP) { key, bundle ->
            checkBeforePlayUtil.onFragmentResult(key, bundle)
        }
//        binding.containerButtonShare.layoutShare.setOnClickListener {
//            ShareScheduleSportUtils(activity).shareScheduleContent(metaTitle,
//                getString(R.string.base_url_deep_link_sport, ShareScheduleSportUtils(activity).formatTextTitle(metaName),safeArgs?.id))
//        }
    }
    override fun bindData() {
        super.bindData()
        idApp?.apply {
            TrackingUtil.currentAppId = this
            TrackingUtil.currentAppName = pageTitle
        }
        Logger.d("trangtest bindData idApp = $idApp pageTitle = $pageTitle" + "Page id: ${pageId.id}")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewLifecycleOwner.lifecycle.removeObserver(globalObserver)
        // Pairing Control
        removePairingControlListener()
        //
        toolBarContainer = null
        _appbarBinding = null
        _headerViewBinding = null
        _appbarWithoutIconsBinding = null
    }

    private fun addHeaderToolbar(metaData: StructureMeta) {
        if (toolBarContainer != null) {
            binding.root.removeView(toolBarContainer)
        }

        toolBarContainer = ConstraintLayout(requireContext())
        toolBarContainer?.let { toolBarContainer ->
            toolBarContainer.id = View.generateViewId()
            val lP = Constraints.LayoutParams(
                Constraints.LayoutParams.MATCH_PARENT,
                Constraints.LayoutParams.WRAP_CONTENT
            )
            toolBarContainer.layoutParams = lP
            binding.root.addView(toolBarContainer)
            val constraintSet = ConstraintSet()
            constraintSet.clone(binding.root)
            constraintSet.connect(
                toolBarContainer.id,
                ConstraintSet.TOP,
                binding.root.id,
                ConstraintSet.TOP,
                requireContext().resources.getDimensionPixelSize(R.dimen.app_margin_negative)
            )
            constraintSet.applyTo(binding.root)
        }
        if (isHeaderWithoutToolbar()) {
            toolBarContainer?.let { toolBarContainer ->
                _appbarWithoutIconsBinding = HomeAppBarWithoutIconsBinding.inflate(layoutInflater, toolBarContainer as ViewGroup)
                appbarWithoutIconsBinding.ivBack.isVisible = true
                appbarWithoutIconsBinding.apply {
                    llLogo.setOnClickListener { findNavController().popBackStack() }
                    if (metaData.headerUrl.isNotEmpty()) {
                        tvTitle.isVisible = false
                        ivLogo.isVisible = true
                        ImageProxy.load(
                            context = root.context,
                            url = metaData.headerUrl,
                            width = root.context.resources.getDimensionPixelSize(R.dimen.home_header_without_icons_logo_width),
                            height = root.context.resources.getDimensionPixelSize(R.dimen.home_header_without_icons_logo_height),
                            target = ivLogo
                        )
                    } else {
                        tvTitle.isVisible = true
                        ivLogo.isVisible = false
                        tvTitle.text = pageTitle
                    }

                    if (metaData.headerBackgroundUrl.isNotEmpty()) {
                        ImageProxy.load(
                            context = root.context,
                            url = metaData.headerBackgroundUrl,
                            width = root.context.resources.getDimensionPixelSize(R.dimen.home_header_without_icons_image_background_width),
                            height = 0,
                            target = ivAppBarBackground,
                        )
                    }
                }
            }
        } else { // General Appbar
            toolBarContainer?.let { toolBarContainer ->
                _appbarBinding =
                    HomeAppBarBinding.inflate(layoutInflater, toolBarContainer as ViewGroup)
                _headerViewBinding = HomeToolBarBinding.bind(appbarBinding.root)
                headerViewBinding.ivBack.isVisible = true
                appbarBinding.apply {
                    if (pageId.id == "channel") {
                        tvSelectCategory.text =
                            appbarBinding.tvSelectCategory.context.getString(R.string.view_list_channel)
                    } else {
                        tvSelectCategory.text =
                            appbarBinding.tvSelectCategory.context.getString(R.string.select_categories)
                    }
                    tvSelectCategory.isVisible = isShowTitle
                    tvSelectCategory.setOnClickListener {
                        if (pageId.id == "channel") {
                            findNavController().navigate(NavHomeMainDirections.actionGlobalToTv())
                        } else {
                            findNavController().navigate(
                                NavHomeMainDirections.actionGlobalToCategoryOfCategoriesFragment(
                                    viewModel.structureType,
                                    viewModel.blockId,
                                    viewModel.blockType,
                                    viewModel.customData
                                )
                            )
                        }
                    }
                }
                headerViewBinding.apply {
                    if (MultiProfileUtils.isCurrentProfileKid(sharedPreferences)) {
                        ivPayment.gone()
                        ivNotification.gone()
                        ivCast.isVisible = MainApplication.INSTANCE.appConfig.castType == 1

                        ivProfileAvatar.bindProfile(
                            url = sharedPreferences.profileAvatar(),
//                    url ="https://images.fptplay.net/media/photo/OTT/2023/12/20/brother20-12-2023_16g19-51.png",
                            width = Utils.getSizeInPixel(root.context, R.dimen.app_bar_icon),
                            height = Utils.getSizeInPixel(root.context, R.dimen.app_bar_icon),
                            kidProfile = true
                        )
                        ivProfileAvatar.visible()


                    } else {
                        ivNotification.isActivated =
                            MainApplication.INSTANCE.appConfig.hasNewNotification

                        ivCast.isVisible = MainApplication.INSTANCE.appConfig.castType == 1

                        ivProfileAvatar.gone()
                    }
                    tvTitle.isVisible = true
                    ivLogo.isVisible = false
                    tvTitle.text = pageTitle
                    llLogo.setOnClickListener { findNavController().popBackStack() }
                    ivSearch.setOnClickListener {
                        findNavController().navigate(NavHomeMainDirections.actionGlobalToSearchFragmentDialog())
//                findNavController().navigate(NavHomeMainDirections.actionGlobalToMomentsFragment())
                    }

                    ivNotification.setOnClickListener {
                        if (sharedPreferences.userLogin()) {
                            findNavController().navigate(NavHomeMainDirections.actionGlobalToNotification())
                        } else {
                            navigateToLoginWithParams(
                                navigationId = R.id.action_global_to_notification,
                                isDirect = true
                            )
                        }
                    }

                    ivPayment.setOnClickListener {
                        findNavController().navigate(NavHomeMainDirections.actionGlobalToPayment())
                    }
                    ImageProxy.load(
                        context = root.context,
                        url = MainApplication.INSTANCE.appConfig.logo,
                        width = root.context.resources.getDimensionPixelSize(R.dimen.home_fpt_logo),
                        height = 0,
                        target = ivLogo,
                        errorDrawableId = R.drawable.fpt_play_logo,
                        placeHolderId = R.drawable.fpt_play_logo
                    )

                    ivCast.setOnClickListener {
                        onClickCastButton()
                    }
                    addPairingControlListener()
                    bindEventPairingControlFragmentResult()
                    updateCastButtonState()
                }
            }
        }
    }

    private fun setBackgroundColor(metaData: StructureMeta) {
        try {
            if (metaData.pageBackground.isNotEmpty()) {
                if (metaData.pageBackground.size > 1)
                    binding.root.background = getPageBackgroundColor(metaData)
                else
                    binding.root.setBackgroundColor(Color.parseColor(metaData.pageBackground.first()))
            } else {
                binding.root.setBackgroundColor(Color.BLACK)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getPageBackgroundColor(meta: StructureMeta): GradientDrawable {
        val colors = meta.pageBackground.map { color -> Color.parseColor(color) }.toIntArray()
        return GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors)
    }

    private fun hasNewNotification() {
        runOnUiThread {
            if (!isHeaderWithoutToolbar()) {
                headerViewBinding.ivNotification.isActivated = true
            }
        }
    }

    private fun noNewNotification() {
        runOnUiThread {
            if (!isHeaderWithoutToolbar()) {
                headerViewBinding.ivNotification.isActivated = false
            }
        }
    }

    override fun showNoInternetView(parentView: View?) {
        super.showNoInternetView(view)
    }

    override fun hideNoInternetView(parentView: View?) {
        super.hideNoInternetView(view)
    }

    override fun showPageError(parentView: View?, title: String?, errorMessage: String, navigationIcon: Int?) {
        super.showPageError(parentView, title, errorMessage, navigationIcon)
    }

    override fun hidePageError(parentView: View?) {
        super.hidePageError(parentView)
    }

    override fun retryLoadPage() {
        getStructure()
    }

    override fun gotoDownload() {
        activity?.navigateToScreen(R.id.action_global_to_downloadMainFragment)
    }

    override fun gotoDownloadMega() {
        activity?.navigateToScreen(R.id.action_global_to_download_v2)
    }

    override fun getStructure(id: String) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetStructure(
                PageProvider.Other(safeArgs?.id ?: ""),
                sharedPreferences.userLogin(),
                revision = MainApplication.INSTANCE.revision
            )
        )
    }

    override fun getStructureItem(
        data: List<Pair<Int, Structure>>,
        userId: String,
        page: Int,
        perPage: Int
    ) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetClusterItem(
                pageProvider = PageProvider.Other(safeArgs?.id ?: ""),
                data = data,
                userId = sharedPreferences.userId(),
                page = page,
                perPage = perPage
            )
        )
    }

    override fun getStructureLocal() {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetStructureLocal(
                pageProvider = PageProvider.Other(safeArgs?.id ?: ""),
                homeBaseAdapter.data().filter { it.contentType != Structure.ContentType.Ads })
        )
    }

    override fun getStructureItemLocal(data: List<Pair<Int, Structure>>) {
        viewModel.dispatchIntent(
            HomeBaseViewModel.HomeBaseIntent.GetClusterItemLocal(
                pageProvider = PageProvider.Other(safeArgs?.id ?: ""),
                data = data
            )
        )
    }

    override fun processStateToUi(state: HomeBaseViewModel.HomeBaseState) {
        when (state) {
            is HomeBaseViewModel.HomeBaseState.Error -> {
                when(state.data) {
                    is HomeBaseViewModel.HomeBaseIntent.GetStructure -> {
                        showPageError(errorMessage = state.message)
                    }
                    else -> {}
                }
            }
            is HomeBaseViewModel.HomeBaseState.ErrorNoInternet -> {
                when(state.data) {
                    is HomeBaseViewModel.HomeBaseIntent.GetStructure -> {
                        showNoInternetView()
                    }
                    else -> {}
                }
            }

            is HomeBaseViewModel.HomeBaseState.StructureOtherResult -> {
                processStructureResult(state.data)
                sendTrackingBlock511(state.data)
            }

            is HomeBaseViewModel.HomeBaseState.ResultOtherClusterStructureItem -> {
                processStructureItemResult(state.data) {
                    if(showButtonShare) {
                        processButtonShare(
                            Structure(
                                contentType = Structure.ContentType.ButtonShare,
                                name = metaName ?: "",
                                id = safeArgs?.id ?: ""
                            )
                        )
                    }
                }
            }

            is HomeBaseViewModel.HomeBaseState.ResultStructureItem -> {
//                Timber.d("*** Execute result: ${state.data.first}")
                updateUIStructureItem(index = state.data.first, state.data.second)
            }

            else -> {}
        }
    }
    private fun updateUIStructureItem(index: Int, structure: Structure) {
//        Timber.d("*** Execute update item $index - ${structure.items.size}")
        homeBaseAdapter.refreshStructureItem(index = index, dataSize = structure.items.size, item = structure)
        if (structure.itype.equals("category")) {
            isShowTitle = true
            appbarBinding.tvSelectCategory.isVisible = isShowTitle
        }
        logRecommendBlockItem(structure = structure)
//        if(structure.style == BlockStyle.SportSideBySideTable){
//            binding.containerButtonShare.layoutShare.visibility = VISIBLE
//        }
    }
    override fun processMetaData(metaData: StructureMeta) {
        super.processMetaData(metaData)
        pageTitle = metaData.name
        metaTitle = if(!metaData.metaTitle.isNullOrEmpty()) metaData.metaTitle else safeArgs?.description.toString()
        metaName = if(!metaData.name.isNullOrEmpty()) metaData.name else safeArgs?.title.toString()
        showButtonShare = metaData.isShare
        logAccessModule()
        //
        processAddHeaderAndBackground(metaData)

        if (!isHeaderWithoutToolbar()) {
            headerViewBinding.tvTitle.text = metaData.name
            if (pageId.id == "channel") {
                isShowTitle = true
                appbarBinding.tvSelectCategory.apply {
                    isVisible = isShowTitle
                }
            }
        }
    }
    override fun navigateToPage(pageId: String, title: String) {
        findNavController()
            .navigate(
                NavHomeMainDirections.actionGlobalToCategoryDetail(id = pageId, title = title, screenProvider = safeArgs?.screenProvider ?: "",
                description =  safeArgs?.description.toString()
                )
            )
    }
    override fun navigateToViewMore(
        blockStyle: String,
        blockType: String,
        viewMoreId: String,
        header: String,
        subHeader: String,
        customData: String
    ) {
        findNavController().navigate(
            NavHomeMainDirections.actionGlobalToViewMoreFragment(
                blockStyle = blockStyle,
                blockType = blockType,
                id = viewMoreId,
                header = header,
                subHeader = subHeader,
                screenProvider = safeArgs?.screenProvider ?: "",
                customData = customData,
                pageId = pageId.id
            )
        )
    }

    private fun processAddHeaderAndBackground(metaData: StructureMeta) {
        addHeaderToolbar(metaData)
        setBackgroundColor(metaData)
    }

    private fun showAppBar(isShow: Boolean) {
        if (isHeaderWithoutToolbar()) {
            appbarWithoutIconsBinding.root.show()
        } else {
            appbarBinding.root.isVisible = true
            headerViewBinding.tvTitle.isVisible = true
            if (isShowTitle) {
                appbarBinding.tvSelectCategory.isVisible = isShowTitle
            }
        }
    }

    private fun hideAppBar() {
        if (isHeaderWithoutToolbar()) {
            appbarWithoutIconsBinding.root.hide()
        } else {
            appbarBinding.root.isVisible = false
        }
    }

    override fun processScroll(dy: Int, recyclerView: RecyclerView) {
        if (dy > 0) hideAppBar()
        else if (dy < 0) {
            showAppBar(false)
            if (!recyclerView.canScrollVertically(-1))
                showAppBar(true)
        }
    }

    private fun logAccessModule() {
        Timber.d("*****Log metadata")
        if (oldAppId == "") { //start app lan dau
            oldAppName = TrackingUtil.currentAppName
            oldAppId = TrackingUtil.currentAppId
        }
        TrackingUtil.currentAppId = safeArgs?.id ?: ""
        TrackingUtil.currentAppName = pageTitle
        idApp = TrackingUtil.currentAppId
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo, logId = "18", appId = oldAppId, appName = oldAppName,
                event = "ChangeModule",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemId = TrackingUtil.currentAppId, itemName = TrackingUtil.currentAppName
            )
        )
        trackingInfo.updateAppSession(System.currentTimeMillis())
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo, logId = "50", appId = TrackingUtil.currentAppId, appName = TrackingUtil.currentAppName,
                event = "Access",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime()
            )
        )
        oldAppName = TrackingUtil.currentAppName
        oldAppId = TrackingUtil.currentAppId
    }

    //region Pairing Control
    private fun onClickCastButton() {
        if (sharedPreferences.userLogin()) {
            // Pairing control
            if (!pairingConnection.isConnected) { // Navigate to search devices
                navigateToPairingControl(type = 0)
            } else {
                navigateToPairingControl(type = 3)
            }
        } else {
            navigateToLoginWithParams(navigationId = R.id.nav_pairing_control_host)
        }
    }

    private fun updateCastButtonState() {
        if (!isHeaderWithoutToolbar()) {
            headerViewBinding.ivCast.isSelected = pairingConnection.isConnected
        }
    }

    private fun bindEventPairingControlFragmentResult() {
        setFragmentResultListener(Utils.PAIRING_DIALOG_TYPE) { _, bundle ->
            val type = bundle.getInt(Utils.PAIRING_DIALOG_TYPE_KEY, 0)
            when (type) {
                0 -> {
                    try {
                        pairingConnection.let {
                            it.getSelectDevice()?.run {
                                when (this) {
                                    is FBoxDeviceInfoV2 -> { // Only logic for BoxC
                                        if (this.response.state == "running") {
                                            it.connect(this)
                                        } else {
                                            pairingScanner.awake(deviceInfo = this, lifecycleScope = lifecycleScope, callback = object :
                                                FScannerAwakeListener {
                                                override fun awakeDeviceCallBack(deviceInfo: DeviceInfo?, isSuccess: Boolean, isRunning: Boolean) {
                                                    if (isRunning) {
                                                        if (deviceInfo is FBoxDeviceInfoV2) {
                                                            it.setSelectDevice(data = FBoxDeviceInfoV2(device = <EMAIL>, response = deviceInfo.response))
                                                            it.getSelectDevice()?.let { selectDevice ->
                                                                it.connect(selectDevice)
                                                            }
                                                        }
                                                    } else {
                                                        it.showToast(message = binding.root.context.getString(R.string.pairing_control_waiting_connection, pairingConnection.getReceiverName()))
                                                        it.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_error))
                                                    }
                                                }
                                            }
                                            )
                                        }
                                    }
                                    else -> {
                                        it.connect(this)
                                    }
                                }

                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                1 -> {}
                2 -> {}
                else -> {}
            }
        }
    }

    /**
     * type: 0 -> Device Bottom Sheet
     * 1 -> Pairing with pin code
     * 2 -> Device Management
     * 3 -> Popup Remote Control
     */
    private fun navigateToPairingControl(type: Int) {
        val navController = (activity as? HomeActivity)?.navHostFragment?.navController
        val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
        navigation?.navigate(NavHomeMainDirections.actionGlobalToPairingControl(type = type))
    }

    private fun addPairingControlListener() {
        pairingConnection.apply {
            addConnectionListener(connectionListener)
        }
    }

    private fun removePairingControlListener() {
        pairingConnection.apply {
            removeConnectionListener(connectionListener)
        }
    }

    private val connectionListener = object : FConnectionManager.FConnectionListener {
        override fun onConnectError(errorCode: Int, message: String) {
            runOnUiThread {
                pairingConnection.showToast(message = if (errorCode == 2) binding.root.context.getString(R.string.pairing_cast_description_connect_error_disable_cast, pairingConnection.getReceiverName().truncateString()) else binding.root.context.getString(R.string.pairing_cast_title_connect_error))
            }
        }
        override fun onConnectSuccess(message: String) {
            runOnUiThread {
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_success, pairingConnection.getReceiverName()))
                updateCastButtonState()
            }

            // Send tracking
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "516",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = TrackingUtil.screen,
                    event = "CastToDevice",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    isRecommend = TrackingUtil.isRecommend
                )
            )
        }
        override fun onDisconnectError(message: String) {}
        override fun onDisconnectSuccess(message: String) {
            runOnUiThread {
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_disconnect_success, pairingConnection.getReceiverName()))
                updateCastButtonState()
            }
        }
    }
    //endregion

}