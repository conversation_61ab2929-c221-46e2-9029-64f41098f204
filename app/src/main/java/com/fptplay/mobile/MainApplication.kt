package com.fptplay.mobile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.ConnectivityManager
import android.net.Network
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.multidex.MultiDexApplication
import coil.ImageLoaderFactory
import coil.annotation.ExperimentalCoilApi
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.LogLevel
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.clevertap.android.pushtemplates.PushTemplateNotificationHandler
import com.clevertap.android.sdk.ActivityLifecycleCallback
import com.clevertap.android.sdk.CleverTapAPI
import com.clevertap.android.sdk.Logger
import com.fplay.module.downloader.DownloadConstants
import com.fplay.module.downloader.VideoDownloadConfig
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.utils.VideoStorageUtils
import com.fptplay.mobile.application.FptPlayLifecycleObserver
import com.fptplay.mobile.common.classes.LatestOrientationState
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogManager
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.common.Config
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import com.fptplay.mobile.features.pairing_control.PairingControlConnectionHelper
import com.fptplay.mobile.features.pairing_control.PairingControlScannerHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.google.android.exoplayer2.database.DatabaseProvider
import com.google.android.exoplayer2.database.StandaloneDatabaseProvider
import com.google.android.exoplayer2.upstream.cache.Cache
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor
import com.google.android.exoplayer2.upstream.cache.SimpleCache
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.util.common.Util
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch

@HiltAndroidApp
class MainApplication : MultiDexApplication(), ImageLoaderFactory {
    

    companion object {
        lateinit var INSTANCE: MainApplication
        lateinit var simpleCache: Cache
    }

    @Inject
    lateinit var fptPlayLifecycleObserver: FptPlayLifecycleObserver
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var alertDialogManager: AlertDialogManager

    @Inject
    lateinit var commonRepository: CommonRepository

    var isReloadHome = false
    var isOpenOnBoardingAndWithProfile = true

    lateinit var appConfig: Config
    val isAppConfigInitialized get() = ::appConfig.isInitialized

    var revision: String = ""

    var isChromeCastConnected = false

    val networkDetector: MutableLiveData<Boolean?> = MutableLiveData()

    lateinit var pairingScannerHelper: PairingControlScannerHelper
    lateinit var pairingConnectionHelper: PairingControlConnectionHelper

    private val _orientationState = MutableStateFlow<LatestOrientationState>(LatestOrientationState.OrientationChanged(null))
    val orientationState: StateFlow<LatestOrientationState> get() = _orientationState
    private var mIsFirstDownload : Boolean = true
    var isFirstDownload
        get() = mIsFirstDownload
        set(value) {
            mIsFirstDownload = value
        }
    init {
        INSTANCE = this
    }

    // region floating button
    private var listFloatingButton: MutableList<String> = ArrayList()

    fun getListFloatingButton(): MutableList<String> {
        return listFloatingButton
    }

    fun setListFloatingButton(stringFloatingButton: String) {
        <EMAIL>(stringFloatingButton)
    }

    // endregion floating button
    // region pladio entry point
    private var listPladioEntryPoint: MutableList<String> = ArrayList()

    fun getListPladioEntryPoint(): MutableList<String> {
        return listPladioEntryPoint
    }

    fun setListPladioEntryPoint(stringPladioEntryPoint: String) {
        <EMAIL>(stringPladioEntryPoint)
    }
    // endregion pladio entry point

    //region VOD Playback speed
    private var savablePlayerSpeed: Float = 1.0f
    fun savePlayerSpeed(speed: Float) {
        savablePlayerSpeed = speed
    }

    fun getPlayerSpeed(): Float {
        return savablePlayerSpeed
    }
    //endregion VOD Playback speed

    var currentActivity: FragmentActivity? = null
        private set

    fun checkLastUpdate(){
        val lastUpdateTime = this.packageManager.getPackageInfo (this.packageName, 0).lastUpdateTime
    }
    @OptIn(ExperimentalCoilApi::class)
    override fun newImageLoader() = ImageProxy.coilConfigs(appContext = applicationContext, filesDir = filesDir, deviceId = sharedPreferences.androidId())

    override fun onCreate() {
        super.onCreate()
        CoroutineScope(Dispatchers.Main).launch {
            Log.e("MainApplication", "MainApplication >> onCreate")
        }
        initAdjust()
        initCache()
        initCleverTap()
        initProcessLifecycleOwner()
        if (BuildConfig.DEBUG) {
            Timber.plant(object : Timber.DebugTree() {
                override fun createStackElementTag(element: StackTraceElement): String {
                    return "Class: ${super.createStackElementTag(element)} Line: ${element.lineNumber} Method: ${element.methodName}"
                }
            })
        } else {
            Timber.plant(ReleaseTree())
        }

        registerActivityLifecycleCallbacks(object: ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                Logger.d("MainApplication onActivityCreated: ${activity.javaClass.simpleName}")
            }

            override fun onActivityStarted(activity: Activity) {
                Logger.d("MainApplication onActivityStarted: ${activity.javaClass.simpleName}")
                if (activity is FragmentActivity) {
                    currentActivity = activity
                }
            }

            override fun onActivityResumed(activity: Activity) {
                Logger.d("MainApplication onActivityResumed: ${activity.javaClass.simpleName}")
                if (activity is FragmentActivity) {
                    currentActivity = activity
                }
            }

            override fun onActivityPaused(activity: Activity) {
                // No-op
            }

            override fun onActivityStopped(activity: Activity) {
                Logger.d("MainApplication onActivityStopped: ${activity.javaClass.simpleName}")
                // No-op
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                // No-op
            }

            override fun onActivityDestroyed(activity: Activity) {
                Logger.d("MainApplication onActivityDestroyed: ${activity.javaClass.simpleName}")
                if (currentActivity == activity) {
                    Logger.d("MainApplication onActivityDestroyed set null: ${activity.javaClass.simpleName}")
                    currentActivity = null
                }
            }
        })

        //Firebase Crashlytics
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true);
        TrackingGA4Proxy.sendTrackingCrashReportUser(sharedPref = sharedPreferences)

        val moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()
        val jsonAdapter: JsonAdapter<Config> = moshi.adapter(Config::class.java)
        val coreConfig = if (sharedPreferences.getAppConfig().isNotBlank()) {
            jsonAdapter.fromJson(sharedPreferences.getAppConfig()) ?: Config()
        } else Config()
        appConfig = coreConfig

        initConfigForDownloader(this)

        // Check internet status
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                val networkAvailable = NetworkUtils.isNetworkAvailable()
                networkDetector.postValue(networkAvailable)
            }

            override fun onLost(network: Network) {
                try {
                    Handler(Looper.getMainLooper()).postDelayed({
                        val networkAvailable = NetworkUtils.isNetworkAvailable()
                        networkDetector.postValue(networkAvailable)
                    }, 200)
                }
                catch (e: Exception) {
                    val networkAvailable = NetworkUtils.isNetworkAvailable()
                    networkDetector.postValue(networkAvailable)
                }
            }
        }
        NetworkUtils.registerNetworkCallback(networkCallback)

        //AppsFlyer
        val conversionDataListener  = object : AppsFlyerConversionListener {
            override fun onConversionDataSuccess(data: MutableMap<String, Any>?) {
                data?.let { cvData ->
                    cvData.map {
                        Timber.i("conversion_attribute:  ${it.key} = ${it.value}")
                    }
                }
            }

            override fun onConversionDataFail(error: String?) {
                Timber.e("error onAttributionFailure :  $error")
            }

            override fun onAppOpenAttribution(data: MutableMap<String, String>?) {
                data?.map {
                    Timber.d( "onAppOpen_attribute: ${it.key} = ${it.value}")
                }
            }

            override fun onAttributionFailure(error: String?) {
                Timber.e("error onAttributionFailure :  $error")
            }
        }
        val key = getString(R.string.flyer_dev)
        AppsFlyerLib.getInstance().init(key, conversionDataListener, this)
        AppsFlyerLib.getInstance().start(this)

        // Pairing Control
        pairingScannerHelper = PairingControlScannerHelper().apply {
            registerScanner(applicationContext = applicationContext)
        }
        pairingConnectionHelper = PairingControlConnectionHelper().apply {
            registerConnection(applicationContext = applicationContext)
        }

        // Alert Dialog Manager
        alertDialogManager.onCreate()

        //MQTT
        initMQTT()
    }

    private fun initMQTT() {
        if (appConfig.mqttEnable) {
            // Register MqttConnectManager as lifecycle observer
            ProcessLifecycleOwner.get().lifecycle.addObserver(MqttConnectManager.INSTANCE)
            MqttConnectManager.INSTANCE.init()
        }
    }

    override fun onTerminate() {
        // Alert Dialog Manager
        alertDialogManager.onDestroy()

        // MQTT cleanup
        if (appConfig.mqttEnable) {
            if(MqttConnectManager.INSTANCE.isConnected()){
                MqttConnectManager.INSTANCE.disconnectMQTT()
            }
        }

        super.onTerminate()
    }

    //endregion

    //region adjust
    private fun initAdjust() {
        val appToken = BuildConfig.ADJUST_APP_TOKEN
        val environment = if(BuildConfig.IS_PRODUCTION) AdjustConfig.ENVIRONMENT_PRODUCTION else AdjustConfig.ENVIRONMENT_SANDBOX
        val config = AdjustConfig(this, appToken, environment)
        if (!BuildConfig.IS_PRODUCTION) {
            config.setLogLevel(LogLevel.VERBOSE)
        }
        config.setOnDeferredDeeplinkResponseListener { deeplink ->
            val broadcastIntent =
                Intent(DeeplinkConstants.ADJUST_TRUE_LINK_BROADCAST_INTENT)
            broadcastIntent.putExtra(
                DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                true
            )
            broadcastIntent.putExtra(
                DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY,
                deeplink.toString()
            )
            LocalBroadcastManager.getInstance(INSTANCE.applicationContext)
                .sendBroadcast(broadcastIntent)

            return@setOnDeferredDeeplinkResponseListener true
        }
        Adjust.initSdk(config)

        //Link clevertap with adjust
        val cleverTapId = CleverTapAPI.getDefaultInstance(applicationContext)?.cleverTapID ?: ""
        Adjust.addGlobalCallbackParameter("clevertap_id", cleverTapId)
    }
    //endregion adjust


    // region Moment Cache
    private fun initCache() {
        val leastRecentlyUsedCacheEvictor = LeastRecentlyUsedCacheEvictor(500 * 1024 * 1024)
        val databaseProvider: DatabaseProvider = StandaloneDatabaseProvider(this)
        simpleCache = SimpleCache(cacheDir, leastRecentlyUsedCacheEvictor, databaseProvider)
    }
    // endregion


    //region Methods
    private fun initCleverTap() {
        // Using
        CoroutineScope(Dispatchers.Main).launch {
            initCleverTapFlow()
                .flowOn(Dispatchers.IO)
                .collect {
                    if (it) {
                        Timber.d("Init Clevertap Successfully")
                    } else {
                        Timber.d("Init Clevertap Failed")

                    }
                }
        }
    }


    private fun initConfigForDownloader(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            val file: File = setStorageUsage(sharedPreferences.storageUsageForDowload())
            val config: VideoDownloadConfig = VideoDownloadManager.Build(context)
                .setCacheRoot(file.absolutePath)
                .setTimeOut(DownloadConstants.READ_TIMEOUT, DownloadConstants.CONN_TIMEOUT)
                .setConcurrentCount(DownloadConstants.CONCURRENT)
                .setIgnoreCertErrors(false)
                .setShouldM3U8Merged(false)
                .setInternalPathRoot(VideoStorageUtils.getInternalFileDir(context).absolutePath)
                .setExternalPathRoot(VideoStorageUtils.getExternalFileDir(context)?.absolutePath ?: "")
                .buildConfig()
            VideoDownloadManager.instance.initConfig(config)
        }
    }

    private suspend fun initCleverTapFlow() = flow {
        try {
            ActivityLifecycleCallback.register(INSTANCE)
            CleverTapAPI.setDebugLevel(if(BuildConfig.DEBUG)CleverTapAPI.LogLevel.DEBUG else CleverTapAPI.LogLevel.VERBOSE) //Set Log level to DEBUG log warnings or other important messages
            CleverTapAPI.setNotificationHandler(PushTemplateNotificationHandler())
            emit(true)
        } catch (ex: Exception) {
            Timber.e(ex)
            emit(false)
        }
    }

    fun setStorageUsage(storage : String?) : File{
        var file: File
        if(storage == Utils.USE_OPTION_EXTERNAL_STORAGE){
            file = VideoStorageUtils.getVideoCacheDir(this, isSDCard = true) ?: run {
                sharedPreferences.setStorageUsageForDowload(Utils.USE_OPTION_INTERNAL_STORAGE)
                VideoStorageUtils.getInternalFileDir(this)
            }
        } else {
            file =  VideoStorageUtils.getInternalFileDir(this)
        }
        return file
    }

    fun initProcessLifecycleOwner() {
        ProcessLifecycleOwner.get().lifecycle.addObserver(fptPlayLifecycleObserver)
    }

    private fun trackingLastedOrientationStatus(newConfig: Configuration){
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
               TrackingGA4Proxy.sendTrackingCrashLastOrientationScreen(orientation = "landscape")
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                TrackingGA4Proxy.sendTrackingCrashLastOrientationScreen(orientation = "portrait")
            }
            else -> {
                TrackingGA4Proxy.sendTrackingCrashLastOrientationScreen(orientation = "")
            }
        }
    }
    override fun onConfigurationChanged(newConfig: Configuration) {
        trackingLastedOrientationStatus(newConfig)
        _orientationState.value = LatestOrientationState.OrientationChanged(newConfig = Configuration(newConfig))
        super.onConfigurationChanged(newConfig)
    }

}