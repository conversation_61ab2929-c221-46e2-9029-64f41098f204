package com.fptplay.mobile.player.thumb

import com.fptplay.mobile.player.thumb.model.ThumbnailObject
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.collections.ArrayList
import kotlin.math.roundToLong

internal object ThumbUtil {
    fun millisecondsToTimeString(milliseconds: Long): String {
        return secondToTimeString(milliseconds.div(1000f).roundToLong())
    }

    fun secondToTimeString(time: Long, forceFull: Boolean = false): String {
        val second = time % 60
        val minute = time / 60 % 60
        val hour = time / 3600 % 24
        return if (hour > 0 || forceFull)
            String.format("%02d:%02d:%02d", hour, minute, second)
        else
            String.format("%02d:%02d", minute, second)
    }

    fun toMillisecond(data: String?): Long {
        var result = 0f
        data?.let {
            val items: ArrayList<String> = arrayListOf()
            items.addAll(it.split(":"))
            val size = items.size
            if (items.size > 0) {
                for (index in 1..size) {
                    when (index) {
                        1 -> {
                            result += items[size - index].toFloat() * 1000
                        }
                        2 -> {
                            result += items[size - index].toFloat() * 60 * 1000
                        }
                        3 -> {
                            result += items[size - index].toFloat() * 3600 * 1000
                        }
                        else -> {

                        }
                    }
                }

            }

        }
        return result.toLong()
    }

    fun getFrame(data: String?): ThumbnailObject.Frame? {
        data?.let {
            val items = it.split(",")
            if (items.size == 4) {
                return ThumbnailObject.Frame(
                    items[0].toInt(),
                    items[1].toInt(),
                    items[2].toInt(),
                    items[3].toInt()
                )
            }
        }
        return null
    }

    internal fun String.getThumbnailTime(): ThumbnailObject.Time? {
        var time: ThumbnailObject.Time? = null
//    val regex = "(\\d{2}:)+(\\d{2})+((:+[\\d]{2})?)+([.].{3})".toRegex()
        val regex = "(\\d{2}:)+(\\d{2})+((:+[\\d]{2})?)".toRegex()
        val result = regex.findAll(this).toList()
        if (result.isNotEmpty()) {
            time = ThumbnailObject.Time(
                toMillisecond(result[0].value),
                toMillisecond(result[1].value)
            )
        }
        return time
    }

    internal fun String.getThumbnailImageUrl(): String? {
        val line3 = split("\n").last()
        val regexIndex = line3.indexOf("#")
        if (regexIndex < 0) return null
        val result = line3.substring(0, regexIndex)
        return result
    }

    internal fun String.getThumbnailFrame(): ThumbnailObject.Frame? {
        val regex = "[\\d]+,[\\d]+,[\\d]+,[\\d]+".toRegex()
        val result = regex.find(this)
        return ThumbUtil.getFrame(result?.value)
    }

    internal fun String.getFilePath(fileName: String): String {
        return this + File.separator + fileName
    }


    internal fun Long.convertToHMS(): String {
        return String.format(
            "%02d:%02d:%02d",
            TimeUnit.MILLISECONDS.toHours(this),
            TimeUnit.MILLISECONDS.toMinutes(this) -
                    TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(this)), // The change is in this line
            TimeUnit.MILLISECONDS.toSeconds(this) -
                    TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(this))
        )
    }

    internal fun Long.toDateTimeString(format: String): String {
        val date = Date(this)
        return SimpleDateFormat(format, Locale.US).format(date)
    }
}