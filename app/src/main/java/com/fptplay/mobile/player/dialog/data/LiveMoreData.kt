package com.fptplay.mobile.player.dialog.data

import com.fptplay.mobile.R
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class LiveMoreData (
    override var id: String = "",
    var title: String,
    var resId: Int?,
    var isVipRequired: Boolean = false,
    var vipImage: String = "",
    var isSelected: Boolean = false,
) : BaseObject() {
    companion object {
        fun getOnlyShareData() : List<LiveMoreData> {
            val result = mutableListOf<LiveMoreData>()
            result.add(LiveMoreData(id = Type.Share.name, title = "Chia sẻ", resId = R.drawable.ic_player_share))
            return result
        }

        fun getDefaultData(hasFollow: Boolean) : List<LiveMoreData> {
            val result = mutableListOf<LiveMoreData>()
            result.add(LiveMoreData(id = Type.Share.name, title = "Chia sẻ", resId = R.drawable.ic_player_share))
            result.add(if (hasFollow) LiveMoreData(id = Type.UnFollow .name, title = "Bỏ theo dõi kênh", resId = R.drawable.ic_unfollow) else LiveMoreData(id = Type.Follow.name, title = "Theo dõi kênh", resId = R.drawable.ic_follow))
            return result
        }



    }

    public enum class Type {
        Share, Follow, UnFollow
    }
}