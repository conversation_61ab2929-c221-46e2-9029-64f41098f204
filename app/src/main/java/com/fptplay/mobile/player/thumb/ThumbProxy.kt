package com.fptplay.mobile.player.thumb

import android.content.Context
import com.tear.modules.player.util.PlayerControlView
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.*
import okhttp3.*
import okio.buffer
import okio.sink
import okio.source
import okio.use
import java.io.File
import java.io.InputStream
import java.util.concurrent.TimeUnit
import kotlin.system.measureTimeMillis

class ThumbProxy(
    val context: Context,
    var coroutineScope: CoroutineScope ?= null,
    var inforUrl: String = "",
    var baseUrl: String = ""
) {

    //region Variables
    var eventsListener: EventsListener ?= null
    private val nameSheetImagesDir: String by lazy { "/sheetImages/" }
    private var executeJob: Job ?= null
    private val results: ArrayList<PlayerControlView.Data.Thumbnail> by lazy { arrayListOf() }
    private val sheetNames: HashSet<String> by lazy { hashSetOf() }
    private val okHttpClient: OkHttpClient by lazy { OkHttpClient().newBuilder().callTimeout(15, TimeUnit.SECONDS).build() }
    //endregion

    //region Constructors
    init {
        eventsListener = object: EventsListener{
            override fun onError(reason: String) {
                Logger.d("ThumbProxy -> onError -> reason: $reason")
            }
        }
        Logger.d("CoroutineScope: $coroutineScope $this")
    }
    //endregion

    //region Util
    fun bind(inforUrl: String, baseUrl: String, eventsListener: EventsListener, coroutineScope: CoroutineScope ?= null){
        reset()
        this.coroutineScope = coroutineScope
        this.inforUrl = inforUrl
        this.baseUrl = baseUrl
        this.eventsListener = eventsListener
        executeJob = coroutineScope?.launch(Dispatchers.IO) {
            clearSheetImagesDir()
            loadInfor()
        }
    }
    //endregion

    //region Process -> Reset
    private fun reset(){
        executeJob?.cancel()
        results.clear()
        sheetNames.clear()
    }
    //endregion

    //region Load file from infor url
    private suspend fun loadInfor(){
        runCatching {
            try {
                val request = Request.Builder().url(inforUrl).build()
                val response = okHttpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    response.body.use {
                        readInfor(data = response.body?.byteStream())
                    }
                } else {
                    eventsListener?.onError(reason = "TP_LI_#2")
                }
            } catch (e: Exception) {
                eventsListener?.onError(reason = e.message ?: "TP_LI_#1")
            }
        }
    }
    //endregion

    //region Read data from infor file from server
    private suspend fun readInfor(data: InputStream?){
        if (data == null) {
            eventsListener?.onError(reason = "TP_RI_#3")
            return
        }else{
            try {
                var thumbnail: PlayerControlView.Data.Thumbnail?= null
                val source = data.source().buffer()
                source.use {
                    while (true){
                        val line = source.readUtf8Line()
                        if (line == null){
                            // Add last items
                            thumbnail?.let { results.add(it) }
                            break
                        }
                        if (line.isEmpty()){
                            thumbnail?.let { results.add(it) }
                            thumbnail = PlayerControlView.Data.Thumbnail()
                        }else{
                            thumbnail.apply {
                                addId(data = line)
                                addTime(data = line)
                                addFrame(data = line)
                                addSheetUrl(data = line)
                                this?.let {
                                    if (it.sheetName.isNotEmpty()) {
                                        it.sheetPath = context.cacheDir.absolutePath + nameSheetImagesDir + sheetName
                                        sheetNames.add(it.sheetName)
                                    }
                                }
                            }
                        }
                    }
                }
                downloadSheetImages()
            }catch (ex: Exception){
                eventsListener?.onError(reason = "TP_RI_#4")
            }
        }
    }
    //endregion

    //region Thumbnail -> Add Id
    private fun PlayerControlView.Data.Thumbnail?.addId(data: String){
        if (this == null) return
        if (data.contains("img", ignoreCase = true)){
            this.id = data
        }
    }
    //endregion

    //region Thumbnail -> Add Time
    private fun PlayerControlView.Data.Thumbnail?.addTime(data: String){
        fun toTime(time: String) : Long{
            val splitTime = time.split(":")
            var result: Long = 0
            result += TimeUnit.HOURS.toMillis(splitTime[0].toLong())
            result += TimeUnit.MINUTES.toMillis(splitTime[1].toLong())
            result += TimeUnit.SECONDS.toMillis(splitTime[2].toLong())
            return result
        }
        if (this == null) return
        try {
            val regex = "(\\d{2}:)+(\\d{2})+((:+[\\d]{2})?)".toRegex()
            val results = regex.findAll(data).toList()
            if (!results.isNullOrEmpty()){
                this.time = PlayerControlView.Data.Thumbnail.Time(
                    start = toTime(time = results[0].value),
                    end = toTime(time = results[1].value)
                )
            }
        }catch (ex: Exception){
            eventsListener?.onError(reason = "TP_AT_#5")
        }
    }
    //endregion

    //region Thumbnail -> Add Frame
    private fun PlayerControlView.Data.Thumbnail?.addFrame(data: String){
        if (this == null) return
        try {
            val regex = "[\\d]+,[\\d]+,[\\d]+,[\\d]+".toRegex()
            val result = regex.find(data)
            if (!result?.value.isNullOrEmpty()){
                val splitResult = result?.value?.split(",")
                splitResult?.let {
                    if (it.size >= 4){
                        this.frame = PlayerControlView.Data.Thumbnail.Frame(
                            x = it[0].toInt(),
                            y = it[1].toInt(),
                            width = it[2].toInt(),
                            height = it[3].toInt()
                        )
                    }
                }
            }
        }catch (ex: Exception){
            eventsListener?.onError(reason = "TP_AF_#6")
        }
    }
    //endregion

    //region Thumbnail -> Add Sheet Url
    private fun PlayerControlView.Data.Thumbnail?.addSheetUrl(data: String){
        if (this == null) return
        try{
            val regexIndex = data.indexOf("#")
            if (regexIndex != -1){
                val result = data.substring(0, regexIndex)
                if (result.isNotEmpty()){
                    this.sheetName = result
                }
            }
        }catch (ex: Exception){
            eventsListener?.onError(reason = "TP_ASU_#7")

        }
    }
    //endregion

    //region Sheet Images -> Download, write, clear...
    private suspend fun downloadSheetImages(){
        if (sheetNames.isEmpty() || coroutineScope == null) return
        val measureTime = measureTimeMillis {
            val deferreds: ArrayList<Deferred<Any?>?> = arrayListOf()
            createSheetImagesDir()
            sheetNames.forEach { sheetName ->
                deferreds.add(
                    coroutineScope?.async(Dispatchers.IO) {
                        try {
                            runCatching {
                                val request = Request.Builder().url(baseUrl + sheetName).build()
                                val response = okHttpClient.newCall(request).execute()
                                if (response.isSuccessful){
                                    response.body.use {
                                        writeSheetImage(
                                            responseBody = response.body,
                                            dirPath = context.cacheDir.absolutePath + nameSheetImagesDir,
                                            fileName = sheetName
                                        )
                                    }
                                }else{
                                    eventsListener?.onError(reason = "TP_DSI_#8")
                                }
                            }
                        } catch (e: Exception) {
                            eventsListener?.onError(reason = e.message ?: "TP_DSI_#9")
                        }
                    }
                )
            }
            deferreds.forEach { it?.await() }
            withContext(Dispatchers.Main) {
                Logger.d("Thumbs: ${results.size} $results")
                if (results.isNotEmpty()) eventsListener?.onSuccess(data = results)
            }
        }
        Logger.d("Measure time of GetStructureItem: ${measureTime/1000.0}s ->  ${Thread.currentThread().name}")
    }

    private fun createSheetImagesDir(){
        try {
            val sheetImagesDir = File(context.cacheDir, nameSheetImagesDir)
            if (!sheetImagesDir.exists()){
                if (!sheetImagesDir.mkdir()){
                    eventsListener?.onError(reason = "TP_CSID_#10")
                }else{
                    sheetImagesDir.mkdirs()
                }
            }
        } catch (e: Exception) {
            eventsListener?.onError(reason = "TP_CSID_#13")
        }
    }

    private fun clearSheetImagesDir(){
        try {
            val sheetImagesDir = File(context.cacheDir, nameSheetImagesDir)
            if (sheetImagesDir.exists()){
                val files = sheetImagesDir.listFiles()
                if (!files.isNullOrEmpty()){
                    files.forEach {
                        if (it.isFile) it.delete()
                    }
                }
            }
        } catch (e: Exception) {
            eventsListener?.onError(reason = "TP_CSID_#12")
        }
    }

    private fun writeSheetImage(responseBody: ResponseBody?, dirPath: String, fileName: String){
        if (responseBody == null) return
        try {
            val imageFile = File(dirPath, fileName)
            val sink = imageFile.sink().buffer()
            sink.use {
                it.writeAll(responseBody.source())
            }
        }catch (ex: Exception){
            eventsListener?.onError(reason = "TP_WSI_#11")
        }
    }
    //endregion

    //region Events
    interface EventsListener {
        fun onError(reason: String) {}
        fun onSuccess(data: List<PlayerControlView.Data.Thumbnail>) {}
    }
    //endregion
}