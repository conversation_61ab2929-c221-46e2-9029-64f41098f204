package com.fptplay.mobile.viewmore.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.util.Util
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.utils.Utils
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.util.image.ImageProxy

class ViewMoreOs4BaseAdapter(private val type: String, private val blockType: String) :
    BaseAdapter<StructureItem, ViewMoreOs4BaseAdapter.ViewMoreViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): ViewMoreOs4BaseAdapter.ViewMoreViewHolder {
        return when (type) {
            BlockStyle.VerticalSliderMedium.id,
            BlockStyle.VerticalSliderSmall.id,
            BlockStyle.NumericRank.id,
            -> {
                ViewMoreViewHolder(
                    viewBinding = ViewMoreBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.view_more_vertical_item, parent, false)
                    )
                )
            }
            else -> {
                ViewMoreViewHolder(
                    viewBinding = ViewMoreBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.view_more_horizontal_item, parent, false)
                    )
                )
            }
        }
    }

    override fun onBindViewHolder(holder: ViewMoreOs4BaseAdapter.ViewMoreViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    inner class ViewMoreViewHolder(private val viewBinding: ViewMoreBinding) :
        RecyclerView.ViewHolder(viewBinding.root) {

        val width: Int
        val height: Int
        private val ribbonHeight by lazy { viewBinding.root.context.resources.getDimensionPixelSize(R.dimen.block_ribbon_height) }

        init {

            if (Utils.useVerticalImage(type)) {
                width =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_vertical_width, 0.5f)
                height =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_vertical_height, 0.5f)
            } else {
                width =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_horizontal_width, 0.5f)
                height =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_horizontal_height, 0.5f)
            }

            viewBinding.root.setOnClickListener {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        private fun showRibbon(ribbonView1: ImageView?, ribbonView2: ImageView?, listRibbon: List<String>) {
            if (listRibbon.isNotEmpty()) {
                ribbonView1?.isVisible = true
                ImageProxy.load(
                    context = viewBinding.root.context,
                    url = listRibbon[0],
                    width = 0,
                    height = ribbonHeight,
                    target = ribbonView1
                )

                if (listRibbon.size > 1) {
                    ribbonView2?.isVisible = true
                    ImageProxy.load(
                        context = viewBinding.root.context,
                        url = listRibbon[1],
                        width = 0,
                        height = ribbonHeight,
                        target = ribbonView2
                    )
                } else {
                    ribbonView2?.isVisible = false
                }
            } else {
                ribbonView1?.isVisible = false
            }
        }


        fun bind(data: StructureItem) {
            val link: String = if (Utils.useVerticalImage(type)) {
                data.verticalImage ?: ""
            } else {
                data.horizontalImage ?: ""
            }

            ImageProxy.load(
                context = itemView.context,
                url = link,
                width = width,
                height = height,
                target = viewBinding.ivThumb,
                placeHolderId = R.drawable.image_placeholder,
                errorDrawableId = R.drawable.image_placeholder
            )

            //region show ribbon
            showRibbon(viewBinding.ivTL1, viewBinding.ivTL2, data.ribbon.tlRibbons)
            //endregion

            if (blockType == "watching") {
                viewBinding.pbTimeWatched?.isVisible = true
                if (data.timeWatched.isNotBlank() && data.detail.durationI.isNotBlank()) {
                    try {
                        val timeWatched = data.timeWatched.toDouble()
                        val timeDuration = data.detail.durationI.toDouble()
                        viewBinding.pbTimeWatched?.progress =
                            ((timeWatched / timeDuration) * 100).toInt()
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                        viewBinding.pbTimeWatched?.progress = 0
                    }
                } else {
                    viewBinding.pbTimeWatched?.progress = 0
                }
            }
        }
    }
}