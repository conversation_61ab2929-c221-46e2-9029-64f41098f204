package com.fptplay.mobile.viewmore.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.utils.Utils
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home.StructureItem
import com.xhbadxx.projects.module.util.image.ImageProxy

class ViewMoreBaseAdapter(private val type: String) :
    BaseAdapter<StructureItem, ViewMoreBaseAdapter.ViewMoreViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): ViewMoreBaseAdapter.ViewMoreViewHolder {
        return when (type) {
            BlockStyle.VerticalSliderMedium.id,
            BlockStyle.VerticalSliderSmall.id,
            BlockStyle.NumericRank.id,
            -> {
                ViewMoreViewHolder(
                    viewBinding = ViewMoreBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.view_more_vertical_item, parent, false)
                    )
                )
            }
            else -> {
                ViewMoreViewHolder(
                    viewBinding = ViewMoreBinding(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.view_more_horizontal_item, parent, false)
                    )
                )
            }
        }
    }

    override fun onBindViewHolder(holder: ViewMoreBaseAdapter.ViewMoreViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }

    inner class ViewMoreViewHolder(private val viewBinding: ViewMoreBinding) :
        RecyclerView.ViewHolder(viewBinding.root) {

        val width: Int
        val height: Int
        private val ribbonHeight by lazy { Utils.getSizeInPixel(context = viewBinding.root.context, resId = R.dimen.block_ribbon_height) }

        init {

            if (Utils.useVerticalImage(type)) {
                width =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_vertical_width, 0.5f)
                height =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_vertical_height, 0.5f)
            } else {
                width =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_horizontal_width, 0.5f)
                height =
                    Utils.getSizeInPixel(context = itemView.context, resId = R.dimen.view_more_horizontal_height, 0.5f)
            }

            viewBinding.root.setOnClickListener {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        private fun showRibbon(ribbonView1: ImageView?, ribbonView2: ImageView?, listRibbon: List<String>) {
            if (listRibbon.isNotEmpty()) {
                ribbonView1?.isVisible = true
                ImageProxy.load(
                    context = viewBinding.root.context,
                    url = listRibbon[0],
                    width = 0,
                    height = ribbonHeight,
                    target = ribbonView1
                )

                if (listRibbon.size > 1) {
                    ribbonView2?.isVisible = true
                    ImageProxy.load(
                        context = viewBinding.root.context,
                        url = listRibbon[1],
                        width = 0,
                        height = ribbonHeight,
                        target = ribbonView2
                    )
                } else {
                    ribbonView2?.isVisible = false
                }
            } else {
                ribbonView1?.isVisible = false
            }
        }


        fun bind(data: StructureItem) {
            val link: String = if (Utils.useVerticalImage(type)) {
                data.verticalImage ?: ""
            } else {
                data.horizontalImage ?: ""
            }

            ImageProxy.load(
                context = itemView.context,
                url = link,
                width = width,
                height = height,
                target = viewBinding.ivThumb,
                placeHolderId = R.drawable.image_placeholder,
                errorDrawableId = R.drawable.image_placeholder
            )

            val tlRibbons = arrayListOf<String>().apply {
                if(data.ribbonPayment.isNotBlank()) add(data.ribbonPayment)
                if (MainApplication.INSTANCE.appConfig.isShowAge == 1) {
                    if(data.ribbonAge.isNotBlank()) add(data.ribbonAge)
                } else {
                    if(data.isNew) add(MainApplication.INSTANCE.appConfig.iconNew)
                }
            }

            //region show ribbon
            showRibbon(viewBinding.ivTL1, viewBinding.ivTL2, tlRibbons)
            //endregion
        }
    }
}