package com.fptplay.mobile.viewmore

import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.home.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemContainer
import com.xhbadxx.projects.module.domain.repository.fplay.HomeOs4Repository
import com.xhbadxx.projects.module.domain.repository.fplay.HomeRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collect
import javax.inject.Inject

@HiltViewModel
class ViewMoreBaseViewModel @Inject constructor(
    private val homeRepository: HomeRepository,
    private val homeOs4Repository: HomeOs4Repository,
) : BaseViewModel<ViewMoreBaseViewModel.ViewMoreBaseIntent, ViewMoreBaseViewModel.ViewMoreBaseState>() {

    sealed class ViewMoreBaseState : ViewState {
        data class Loading(val data: ViewMoreBaseIntent? = null) : ViewMoreBaseState()
        data class Error(val message: String, val data: ViewMoreBaseIntent? = null) : ViewMoreBaseState()
        data class Done(val data: ViewMoreBaseIntent? = null) : ViewMoreBaseState()
        data class ResultStructureItemsOs4(
            val isCached: Boolean,
            val data: StructureItemContainer,
            val isBind: Boolean
        ) : ViewMoreBaseState()
    }

    sealed class ViewMoreBaseIntent : ViewIntent {

        data class GetStructureItemsOs4(
            val structureId: String,
            val type: String,
            val blockType: String,
            val userId: String,
            val page: Int,
            val perPage: Int,
            val watchingVersion: String?,
            val customData: String,
            val pageId: String? = null
        ) : ViewMoreBaseIntent()
    }

    override fun dispatchIntent(intent: ViewMoreBaseIntent) {
        safeLaunch {
            when (intent) {
                is ViewMoreBaseIntent.GetStructureItemsOs4 -> {
                    homeOs4Repository.getStructureItemWithMeta(
                        type = intent.type,
                        blockId = intent.structureId,
                        blockType = intent.blockType,
                        pageIndex = intent.page,
                        pageSize = intent.perPage,
                        watchingVersion = intent.watchingVersion,
                        customData = intent.customData,
                        pageId = intent.pageId
                    ).collect {
                        _state.value = it.reduce { isCached, data ->
                            ViewMoreBaseState.ResultStructureItemsOs4(isCached = isCached, data = data, isBind = intent.page == 1)
                        }
                    }
                }
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: ViewMoreBaseIntent?,
        successFun: (Boolean, T) -> ViewMoreBaseState
    ): ViewMoreBaseState {
        return when (this) {
            is Result.Init -> ViewMoreBaseState.Loading(intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.Error -> ViewMoreBaseState.Error(message = this.message, data = intent)
            Result.Done -> ViewMoreBaseState.Done(data = intent)
        }
    }
}