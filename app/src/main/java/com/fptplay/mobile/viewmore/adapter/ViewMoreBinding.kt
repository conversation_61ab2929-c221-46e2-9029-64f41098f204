package com.fptplay.mobile.viewmore.adapter

import android.view.View
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.viewbinding.ViewBinding
import com.fptplay.mobile.R

class ViewMoreBinding(private val root: View) : ViewBinding {
    val ivThumb: ImageView = root.findViewById(R.id.iv_thumb)
    val ivTL1: ImageView? by lazy { root.findViewById(R.id.iv_tl_1) }
    val ivTL2: ImageView? by lazy { root.findViewById(R.id.iv_tl_2) }
    val ivBL1: ImageView? by lazy { root.findViewById(R.id.iv_bl_1) }
    val ivBL2: ImageView? by lazy { root.findViewById(R.id.iv_bl_2) }
    val ivBR1: ImageView? by lazy { root.findViewById(R.id.iv_br_1) }
    val pbTimeWatched: ProgressBar? by lazy { root.findViewById(R.id.pb_time_watched) }

    override fun getRoot() = root
}