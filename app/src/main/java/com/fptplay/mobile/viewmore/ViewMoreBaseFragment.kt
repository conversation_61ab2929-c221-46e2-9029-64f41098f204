package com.fptplay.mobile.viewmore

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.block.BlockItemAdapter
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.viewutils.GridItemDecoration
import com.fptplay.mobile.databinding.ViewmoreBaseFragmentBinding
import com.fptplay.mobile.features.package_validation.PackageValidationViewModel
import com.fptplay.mobile.features.pladio.playback.OpenPanel
import com.fptplay.mobile.features.pladio.search.adapter.PladioSearchItemAdapter
import com.fptplay.mobile.features.pladio.util.PladioConstants
import com.fptplay.mobile.viewmore.adapter.ViewMoreBaseAdapter
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemContainer
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.LoadMoreHandler
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
open class ViewMoreBaseFragment :
    BaseFullDialogFragment<ViewMoreBaseViewModel.ViewMoreBaseState, ViewMoreBaseViewModel.ViewMoreBaseIntent>() {
    override val hasEdgeToEdge = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override val viewModel: ViewMoreBaseViewModel by activityViewModels()

    private var _binding: ViewmoreBaseFragmentBinding? = null
    private val binding get() = _binding!!

    private val safeArgs: ViewMoreBaseFragmentArgs by navArgs()
    open val blockStyle: String by lazy { safeArgs.blockStyle }
    private val totalItemInPage by lazy { MainApplication.INSTANCE.appConfig.numItemOfPage }
    private val itemInRow: Int by lazy { 2 }
    private var mOs4Adapter: BlockItemAdapter? = null
    private var itemDecoration: RecyclerView.ItemDecoration ?= null
    private var viewMoreItemStyle: ViewMoreItemStyle = ViewMoreItemStyle.Horizontal
    private var lastOrientation = Configuration.ORIENTATION_UNDEFINED

    private val loadMoreOs4Handler: LoadMoreHandler by lazy {
        LoadMoreHandler(
            totalItem = mOs4Adapter?.size() ?: 0,
            totalItemInPage = totalItemInPage,
            totalItemInRow = itemInRow,
            onScroll = { page ->
                viewModel.dispatchIntent(
                    ViewMoreBaseViewModel.ViewMoreBaseIntent.GetStructureItemsOs4(
                        structureId = safeArgs.id,
                        userId = sharedPreferences.userId(),
                        page = page,
                        perPage = totalItemInPage,
                        blockType = blockStyle,
                        type = safeArgs.blockType,
                        watchingVersion = Constants.WATCHING_VERSION,
                        customData = safeArgs.customData,
                        pageId = safeArgs.pageId
                    )
                )
            })
    }

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        logViewMoreBlock()
        lifecycle.addObserver(checkBeforePlayUtil)

    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = ViewmoreBaseFragmentBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkBeforePlayUtil)
        mOs4Adapter?.run {
            lifecycle.removeObserver(this)
        }

        if(safeArgs.deeplink) {
            GlobalEvent.pushEvent(DeeplinkConstants.DEEPLINK__NAVIGATE__STOP, "")
        }
    }

    override fun bindData() {
        GlobalEvent.pushEvent(GlobalEvent.REMOVE_COUNT_DOWN_TIME_EVENT, true) // Stop player in home if needed
        checkBeforePlayUtil.setScreenProvider(safeArgs.screenProvider)
        if(safeArgs.deeplink) {
            GlobalEvent.pushEvent(DeeplinkConstants.DEEPLINK__NAVIGATE__START, "")
        }
        getItems()
    }

    override fun bindComponent() {
        binding.tvHeader.text = safeArgs.header
        binding.tvSubHeader.text = safeArgs.subHeader
        binding.tvSubHeader.isVisible = safeArgs.subHeader.isNotEmpty()
    }

    override fun bindEvent() {
        parentFragment?.parentFragment?.setFragmentResultListener(Constants.CHECK_REQUIRE_VIP) { key, bundle ->
            Timber.d("***OnFragmentResult")
            checkBeforePlayUtil.onFragmentResult(key, bundle)
        }

        binding.rcvContent.apply {
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    mOs4Adapter?.let {
                        if ((recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition() == it.itemCount - 1) {
                            Timber.d("Load morere")
                            loadMoreOs4Handler.canScroll(it.size() - 1)
                        }
                    }

                }
            })
        }

        binding.ivClose.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun bindAdapterOs4(blockStyle: String): BlockItemAdapter {
        return when(blockStyle) {
            BlockStyle.VerticalSliderMedium.id,
            BlockStyle.VerticalSliderSmall.id,
            BlockStyle.NumericRank.id,
            -> {
                BlockItemAdapter(requireContext(), BlockItemAdapter.Type.ViewMoreVertical).apply {
                    setContentType(bindContentType())
                    bindAdapterClickListener(this)
                }
            }
            BlockStyle.VerticalSliderVideoWithBackground.id,
            BlockStyle.VerticalSliderVideo.id -> {
                BlockItemAdapter(requireContext(), BlockItemAdapter.Type.ViewMoreVerticalWithTitle).apply {
                    setContentType(bindContentType())
                    bindAdapterClickListener(this)
                }
            }
            BlockStyle.GameHorizontalSquare.id -> {
                BlockItemAdapter(requireContext(), BlockItemAdapter.Type.ViewMoreGameHorizontalSquare).apply {
                    setContentType(bindContentType())
                    bindAdapterClickListener(this)
                }
            }

            else -> {
                BlockItemAdapter(requireContext(), BlockItemAdapter.Type.ViewMoreHorizontal).apply {
                    setContentType(bindContentType())
                    bindAdapterClickListener(this)
                }
            }
        }
    }

    private fun bindAdapterClickListener(adapter: BlockItemAdapter) {
        adapter.eventListener = object : IEventListener<BaseObject> {
            override fun onClickedItem(position: Int, data: BaseObject) {
                dismiss()
                if(safeArgs.sourceShow == PladioConstants.PLADIO_SCREEN) {
                    if(data is com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem) {
                        LocalBroadcastManager.getInstance(MainApplication.INSTANCE).sendBroadcast(
                            Intent(
                                PladioConstants.PLADIO_MAIN_OPEN_DETAIL).apply {
                                putExtra(PladioConstants.PLADIO_MAIN_OPEN_DETAIL, bundleOf(
                                    PladioConstants.PLADIO_MAIN_OPEN_DETAIL_ITYPE to data.itype.id,
                                    PladioConstants.PLADIO_MAIN_OPEN_DETAIL_CONTENT_TYPE to data.contentType.id,
                                    PladioConstants.PLADIO_MAIN_OPEN_DETAIL_ID to data.id,
                                    PladioConstants.PLADIO_MAIN_OPEN_DETAIL_TITLE to data.title
                                ))
                            })
                    } else {
                        checkBeforePlayUtil.navigateToSelectedContent(data)
                    }
                } else {
                    checkBeforePlayUtil.navigateToSelectedContent(data)
                }

            }
        }
    }

    private fun bindContentType(): BlockItemAdapter.ContentType {
        return if (safeArgs.sourceShow == PladioConstants.PLADIO_SCREEN) {
            return BlockItemAdapter.ContentType.Main
        } else {
            when (safeArgs.blockType) {
                "watching" -> BlockItemAdapter.ContentType.History
                "follow" -> BlockItemAdapter.ContentType.Follow
                else -> BlockItemAdapter.ContentType.Main
            }
        }
    }

    open fun getItems() {

            Timber.d("***Os4 call get data ${safeArgs.blockType} - ${safeArgs.blockStyle}")
            viewModel.dispatchIntent(
                ViewMoreBaseViewModel.ViewMoreBaseIntent.GetStructureItemsOs4(
                    structureId = safeArgs.id,
                    userId = sharedPreferences.userId(),
                    page = 1,
                    perPage = totalItemInPage,
                    blockType = blockStyle,
                    type = safeArgs.blockType,
                    watchingVersion = Constants.WATCHING_VERSION,
                    customData = safeArgs.customData,
                    pageId = safeArgs.pageId
                )
            )
    }

    override fun ViewMoreBaseViewModel.ViewMoreBaseState.toUI() {
        when (this) {
            is ViewMoreBaseViewModel.ViewMoreBaseState.ResultStructureItemsOs4 -> {
                Timber.d("***Os4 ${this.data.listStructureItem.size}")
                if(this.data.meta.name.isNotBlank()) {
                    binding.tvHeader.text = this.data.meta.name
                }
                if (this.data.listStructureItem.isNotEmpty()) {
                    Timber.d("Load morere refresh with ${data.listStructureItem[0].id} adapter size: ${mOs4Adapter?.size()} isBind: $isBind")

                    viewMoreItemStyle = getViewMoreItemStyle(data.meta)
                    binding.rcvContent.apply {
                        val spanCount = getSpanCount(viewMoreItemStyle)
                        if(isBind){
                            updateRecycleViewContent(spanCount,isBind = true)
                        }
                    }
                    if(mOs4Adapter == null) {
                        binding.rcvContent.apply {
                            val spanCount = getSpanCount(viewMoreItemStyle)
                            layoutManager = GridLayoutManager(context, spanCount, RecyclerView.VERTICAL, false)
                            itemDecoration?.let { removeItemDecoration(it) }
                            itemDecoration = GridItemDecoration(
                                spanCount = spanCount,
                                spacing = resources.getDimensionPixelSize(R.dimen.view_more_item_horizontal_margin),
                                includeEdge = false,
                                topSpacing = resources.getDimensionPixelSize(R.dimen.view_more_item_first_top_margin)
                            )
                            itemDecoration?.let { addItemDecoration(it) }
                        }
                        mOs4Adapter = bindAdapterOs4(data.meta.blockStyle).apply {
                            lifecycle.addObserver(this)
                        }
                        binding.rcvContent.adapter = mOs4Adapter
                    }
                    mOs4Adapter?.add(data = this.data.listStructureItem, isBind = this.isBind) {
                        loadMoreOs4Handler.refresh(
                            totalItem = mOs4Adapter?.size() ?: 0,
                            endPage = this.data.listStructureItem.size < totalItemInPage
                        )
                    }
                } else {
                    loadMoreOs4Handler.refresh(
                        totalItem = mOs4Adapter?.size() ?: 0,
                        endPage = true
                    )
                }
            }

            is ViewMoreBaseViewModel.ViewMoreBaseState.Error -> {
                    loadMoreOs4Handler.refresh(totalItem = mOs4Adapter?.size() ?: 0, endPage = false)

            }
            else -> {

            }
        }
    }

    private fun getViewMoreItemStyle(meta: StructureItemContainer.Meta): ViewMoreItemStyle {
        return when(blockStyle) {
            BlockStyle.VerticalSliderMedium.id,
            BlockStyle.VerticalSliderSmall.id,
            BlockStyle.NumericRank.id,
            BlockStyle.VerticalSliderVideoWithBackground.id,
            BlockStyle.VerticalSliderVideo.id -> {
                ViewMoreItemStyle.Vertical
            }
            BlockStyle.GameHorizontalSquare.id -> {
                ViewMoreItemStyle.Square
            }
            else -> {
                ViewMoreItemStyle.Horizontal
            }
        }
    }

    private fun getSpanCount(viewMoreItemStyle: ViewMoreItemStyle): Int {
        val orientation = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation
        return when (viewMoreItemStyle) {
            ViewMoreItemStyle.Vertical -> {
                if (context.isTablet()) {
                    if (orientation == Configuration.ORIENTATION_PORTRAIT) 4 else 6
                } else 2
            }
            ViewMoreItemStyle.Horizontal -> {
                if (context.isTablet()) {
                    if (orientation == Configuration.ORIENTATION_PORTRAIT) 3 else 4
                } else 2
            }
            ViewMoreItemStyle.Square -> {
                if (context.isTablet()) {
                    if (orientation == Configuration.ORIENTATION_PORTRAIT) 5 else 7
                } else 3
            }
        }
    }

    private fun logViewMoreBlock(){
        TrackingUtil.blockId = safeArgs.header
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo, logId = "57",
                appId = if(safeArgs.sourceShow == PladioConstants.PLADIO_SCREEN) TrackingConstants.EVENT_PLADIO_APP_ID else TrackingUtil.currentAppId,
                appName = if(safeArgs.sourceShow == PladioConstants.PLADIO_SCREEN) TrackingConstants.EVENT_PLADIO_APP_NAME else  TrackingUtil.currentAppName,
                itemId = safeArgs.id,
                itemName = TrackingUtil.blockId,
                screen = "MainMenu",
                event = "EnterMainMenu",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime()
            )
        )
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        configurationChanged(newConfig = MainApplication.INSTANCE.applicationContext.resources.configuration)
    }

    private fun configurationChanged(newConfig: Configuration?){
        if (lastOrientation == newConfig?.orientation) return
        lastOrientation = newConfig?.orientation ?: Configuration.ORIENTATION_UNDEFINED
        val spanCount = getSpanCount(viewMoreItemStyle)
        updateRecycleViewContent(spanCount)
    }

    private fun updateRecycleViewContent(spanCount : Int,isBind:Boolean = false){
        if(isBind){
            binding.rcvContent.apply {
                layoutManager = GridLayoutManager(context, spanCount, RecyclerView.VERTICAL, false)
                itemDecoration?.let { removeItemDecoration(it) }
                itemDecoration = GridItemDecoration(
                    spanCount = spanCount,
                    spacing = resources.getDimensionPixelSize(R.dimen.view_more_item_horizontal_margin),
                    includeEdge = false,
                    topSpacing = 0
                )
                itemDecoration?.let { addItemDecoration(it) }
            }
        }
        else{
            binding.rcvContent.apply {
                try {
                    (layoutManager as? GridLayoutManager)?.spanCount = getSpanCount(viewMoreItemStyle)
                    itemDecoration?.let { removeItemDecoration(it) }
                    itemDecoration = GridItemDecoration(
                        spanCount = spanCount,
                        spacing = resources.getDimensionPixelSize(R.dimen.view_more_item_horizontal_margin),
                        includeEdge = false,
                        topSpacing = 0
                    )
                    itemDecoration?.let { addItemDecoration(it) }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
        }
    }

    sealed interface ViewMoreItemStyle {
        object Horizontal : ViewMoreItemStyle
        object Vertical : ViewMoreItemStyle
        object Square : ViewMoreItemStyle
    }
}