package com.fptplay.mobile.common.utils

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.xhbadxx.projects.module.util.logger.Logger
import java.nio.charset.Charset
import java.security.MessageDigest

class StartCropTransformation(private val screenWidth: Int, private val screenHeight: Int) : BitmapTransformation() {
    private val tag = "StartCropTransformation"

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update("StartCropTransformation".toByteArray(Charset.defaultCharset()))
    }

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap {
        if (toTransform == null) return Bitmap.createBitmap(outWidth, outHeight, Bitmap.Config.ARGB_8888)

        Logger.d("$tag: screenWidth = $screenWidth, screenHeight = $screenHeight")
        Logger.d("$tag: toTransform.width = ${toTransform.width}, toTransform.height = ${toTransform.height}, outWidth = $outWidth, outHeight = $outHeight")

        // Calculate scale to maintain the aspect ratio
        val scale = maxOf(
            screenWidth.toFloat() / toTransform.width,
            screenHeight.toFloat() / toTransform.height
        )

        Logger.d("$tag: scale = $scale")

        // Determine translation to crop from the start (left in LTR layouts)
        val dx = 0f // Start cropping from the start (left side)
        val dy = 0f // No vertical offset; crop from the top

        val matrix = Matrix().apply {
            setScale(scale, scale)
            postTranslate(dx, dy)
        }

        val croppedBitmap = pool.get(outWidth, outHeight, Bitmap.Config.ARGB_8888)
            ?: Bitmap.createBitmap(outWidth, outHeight, Bitmap.Config.ARGB_8888)

        Canvas(croppedBitmap).apply {
            drawBitmap(toTransform, matrix, Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG))
        }

        return croppedBitmap
    }
}
