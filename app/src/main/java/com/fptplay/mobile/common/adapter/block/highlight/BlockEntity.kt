package com.fptplay.mobile.common.adapter.block.highlight

import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class BlockEntity(
    val blockId: String = "",
    val blockStyle: String = "",
    val iType: String = "",
    val header: String? = "",
    val subHeader: String? = "",
    val background: String? = "",
    val items: List<BaseObject>? = null,
    //region view more
    val hasViewMore: Boolean = false,
    val viewMoreText: String = "",
    val viewMoreIcon: String = "",
    val viewMoreType: String = "",
    val viewMoreId: String, // Id of page or structure to get data when click on view more
    val customData: String,
    //endregion
    //region pladio
    val isEnableWatchingProgress: Boolean = true
    //endregion
) : BaseObject()