package com.fptplay.mobile.common.ui.view

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import androidx.fragment.app.FragmentActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.view.SnackbarManager.SnackbarItem
import com.fptplay.mobile.common.utils.NetworkUtils
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber
object GlobalSnackbarManager {
    private const val TAG = "GlobalSnackbarManager"
    private val currentActivityRef get() = MainApplication.INSTANCE.currentActivity
    @SuppressLint("StaticFieldLeak")
    private var currentSnackbarManager: SnackbarManager? = null
    private val handler = Handler(Looper.getMainLooper())
    var isFirstOpenApp = true

    fun cleanup() {
        currentSnackbarManager?.hideSnackbar()
        currentSnackbarManager = null
        isFirstOpenApp = true
    }

    fun build(activity: FragmentActivity) {
        cleanup()
        currentSnackbarManager = SnackbarManager(activity)
        currentSnackbarManager
    }

    private fun getCurrentSnackbarManager(): SnackbarManager? {
        val activity = currentActivityRef
        return if (activity != null && !activity.isFinishing && !activity.isDestroyed) {
            if (currentSnackbarManager != null) {
                currentSnackbarManager
            } else {
                currentSnackbarManager = SnackbarManager(activity)
                currentSnackbarManager
            }
        } else {
            currentSnackbarManager = null
            null
        }
    }

    fun with(): SnackbarManager.Builder? {
        val activity = currentActivityRef
        return if (activity != null && !activity.isFinishing && !activity.isDestroyed) {
            SnackbarManager.Builder(activity)
        } else {
            Timber.w(TAG, "Cannot create builder: No valid activity context")
            null
        }
    }

    fun withCurrent(): SnackbarItem?{
       return getCurrentSnackbarManager()?.getCurrentItem()
    }

    fun hideSnackbar() {
        handler.post { getCurrentSnackbarManager()?.hideSnackbar() }
    }

    fun clearQueue() {
        getCurrentSnackbarManager()?.clearQueue()
    }

    fun getQueueSize(): Int {
        return getCurrentSnackbarManager()?.getQueueSize() ?: 0
    }

    fun isCurrentlyShowing(): Boolean {
        return getCurrentSnackbarManager()?.isCurrentlyShowing() ?: false
    }

    private fun getCurrentActivity(): FragmentActivity? {
        return currentActivityRef
    }


    fun showMessage(message: String, duration: Long = SnackbarManager.DEFAULT_DURATION_MS) {
        handler.post {
            with()
                ?.text(message)
                ?.style(getCurrentActivity()?.let { SnackbarManager.Styles.default(it) } ?: SnackbarManager.SnackbarStyle())
                ?.duration(duration)
                ?.animation(SnackbarManager.AnimationType.SLIDE_UP)
                ?.priority(SnackbarManager.Priority.HIGH)
                ?.onlyTimeShow(true)
                ?.show()
        }
    }

    fun showMessageWithDrawable(
        message: String,
        duration: Long = SnackbarManager.DEFAULT_DURATION_MS,
        drawableId: Int? = null
    ) {
        handler.post {
                with()
                ?.text(message)
                ?.style(getCurrentActivity()?.let { SnackbarManager.Styles.default(it) } ?: SnackbarManager.SnackbarStyle())
                ?.iconLocal(drawableId ?: R.drawable.ic_error_notification)
                ?.duration(duration)
                ?.priority(SnackbarManager.Priority.HIGH)
                ?.animation(SnackbarManager.AnimationType.SLIDE_UP)
                ?.onlyTimeShow(true)
                ?.show()
            }
        }

    fun showMessageWithItem(item: SnackbarManager.SnackbarItem) {
        handler.post {
            with()?.showWithItem(item) ?: Timber.w(TAG, "Cannot show snackbar: No valid activity context")
        }
    }

    fun showMessageWithParam(
        text: String,
        iconUrl: String? = "",
        iconLocal: Int? = 0,
        duration: Long = SnackbarManager.DEFAULT_DURATION_MS,
        placeHolderId: Int = R.drawable.ic_error_notification,
        errorId: Int = R.drawable.ic_error_notification,
        actionClickListener: (() -> Unit)? = null,
        isFullWidth: Boolean = false,
        action: String = "",
        isOnlyTimeShow: Boolean = false,
        paddingVertical: Int = -1,
        position: SnackbarManager.Position = SnackbarManager.Position.BOTTOM_CENTER
    ) {
        handler.post {
            getCurrentSnackbarManager()?.showSnackbar(
                text = text,
                iconUrl = iconUrl,
                iconLocal = iconLocal,
                duration = duration,
                placeHolderId = placeHolderId,
                errorId = errorId,
                actionClickListener = actionClickListener,
                isFullWidth = isFullWidth,
                action = action,
                position = position,
                isOnlyTimeShow = isOnlyTimeShow,
                paddingVertical = paddingVertical,
            ) ?: Timber.w(TAG, "Cannot show snackbar: No valid activity context")
        }
    }
    fun showMessageNetworkDetector(isHasNetwork: Boolean) {
        if (!isFirstOpenApp) {
            val item = withCurrent()?.apply {
                text = getErrorMessageNetwork(isHasNetwork, MainApplication.INSTANCE.sharedPreferences)
                isOnlyTimeShow = true
                isOnly = false
                priority = SnackbarManager.Priority.CRITICAL
                style = getCurrentActivity()?.let { SnackbarManager.Styles.default(it) } ?: SnackbarManager.SnackbarStyle()
                iconLocal = getIconNetwork(isHasNetwork)
            }
            if (item!=null){
                handler.post {
                    getCurrentSnackbarManager()?.showSnackbar(item)
                }
            }
        }
        isFirstOpenApp = false
    }
    // Get text default
    private val defaultTxtConnectWifi get()= currentActivityRef?.getString(R.string.connected_internet_text) ?: ""
    private val defaultTxtNoInternet get()= currentActivityRef?.getString(R.string.error_no_internet_text) ?: ""
    private val defaultTxtConnectMobile get()= currentActivityRef?.getString(R.string.connected_internet_text) ?: ""

    private fun getErrorMessageNetwork(
        isHasNetwork:Boolean,
        sharedPreferences: SharedPreferences,
    ): String {
        val isWifi = NetworkUtils.isConnectedWifi()
        val isCellular = NetworkUtils.isConnectedCellular()
        return when {
            isHasNetwork && isWifi  ->  sharedPreferences.msgHaveInternet()?.takeIf { it.isNotEmpty() } ?: defaultTxtConnectWifi
            isHasNetwork && isCellular  -> sharedPreferences.getMsgNotiUseMobileNetwork().takeIf { it.isNotBlank() } ?: defaultTxtConnectMobile
            else -> sharedPreferences.msgNoInternet()?.takeIf { it.isNotEmpty() } ?: defaultTxtNoInternet
        }
    }

    private fun getIconNetwork(isHasNetwork:Boolean):Int {
        val isWifi = NetworkUtils.isConnectedWifi()
        val isCellular = NetworkUtils.isConnectedCellular()
        return when {
            isHasNetwork && isWifi  ->  R.drawable.ic_wifi
            isHasNetwork && isCellular  ->  R.drawable.ic_mobile_connected
            else -> R.drawable.ic_wifi_off
        }
    }
}
