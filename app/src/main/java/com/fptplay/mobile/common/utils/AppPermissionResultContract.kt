package com.fptplay.mobile.common.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion.ACTION_REQUEST_PERMISSIONS
import androidx.core.app.ActivityOptionsCompat
import com.fptplay.mobile.common.utils.AppPermissionResultContract.Companion.PERMISSION_RESULT_EXTRA_DATA
import com.fptplay.mobile.common.utils.AppPermissionResultContract.Companion.REQUEST_CODE
import com.fptplay.mobile.features.mini_app.model.MiniAppDownloadRequest

class AppPermissionResultContract: ActivityResultContract<Intent, AppPermissionResultData>() {

    companion object {
        const val REQUEST_CODE = "com.fptplay.mobile.common.utils.activity_result.permissions.REQUEST_CODE"
        const val REQUEST_CODE_DEFAULT = Int.MAX_VALUE
        const val PERMISSION_RESULT_EXTRA_DATA = "permission-result-extra-data"
    }

    private var requestCode = REQUEST_CODE_DEFAULT
    private var extraBundle: Bundle? = null

    override fun createIntent(context: Context, input: Intent): Intent {
        requestCode = input.getIntExtra(REQUEST_CODE, requestCode)
        extraBundle = input.getBundleExtra(PERMISSION_RESULT_EXTRA_DATA)
        return input
    }

    override fun parseResult(resultCode: Int, intent: Intent?): AppPermissionResultData {
        val grantResults = mapResult(resultCode, intent)
        return AppPermissionResultData(
            requestCode = requestCode,
            resultCode = resultCode,
            grantResults = grantResults,
            extraBundle = extraBundle
        )
    }

    private fun mapResult(resultCode: Int, intent: Intent?): Map<String, Boolean> {
        if(resultCode != Activity.RESULT_OK) return emptyMap()
        if(intent == null) return emptyMap()
        val permissions = intent.getStringArrayExtra(ActivityResultContracts.RequestMultiplePermissions.EXTRA_PERMISSIONS)
        val grantResults = intent.getIntArrayExtra(ActivityResultContracts.RequestMultiplePermissions.EXTRA_PERMISSION_GRANT_RESULTS)
        if (grantResults == null || permissions == null) return emptyMap()
        val grantState = grantResults.map { result ->
            result == PackageManager.PERMISSION_GRANTED
        }
        return permissions.filterNotNull().zip(grantState).toMap()
    }
}

data class AppPermissionResultData(val requestCode: Int, val resultCode: Int, val grantResults: Map<String, Boolean>, val extraBundle: Bundle? = null)

fun ActivityResultLauncher<Intent>.launch(requestCode: Int, permissions: Array<String>, extraBundle: Bundle? = null, launchOptions: ActivityOptionsCompat? = null) {
    val intent = Intent(ACTION_REQUEST_PERMISSIONS).apply {
        putExtra(REQUEST_CODE, requestCode)
        putExtra(PERMISSION_RESULT_EXTRA_DATA, extraBundle)
        putExtra(ActivityResultContracts.RequestMultiplePermissions.EXTRA_PERMISSIONS, permissions)
    }
    launch(intent, launchOptions)
}