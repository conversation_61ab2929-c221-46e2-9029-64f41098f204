package com.fptplay.mobile.common.ui.view

import android.content.DialogInterface
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.KeyEventHelper
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.RequiredBuyPackageDialogBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.pairing_control.PairingControlVolumeKeyHandler
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Constant
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RequireBuyPackageInAppDialog : DialogFragment(), KeyEventHelper.OnKeyHandler {
    private var _binding: RequiredBuyPackageDialogBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: RequireBuyPackageInAppDialogArgs by navArgs()

    //
    private var isNavigateToPayment = false
    //
    //region Override
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.FullScreenDialog)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = RequiredBuyPackageDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindComponent()
        bindEvent()
    }
    override fun onStart() {
        super.onStart()
        //
        addKeyHandler()

        dialog?.run {
            setOnKeyListener { _, keyCode, event ->
                if (event.action == KeyEvent.ACTION_DOWN && (event.keyCode == KeyEvent.KEYCODE_VOLUME_UP || event.keyCode == KeyEvent.KEYCODE_VOLUME_DOWN)) {
                    activity?.onKeyDown(keyCode, event) ?: false
                } else {
                    false
                }
            }
        }
        //
    }

    override fun onStop() {
        //
        removeKeyHandler()
        //
        super.onStop()
    }

    override fun onDismiss(dialog: DialogInterface) {
        if (!isNavigateToPayment) {
            if (safeArgs.requestFromCast) {
                GlobalEvent.pushEvent(GlobalEvent.PAYMENT_EVENT, false)
            }
        }
        super.onDismiss(dialog)
    }
    //endregion

    private fun bindComponent() {
        if (safeArgs.isDirect) {
            isNavigateToPayment = true
            navigateToPackageInAppPurchase()
        } else {
            if (safeArgs.title.isNotEmpty()) binding.tvTitle.text = safeArgs.title
            if (safeArgs.message.isNotEmpty()) binding.tvMessage.text = safeArgs.message
            if (safeArgs.positiveText.isNotEmpty()) binding.btnPositive.text = safeArgs.positiveText
            if (safeArgs.negativeText.isNotEmpty()) binding.btnNegative.text = safeArgs.negativeText
        }
    }

    private fun bindEvent() {
        binding.apply {
            btnPositive.setOnClickListener {
                isNavigateToPayment = true
                TrackingGA4Proxy.saveTrackingTypeClickAndContentName(TrackingGA4Proxy.TypeCallDetail.popup, safeArgs.idToPlay?: "")
                navigateToPackageInAppPurchase()
            }
            btnNegative.setOnClickListener {
                AdjustAllEvent.sendPaymentUserCancelEvent()
                TrackingGA4Proxy.sendTrackingPaymentUserCancel(safeArgs.packageId, "")
                findNavController().navigateUp()
                setFragmentResult(
                    Constants.WARNING_REQUEST_KEY,
                    bundleOf(Constants.WARNING_REQUEST_KEY to safeArgs.requestKey, Constants.WARNING_RESULT to false)
                )
            }
        }
    }

    private fun navigateToPackageInAppPurchase() {
        findNavController().navigate(RequireBuyPackageInAppDialogDirections.actionRequireBuyPackageInAppDialogToPackageInAppPurchaseFragment(
            title = safeArgs.title,
            message = safeArgs.message,
            negativeText = safeArgs.negativeText,
            positiveText = safeArgs.positiveText,
            requestKey = safeArgs.requestKey,
            packageId = safeArgs.packageId,
            continueWatch = safeArgs.continueWatch,
            navigationId = safeArgs.navigationId,
            idToPlay = safeArgs.idToPlay,
            timeShift = safeArgs.timeShift,
            timeShiftLimit = safeArgs.timeShiftLimit,
            popUpToInclusive = safeArgs.popUpToInclusive,
            popupToId = safeArgs.popupToId
        ))
    }

    //region Pairing Control
    private fun addKeyHandler() {
        KeyEventHelper.getInstance().addKeyHandler(this)
    }

    private fun removeKeyHandler() {
        KeyEventHelper.getInstance().removeKeyHandler(this)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (event?.keyCode) {
            KeyEvent.KEYCODE_VOLUME_DOWN,
            KeyEvent.KEYCODE_VOLUME_UP -> {
                if (isCasting()) {
                    PairingControlVolumeKeyHandler.handleOnKeyDown(keyCode = keyCode, event = event)
                    return true
                }
            }
        }
        return false
    }

    private fun isCasting() = PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast

    //endregion
}