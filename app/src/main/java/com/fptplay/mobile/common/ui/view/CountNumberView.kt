package com.fptplay.mobile.common.ui.view

import android.animation.Animator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.fptplay.mobile.R
import java.util.Locale

class CountNumberView : FrameLayout {
    private var currentTextView: TextView? = null
    private var nextTextView: TextView? = null
    private var mDelay = DELAY
    private var mStartDelay = 0L
    private var mAnimationDuration = ANIMATION_DURATION
    private var mStartValue: Int? = null
    private var mEndValue: Int? = null
    private var currentValue: Int? = null
    private var mDirection = Direction.INCREASE
    var isLooper = false
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context) : super(context) {
        init(context)
    }

    private fun init(context: Context) {
        LayoutInflater.from(context).inflate(R.layout.count_number_view, this)
        currentTextView = rootView.findViewById<View>(R.id.currentTextView) as TextView
        nextTextView = rootView.findViewById<View>(R.id.nextTextView) as TextView
        nextTextView!!.translationY = height.toFloat()
    }

    fun setDelay(delay: Long) {
        mDelay = delay
    }

    fun setStartDelay(delay: Long) {
        mStartDelay = delay
    }

    fun setAnimationDuration(duration: Long) {
        mAnimationDuration = duration
    }

    fun setValue(startValue: Int, endValue: Int) {
        mStartValue = startValue
        mEndValue = endValue
    }

    fun setDirection(direction: Direction) {
        mDirection = direction
    }

    fun getCurrentValue(): Int? {
        return currentValue
    }
    fun startCounting() {
        checkShowTextView()
        mStartValue?.let { start ->
            mEndValue?.let { end ->
                var nextValue: Int = start
                var delay: Long = mStartDelay
                currentValue?.let {
                    delay = mDelay
                    nextValue = if(currentValue == end) {
                        start
                    } else if (start > end) {
                        it - 1
                    } else if (start < end) {
                        it + 1
                    } else return
                    currentTextView!!.text = String.format(Locale.getDefault(), "%d", it)
                }
                nextTextView!!.text = String.format(Locale.getDefault(), "%d", nextValue)
                startAnimation(nextValue, delay)
            }
        }
    }

    fun countOneTime(tag: String, startValue: Int, endValue: Int) {
        checkShowTextView()
        if (startValue > -1 && currentValue != endValue) {
            currentTextView!!.text = String.format(Locale.getDefault(), "%d", startValue)
            nextTextView!!.text = String.format(Locale.getDefault(), "%d", endValue)
            startAnimation(endValue,0)
        }
    }

    fun setInitialNumber(number: Int) {
        nextTextView!!.visibility = View.VISIBLE
        nextTextView!!.text = String.format(Locale.getDefault(), "%d", number)
        nextTextView!!.translationY = 0f
        currentValue = number
    }

    private fun checkShowTextView() {
        if (currentTextView!!.visibility != View.VISIBLE) {
            currentTextView!!.visibility = View.VISIBLE
        }
        if (nextTextView!!.visibility != View.VISIBLE) {
            nextTextView!!.visibility = View.VISIBLE
        }
    }

    private fun startAnimation(nextValue: Int, delay: Long) {
        currentTextView!!.postOnAnimation {
            currentTextView!!.translationY = 0f
        }
        nextTextView!!.postOnAnimation {
            if (mDirection == Direction.DECREASE) {
                nextTextView!!.translationY = nextTextView!!.height.toFloat()
                decreaseAnimation(nextValue, delay)
            } else {
                nextTextView!!.translationY = -nextTextView!!.height.toFloat()
                increaseAnimation(nextValue, delay)
            }
        }
    }
    private fun decreaseAnimation(nextValue: Int, delay: Long) {
        currentTextView!!.postOnAnimation {
            currentTextView!!.animate().translationY(-height.toFloat()).setDuration(
                mAnimationDuration
            ).setStartDelay(delay).start()
        }
        nextTextView!!.postOnAnimation {
            nextTextView!!.animate().translationY(0f).setDuration(mAnimationDuration)
                .setListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {
                        currentValue = nextValue
                        if (nextValue != mEndValue || isLooper) {
                            startCounting()
                        }
                    }

                    override fun onAnimationEnd(animation: Animator) {}
                    override fun onAnimationCancel(animation: Animator) {}
                    override fun onAnimationRepeat(animation: Animator) {}
                }).setStartDelay(delay).start()
        }
    }
    private fun increaseAnimation(nextValue: Int, delay: Long) {
        currentTextView!!.postOnAnimation {
            currentTextView!!.animate().translationY(height.toFloat()).setDuration(
                mAnimationDuration
            ).setStartDelay(delay).start()
        }
        nextTextView!!.postOnAnimation {
            nextTextView!!.animate().translationY(0f).setDuration(mAnimationDuration)
                .setListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {
                        currentValue = nextValue
                        if (nextValue != mEndValue || isLooper) {
                            startCounting()
                        }
                    }

                    override fun onAnimationEnd(animation: Animator) {}
                    override fun onAnimationCancel(animation: Animator) {}
                    override fun onAnimationRepeat(animation: Animator) {}
                }).setStartDelay(delay).start()
        }
    }
    private fun stopAnimation() {
        stopViewAnimation(currentTextView!!)
        stopViewAnimation(nextTextView!!)
    }
    private fun stopViewAnimation(v: View) {
        v.animate().setListener(null).cancel()
        v.clearAnimation()
    }
    fun resetAnimation() {
        stopAnimation()
        mDelay = DELAY
        mStartDelay = 0L
        mAnimationDuration = ANIMATION_DURATION
        mStartValue = null
        mEndValue = null
        currentValue = null
        mDirection = Direction.INCREASE
        currentTextView!!.text = "0"
        currentTextView!!.visibility = View.INVISIBLE
        nextTextView!!.text = "0"
        nextTextView!!.visibility = View.INVISIBLE
    }
    fun restartAnimation() {
        stopAnimation()
        currentValue = null
        startCounting()
    }
    companion object {
        private const val ANIMATION_DURATION = 500L
        private const val DELAY = 1000L
    }
    enum class Direction {
        INCREASE, DECREASE
    }

}