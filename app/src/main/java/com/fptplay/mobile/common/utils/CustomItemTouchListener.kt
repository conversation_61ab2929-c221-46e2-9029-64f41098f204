package com.fptplay.mobile.common.utils

import android.graphics.PointF
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView

class CustomItemTouchListener : RecyclerView.OnItemTouchListener{

    var prevX: Float = -1f
    var prevY: Float = -1f
    //Angle to scroll horizontal recyclerView (Left/Right)
    val angleSwipe = 60

    override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
    }

    override fun onInterceptTouchEvent(rv: RecyclerView, event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                prevX = event.x
                prevY = event.y
            }
            MotionEvent.ACTION_MOVE -> {
                // Not apply for un-scrollable list left and right
                if (!rv.canScrollHorizontally(-1) && !rv.canScrollHorizontally(1)) return false

                if (prevX != -1f && prevY != -1f) {
                    val deltaX = prevX - event.x
                    val deltaY = prevY - event.y
                    val angle = getAngle(PointF(prevX, prevY), PointF(event.x, event.y))
                    val disallowIntercept = when {
                        deltaX > 0 && deltaY > 0 -> angle < angleSwipe
                        deltaX < 0 && deltaY > 0 -> angle - 90 > 90 - angleSwipe
                        deltaX < 0 && deltaY < 0 -> angle - 180 < angleSwipe
                        deltaX > 0 && deltaY < 0 -> angle - 270 > 90 - angleSwipe
                        else -> false
                    }
                    rv.parent.requestDisallowInterceptTouchEvent(disallowIntercept)
                    //Log.d("DEBUG", " X-Offset: $deltaX | Y-Offset: $deltaY | Angle: $angle")
                }
            }
            else -> {
                prevX = -1f
                prevY = -1f
            }
        }
        return false
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
    }

    fun getAngle(src: PointF, des: PointF): Float {
        var angle =
            Math.toDegrees(Math.atan2((src.y - des.y).toDouble(), (src.x - des.x).toDouble())).toFloat()

        if (angle < 0) {
            angle += 360f
        }

        return angle
    }

}