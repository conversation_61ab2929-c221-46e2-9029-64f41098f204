package com.fptplay.mobile.common.ui.view

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.view.*
import androidx.annotation.DrawableRes
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.DialogFragment
import com.fptplay.mobile.R
import com.fptplay.mobile.databinding.WarningDialogFragmentBinding
import com.xhbadxx.projects.module.util.common.Util.checkToShowContent
import com.xhbadxx.projects.module.util.common.Util.show
import java.util.*

class WarningDialogFragment(
    private val message: String,
    private val expandMessage: String? = null,
    private val negativeButtonTitle: String = "",
    private val positiveButtonTitle: String = "",
    var negativeClickListener: View.OnClickListener? = null,
    var positiveClickListener: View.OnClickListener? = null,
    var autoDismiss: Boolean = true,
) : DialogFragment() {
    private var _binding: WarningDialogFragmentBinding? = null
    private val binding get() = _binding!!

    private var msgExpanded = false
    // region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = WarningDialogFragmentBinding.inflate(inflater)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindComponent()
        bindEvent()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            setCanceledOnTouchOutside(false)
            setCancelable(<EMAIL>)

            setOnKeyListener { dialogInterface: DialogInterface?, keyCode: Int, keyEvent: KeyEvent ->
                if (keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP && !<EMAIL>) return@setOnKeyListener true
                false
            }

            window?.setBackgroundDrawable(
                ResourcesCompat.getDrawable(resources, R.drawable.warning_dialog_background, null)
            )

        }
    }

    // endregion Overrides

    // region Commons
    private fun bindComponent() {
        binding.apply {
            tvMessage.checkToShowContent(message, goneViewWhenNoText = false)
            if(negativeButtonTitle.isNotEmpty()) {
                btnNegative.text = negativeButtonTitle
                btnNegative.show()
            }
            if(positiveButtonTitle.isNotEmpty()) {
                btnPositive.text = positiveButtonTitle
                btnPositive.show()
            }

        }
    }

    private fun bindEvent() {
        binding.btnNegative.setOnClickListener {
            if(autoDismiss) dismissAllowingStateLoss()
            negativeClickListener?.onClick(it)
        }

        binding.btnPositive.setOnClickListener {
            if(autoDismiss) dismissAllowingStateLoss()
            positiveClickListener?.onClick(it)
        }

        binding.tvMessage.setOnClickListener {
            if(!msgExpanded && !expandMessage.isNullOrBlank()) {
                binding.tvMessage.text = String.format(Locale.getDefault(), "%s \n %s", message, expandMessage)
                msgExpanded = true
            }
        }
    }
    // endregion Commons
}