<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />
    <uses-feature android:name="android.hardware.camera.any"/>
    <!-- For android 9 above-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.SEND" />
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>

    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.FPTPlay"
        android:windowSoftInputMode="adjustPan|adjustResize"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:replace="android:allowBackup,android:name">

        <meta-data
            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
            android:value="com.fptplay.dial.chromecast.CastOptionsProvider" />

        <activity
            android:name=".WelcomeActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:configChanges="orientation|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:supportsPictureInPicture="true"
            android:theme="@style/Theme.FPTPlay.SplashScreen">

            <!--fptplay://fptplay.vn-->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="fptplay" />
            </intent-filter>

            <!--https://fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="fptplay.vn" />
            </intent-filter>
            <!--https://www.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="www.fptplay.vn" />
            </intent-filter>
            <!--https://staging.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="staging.fptplay.vn" />
            </intent-filter>
            <!--https://www.staging.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="www.staging.fptplay.vn" />
            </intent-filter>
            <!--https://dev.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="dev.fptplay.vn" />
            </intent-filter>
            <!--https://www.dev.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="www.dev.fptplay.vn" />
            </intent-filter>
            <!--http://fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="fptplay.vn" />
            </intent-filter>
            <!--http://www.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="www.fptplay.vn" />
            </intent-filter>
            <!--http://staging.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="staging.fptplay.vn" />
            </intent-filter>
            <!--http://www.staging.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="www.staging.fptplay.vn" />
            </intent-filter>
            <!--http://dev.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="dev.fptplay.vn" />
            </intent-filter>
            <!--http://www.dev.fptplay.vn-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:host="www.dev.fptplay.vn" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:host="fptplay.go.link"/>
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:host="fptplay-staging.go.link"/>
            </intent-filter>

            <nav-graph android:value="@navigation/nav_welcome" />
        </activity>
        <activity
            android:name=".HomeActivity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:supportsPictureInPicture="true">
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>
        <activity-alias
            android:exported="true"
            android:name=".DefaultLauncherAlias"
            android:enabled="true"
            android:icon="@mipmap/ic_launcher"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:label="@string/app_name"
            android:targetActivity=".WelcomeActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity-alias
            android:exported="true"
            android:name=".EventLunarNewYear2025Alias"
            android:enabled="false"
            android:label="@string/app_name"
            android:icon="@mipmap/ic_launcher_new_year_2025"
            android:roundIcon="@mipmap/ic_launcher_new_year_2025_round"
            android:targetActivity=".WelcomeActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <!--region Facebook Sharing-->
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />

        <meta-data android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token"/>
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider{com.fplay.activity}"
            android:exported="true" />
        <!--endregion-->

        <!--region Airline-->
        <activity
            android:name=".features.mega.apps.airline.AirlineActivity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:supportsPictureInPicture="true"/>
        <!--endregion-->

        <!--region Zendesk-->
        <activity android:name="zendesk.support.guide.HelpCenterActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <activity android:name="zendesk.support.guide.ViewArticleActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <activity android:name="zendesk.support.request.RequestActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <activity android:name="zendesk.support.requestlist.RequestListActivity"
            android:theme="@style/ZendeskCustomTheme" />
        <!--endregion-->


        <!--region Notifications -->
        <service android:name=".services.NotificationIntentService" />

        <service android:name=".services.FireBaseMessagingIntentService"/>

        <!--region FCM-->
        <service
            android:name=".services.FireBaseMessagingManagerService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.clevertap.pushtemplates.PushTemplateMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <service
            android:name="com.clevertap.pushtemplates.PTNotificationIntentService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.clevertap.PT_PUSH_EVENT"/>
            </intent-filter>
        </service>
        <receiver
            android:name="com.clevertap.pushtemplates.PTPushNotificationReceiver"
            android:exported="false"
            android:enabled="true">
        </receiver>

        <receiver
            android:name="com.clevertap.pushtemplates.PushTemplateReceiver"
            android:exported="false"
            android:enabled="true">
        </receiver>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification_transparent" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />
        <!--endregion FCM-->

        <!--region AppsFlyer-->
        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>
        <!--endregion AppsFlyer-->

        <!--region Clever tap-->

        <meta-data
            android:name="CLEVERTAP_ACCOUNT_ID"
            android:value="${clevertap_pj_id}"/>
        <meta-data
            android:name="CLEVERTAP_TOKEN"
            android:value="${clevertap_pj_token}"/>
        <!-- IMPORTANT: To force use Google AD ID to uniquely identify  users, use the following meta tag. GDPR mandates that if you are using this tag, there is prominent disclousure to your end customer in their application. Read more about GDPR here - https://clevertap.com/blog/in-preparation-of-gdpr-compliance/ -->
        <meta-data
            android:name="CLEVERTAP_USE_GOOGLE_AD_ID"
            android:value="1"/>

        <meta-data
            android:name="CLEVERTAP_BACKGROUND_SYNC"
            android:value="1"/>

        <!--use CTBackgroundJobService to target users on and above Android 21 (Lollipop)-->
        <service
            android:name="com.clevertap.android.sdk.pushnotification.amp.CTBackgroundJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="false"
            />

        <meta-data
            android:name="CLEVERTAP_NOTIFICATION_ICON"
            android:value="ic_notification_transparent"/>
        <!-- name of your file in the drawable directory without the file extension. -->

        <receiver
            android:name="com.clevertap.android.sdk.pushnotification.CTPushNotificationReceiver"
            android:exported="false"
            android:enabled="true">
        </receiver>

        <service
            android:name="com.clevertap.android.sdk.pushnotification.CTNotificationIntentService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.clevertap.PUSH_EVENT"/>
            </intent-filter>
        </service>

        <meta-data
            android:name="CLEVERTAP_REGION"
            android:value="sg1"/>

        <meta-data
            android:name="FCM_SENDER_ID"
            android:value="id:${clevertap_sender_id}"/>

        <meta-data
            android:name="CLEVERTAP_INAPP_EXCLUDE"
            android:value="com.fptplay.mobile.WelcomeActivity, com.fptplay.mobile.DefaultLauncherAlias, com.fptplay.mobile.EventLunarNewYear2025Alias" />
        <!--endregion Clever tap-->

        <service
            android:name=".services.player.BackgroundPlayerService"
            android:foregroundServiceType="mediaPlayback"
            android:exported="false" />

        <activity
            android:name="net.openid.appauth.RedirectUriReceiverActivity"
            android:exported="true"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="com.fptplay.mobile"
                    android:path="/signin-callback"/>
            </intent-filter>
        </activity>
        <receiver android:name="com.fptplay.mobile.features.login.MySMSBroadcastReceiver" android:exported="true"
            android:permission="com.google.android.gms.auth.api.phone.permission.SEND">
            <intent-filter>
                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED"/>
            </intent-filter>
        </receiver>


        <!-- region Pladio -->
        <service
            android:name=".features.pladio.service.PlaybackService"
            android:exported="true"
            android:foregroundServiceType="mediaPlayback"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="android.media.browse.MediaBrowserService" />
            </intent-filter>
        </service>
        <receiver
            android:name=".features.pladio.service.MediaButtonIntentReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <!-- endregion Pladio -->

    </application>
</manifest>