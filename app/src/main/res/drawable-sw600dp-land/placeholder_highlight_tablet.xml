<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:width="@dimen/_466sdp"
        android:height="@dimen/_200sdp">
        <shape
            android:padding="@dimen/_100sdp"
            android:shape="rectangle">
            <solid android:color="@color/place_holder_color"/>
            <corners
                android:bottomLeftRadius="@dimen/_2sdp"
                android:bottomRightRadius="@dimen/_2sdp"
                android:topLeftRadius="@dimen/_2sdp"
                android:topRightRadius="@dimen/_2sdp" />
        </shape>
    </item>
    <item
        android:width="@dimen/_89sdp"
        android:height="@dimen/_20sdp"
        android:drawable="@drawable/all_image_placeholder"
        android:gravity="center">
    </item>
</layer-list>