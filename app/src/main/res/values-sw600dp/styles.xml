<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="PrimaryTextAppearance">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="BottomNavigationTab">
        <item name="android:textSize">@dimen/_5ssp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="ListTVTab" parent="@android:style/TextAppearance.Widget.TabWidget">
        <item name="android:titleMarginStart" tools:targetApi="n">@dimen/_5sdp</item>
        <item name="android:titleMarginEnd" tools:targetApi="n">@dimen/_5sdp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Text View -->
    <style name="WarningDialogText">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="WarningDialogText.TitleMessage" parent="WarningDialogText">
    </style>

    <!-- Progress Bar -->
    <style name="ProgressBarCircle" parent="android:Widget.ProgressBar.Large">
        <item name="android:indeterminateDrawable">@drawable/all_progress_bar_circle</item>
        <item name="android:indeterminate">true</item>
    </style>

    <!--  region Warning Dialog  -->
    <style name="WarningDialogText.TextButton">
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:textColor">@color/all_color_warning_dialog_button_text</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:paddingTop">10dp</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>

    </style>
    <!--  endregion Warning Dialog  -->


    <!--region GameChoiHayChia-->
    <style name="GameChoiHayChia" parent="PrimaryTextAppearance">
        <item name="fontFamily">@font/i_ciel_soup_of_justice</item>
        <item name="android:fontFamily">@font/i_ciel_soup_of_justice</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="GameChoiHayChia.Description">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:maxLines">4</item>
        <item name="android:textColor">#DEDEDE</item>
    </style>

    <style name="GameChoiHayChia.Title">
        <item name="android:textColor">#FFB800</item>
        <item name="android:textSize">@dimen/_20ssp</item>
    </style>

    <style name="WarningGameChoiHayChia"  parent="PrimaryTextAppearance">

    </style>

    <style name="WarningGameChoiHayChia.TitleMessage">
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/_17ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:maxLines">3</item>
    </style>

    <style name="GameChoiHayChia.StatusValue">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/_18ssp</item>
        <item name="android:layout_marginLeft">@dimen/_12sdp</item>
    </style>

    <style name="GameChoiHayChia.Button">
        <item name="android:shadowColor">#E8760C</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">10</item>
        <item name="android:shadowRadius">5</item>
        <item name="android:textSize">@dimen/_20ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@drawable/background_game_button</item>
        <item name="android:paddingTop">@dimen/_7sdp</item>
        <item name="android:paddingBottom">@dimen/_12sdp</item>
        <item name="android:paddingLeft">@dimen/_36sdp</item>
        <item name="android:paddingRight">@dimen/_36sdp</item>
    </style>

    <style name="GameChoiHayChia.ButtonSmall">
        <item name="android:shadowColor">#E8760C</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">10</item>
        <item name="android:shadowRadius">5</item>
        <item name="android:textSize">@dimen/_22ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@drawable/background_game_button</item>
        <item name="android:paddingTop">@dimen/_5sdp</item>
        <item name="android:paddingBottom">@dimen/_10sdp</item>
    </style>

    <style name="GameChoiHayChia.WarningButtonSmall">
        <item name="android:shadowColor">#E8760C</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">10</item>
        <item name="android:shadowRadius">5</item>
        <item name="android:textSize">@dimen/_17ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@drawable/background_game_button</item>
        <item name="android:paddingTop">@dimen/_5sdp</item>
        <item name="android:paddingBottom">@dimen/_10sdp</item>
    </style>

    <style name="GameChoiHayChia.EditTextInput">
        <item name="android:textCursorDrawable">@drawable/choi_hay_chia_accent_cursor</item>
        <item name="android:layout_marginTop">@dimen/_4sdp</item>
        <item name="android:layout_marginBottom">@dimen/_4sdp</item>
        <item name="android:layout_marginLeft">@dimen/_16sdp</item>
        <item name="android:layout_marginRight">@dimen/_16sdp</item>
        <item name="android:paddingLeft">@dimen/_16sdp</item>
        <item name="android:paddingRight">@dimen/_16sdp</item>
        <item name="android:paddingTop">@dimen/_8sdp</item>
        <item name="android:paddingBottom">@dimen/_8sdp</item>
        <item name="android:background">@drawable/choi_hay_chia_background_view_input</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textColorHint">#777777</item>
        <item name="android:singleLine">true</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textColor">#FFB800</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_18ssp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.Title">
        <item name="fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textSize">@dimen/_13ssp</item>
    </style>
    <style name="Game30sGuideToolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="fontFamily">@font/sf_pro_display_semibold</item>
        <item name="android:color">@color/white_87</item>
    </style>
    <style name="GameChoiHayChia.DialogQuestionAnswer.Question">
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingLeft">@dimen/_8sdp</item>
        <item name="android:paddingRight">@dimen/_8sdp</item>
        <item name="android:maxLines">4</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.Answer">
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">left|center_vertical</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.TextViewNumber">
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">left|center_vertical</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.TextViewError">
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:gravity">left|center_vertical</item>
        <item name="android:textColor">#FFAE12</item>
        <item name="android:maxLines">10</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.NotificationError">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:textColor">#FFAE12</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.Button">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.TextView">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="GameChoiHayChia.DialogQuestionAnswer.ButtonEvenOdd">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@drawable/choi_hay_chia_dialog_question_answer_button_even_odd_background</item>
    </style>

    <style name="GameChoiHayChia.DialogRank">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_18ssp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="GameChoiHayChia.DialogRank.RankNumber">
        <item name="android:textSize">@dimen/_14ssp</item>
    </style>


    <style name="GameChoiHayChia.DialogRank.NumberUser">
        <item name="android:textSize">@dimen/_16ssp</item>
        <item name="android:textColor">#4C2A01</item>
    </style>

    <style name="GameChoiHayChia.DialogRank.Score">
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:textColor">#FFAE12</item>
        <item name="android:gravity">left|center_vertical</item>
    </style>

    <style name="GameChoiHayChia.DialogRank.Name">
        <item name="android:textSize">@dimen/_20ssp</item>
        <item name="android:textColor">#FFAE12</item>
    </style>

    <style name="GameChoiHayChia.DialogRank.Prize">
        <item name="fontFamily">@font/sf_pro_display_heavy</item>
        <item name="android:fontFamily">@font/sf_pro_display_heavy</item>
        <item name="android:textSize">@dimen/_27ssp</item>
        <item name="android:textColor">#ECB346</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="GameChoiHayChia.DialogRank.SubTitle">
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:maxLines">2</item>
        <item name="android:paddingLeft">@dimen/_4sdp</item>
        <item name="android:paddingRight">@dimen/_4sdp</item>
    </style>
    <style name="GameChoiHayChia.DialogRank.Description">
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:maxLines">2</item>
        <item name="android:textColor">#A3A6AB</item>
        <item name="android:paddingLeft">@dimen/_10sdp</item>
        <item name="android:paddingRight">@dimen/_10sdp</item>
    </style>

    <style name="GameChoiHayChia.Error">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:textColor">@android:color/holo_red_light</item>
    </style>

    <style name="GameChoiHayChia.TextViewExplain">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/_18ssp</item>
        <item name="android:textColor">#EFB032</item>
        <item name="android:gravity">left|center_vertical</item>
    </style>
    <!--endregion GameChoiHayChia-->

    <!-- region Detail Event Game  -->
    <style name="DetailEvent">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
    </style>

    <style name="DetailEvent.Game">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
    </style>

    <style name="DetailEvent.Game.Title">
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginTop">@dimen/_11sdp</item>
        <item name="android:layout_marginLeft">@dimen/_11sdp</item>
        <item name="android:paddingLeft">@dimen/_3sdp</item>
        <item name="android:paddingRight">@dimen/_3sdp</item>
    </style>

    <style name="DetailEvent.Game.ValueCcu">
        <item name="android:paddingLeft">@dimen/_7sdp</item>
        <item name="android:paddingRight">@dimen/_7sdp</item>
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:drawablePadding">@dimen/_3sdp</item>
        <item name="android:layout_marginLeft">@dimen/_4sdp</item>
    </style>
    <!--endregion Detail Event Game-->


    <!--region Button-->
    <style name="ButtonBackground">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:background">@drawable/all_background_gradient_button</item>
    </style>
    <!--endregion-->

    <!--region Rating Dialog-->
    <style name="RatingDialog">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_18ssp</item>
        <item name="android:textColor">@color/colorNotificationDialogText</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="RatingDialog.Title">
        <item name="android:maxLines">2</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_18ssp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="RatingDialog.Message">
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="android:maxLines">3</item>
        <item name="android:gravity">center</item>
    </style>
    <!--endregion-->

    <!--region VOD-->
    <style name="ImageViewCorner">
        <item name="cornerSizeTopRight">@dimen/_3sdp</item>
        <item name="cornerSizeTopLeft">@dimen/_3sdp</item>
        <item name="cornerSizeBottomLeft">@dimen/_3sdp</item>
        <item name="cornerSizeBottomRight">@dimen/_3sdp</item>
        <item name="cornerFamily">rounded</item>
    </style>
    <!--endregion-->

    <style name="ImageView2TopCorner">
        <item name="cornerSizeTopRight">@dimen/_3sdp</item>
        <item name="cornerSizeTopLeft">@dimen/_3sdp</item>
        <item name="cornerFamily">rounded</item>
    </style>

    <!--region Customer Gratitude-->
    <style name="TextViewBorderless">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:textColor">#666666</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="TextViewShareFacebook" parent="TextViewBorderless">
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:background">@drawable/all_background_button_share_facebook</item>
    </style>
    <!--endregion-->

    <!--region Customer Gratitude-->
    <style name="ImageViewTopCorner">
        <item name="cornerSizeTopRight">@dimen/_4sdp</item>
        <item name="cornerSizeTopLeft">@dimen/_4sdp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerFamily">rounded</item>
    </style>
    <!--endregion-->

    <!--region VnAirline-->
    <style name="VnAirline">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="VnAirline.Title" parent="VnAirline">
        <item name="android:maxLines">3</item>
    </style>

    <style name="VnAirline.EditText" parent="VnAirline">
        <item name="android:textColorHint">@color/colorVnAirlineInputPNR</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
    </style>

    <style name="VnAirline.LocationItemText" parent="VnAirline">
        <item name="android:textColor">@color/colorVnAirlineInputPNR</item>
    </style>

    <style name="PlayerTextViewStatusBrightnessVolumeTimeSeek">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/all_background_grey_text_view</item>
        <item name="android:maxLines">1</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="YTOSecondsTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style>
    <!--endregion-->

    <!--region Download Navigation-->
    <style name="Download.Navigation.Title" parent="PrimaryTextAppearance">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textColor">@color/download_option_dialog_title</item>
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="Download.Navigation.OptionOne" parent="PrimaryTextAppearance">
        <item name="android:textColor">@color/download_option_dialog_option_one</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="Download.Navigation.OptionTwo" parent="PrimaryTextAppearance">
        <item name="android:textColor">@color/download_option_dialog_option_two</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="DownloadProgress.Navigation.Title" parent="PrimaryTextAppearance">
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:textSize">@dimen/_14ssp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="DownloadProgress.Navigation" parent="PrimaryTextAppearance">
        <item name="android:textColor">@color/download_option_dialog_title</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="CustomBottomSheetStyleBackgroundTransparent" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="CustomBottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyleBackgroundTransparent</item>
    </style>

    <!--endregion Download Navigation-->

    <!--  region Download  -->
    <style name="Download">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="Download.TextViewWarning">
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/download_title_text_color</item>
    </style>

    <style name="Download.TextViewTitleVietNam">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/download_title_text_color</item>
    </style>

    <style name="Download.TextViewTitleStorage">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:textColor">@color/download_title_text_color</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="Download.TextViewTitleEnglish">
        <item name="fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/download_sub_title_text_color</item>
    </style>

    <style name="Download.TextViewInformation">
        <item name="fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/download_sub_title_text_color</item>
    </style>

    <style name="Download.TextViewProgress">
        <item name="fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:textColor">@color/download_progress_text_color</item>
    </style>

    <style name="Download.ButtonBackground" parent="Download">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:background">@drawable/all_background_gradient_button</item>
    </style>

    <!--  endregion Download  -->

    <!--  region Warning Dialog  -->
    <style name="ZendeskCustomTheme" parent="ZendeskSdkTheme.Light">
        <item name="colorPrimary">#000</item>
        <item name="colorPrimaryDark">#000</item>
        <item name="colorAccent">@color/accent</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <!--  endregion Warning Dialog  -->

    <!-- region loyalty -->

    <style name="loyalty_history_sub_title">
        <item name="android:textColor">#99FFFFFF</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="fontFamily">font/sf_pro_display_regular</item>
    </style>

    <style name="loyalty_history_sub_value">
        <item name="android:textColor">#DEFFFFFF</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="fontFamily">font/sf_pro_display_regular</item>
    </style>

    <style name="loyalty_evoucher_white_title">
        <item name="maxLine">1</item>
        <item name="android:textColor">#DEFFFFFF</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_8ssp</item>
    </style>

    <style name="loyalty_evoucher_sub_title_text">
        <item name="maxLine">1</item>
        <item name="android:textColor">#99FFFFFF</item>
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_6ssp</item>
    </style>

    <style name="loyalty_evoucher_normal_text">
        <item name="maxLine">1</item>
        <item name="android:textColor">#DEFFFFFF</item>
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_6ssp</item>
    </style>

    <style name="loyalty_evoucher_red_title">
        <item name="android:textColor">#FE592A</item>
        <item name="maxLine">1</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_6ssp</item>
    </style>
    <!-- endregion loyalty -->

    <style name="BaseViewShadow">
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">@dimen/base_view_shadow_dx</item>
        <item name="android:shadowDy">@dimen/base_view_shadow_dy</item>
        <item name="android:shadowRadius">@dimen/base_view_shadow_radius</item>
    </style>

    <style name="player_age_restriction" parent="BaseViewShadow">
        <item name="android:maxLines">1</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">@dimen/_6ssp</item>
    </style>

    <style name="player_warning_contents" parent="BaseViewShadow">
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_5ssp</item>
    </style>
    <style name="ReportDialogText">
        <item name="fontFamily">@font/sf_pro_display_semibold</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:gravity">center</item>
    </style>


    <!--  region Account Delete OTP   -->
    <style name="CustomEditTextDeleteAccountOTP" parent="Widget.AppCompat.EditText">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textColorHint">@color/white_38</item>
        <item name="android:maxLines">1</item>
        <item name="android:maxLength">4</item>
    </style>
    <style name="OTPTextTitleStyle">
        <item name="android:fontFamily">@font/sf_pro_display_semibold</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="OTPTextStyle">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textColor">@color/app_content_text_disable_color</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="OTPTextLineStyle">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textColor">@color/app_orange_text_no_opacity_color</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>
    <!--  endregion region Account Delete OTP  -->

    <style name="MiniAppPaymentTextStyle">
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@color/white_60</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
    </style>

    <style name="PladioDetailBottomSheetDialogTheme" parent="Theme.Design.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/TransparentBottomSheetStyle</item>
        <item name="android:backgroundDimAmount">0.8</item>
    </style>

    <style name="TransparentBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@color/transparent</item>
    </style>

    <style name="ShortVideoTab" parent="Widget.MaterialComponents.TabLayout">
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="paddingStart">@dimen/_5sdp</item>
        <item name="paddingEnd">@dimen/_5sdp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
    </style>
</resources>