<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1F1F1F"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".features.ai_chatbot.AIChatBotFragment">
    <com.fptplay.mobile.features.ai_chatbot.view.AIChatBoxToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/ai_chat_box_toolbar_height"
        app:layout_constraintTop_toTopOf="parent" />
    <com.fpl.plugin.mini_app_sdk.android_view.MiniAppView
        android:id="@+id/mini_app_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="0dp" />
    <com.fptplay.mobile.common.ui.popup.loading.LoadingView
        android:id="@+id/lv_ai_chat_bot"
        tools:visibility="visible"
        android:visibility="gone"
        android:layout_width="@dimen/vod_comment_ai_loading_size"
        android:layout_height="@dimen/vod_comment_ai_loading_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />
    <FrameLayout
        android:elevation="@dimen/_2sdp"
        tools:visibility="visible"
        android:visibility="gone"
        android:background="#1F1F1F"
        android:id="@+id/fl_error_page"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>