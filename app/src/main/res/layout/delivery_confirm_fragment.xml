<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".features.loyalty.delivery.DeliveryConfirmFragment">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:gravity="center"
        android:paddingVertical="@dimen/_13sdp"
        android:text="Xác nhận thông tin"
        android:textColor="#DEFFFFFF"
        android:textSize="@dimen/_13ssp"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@id/ic_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14sdp"
        android:src="@drawable/ic_back"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <LinearLayout
        android:id="@+id/ll_user_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_14sdp"
        android:background="@drawable/loyalty_delivery_background"
        android:orientation="vertical"
        android:padding="@dimen/_11sdp"
        app:layout_constraintStart_toStartOf="@id/ic_back"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/ic_location"
            android:drawablePadding="@dimen/_4sdp"
            android:fontFamily="@font/sf_pro_display_medium"
            android:text="Trần Bá Phước"
            android:textColor="#DEFFFFFF"
            android:textSize="@dimen/_10ssp" />

        <TextView
            android:id="@+id/tv_user_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_medium"
            android:text="0384070564"
            android:textColor="#61FFFFFF"
            android:textSize="@dimen/_10ssp" />

        <TextView
            android:id="@+id/tv_user_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_medium"
            android:text="<EMAIL>"
            android:textColor="#61FFFFFF"
            android:textSize="@dimen/_10ssp" />

        <TextView
            android:id="@+id/tv_user_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_medium"
            android:text="B14 Phố chợ hoàng hoa thám, p 13, quận Tân Bình,
Thành Phố Hồ Chí Minh"
            android:textColor="#61FFFFFF"
            android:textSize="@dimen/_10ssp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ic_list_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="@id/ll_user_info"
        android:layout_marginEnd="@dimen/_21sdp"
        app:layout_constraintEnd_toEndOf="@id/ll_user_info"
        app:layout_constraintTop_toTopOf="@id/ll_user_info" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctv_product"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_14sdp"
        android:layout_marginTop="@dimen/_11sdp"
        android:background="@drawable/loyalty_delivery_background"
        android:padding="@dimen/_11sdp"
        app:layout_constraintTop_toBottomOf="@id/ll_user_info">

        <TextView
            android:id="@+id/tv_product_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_product"
            android:drawablePadding="@dimen/_4sdp"
            android:fontFamily="@font/sf_pro_display_medium"
            android:text="Thông tin sản phẩm"
            android:textColor="#DEFFFFFF"
            android:textSize="@dimen/_10ssp"
            app:layout_constraintStart_toStartOf="@id/ctv_product"
            app:layout_constraintTop_toTopOf="@id/ctv_product" />

        <ImageView
            android:id="@+id/iv_product"
            android:layout_width="@dimen/_68sdp"
            android:layout_height="@dimen/_76sdp"
            android:layout_marginTop="@dimen/_6sdp"
            android:background="@color/rgb9E9E9D"
            app:layout_constraintStart_toStartOf="@id/tv_product_info"
            app:layout_constraintTop_toBottomOf="@id/tv_product_info" />

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_9sdp"
            android:layout_marginTop="@dimen/_16sdp"
            android:fontFamily="@font/sf_pro_display_regular"
            android:text="Mũ bảo hiểm Andes"
            android:textSize="@dimen/_10ssp"
            app:layout_constraintStart_toEndOf="@id/iv_product"
            app:layout_constraintTop_toTopOf="@id/iv_product" />

        <TextView
            android:id="@+id/tv_amount_product"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2sdp"
            android:text="SL: 1"
            android:textSize="@dimen/_10ssp"
            app:layout_constraintStart_toStartOf="@id/tv_product_name"
            app:layout_constraintTop_toBottomOf="@id/tv_product_name" />

        // maybe it's a image api response

        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2sdp"
            android:background="@drawable/loyalty_history_button_background"
            android:drawableStart="@drawable/ic_loyalty_fgold"
            android:padding="@dimen/_2sdp"
            android:text="1.000"
            app:layout_constraintStart_toStartOf="@id/tv_amount_product"
            app:layout_constraintTop_toBottomOf="@id/tv_amount_product" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_note"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_14sdp"
        android:layout_marginTop="@dimen/_11sdp"
        android:background="@drawable/loyalty_delivery_background"
        android:orientation="vertical"
        android:padding="@dimen/_11sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ctv_product">

        <EditText
            android:id="@+id/edt_note"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_27sdp"
            android:background="@drawable/loyalty_delivery_editext_background"
            android:drawableEnd="@drawable/ic_note"
            android:hint="Ghi chú giao hàng"
            android:paddingStart="@dimen/_14sdp"
            android:paddingEnd="@dimen/_7sdp"
            android:textCursorDrawable="@drawable/loyalty_confirm_info_cursor_color" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_34sdp"
        android:layout_marginBottom="@dimen/_28sdp"
        android:background="@drawable/loyalty_history_button_background"
        android:gravity="center"
        android:text="@string/confirm"
        android:layout_marginHorizontal="@dimen/_14sdp"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>