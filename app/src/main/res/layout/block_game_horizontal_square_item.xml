<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/horizontal_container"
    android:layout_width="@dimen/item_game_horizontal_square_width"
    android:layout_height="wrap_content">

    <com.xhbadxx.projects.module.util.view.BaseCardView
        android:id="@+id/cv_thumb"
        android:layout_width="@dimen/item_game_horizontal_square_thumb_width"
        android:layout_height="@dimen/item_game_horizontal_square_thumb_height"
        android:layout_margin="@dimen/item_poster_margin_small"
        app:cardBackgroundColor="@color/transparent"
        app:cardCornerRadius="@dimen/item_game_horizontal_square_radius"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_thumb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_ribbon"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include layout="@layout/block_item_ribbon" />

                <include layout="@layout/block_item_playlist" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.xhbadxx.projects.module.util.view.BaseCardView>

    <com.fptplay.mobile.features.poster_overlay.PosterOverlayView
        android:id="@+id/poster_overlay"
        android:visibility="gone"
        android:layout_width="@dimen/item_game_horizontal_square_thumb_container_width"
        android:layout_height="@dimen/item_game_horizontal_square_thumb_container_width"
        android:translationZ="@dimen/_40sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/item_game_horizontal_square_title_height"
        android:layout_marginHorizontal="@dimen/item_poster_margin_small"
        android:layout_marginTop="@dimen/item_game_horizontal_square_divider_space"
        android:ellipsize="end"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="@color/app_content_text_color"
        android:textSize="@dimen/item_game_horizontal_square_title_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cv_thumb"
        tools:text="Vuong Lao Giad" />
</androidx.constraintlayout.widget.ConstraintLayout>