<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:transitionGroup="true">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/explore_nav_host"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:layout_behavior="com.fptplay.mobile.features.pladio.ui.BottomSheetContentBehavior"
        tools:layout="@layout/pladio_fragment" />

    <FrameLayout
        android:id="@+id/fl_pladio_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/slidingPanel"
        style="@style/Widget.Pladio.DisableDropShadows"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/pladio_playback_background_color_transition"
        app:behavior_hideable="false"
        app:behavior_peekHeight="0dp"
        app:gestureInsetBottomIgnored="true"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
        tools:visibility="invisible">

        <FrameLayout
            android:id="@+id/playbackPanelContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <fragment
            android:id="@+id/playbackBarFragment"
            android:name="com.fptplay.mobile.features.pladio.playback.bar.PladioPlaybackBarFragment"
            android:layout_width="match_parent"
            android:layout_height="@dimen/pladio_playback_bar_height"
            tools:layout="@layout/pladio_playback_bar_fragment" />

    </FrameLayout>

    <com.fptplay.mobile.features.pladio.ui.CustomBottomNavigationView
        android:id="@+id/navigationView"
        style="@style/PladioBottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pladio_bottom_navigation_bar_height"
        android:layout_gravity="bottom"
        app:itemHorizontalTranslationEnabled="false"
        tools:viewBindingType="com.google.android.material.navigation.NavigationBarView" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
