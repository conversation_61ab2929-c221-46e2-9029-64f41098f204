<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#333333"
    tools:context=".features.mini_app.MiniAppFragment">

    <LinearLayout
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ctl_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/mini_app__header__padding_vertical"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/mini_app__header__title_margin_horizontal"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/mini_app__header__title_text_size"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="joweryuoiweurioewurioewurioewurioweuioruweioruweioruoiweur" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/mini_app__header__button_close_size"
                android:layout_height="@dimen/mini_app__header__button_close_size"
                android:layout_marginEnd="@dimen/mini_app__header__button_close_margin_end"
                android:padding="@dimen/mini_app__header__button_close_padding"
                android:scaleType="fitXY"
                android:src="@drawable/ic_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <WebView
            android:id="@+id/web_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>


    <ImageView
        android:id="@+id/fbtn_close"
        android:layout_width="@dimen/mini_app__floating_close_button_width"
        android:layout_height="@dimen/mini_app__floating_close_button_height"
        android:layout_marginTop="@dimen/mini_app__floating_close_button_margin_top"
        android:paddingVertical="@dimen/mini_app__floating_close_button_padding"
        android:paddingStart="@dimen/mini_app__floating_close_button_padding"
        android:scaleType="fitXY"
        android:src="@drawable/mini_app_close_button_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>