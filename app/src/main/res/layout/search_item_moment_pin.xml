<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_thumb"
        android:layout_width="@dimen/search_result_moment_item_container_width"
        android:layout_height="@dimen/search_result_moment_item_container_height"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_thumb"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/item_poster_margin_small"
            android:background="@color/place_holder_color"
            android:scaleType="fitXY"
            app:shapeAppearanceOverlay="@style/BlockPoster" />

        <com.fptplay.mobile.features.poster_overlay.PosterOverlayView
            android:id="@+id/poster_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:translationZ="@dimen/_40sdp"
            android:visibility="gone"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/search_item_moment_pin_thumb_margin_horizontal"
        android:ellipsize="end"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/white_87"
        android:textSize="@dimen/search_item_moment_pin_title_size"
        app:layout_constraintBottom_toTopOf="@+id/tv_meta"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cl_thumb"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_meta"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/search_moment_pin_margin_vertical"
        android:ellipsize="end"
        android:fontFamily="@font/sf_pro_display_medium"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/white_60"
        android:textSize="@dimen/search_item_moment_pin_meta_size"
        app:layout_constraintBottom_toTopOf="@+id/tv_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/search_moment_pin_margin_vertical"
        android:ellipsize="end"
        android:fontFamily="@font/sf_pro_display_regular"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="@color/white_60"
        android:textSize="@dimen/search_moment_pin_description_size"
        app:layout_constraintBottom_toTopOf="@+id/cl_play_now"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_meta" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_play_now"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/search_moment_pin_button_margin_vertical"
        android:background="@drawable/search_item_moment_pin_button_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_description">

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="@dimen/search_moment_pin_icon_play_size"
            android:layout_height="@dimen/search_moment_pin_icon_play_size"
            android:layout_marginVertical="@dimen/_2sdp"
            android:layout_marginStart="@dimen/_6sdp"
            android:src="@drawable/ic_search_play_now"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_2sdp"
            android:layout_marginEnd="@dimen/_11sdp"
            android:ellipsize="end"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@string/view_now"
            android:textColor="@color/white_87"
            android:textSize="@dimen/search_item_moment_pin_title_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_play"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_favorite"
        android:layout_width="@dimen/search_moment_pin_icon_size"
        android:layout_height="@dimen/search_moment_pin_icon_size"
        android:layout_marginStart="@dimen/_9sdp"
        android:background="@drawable/search_item_moment_pin_button_background"
        android:paddingVertical="@dimen/_4sdp"
        android:src="@drawable/ic_search_favorite"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/cl_play_now"
        app:layout_constraintStart_toEndOf="@+id/cl_play_now"
        app:layout_constraintTop_toTopOf="@+id/cl_play_now" />

    <ImageView
        android:id="@+id/iv_share"
        android:layout_width="@dimen/search_moment_pin_icon_size"
        android:layout_height="@dimen/search_moment_pin_icon_size"
        android:layout_marginStart="@dimen/search_moment_pin_icon_mmargin"
        android:background="@drawable/search_item_moment_pin_button_background"
        android:paddingVertical="@dimen/search_moment_pin_icon_padding_vertical"
        android:src="@drawable/ic_search_share"
        app:layout_constraintBottom_toBottomOf="@+id/cl_play_now"
        app:layout_constraintStart_toEndOf="@+id/iv_favorite"
        app:layout_constraintTop_toTopOf="@+id/cl_play_now" />
</androidx.constraintlayout.widget.ConstraintLayout>
