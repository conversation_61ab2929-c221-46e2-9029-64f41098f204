<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ctl_noti_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/_4sdp"
    android:background="@drawable/notification_background_item"
    android:padding="@dimen/_9sdp">

    <ImageView
        android:id="@+id/iv_noti"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_noti_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="top"
        android:maxWidth="@dimen/_200sdp"
        android:maxLines="1"
        android:singleLine="true"
        android:textSize="@dimen/_11ssp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="19:30 Viet Nam - Indonesiaaaaaaaaaaaaaaaaaaaaa" />

    <TextView
        android:id="@+id/tv_des"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6sdp"
        android:ellipsize="end"
        android:gravity="top"
        android:maxLines="2"
        android:textSize="@dimen/_10ssp"
        android:textColor="@color/noti_sub_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ic_noti_item_more"
        app:layout_constraintTop_toBottomOf="@id/tv_noti_title"
        tools:text="Tôi thấy hoa vàng trên cỏ xanh là phim điện ảnh được chuyển thể từ tiểu thuyết cùng ..." />

    <TextView
        android:id="@+id/tv_hour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/noti_hour"
        android:textSize="@dimen/_9ssp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_noti_title"
        tools:text="10 phut" />

    <ImageView
        android:id="@+id/ic_noti_item_more"
        android:layout_width="@dimen/_24sdp"
        android:layout_height="@dimen/_24sdp"
        android:paddingHorizontal="@dimen/_7sdp"
        android:paddingTop="@dimen/_6sdp"
        android:src="@drawable/ic_show_more"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>