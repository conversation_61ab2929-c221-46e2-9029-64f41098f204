<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0A0A0A"
    android:clickable="true"
    android:focusable="true">

    <View
        android:id="@+id/v_background"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/guideline_horizontal_55"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_top_dim"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pladio_playback_panel_top_dim_height"
        android:background="@drawable/pladio_player_panel_top_dim"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_15"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.15" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_55"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.55" />

    <FrameLayout
        android:id="@+id/statusBarContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include layout="@layout/pladio_status_bar" />
    </FrameLayout>

    <RelativeLayout
        android:id="@+id/rl_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/pladio_playback_panel_header_margin_horizontal"
        android:layout_marginTop="@dimen/pladio_playback_panel_header_margin_top"
        app:layout_constraintTop_toBottomOf="@id/statusBarContainer">

        <ImageButton
            android:id="@+id/ib_back"
            android:layout_width="@dimen/pladio_playback_panel_header_back_icon_size"
            android:layout_height="@dimen/pladio_playback_panel_header_back_icon_size"
            android:layout_alignParentStart="true"
            android:background="@drawable/all_background_circle_ripple"
            android:src="@drawable/ic_player_arrow_down" />

        <ImageButton
            android:id="@+id/ib_playback_more"
            android:layout_width="@dimen/pladio_playback_panel_header_back_icon_size"
            android:layout_height="@dimen/pladio_playback_panel_header_back_icon_size"
            android:layout_alignParentEnd="true"
            android:background="@drawable/all_background_circle_ripple"
            android:src="@drawable/pladio_player_control_more" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_poster_player_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/guideline_horizontal_55"
        app:layout_constraintTop_toBottomOf="@id/guideline_horizontal_15">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_song_poster"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:elevation="@dimen/elevation_normal"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/PladioSongThumbStyle" />


        <FrameLayout
            android:id="@+id/fl_player_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="16:9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_image_hover"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="16:9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_song_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/app_margin"
        android:layout_marginTop="@dimen/pladio_playback_panel_player_control_button_margin_top"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/guideline_horizontal_55">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_live"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/pladio_playback_panel_live_height"
                android:layout_gravity="center_vertical"
                android:background="@drawable/bg_pladio_label_live"
                android:paddingHorizontal="@dimen/pladio_playback_label_live_padding_horizontal"
                android:layout_marginEnd="@dimen/pladio_playback_panel_live_margin_horizontal"
                android:visibility="gone">
                <TextView
                    android:id="@+id/tv_live"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:visibility="visible"
                    tools:text="LIVE"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/iv_dot"
                    android:layout_marginEnd="@dimen/pladio_playback_text_live_margin_end"
                    style="@style/PladioTextViewStyle.SubTitle"
                    android:textColor="@color/white"
                    android:fontFamily="@font/sf_pro_display_semibold" />
                <ImageView
                    android:id="@+id/iv_dot"
                    android:layout_width="@dimen/pladio_playback_live_dot_size"
                    android:layout_height="@dimen/pladio_playback_live_dot_size"
                    android:src="@drawable/ic_pladio_live_dot"
                    android:scaleType="fitXY"
                    app:layout_constraintTop_toTopOf="@+id/tv_live"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_live"
                    app:layout_constraintStart_toEndOf="@+id/tv_live"
                    app:layout_constraintEnd_toEndOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_song_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:ellipsize="marquee"
                android:focusable="true"
                android:fontFamily="@font/sf_pro_display_bold"
                android:freezesText="true"
                android:gravity="start"
                android:marqueeRepeatLimit="marquee_forever"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="@dimen/pladio_playback_panel_song_name_text_size"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/progressSlider"
                tools:text="@tools:sample/lorem/random" />

        </LinearLayout>


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_song_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginTop="@dimen/pladio_playback_panel_song_header_divider"
            android:ellipsize="end"
            android:fontFamily="@font/sf_pro_display_medium"
            android:gravity="start"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/pladio_playback_panel_song_description_text_size"
            app:layout_constraintBottom_toTopOf="@+id/text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressSlider"
            tools:text="@tools:sample/lorem/random" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_start_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginTop="@dimen/pladio_playback_panel_song_header_divider"
            android:ellipsize="end"
            android:fontFamily="@font/sf_pro_display_medium"
            android:gravity="start"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/pladio_playback_panel_song_description_text_size"
            app:layout_constraintBottom_toTopOf="@+id/text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressSlider"
            tools:text="@tools:sample/lorem/random" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>