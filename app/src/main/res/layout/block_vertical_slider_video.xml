<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <include layout="@layout/block_title_view"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_item"
        android:layout_width="match_parent"
        android:translationZ="@dimen/_40sdp"
        android:layout_height="@dimen/item_vertical_slider_video_container_height"
        android:layout_marginTop="@dimen/block_item_margin_top"
        app:layout_constraintTop_toBottomOf="@id/cl_header" />

</androidx.constraintlayout.widget.ConstraintLayout>