<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_img_small_icon"
        android:layout_alignParentRight="true"
        android:layout_width="32dp"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true">

        <ImageView
            android:scaleType="fitXY"
            android:id="@+id/img_v_small_icon"
            android:layout_centerInParent="true"
            android:src="@android:drawable/sym_def_app_icon"
            android:layout_width="@dimen/notification_size_img_small"
            android:layout_height="@dimen/notification_size_img_small" />

    </RelativeLayout>

    <LinearLayout
        android:layout_toLeftOf="@+id/rl_img_small_icon"
        android:layout_alignParentLeft="true"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_toStartOf="@+id/rl_img_small_icon">

        <TextView
            android:textColor="@android:color/black"
            android:textSize="@dimen/notification_size_text_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:id="@+id/v_title" />

        <TextView
            android:textColor="@android:color/darker_gray"
            android:ellipsize="end"
            android:textSize="@dimen/notification_size_text_content"
            android:maxLines="1"
            android:id="@+id/v_content"
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>


</RelativeLayout>