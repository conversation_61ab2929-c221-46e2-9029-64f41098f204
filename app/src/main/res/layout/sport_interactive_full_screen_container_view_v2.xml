<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tool:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_control_safe_start"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="0dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_control_safe_end"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_end="0dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_control_safe_end"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_min="@dimen/sport_interactive_full_screen_fragment_container_view_min_width"
        app:layout_constraintWidth_percent="0.4">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_main"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginVertical="@dimen/sport_interactive_full_screen_fragment_container_margin_vertical"
            android:layout_marginEnd="@dimen/sport_interactive_full_screen_tab_layout_item_spacing"
            android:background="@drawable/sport_interactive_full_screen_container_tab_background"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/rv_sport_tab"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tool:visibility="visible" />

        <ImageButton
            android:id="@+id/ib_close"
            android:layout_width="@dimen/sport_interactive_full_screen_button_close_size"
            android:layout_height="@dimen/sport_interactive_full_screen_button_close_size"
            android:layout_marginTop="@dimen/sport_interactive_full_screen_button_close_margin_top"
            android:background="@color/transparent"
            android:scaleType="fitXY"
            android:src="@drawable/sport_interactive_full_screen_container_close_ic"
            app:layout_constraintEnd_toEndOf="@id/rv_sport_tab"
            app:layout_constraintStart_toStartOf="@id/rv_sport_tab"
            app:layout_constraintTop_toTopOf="@id/rv_main" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_sport_tab"
            android:layout_width="@dimen/sport_interactive_full_screen_tab_layout_width"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/sport_interactive_full_screen_tab_layout_margin_right"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tool:listitem="@layout/sport_interactive_full_screen_tab_item" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>