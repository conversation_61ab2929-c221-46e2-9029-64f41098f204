<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fadeScrollbars="false"
    android:scrollbars="none"
    android:fillViewport="true">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@color/black"
        android:paddingTop="@dimen/delete_account_text_size_padding_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
            android:textSize="@dimen/delete_account_text_size_center_toolbar"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:id="@+id/toolbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="0dp"
            android:background="@color/black"
            android:theme="@style/Account.Toolbar.MenuTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@drawable/ic_arrow_left"
            app:title="@string/delete_account" />
        <androidx.constraintlayout.widget.ConstraintLayout
            tools:visibility="visible"
            android:paddingHorizontal="@dimen/delete_account_policy_padding_horizontal"
            android:id="@+id/layout_user_info"
            android:layout_width="@dimen/delete_account_policy_layout_width"
            android:layout_height="0dp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toBottomOf="parent">
            <TextView
                android:layout_marginTop="@dimen/delete_account_margin_top_layout"
                android:gravity="center"
                android:id="@+id/tv_title"
                android:textSize="@dimen/delete_account_text_size_title_delete"
                style="@style/BaseTextView"
                android:fontFamily="@font/sf_pro_display_medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/delete_account_confirm_message"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <TextView
                android:ellipsize="end"
                android:maxLines="2"
                android:fontFamily="@font/sf_pro_display_medium"
                android:gravity="center"
                android:id="@+id/tv_description"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/delete_account_description_message"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />
            <WebView
                android:background="@android:color/black"
                android:layout_marginTop="@dimen/delete_account_margin_top_layout"
                android:id="@+id/web_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginBottom="@dimen/delete_account_webView_margin_bottom_layout"
                android:overScrollMode="never"
                android:scrollbars="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_description"
                app:layout_constraintBottom_toTopOf="@id/cb_confirm"/>
            <CheckBox
                android:textColor="@color/white_38"
                style="@style/BaseTextView"
                android:fontFamily="@font/sf_pro_display_medium"
                android:id="@+id/cb_confirm"
                android:layout_width="wrap_content"
                android:textSize="@dimen/delete_account_text_size_checkbox"
                android:layout_height="wrap_content"
                android:buttonTint="@drawable/checkbox_filter_tint"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toTopOf="@id/btn_delete_account"
                android:onClick="onCheckboxClicked"
                android:text="@string/delete_account_confirm_message_checkbox" />
            <androidx.appcompat.widget.AppCompatButton
                android:enabled="true"
                android:id="@+id/btn_delete_account"
                android:layout_width="0dp"
                android:layout_height="@dimen/delete_account_height_button"
                style="@style/BaseTextView"
                android:fontFamily="@font/sf_pro_display_medium"
                android:text="@string/delete_account"
                android:textAllCaps="false"
                android:textColor="@color/white_87"
                android:layout_marginTop="@dimen/delete_account_margin_top_layout"
                android:layout_marginBottom="@dimen/delete_account_margin_top_layout"
                android:background="@drawable/account_rounded_btn_background_disable"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/btn_cancel"/>
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="@dimen/delete_account_height_button"
                style="@style/BaseTextView"
                android:fontFamily="@font/sf_pro_display_medium"
                android:text="@string/delete_account_cancel_button"
                android:textAllCaps="false"
                android:textColor="@color/white_87"
                android:layout_marginBottom="@dimen/delete_account_margin_bottom"
                android:background="@drawable/account_rounded_btn_background_disable"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>