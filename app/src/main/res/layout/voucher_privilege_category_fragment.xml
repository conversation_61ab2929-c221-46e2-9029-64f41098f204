<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".features.loyalty.voucher_detail.VoucherPrivilegeCategoryFragment">

    <View
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="@dimen/_102sdp"
        android:background="@drawable/loyalty_background_toolbar"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lo_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_31sdp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/_16sdp"
            android:paddingVertical="@dimen/_8sdp"
            android:src="@drawable/ic_arrow_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/title_privilege_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:textSize="@dimen/_13ssp"
            android:theme="@style/BlockHeaderText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_14sdp"
        android:layout_marginTop="@dimen/_21sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lo_title" />

    <TextView
        android:id="@+id/tv_not_data"
        android:layout_width="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/white_60"
        android:fontFamily="@font/sf_pro_display_regular"
        android:textSize="@dimen/_13ssp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>