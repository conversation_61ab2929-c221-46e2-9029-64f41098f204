<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tool:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/input_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/multi_profile_password_view_bg">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_password"
            style="@style/BaseTextView"
            android:layout_width="0dp"
            android:layout_height="@dimen/multi_profile_password_view_edt_height"
            android:layout_marginStart="@dimen/multi_profile_password_view_edt_margin_left"
            android:background="@null"
            android:inputType="textPassword"
            android:textColorHint="#61FFFFFF"
            android:textCursorDrawable="@drawable/account_edittext_cursor"
            android:textSize="@dimen/multi_profile_password_view_edt_text_size"
            app:layout_constraintEnd_toStartOf="@id/iv_show_password"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_show_password"
            android:layout_width="@dimen/multi_profile_password_view_icon_show_psw_size"
            android:layout_height="@dimen/multi_profile_password_view_icon_show_psw_size"
            app:srcCompat="@drawable/multi_profile_hide_password_ic"
            android:padding="@dimen/multi_profile_password_view_icon_show_psw_padding"
            app:layout_constraintBottom_toBottomOf="@id/edt_password"
            app:layout_constraintEnd_toStartOf="@id/iv_clear"
            app:layout_constraintStart_toEndOf="@id/edt_password"
            app:layout_constraintTop_toTopOf="@id/edt_password"
            android:scaleType="fitXY"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_clear"
            android:layout_width="@dimen/multi_profile_password_view_icon_show_psw_size"
            android:layout_height="@dimen/multi_profile_password_view_icon_show_psw_size"
            android:layout_marginEnd="@dimen/multi_profile_password_view_icon_clear_margin_end"
            android:padding="@dimen/multi_profile_password_view_icon_clear_padding"
            app:srcCompat="@drawable/ic_clear_white"
            app:layout_constraintBottom_toBottomOf="@id/edt_password"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/edt_password"
            android:scaleType="fitXY"/>

<!--        <androidx.appcompat.widget.AppCompatImageView-->
<!--            android:id="@+id/iv_error"-->
<!--            android:layout_width="@dimen/_26sdp"-->
<!--            android:layout_height="@dimen/_26sdp"-->
<!--            android:layout_marginEnd="@dimen/_7sdp"-->
<!--            android:padding="@dimen/_7sdp"-->
<!--            android:visibility="gone"-->
<!--            tool:visibility="visible"-->
<!--            app:srcCompat="@drawable/ic_warning"-->
<!--            app:layout_constraintBottom_toBottomOf="@id/edt_password"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="@id/edt_password" />-->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_error"
        style="@style/BaseTextView"
        android:textSize="@dimen/multi_profile_password_view_error_text_size"
        android:textColor="@color/color_accent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/multi_profile_password_view_error_margin_top"
        android:layout_marginStart="@dimen/multi_profile_password_view_error_margin_start"
        app:layout_constraintTop_toBottomOf="@id/input_layout"
        app:layout_constraintStart_toStartOf="@id/input_layout"
        android:visibility="gone"/>
</merge>