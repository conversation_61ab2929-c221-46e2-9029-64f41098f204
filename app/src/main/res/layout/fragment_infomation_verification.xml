<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black">
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/constraintLayout2"
        android:background="@drawable/bg_to_verify"
        android:layout_marginBottom="@dimen/_6sdp"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/_31sdp">
        <include layout="@layout/top_bar_loyalty"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/_17sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_top_bar">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView7"
            android:layout_width="@dimen/_26sdp"
            android:layout_height="@dimen/_26sdp"
            app:cardBackgroundColor="#1FFFFFFF"
            app:cardCornerRadius="@dimen/_13sdp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_content_step_1"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="@dimen/_9sdp">

            <TextView
                android:id="@+id/tv_step_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/sf_pro_display_regular"
                android:text="1"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_13ssp" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tv_content_step_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_regular"
            android:text="@string/e_verify_step_1"
            android:textColor="@color/white"
            android:textSize="@dimen/_10ssp"
            android:layout_marginEnd="@dimen/_23sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guide_line_center"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_line_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.50121653" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView8"
            android:layout_width="@dimen/_26sdp"
            android:layout_height="@dimen/_26sdp"
            app:cardBackgroundColor="#1FFFFFFF"
            app:cardCornerRadius="@dimen/_13sdp"
            android:layout_marginStart="@dimen/_10sdp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guide_line_center"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_step_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/sf_pro_display_regular"
                android:text="2"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_13ssp" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tv_content_step_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_9sdp"
            android:fontFamily="@font/sf_pro_display_regular"
            android:text="@string/e_verify_step_2"
            android:textColor="@color/white"
            android:textSize="@dimen/_10ssp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cardView8"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout2"
        app:layout_constraintBottom_toTopOf="@id/cl_next_step"
        android:layout_marginBottom="@dimen/_10sdp"
        android:scrollbars="none">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/_12sdp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_14sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_profile_edit"
                android:padding="@dimen/_11sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:text="Họ và tên"
                    android:textColor="@color/info_title_color"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView3"
                    app:layout_constraintTop_toTopOf="@+id/textView3" />
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edt_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="textCapCharacters"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView3" />

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Ngày sinh"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    android:fontFamily="@font/sf_pro_display_regular"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edt_name" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView4"
                    app:layout_constraintTop_toTopOf="@+id/textView4" />
                <TextView
                    android:id="@+id/tv_birth_day"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    android:layout_marginEnd="@dimen/_4sdp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView4" />

                <TextView
                    android:id="@+id/textView5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Giới tính"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="@+id/rg_sex"
                    app:layout_constraintTop_toBottomOf="@+id/edt_name" />
                <RadioGroup
                    android:id="@+id/rg_sex"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/guideline2"
                    app:layout_constraintTop_toTopOf="@+id/tv_birth_day"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_birth_day"
                    android:layout_marginHorizontal="@dimen/_12sdp"
                    android:orientation="horizontal">
                    <RadioButton
                        android:id="@+id/rb_male"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:buttonTint="@color/info_content_color"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:textColor="@color/info_title_color"
                        android:textSize="@dimen/_10ssp"
                        android:checked="true"
                        android:text="Nam"
                        />
                    <RadioButton
                        android:id="@+id/rb_female"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:buttonTint="@color/info_content_color"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:textColor="@color/info_title_color"
                        android:textSize="@dimen/_10ssp"
                        android:checked="false"
                        android:text="Nữ"
                        />
                </RadioGroup>

                <TextView
                    android:id="@+id/textView6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Số CMND/CCCD"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_birth_day" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView6"
                    app:layout_constraintTop_toTopOf="@+id/textView6" />
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edt_cccd"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="number"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView6"/>

                <TextView
                    android:id="@+id/textView7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Nơi cấp"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edt_cccd" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView7"
                    app:layout_constraintTop_toTopOf="@+id/textView7" />
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edt_issued_by"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="textCapCharacters"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView7" />

                <TextView
                    android:id="@+id/textView8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Ngày cấp"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edt_issued_by" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView8"
                    app:layout_constraintTop_toTopOf="@+id/textView8" />
                <TextView
                    android:id="@+id/tv_from"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="date"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    android:layout_marginEnd="@dimen/_4sdp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView8"/>

                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Giá trị đến"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/guideline2"
                    app:layout_constraintTop_toBottomOf="@+id/edt_issued_by" />

                <TextView
                    android:id="@+id/tv_to"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="date"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginStart="@dimen/_4sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/guideline2"
                    app:layout_constraintTop_toBottomOf="@+id/textView9"/>

                <TextView
                    android:id="@+id/textView10"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Nguyên quán"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_from" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView10"
                    app:layout_constraintTop_toTopOf="@+id/textView10" />
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edt_nationality"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="textCapCharacters"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView10"/>

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:text="Nơi thường trú"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edt_nationality" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="*"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_10ssp"
                    app:layout_constraintStart_toEndOf="@+id/textView11"
                    app:layout_constraintTop_toTopOf="@+id/textView11" />
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edt_residential_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/InfoEditText"
                    android:inputType="textCapCharacters"
                    android:paddingHorizontal="@dimen/_14sdp"
                    android:paddingVertical="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView11" />
                <View
                    android:id="@+id/view1"
                    android:layout_width="match_parent"
                    app:layout_constraintTop_toBottomOf="@+id/edt_residential_address"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:background="@drawable/line_gradient"
                    android:layout_height="1dp"/>
                <TextView
                    android:id="@+id/txtnoti"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_15sdp"
                    android:text="*"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:textColor="@color/info_content_color_default"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textSize="@dimen/_9ssp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view1" />
                <TextView
                    android:id="@+id/txtnoti1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/information_verification_notification"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/info_title_color"
                    android:textSize="@dimen/_9ssp"
                    android:textStyle="italic"
                    android:layout_marginStart="@dimen/_4sdp"
                    app:layout_constraintStart_toEndOf="@+id/txtnoti"
                    app:layout_constraintBottom_toBottomOf="@+id/txtnoti"
                    app:layout_constraintTop_toTopOf="@+id/txtnoti"
                    />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_9sdp"
                android:text="@string/text_policy_detech_info"
                android:gravity="center"
                android:textColor="@color/info_title_color"
                android:fontFamily="@font/sf_pro_display_regular"
                android:textSize="@dimen/_9ssp"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout3"
                app:layout_constraintStart_toStartOf="@+id/constraintLayout3"
                app:layout_constraintEnd_toEndOf="@+id/constraintLayout3"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_next_step"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_23sdp"
        android:layout_marginHorizontal="@dimen/_14sdp"
        android:background="@drawable/background_btn_verify_next_step_enable">

        <TextView
            android:id="@+id/tv_next_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_bold"
            android:text="Gửi yêu cầu xác thực"
            android:textColor="@color/color_text_title_dialog"
            android:textSize="@dimen/_11ssp"
            android:layout_marginVertical="@dimen/_11sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>