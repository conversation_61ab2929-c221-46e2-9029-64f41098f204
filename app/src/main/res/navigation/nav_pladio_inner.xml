<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_pladio_inner"
    app:startDestination="@id/pladio_home_fragment">

    <fragment
        android:id="@+id/pladio_home_fragment"
        android:name="com.fptplay.mobile.features.pladio.home.PladioHomeFragment"
        android:label="Pladio Home Fragment"
        tools:layout="@layout/pladio_home_fragment">

        <argument
            android:name="pageId"
            android:defaultValue=""
            app:argType="string" />

        <action
            android:id="@+id/action_pladio_home_fragment_to_category_of_categories_fragment"
            app:destination="@id/categoryOfCategoriesFragment">
            <argument
                android:name="type"
                android:defaultValue=""
                app:argType="string" />
            <argument
                android:name="blockId"
                android:defaultValue=""
                app:argType="string" />
            <argument
                android:name="blockType"
                android:defaultValue=""
                app:argType="string" />
            <argument
                android:name="customData"
                android:defaultValue=""
                app:argType="string" />

        </action>
        <action
            android:id="@+id/action_global_to_view_more_fragment"
            app:destination="@id/view_more_fragment">
            <argument
                android:name="blockStyle"
                android:defaultValue=""
                app:argType="string" />
            <!--To call api if type = default-->
            <argument
                android:name="blockType"
                android:defaultValue=""
                app:argType="string" />

            <argument
                android:name="id"
                android:defaultValue=""
                app:argType="string" />

            <argument
                android:name="header"
                android:defaultValue=""
                app:argType="string" />

            <argument
                android:name="subHeader"
                android:defaultValue=""
                app:argType="string" />

            <argument
                android:name="deeplink"
                android:defaultValue="false"
                app:argType="boolean" />

            <argument
                android:name="screenProvider"
                android:defaultValue=""
                app:argType="string" />

            <argument
                android:name="customData"
                android:defaultValue=""
                app:argType="string" />

            <argument
                android:name="source_show"
                android:defaultValue=""
                app:argType="string"
                app:nullable="true"/>

            <argument
                android:name="page_id"
                android:defaultValue=""
                app:argType="string" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/pladio_search_fragment"
        android:name="com.fptplay.mobile.features.pladio.search.PladioSearchFragment"
        android:label="Pladio Search Fragment"
        tools:layout="@layout/pladio_search_fragment">

        <argument
            android:name="pageId"
            android:defaultValue=""
            app:argType="string" />

        <action
            android:id="@+id/action_pladio_search_fragment_to_category_of_categories_fragment"
            app:destination="@id/categoryOfCategoriesFragment">
            <argument
                android:name="type"
                android:defaultValue=""
                app:argType="string" />
            <argument
                android:name="blockId"
                android:defaultValue=""
                app:argType="string" />
            <argument
                android:name="blockType"
                android:defaultValue=""
                app:argType="string" />
            <argument
                android:name="customData"
                android:defaultValue=""
                app:argType="string" />

        </action>
    </fragment>



    <dialog
        android:id="@+id/view_more_fragment"
        android:name="com.fptplay.mobile.viewmore.ViewMoreBaseFragment"
        android:label="ViewMore Fragment Dialog"
        tools:layout="@layout/viewmore_base_fragment">

        <!--To show data in horizontal or vertical ratio-->
        <argument
            android:name="blockStyle"
            android:defaultValue=""
            app:argType="string" />

        <!--To call api if type = default-->
        <argument
            android:name="blockType"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="header"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="subHeader"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="deeplink"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="customData"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="source_show"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true"/>

        <argument
            android:name="page_id"
            android:defaultValue=""
            app:argType="string" />
    </dialog>



    <dialog
        android:id="@+id/categoryOfCategoriesFragment"
        android:name="com.fptplay.mobile.features.categories.CategoryOfCategoriesFragment"
        android:label="CategoryOfCategoriesFragment"
        tools:layout="@layout/category_of_categories_fragment">
        <argument
            android:name="type"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="blockId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="blockType"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="customData"
            android:defaultValue=""
            app:argType="string" />
    </dialog>


    <dialog
        android:id="@+id/pladioDetailBottomSheetDialog"
        android:name="com.fptplay.mobile.features.pladio.detail.PladioDetailBottomSheetDialog"
        android:label="PladioDetailBottomSheetDialog"
        tools:layout="@layout/pladio_detail_bottom_sheet_dialog">
        <argument
            android:name="imageUrl"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="subTitle"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string"/>
    </dialog>


    <fragment
        android:id="@+id/pladio_detail_music_fragment"
        android:name="com.fptplay.mobile.features.pladio.detail.PladioDetailMusicFragment"
        android:label="Pladio Detail Music Fragment"
        tools:layout="@layout/pladio_detail_music_fragment" >
        <action
            android:id="@+id/action_pladio_detail_music_fragment_to_pladioDetailBottomSheetDialog"
            app:destination="@id/pladioDetailBottomSheetDialog" />
        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="contentType"
            android:defaultValue="2"
            app:argType="integer" />

        <!-- Deeplink -->
        <argument
            android:name="fromDeeplink"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="isAutoOpenPlayer"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="targetIdPlay"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="startPlayPositionMs"
            android:defaultValue="0"
            app:argType="integer" />

        <argument
            android:name="isAutoPlay"
            android:defaultValue="true"
            app:argType="boolean" />

        <argument
            android:name="episodeId"
            android:defaultValue=""
            app:argType="string" />
        <!-- Deeplink -->


    </fragment>
    <action
        android:id="@+id/action_global_to_pladio_detail_music_fragment"
        app:destination="@id/pladio_detail_music_fragment" >

        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="contentType"
            android:defaultValue="2"
            app:argType="integer" />

        <!-- Deeplink -->
        <argument
            android:name="fromDeeplink"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="isAutoOpenPlayer"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="targetIdPlay"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="startPlayPositionMs"
            android:defaultValue="0"
            app:argType="integer" />

        <argument
            android:name="isAutoPlay"
            android:defaultValue="true"
            app:argType="boolean" />

        <argument
            android:name="episodeId"
            android:defaultValue=""
            app:argType="string" />
        <!-- Deeplink -->

    </action>


    <fragment
        android:id="@+id/pladio_playlist_current_fragment"
        android:name="com.fptplay.mobile.features.pladio.detail.PladioPlaylistCurrentFragment"
        android:label="Pladio Playlist Current Fragment"
        tools:layout="@layout/pladio_playlist_current_fragment">
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
    </fragment>
    <action
        android:id="@+id/action_global_to_pladio_playlist_current_fragment"
        app:destination="@id/pladio_playlist_current_fragment">
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
    </action>
</navigation>