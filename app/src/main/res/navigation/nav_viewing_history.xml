<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_viewing_history"
    app:startDestination="@id/multiProfileViewingHistoryFragment">
    <include app:graph="@navigation/nav_vod" />
    <include app:graph="@navigation/nav_tv" />
    <include app:graph="@navigation/nav_tv_detail" />
    <include app:graph="@navigation/nav_moment" />
    <include app:graph="@navigation/nav_pladio_outer"/>

    <fragment
        android:id="@+id/multiProfileViewingHistoryFragment"
        android:name="com.fptplay.mobile.features.multi_profile.MultiViewingHistoryFragment"
        android:label="MultiViewingHistoryFragment"
        tools:layout="@layout/multi_viewing_history_fragment" >
        <argument
            android:name="profileId"
            android:defaultValue=""
            app:argType="string"/>
    </fragment>

    <action
        android:id="@+id/action_global_to_vod"
        app:destination="@id/nav_vod"
        app:popUpTo="@id/nav_vod">
        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="isPlaylist"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <action
        android:id="@+id/action_global_to_tv"
        app:destination="@id/nav_tv">
        <argument
            android:name="shouldNavigate"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="request_focus_group"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_tv_detail"
        app:destination="@id/nav_tv_detail" >
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />

        <argument
            android:name="use_args"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="groupId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="startTime"
            android:defaultValue="0"
            app:argType="string" />
        <argument
            android:name="timeShow"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="name"
            android:defaultValue=""
            app:argType="string" />
    </action>
    <action android:id="@+id/action_global_to_momentsFragment"
        app:destination="@id/nav_moment">
        <argument
            android:name="momentId"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="chapterId"
            android:defaultValue=""
            app:argType="string"/>
        <argument android:name="showMetadata"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument android:name="sourceScreen"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="relatedId"
            app:argType="string"
            android:defaultValue=""
            app:nullable="true"/>
        <argument android:name="isFromDeeplink"
            app:argType="boolean"
            android:defaultValue="false"/>
    </action>
    <action
        android:id="@+id/action_global_to_nav_pladio"
        app:destination="@id/nav_pladio_outer"
        app:popUpTo="@id/nav_pladio_outer"
        app:popUpToInclusive="true">


        <argument
            android:name="contentId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>
        <argument
            android:name="contentType"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>

        <argument
            android:name="navigateType"
            android:defaultValue="-1"
            app:argType="integer" />

        <argument
            android:name="pladioContentType"
            android:defaultValue="-1"
            app:argType="integer"/>
        <argument
            android:name="pladioType"
            android:defaultValue="-1"
            app:argType="integer"/>

        <argument
            android:name="playlistId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>

        <argument
            android:name="startTime"
            app:argType="integer"
            android:defaultValue="0"/>
        <argument
            android:name="autoplay"
            app:argType="boolean"
            android:defaultValue="true"/>

        <argument
            android:name="episodeId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>
    </action>
</navigation>