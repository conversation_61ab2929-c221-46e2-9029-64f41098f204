<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/_55sdp"
    android:layout_height="@dimen/_27sdp"
    android:background="@drawable/loyalty_history_transaction_item_background"
    android:gravity="center">

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="#DEFFFFFF"
        android:textSize="@dimen/_7ssp"
        android:layout_marginTop="@dimen/_6sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_gold_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_2sdp"
        android:layout_marginEnd="@dimen/_2sdp"
        android:fontFamily="@font/sf_pro_display_medium"
        android:includeFontPadding="false"
        android:textColor="#99FFFFFF"
        android:textSize="@dimen/_5ssp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_price" />
</androidx.constraintlayout.widget.ConstraintLayout>