<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_gravity="center"
        android:background="@drawable/dialog_alert_background"
        android:elevation="24dp"
        android:minWidth="@dimen/_230sdp"
        app:layout_constraintWidth_percent="0.6"
        >


        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image_view_mobile_provider"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/movie_background_toolbar"
            app:shapeAppearanceOverlay="@style/ImageViewTopCorner"
            android:fitsSystemWindows="true"
            android:scaleType="fitXY"
            app:layout_collapseMode="parallax"
            app:layout_constraintDimensionRatio="H,16:9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/text_view_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/sf_pro_display_regular"
            android:gravity="left"
            android:maxLines="7"
            android:padding="@dimen/_8sdp"
            android:paddingBottom="@dimen/_8sdp"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/_7ssp"
            app:layout_constraintBottom_toTopOf="@+id/ll_func_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image_view_mobile_provider" />

        <LinearLayout
            android:id="@+id/ll_func_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_21sdp"
            android:background="@drawable/dialog_3g_background"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/tvConfirm"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:fontFamily="@font/sf_pro_display_bold"
                android:gravity="center"
                android:text="@string/warning_dialog_button_positive_text"
                android:background="@drawable/dialog_3g_positive_text_bg"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/_7ssp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvCanel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:fontFamily="@font/sf_pro_display_bold"
                android:gravity="center"
                android:text="@string/warning_dialog_button_negative_text"
                android:textColor="#99FFFFFF"
                android:textSize="@dimen/_7ssp"
                android:textStyle="bold" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
