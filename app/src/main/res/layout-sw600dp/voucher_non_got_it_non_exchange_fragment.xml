<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    tools:context=".features.loyalty.voucher_detail.VoucherNonGotItNonExchangeFragment">

    <ImageView
        android:id="@+id/iv_background_app_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_42sdp"
        android:background="@drawable/loyalty_background_toolbar"
        android:paddingTop="@dimen/_25sdp"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageButton
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_7sdp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:elevation="@dimen/_5sdp"
        android:src="@drawable/ic_back_loyalty"
        app:layout_constraintBottom_toBottomOf="@+id/iv_background_app_bar"
        app:layout_constraintStart_toStartOf="parent" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/lo_btn_convert_gift"
        app:layout_constraintTop_toBottomOf="@+id/iv_background_app_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_background"
                app:cardCornerRadius="@dimen/_5sdp"
                android:layout_width="match_parent"
                android:layout_marginHorizontal="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_9sdp"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/iv_background"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_143sdp"
                    android:background="@drawable/placeholder_highlight"
                    android:scaleType="fitXY"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/title_cover"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/_21sdp"
                    android:textSize="@dimen/_10ssp"
                    android:fontFamily="@font/sf_pro_display_bold"
                    tools:text="Phiếu sử dụng dịch vụ (thiết bị iHome) 100K" />

            </androidx.cardview.widget.CardView>


            <androidx.cardview.widget.CardView
                android:id="@+id/lo_vouchers_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_50sdp"
                android:layout_marginTop="@dimen/_9sdp"
                app:cardCornerRadius="@dimen/_5sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cv_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/loyalty_background"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_title_vouchers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_8sdp"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:elevation="@dimen/_12sdp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:fontFamily="@font/sf_pro_display_semibold"
                        android:textColor="@color/white_87"
                        android:textSize="@dimen/_8ssp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_stock"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_8sdp"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:elevation="@dimen/_12sdp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/loyalty_color_text"
                        android:textSize="@dimen/_6ssp" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/loyalty_rounded_btn_background_fgold"
                        android:paddingHorizontal="@dimen/_1sdp"
                        android:layout_marginHorizontal="@dimen/_8sdp"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:layout_marginBottom="@dimen/_8sdp">

                        <TextView
                            android:id="@+id/tv_fgold_converted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/sf_pro_display_medium"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_6ssp"
                            android:minWidth="@dimen/_6sdp"
                            android:layout_marginEnd="@dimen/_1sdp"
                            android:gravity="center_horizontal"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/iv_circle_cost_converted"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_marginEnd="@dimen/_2sdp"
                            android:layout_marginVertical="@dimen/_1sdp"
                            android:src="@drawable/ic_fgold"
                            app:layout_constraintBottom_toBottomOf="@+id/tv_fgold_converted"
                            app:layout_constraintDimensionRatio="1:1"
                            app:layout_constraintEnd_toStartOf="@+id/tv_fgold_converted"
                            app:layout_constraintTop_toTopOf="@+id/tv_fgold_converted" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@+id/lo_vouchers_view">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_13sdp"
                    android:background="@drawable/vod_detail_tablayout_bg">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_50sdp"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/btnDescription"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/transparent"
                            android:fontFamily="@font/sf_pro_display_regular"
                            android:gravity="center"
                            android:paddingVertical="@dimen/_7sdp"
                            android:text="@string/text_description_product"
                            android:textAllCaps="false"
                            android:textColor="@color/white_87"
                            android:textSize="@dimen/_7ssp"
                            android:textStyle="normal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="@dimen/_2sdp"
                            android:backgroundTint="@color/loyalty_background_accent_text"
                            app:cardCornerRadius="@dimen/_1sdp"
                            app:layout_constraintEnd_toEndOf="@+id/btnDescription"
                            app:layout_constraintStart_toStartOf="@+id/btnDescription"
                            app:layout_constraintTop_toBottomOf="@+id/btnDescription" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginHorizontal="@dimen/_50sdp"
                    android:layout_marginTop="@dimen/_9sdp"
                    android:layout_marginBottom="@dimen/_6sdp"
                    android:textColor="@color/white_60"
                    android:textSize="@dimen/_6ssp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lo_btn_convert_gift"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_convert_gift"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginHorizontal="@dimen/_78sdp"
            android:layout_marginTop="@dimen/_7sdp"
            android:layout_marginBottom="@dimen/_13sdp"
            android:background="@drawable/loyalty_rounded_btn_background_enable"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:paddingVertical="@dimen/_6sdp"
            android:text="@string/text_convert_voucher_success"
            android:textAllCaps="false"
            android:textSize="@dimen/_7ssp"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
