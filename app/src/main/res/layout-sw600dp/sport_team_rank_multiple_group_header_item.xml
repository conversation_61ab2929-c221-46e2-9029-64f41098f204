<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#12FFFFFF"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_14sdp">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_sport_team"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.15" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_match"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.75" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_gd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.85" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_score"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.95" />

    <TextView
        android:id="@+id/tv_rank"
        style="@style/Sport.RankItem.Header.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/rank"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/gl_rank"
        app:layout_constraintStart_toStartOf="@id/gl_rank"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_team"
        style="@style/Sport.RankItem.Header.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sport_team"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/gl_sport_team"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_match"
        style="@style/Sport.RankItem.Header.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/match"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/gl_match"
        app:layout_constraintStart_toStartOf="@id/gl_match"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_gd"
        style="@style/Sport.RankItem.Header.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/goal_difference"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/gl_gd"
        app:layout_constraintStart_toStartOf="@id/gl_gd"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_score"
        style="@style/Sport.RankItem.Header.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/score"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/gl_score"
        app:layout_constraintStart_toStartOf="@id/gl_score"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>