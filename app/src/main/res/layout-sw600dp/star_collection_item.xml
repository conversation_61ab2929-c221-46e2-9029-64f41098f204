<?xml version="1.0" encoding="utf-8"?>
<com.xhbadxx.projects.module.util.view.BaseCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:elevation="0dp"
    app:cardBackgroundColor="@color/black"
    app:cardCornerRadius="@dimen/block_item_corner_radius">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/iv_thumb"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            android:src="@drawable/placeholder_vertical_medium"
            app:layout_constraintDimensionRatio="85:127"
            app:layout_constraintTop_toTopOf="parent" />
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:src="@drawable/botton_grid_block_gradient"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@+id/rv_hashtag"
            android:layout_marginTop="-10dp"/>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_hashtag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_3sdp"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginEnd="@dimen/_5sdp"
            app:layout_constraintBottom_toTopOf="@id/tv_name"
            />
        <TextView
            android:visibility="gone"
            android:ellipsize="end"
            android:maxLines="1"
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:textColor="@color/white"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginEnd="@dimen/_5sdp"

            android:layout_marginBottom="@dimen/_3sdp"
            android:textSize="@dimen/_5ssp"
            app:layout_constraintBottom_toTopOf="@id/lnl_like_start"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/lnl_like_start"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/iv_thumb"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginEnd="@dimen/_5sdp"
            android:layout_marginBottom="@dimen/_3sdp"
            app:layout_constraintLeft_toLeftOf="parent">
            <LinearLayout
                android:id="@+id/ll_bottom_icon"
                android:layout_marginRight="@dimen/_4sdp"
                android:layout_width="0dp"
                android:gravity="center"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/ll_voiting">
                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="@dimen/_5sdp">
                    <ImageView
                        android:id="@+id/iv_avatar_team"
                        android:layout_gravity="center_vertical"
                        android:layout_width="@dimen/_5sdp"
                        android:layout_height="@dimen/_5sdp"
                        android:scaleType="fitXY"
                        android:src="@drawable/placeholder_horizontal_slider"
                        android:background="@color/place_holder_color"
                        />
                </androidx.cardview.widget.CardView>
                <TextView
                    android:textColor="@color/white"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:layout_marginStart="@dimen/_1sdp"
                    android:id="@+id/tv_name_member"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/_3ssp"
                    />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_voiting"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">
                <ImageView
                    android:id="@+id/image_vote"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_gravity="center_vertical"
                    android:layout_width="@dimen/_4sdp"
                    android:layout_height="@dimen/_4sdp"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_heart_vote"
                    app:tint="#FFFFFF"
                    />
                <TextView
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:gravity="center_vertical"
                    android:fontFamily="@font/sf_pro_display_regular"
                    android:textColor="@color/white"
                    android:layout_marginStart="@dimen/_1sdp"
                    android:id="@+id/tv_number_vote"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/_3ssp"

                    />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.xhbadxx.projects.module.util.view.BaseCardView>