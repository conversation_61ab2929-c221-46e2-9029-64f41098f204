<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <me.dm7.barcodescanner.zxing.ZXingScannerView
        android:id="@+id/scanner"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:borderColor="@color/white"
        app:borderLength="@dimen/_10sdp"
        app:borderWidth="@dimen/_4sdp"
        app:laserColor="@color/white"
        app:cornerRadius="1dp"
        app:roundedCorner="true"
        app:finderOffset="@dimen/_30sdp"
        app:squaredFinder="true" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5"/>

        <TextView
            app:layout_constraintBottom_toTopOf="@id/guideline_center"
            android:layout_marginBottom="@dimen/_95sdp"
            android:textColor="#A8A4A4"
            android:text="@string/quick_login_scan_qr"
            android:textAlignment="center"
            android:textSize="@dimen/_7ssp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/_14sdp"
            android:layout_height="@dimen/_14sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginEnd="@dimen/dialog_close_ic_margin"
            android:padding="@dimen/_4sdp"
            android:src="@drawable/ic_close"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_goneMarginTop="@dimen/app_margin"
            android:contentDescription="@string/talkback_icon_exit" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>