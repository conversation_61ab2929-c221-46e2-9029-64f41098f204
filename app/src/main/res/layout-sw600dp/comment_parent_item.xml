<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_8sdp"
    android:layout_marginBottom="@dimen/_6sdp"
    android:orientation="vertical">

    <include
        android:id="@+id/comment_child"
        layout="@layout/comment_child_item" />

    <TextView
        android:id="@+id/tv_view_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_28sdp"
        android:layout_marginTop="@dimen/_2sdp"
        android:drawablePadding="@dimen/_4sdp"
        android:textColor="#DEFFFFFF"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_19sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:visibility="gone" />

</LinearLayout>