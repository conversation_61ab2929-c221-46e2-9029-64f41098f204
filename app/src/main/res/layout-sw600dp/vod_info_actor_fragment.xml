<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tvDes"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_9sdp"
            android:textColor="#99FFFFFF"
            android:textSize="@dimen/_7ssp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>