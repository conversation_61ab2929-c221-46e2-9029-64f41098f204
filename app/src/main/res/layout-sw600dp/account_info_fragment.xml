<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:fillViewport="true"
    android:background="@color/black">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="0dp"
            android:paddingEnd="@dimen/_6ssp"
            android:theme="@style/Account.Toolbar.Tablet.MenuTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@drawable/ic_arrow_left"
            app:title="@string/account_info" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_user_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_18sdp"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_avatar"
                android:layout_width="@dimen/_39sdp"
                android:layout_height="@dimen/_39sdp"
                android:layout_marginTop="@dimen/_14sdp"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearanceOverlay="@style/CircleImageViewStyle" />

            <Button
                android:id="@+id/btn_change_avatar"
                style="@style/BaseTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:text="@string/change_avatar"
                android:textAllCaps="false"
                android:textColor="@color/accent"
                android:textSize="@dimen/_6ssp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="@id/tv_display_name"
                app:layout_constraintStart_toEndOf="@id/tv_display_name" />

            <View
                android:id="@+id/view_divider_avatar"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:visibility="gone"
                android:background="#1AFFFFFF"
                app:layout_constraintTop_toBottomOf="@id/btn_change_avatar"
                android:layout_marginTop="@dimen/_10sdp"/>

            <TextView
                android:id="@+id/tv_id"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="@string/id"
                app:layout_constraintEnd_toEndOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_change_avatar" />

            <TextView
                android:id="@+id/tv_id_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                app:layout_constraintBottom_toBottomOf="@id/tv_id"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_id"
                app:layout_constraintTop_toTopOf="@id/tv_id" />

            <View
                android:id="@+id/view_divider_id"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_id"
                app:layout_constraintTop_toBottomOf="@id/tv_id" />

            <TextView
                android:id="@+id/tv_account"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/account"
                app:layout_constraintEnd_toEndOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_id" />

            <TextView
                android:id="@+id/tv_account_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                app:layout_constraintBottom_toBottomOf="@id/tv_account"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_account"
                app:layout_constraintTop_toTopOf="@id/tv_account" />

            <View
                android:id="@+id/view_divider_account"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_account"
                app:layout_constraintTop_toBottomOf="@id/tv_account" />

            <TextView
                android:id="@+id/tv_contract"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/contract"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_account" />

            <TextView
                android:id="@+id/tv_contract_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tv_contract"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_contract"
                app:layout_constraintTop_toTopOf="@id/tv_contract" />

            <View
                android:id="@+id/view_divider_contract"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_contract"
                app:layout_constraintTop_toBottomOf="@id/tv_contract" />

            <TextView
                android:id="@+id/tv_display_name"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/display_name"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_contract" />

            <TextView
                android:id="@+id/tv_display_name_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                app:layout_constraintBottom_toBottomOf="@id/tv_display_name"
                app:layout_constraintEnd_toStartOf="@id/iv_edit_display_name"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toTopOf="@id/tv_display_name" />

            <ImageView
                android:id="@+id/iv_edit_display_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_15sdp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="@id/tv_display_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_display_name" />

            <View
                android:id="@+id/view_divider_display_name"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_display_name"
                app:layout_constraintTop_toBottomOf="@id/tv_display_name" />

            <TextView
                android:id="@+id/tv_password"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/password"
                app:layout_constraintEnd_toEndOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_display_name" />

            <ImageView
                android:id="@+id/iv_password"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                android:src="@drawable/account_password_dot"
                android:scaleType="fitStart"
                app:layout_constraintBottom_toBottomOf="@id/tv_password"
                app:layout_constraintEnd_toStartOf="@id/iv_edit_password"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toTopOf="@id/tv_password" />

            <ImageView
                android:id="@+id/iv_edit_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_15sdp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="@id/tv_password"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_password" />

            <View
                android:id="@+id/view_divider_password"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_password"
                app:layout_constraintTop_toBottomOf="@id/tv_password" />

            <TextView
                android:id="@+id/tv_email"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/email"
                app:layout_constraintEnd_toEndOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_password" />

            <TextView
                android:id="@+id/tv_email_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_15sdp"
                app:layout_constraintBottom_toBottomOf="@id/tv_email"
                app:layout_constraintEnd_toStartOf="@id/iv_edit_email"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toTopOf="@id/tv_email" />

            <ImageView
                android:id="@+id/iv_edit_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_15sdp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="@id/tv_email"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_email" />

            <View
                android:id="@+id/view_divider_email"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_email"
                app:layout_constraintTop_toBottomOf="@id/tv_email" />

            <TextView
                android:id="@+id/tv_sex"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/sex"
                app:layout_constraintEnd_toStartOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_email" />

            <TextView
                android:id="@+id/tv_sex_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/tv_sex"
                app:layout_constraintEnd_toStartOf="@id/iv_edit_sex"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toTopOf="@id/tv_sex" />

            <ImageView
                android:id="@+id/iv_edit_sex"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_15sdp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="@id/tv_sex"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_sex" />

            <View
                android:id="@+id/view_divider_sex"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_sex"
                app:layout_constraintTop_toBottomOf="@id/tv_sex" />

            <TextView
                android:id="@+id/tv_birthday"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/birthday"
                app:layout_constraintEnd_toStartOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_sex" />

            <TextView
                android:id="@+id/tv_birthday_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/tv_birthday"
                app:layout_constraintEnd_toStartOf="@id/iv_edit_birthday"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toTopOf="@id/tv_birthday" />

            <ImageView
                android:id="@+id/iv_edit_birthday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_15sdp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="@id/tv_birthday"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_birthday" />

            <View
                android:id="@+id/view_divider_birthday"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="@dimen/_14sdp"
                android:background="#1AFFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_birthday"
                app:layout_constraintTop_toBottomOf="@id/tv_birthday" />

            <TextView
                android:id="@+id/tv_location"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8sdp"
                android:text="@string/location"
                app:layout_constraintEnd_toStartOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_birthday" />

            <TextView
                android:id="@+id/tv_location_value"
                style="@style/BaseTabletTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/tv_location"
                app:layout_constraintEnd_toStartOf="@id/iv_edit_location"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toTopOf="@id/tv_location" />

            <ImageView
                android:id="@+id/iv_edit_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_15sdp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="@id/tv_location"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_location" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_logout"
                style="@style/BaseTextView"
                android:layout_width="@dimen/_217sdp"
                android:layout_height="@dimen/_23sdp"
                android:layout_marginHorizontal="@dimen/_14sdp"
                android:layout_marginBottom="@dimen/_22sdp"
                android:layout_marginTop="@dimen/_17sdp"
                android:background="@drawable/account_rounded_btn_background_disable"
                android:fontFamily="@font/sf_pro_display_bold"
                android:text="@string/logout"
                android:textAllCaps="false"
                android:textColor="@color/white_87"
                android:textSize="@dimen/_7ssp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_location"
                app:layout_constraintBottom_toBottomOf="@+id/iv_logo" />
            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_logo"
                android:layout_width="@dimen/_16sdp"
                android:layout_height="@dimen/_16sdp"
                android:layout_marginTop="@dimen/_17sdp"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/ic_launcher"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_logout"
                />
            <TextView
                android:layout_marginTop="@dimen/_6sdp"
                android:gravity ="center"
                tools:text="Phiên bản 4.10.1"
                android:id="@+id/tv_version"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_logo"
                />
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_delete"
                style="@style/BaseTextView"
                android:layout_width="@dimen/_217sdp"
                android:layout_height="@dimen/_23sdp"
                android:layout_marginHorizontal="@dimen/_14sdp"
                android:layout_marginTop="@dimen/_17sdp"
                android:layout_marginBottom="@dimen/_22sdp"
                android:background="@drawable/account_rounded_btn_background_disable"
                android:fontFamily="@font/sf_pro_display_bold"
                android:text="@string/delete_account"
                android:textAllCaps="false"
                android:textColor="@color/white_87"
                android:textSize="@dimen/_7ssp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_version"
                app:layout_constraintBottom_toBottomOf="parent"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/pb_loading"
            layout="@layout/all_view_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8sdp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>