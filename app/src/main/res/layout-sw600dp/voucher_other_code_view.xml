<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lo_code_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_50sdp"
        android:layout_marginTop="@dimen/_13sdp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_code_used"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:text="@string/text_code_using"
            android:textColor="@color/white_87"
            android:textSize="@dimen/_7ssp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_code_used_once"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:text="@string/text_code_using_once"
            android:textColor="@color/white_38"
            android:textSize="@dimen/_6ssp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_code_used"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            app:cardCornerRadius="@dimen/_3sdp"
            app:layout_constraintTop_toBottomOf="@+id/tv_code_used_once">

            <LinearLayout
                android:layout_width="match_parent"
                android:background="@color/loyalty_background"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/lo_code_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_7sdp"
                    android:elevation="@dimen/_2sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tv_code"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:textSize="@dimen/_6ssp"
                        android:gravity="start"
                        android:layout_marginEnd="@dimen/_10sdp"
                        app:layout_constraintEnd_toStartOf="@+id/cb_use_click"
                        android:textColor="@color/white_60"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <CheckBox
                        android:id="@+id/cb_use_click"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/_3sdp"
                        android:layout_marginEnd="@dimen/_minus3sdp"
                        android:textColor="@color/white_87"
                        android:textSize="@dimen/_6ssp"
                        android:visibility="gone"
                        app:layout_constraintTop_toTopOf="@+id/tv_code"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_code"
                        app:layout_constraintEnd_toEndOf="parent"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <ImageView
                    android:id="@+id/iv_divider"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_7sdp"
                    android:layout_marginHorizontal="@dimen/_7sdp"
                    android:elevation="@dimen/_2sdp"
                    android:src="@drawable/loyalty_divider_voucher" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_7sdp"
                    android:elevation="@dimen/_2sdp">

                    <TextView
                        android:id="@+id/tv_converted"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:text="@string/text_converted"
                        android:textColor="@color/white_60"
                        android:textSize="@dimen/_6ssp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_time_converted"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:textColor="@color/white_87"
                        android:textSize="@dimen/_6ssp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_7sdp"
                    android:elevation="@dimen/_2sdp">

                    <TextView
                        android:id="@+id/tv_using"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:textColor="@color/white_60"
                        android:textSize="@dimen/_6ssp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_time_using"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:textColor="@color/white_87"
                        android:textSize="@dimen/_6ssp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_7sdp"
                    android:layout_marginVertical="@dimen/_7sdp"
                    android:elevation="@dimen/_2sdp">

                    <TextView
                        android:id="@+id/tv_cost_convert"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_regular"
                        android:text="@string/gold_converted"
                        android:textColor="@color/white_60"
                        android:textSize="@dimen/_6ssp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/loyalty_rounded_btn_background_fgold"
                        android:paddingHorizontal="@dimen/_1sdp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_cost_convert"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_cost_convert">

                        <TextView
                            android:id="@+id/tv_fgold_converted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/sf_pro_display_medium"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_6ssp"
                            android:minWidth="@dimen/_6sdp"
                            android:layout_marginEnd="@dimen/_1sdp"
                            android:gravity="center_horizontal"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/iv_circle_cost_converted"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_marginEnd="@dimen/_1sdp"
                            android:layout_marginVertical="@dimen/_1sdp"
                            android:src="@drawable/ic_fgold"
                            app:layout_constraintBottom_toBottomOf="@+id/tv_fgold_converted"
                            app:layout_constraintDimensionRatio="1:1"
                            app:layout_constraintEnd_toStartOf="@+id/tv_fgold_converted"
                            app:layout_constraintTop_toTopOf="@+id/tv_fgold_converted" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/lo_btn_share_code"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@+id/cardView_code">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_2sdp"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_share"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_17sdp"
                    android:background="@drawable/loyalty_rounded_btn_background_disable_tablet"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="@+id/btn_share"
                    app:layout_constraintEnd_toEndOf="@+id/btn_share"
                    app:layout_constraintStart_toStartOf="@+id/btn_share"
                    app:layout_constraintTop_toTopOf="@+id/btn_share">

                    <ImageView
                        android:layout_width="@dimen/_8sdp"
                        android:layout_height="@dimen/_7sdp"
                        android:layout_gravity="center"
                        android:layout_marginHorizontal="@dimen/_3sdp"
                        android:src="@drawable/ic_share" />

                    <TextView
                        android:id="@+id/tv_share"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_semibold"
                        android:text="@string/share"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_7ssp" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_2sdp"
                android:layout_weight="1">

                <LinearLayout
                    android:id="@+id/btn_get_code"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_17sdp"
                    android:background="@drawable/loyalty_rounded_btn_background_enable_tablet"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/iv_btn_get_code"
                        android:layout_width="@dimen/_10sdp"
                        android:layout_height="@dimen/_10sdp"
                        android:layout_gravity="center"
                        android:layout_marginHorizontal="@dimen/_3sdp"
                        android:src="@drawable/ic_duplicate" />

                    <TextView
                        android:id="@+id/tv_get_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sf_pro_display_semibold"
                        android:text="@string/text_get_code"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_7ssp"/>
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>