<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:id="@+id/btn_buy_package"
            style="@style/AllButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_17sdp"
            android:layout_marginHorizontal="@dimen/_8sdp"
            android:layout_marginVertical="@dimen/_8sdp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_buy_package"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:gravity="center"
                android:drawableLeft="@drawable/view_now_icon"
                android:drawablePadding="@dimen/all_button_icon_padding_right"
                android:enabled="false"
                android:text="@string/buy_package_something"
                android:textColor="@color/white"
                android:textSize="@dimen/all_button_text_size" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_8sdp"
            app:layout_constraintTop_toBottomOf="@id/btn_buy_package">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:textColor="#DEFFFFFF"
                android:textSize="@dimen/_8ssp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_round"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4sdp"
                android:textColor="#99FFFFFF"
                android:textSize="@dimen/_6ssp" />

            <TextView
                android:id="@+id/tv_des"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:textColor="#99FFFFFF"
                android:textSize="@dimen/_6ssp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>