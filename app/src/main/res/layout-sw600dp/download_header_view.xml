<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctl_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@drawable/ic_arrow_left"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_storage_internal_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            style="@style/Download.TextViewTitleStorage"
            android:layout_marginStart="@dimen/_11sdp"
            android:layout_marginEnd="@dimen/_11sdp"
            android:visibility="visible"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_storage_external_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tv_storage_internal_status"
            style="@style/Download.TextViewTitleStorage"
            android:layout_marginTop="@dimen/_3sdp"
            android:layout_marginStart="@dimen/_11sdp"
            android:layout_marginEnd="@dimen/_11sdp"
            android:visibility="gone"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>