<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@android:color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:title="@string/sport_ranking"
        android:background="#121212"
        app:navigationIcon="@drawable/ic_arrow_left_tablet"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/iv_share"
        android:layout_width="@dimen/_16sdp"
        android:layout_height="@dimen/_16sdp"
        android:layout_marginEnd="@dimen/_8sdp"
        android:background="@drawable/all_background_circle_ripple"
        app:layout_constraintBottom_toBottomOf="@+id/toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_share" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_table"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_7sdp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/sport_table_team_rank_multiple_group" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="@string/sport_tournament_rank_error"
        android:textColor="@color/app_content_text_color"
        android:textSize="@dimen/_14ssp"
        android:visibility="gone" />

    <include
        android:id="@+id/pb_loading"
        layout="@layout/all_view_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8sdp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>