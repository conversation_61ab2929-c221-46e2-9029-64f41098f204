<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@drawable/account_group_package_user_background"
    android:padding="@dimen/mega_margin_vertical_layout"
    android:layout_height="@dimen/mega_max_height_item_size"
    android:layout_marginTop="@dimen/_5sdp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/BaseTabletTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/tv_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Mua thành công gói MAX" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/BaseTabletTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_3sdp"
        android:textColor="#66FFFFFF"
        android:textSize="@dimen/_6ssp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Giao dịch lúc 19:00 ngày 16/02/2022" />
</androidx.constraintlayout.widget.ConstraintLayout>