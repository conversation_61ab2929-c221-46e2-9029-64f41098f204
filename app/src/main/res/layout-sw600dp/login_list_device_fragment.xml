<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:id="@+id/tvExit"
        android:layout_alignParentEnd="true"
        android:text="@string/login_exit"
        android:textSize="@dimen/_6ssp"
        android:paddingStart="@dimen/_4sdp"
        android:paddingEnd="0dp"
        android:paddingTop="@dimen/_5sdp"
        android:paddingBottom="@dimen/_5sdp"
        android:layout_marginTop="@dimen/_24sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:textColor="@color/white_87"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_centerInParent="true"
        android:paddingStart="@dimen/_10sdp"
        android:paddingEnd="@dimen/_10sdp"
        android:paddingBottom="@dimen/_10sdp"
        android:paddingTop="@dimen/_27sdp"
        android:maxWidth="@dimen/_250sdp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/tvTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:text="@string/login_list_device_title"
            android:textSize="@dimen/_8ssp"
            android:textColor="@color/white_87"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_des_top"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_18sdp"
            android:textSize="@dimen/_6ssp"
            android:textColor="@color/white_60"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <androidx.core.widget.NestedScrollView
            app:layout_constraintTop_toBottomOf="@id/tv_des_top"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/btn_continue"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_10sdp"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="0dp">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvListDevice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tv_des_bottom"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:textSize="@dimen/_6ssp"
                    android:textColor="@color/white_38"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_continue"
            style="@style/LoginButtonTabletV2"
            android:layout_width="0dp"
            android:layout_height="@dimen/_20sdp"
            android:enabled="false"
            android:text="@string/login_continue"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
        <LinearLayout
            android:id="@+id/llAllWhitelist"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@color/black"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_height="0dp">
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_all_whitelist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvTitleAllWhitelist"
                android:textSize="@dimen/_8ssp"
                android:layout_marginTop="@dimen/_13sdp"
                android:textColor="@color/white_87"
                android:textStyle="bold"
                android:textAlignment="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvDesAllWhiteList"
                android:layout_marginTop="@dimen/_10sdp"
                android:textAlignment="center"
                android:layout_marginStart="@dimen/_16sdp"
                android:layout_marginEnd="@dimen/_16sdp"
                android:textSize="@dimen/_6ssp"
                android:textColor="@color/white_60"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnExit"
                style="@style/LoginButtonTabletV2"
                android:layout_marginStart="@dimen/_29sdp"
                android:layout_marginEnd="@dimen/_29sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_20sdp"
                android:enabled="true"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>