<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/back_icon"
        android:background="#121212"
        app:title="@string/buy_package"
        android:contentDescription="@string/talkback_icon_back" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsv_main"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:layout_marginTop="@dimen/_10sdp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_container"
            android:layout_width="@dimen/_236sdp"
            android:layout_gravity="center_horizontal"
            android:layout_height="wrap_content">

            <!--2 main payment packages-->
            <include layout="@layout/package_main_view" />

            <!--Expansion packages-->
            <include layout="@layout/package_expansion_view" />
            <TextView
                android:letterSpacing=".05"
                android:lineSpacingExtra="1dp"
                android:id="@+id/tv_des_1"
                android:layout_width="@dimen/_160sdp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/payment_package_margin"
                android:layout_marginTop="@dimen/_10sdp"
                android:textColor="@color/white_38"
                android:fontFamily="@font/sf_pro_display_light"
                android:textSize="@dimen/_6ssp"
                app:layout_constraintTop_toBottomOf="@id/rv_expansion_pack" />

            <TextView
                android:letterSpacing=".05"
                android:lineSpacingExtra="1dp"
                android:id="@+id/tv_des_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_58sdp"
                android:drawableRight="@drawable/all_app_platform"
                android:drawablePadding="@dimen/_11sdp"
                android:text="Các nền tảng hỗ trợ"
                android:textColor="@color/white_38"
                android:textSize="@dimen/_6ssp"
                android:fontFamily="@font/sf_pro_display_light"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tv_des_1"
                app:layout_constraintTop_toBottomOf="@id/tv_des_1" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>