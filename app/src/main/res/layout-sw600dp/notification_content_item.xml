<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ctl_noti_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/_3sdp"
    android:background="@drawable/notification_background_item"
    android:padding="@dimen/_5sdp">

    <ImageView
        android:id="@+id/iv_noti"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_noti_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_24sdp"
        android:ellipsize="end"
        android:gravity="top"
        android:maxLines="1"
        android:singleLine="true"
        android:textSize="@dimen/_7ssp"
        app:layout_constraintEnd_toStartOf="@id/tv_hour"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="19:30 Viet Nam - Indonesiaaaaaaaaaaaaadddddddddddddd aaaaaaaadddddddddddddaaaaaaaadddddddddddddaaaaaaaaddddddddddddd aaaaaaaa" />

    <TextView
        android:id="@+id/tv_des"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4sdp"
        android:layout_marginEnd="@dimen/_24sdp"
        android:ellipsize="end"
        android:gravity="top"
        android:maxLines="2"
        android:textColor="@color/noti_sub_title"
        android:textSize="@dimen/_6ssp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_noti_title"
        tools:text="Tôi thấy hoa vàng trên cỏ xanh là phim điện ảnh được chuyển thể từ tiểu thuyết cùng chuyển thể từ tiểu thuyết cùng chuyển thể từ tiểu thuyết cùng chuyển thể từ tiểu thuyết cùng " />

    <TextView
        android:id="@+id/tv_hour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/noti_hour"
        android:textSize="@dimen/_5ssp"
        app:layout_constraintBottom_toBottomOf="@id/tv_noti_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_noti_title"
        tools:text="10 phut" />

    <ImageButton
        android:id="@+id/ic_noti_item_more"
        android:layout_width="@dimen/_18sdp"
        android:layout_height="@dimen/_14sdp"
        android:layout_marginTop="@dimen/_4sdp"
        android:background="@drawable/all_background_circle_ripple"
        android:src="@drawable/ic_show_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_noti_title" />

</androidx.constraintlayout.widget.ConstraintLayout>