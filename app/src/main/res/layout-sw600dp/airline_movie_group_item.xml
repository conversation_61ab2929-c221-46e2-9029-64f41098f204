<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_group_title"
        style="@style/AirlineVodStructureGroupTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_7sdp"
        android:paddingHorizontal="@dimen/_7sdp"
        android:paddingBottom="@dimen/_4sdp"
        app:layout_constraintEnd_toStartOf="@+id/tv_view_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_view_more"
        style="@style/AirlineVodStructureGroupViewMore"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center"
        android:paddingTop="@dimen/_7sdp"
        android:paddingHorizontal="@dimen/_7sdp"
        android:paddingBottom="@dimen/_4sdp"
        android:text="@string/view_more"
        app:layout_constraintBottom_toBottomOf="@+id/tv_group_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_group_title" />

    <com.fptplay.mobile.features.mega.apps.airline.util.NestedScrollableHost
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_group_title">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_movie_group"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingStart="@dimen/_7sdp"
            android:paddingEnd="@dimen/_7sdp"/>
    </com.fptplay.mobile.features.mega.apps.airline.util.NestedScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>