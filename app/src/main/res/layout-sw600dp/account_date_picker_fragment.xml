<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/round_behind_account_info">

    <com.shawnlin.numberpicker.NumberPicker
        android:id="@+id/dayPicker"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_117sdp"
        android:layout_gravity="start"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_weight="1"
        android:gravity="center"
        app:layout_constraintEnd_toStartOf="@+id/monthPicker"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:np_dividerColor="@color/transparent"
        app:np_dividerThickness="0.3dp"
        app:np_selectedTextAlign="selectedTextAlignLeft"
        app:np_selectedTextColor="@color/white"
        app:np_selectedTextSize="@dimen/_9sdp"
        app:np_textAlign="textAlignLeft"
        app:np_textColor="@color/app_content_text_disable_color"
        app:np_textSize="@dimen/_7sdp"
        app:np_wheelItemCount="5" />

    <com.shawnlin.numberpicker.NumberPicker
        android:id="@+id/monthPicker"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_117sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_weight="5"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:np_dividerColor="@color/transparent"
        app:np_dividerThickness="0.3dp"
        app:np_selectedTextAlign="selectedTextAlignCenter"
        app:np_selectedTextColor="@color/white"
        app:np_selectedTextSize="@dimen/_9sdp"
        app:np_textAlign="textAlignCenter"
        app:np_textColor="@color/app_content_text_disable_color"
        app:np_textSize="@dimen/_7sdp"
        app:np_wheelItemCount="5" />

    <com.shawnlin.numberpicker.NumberPicker
        android:id="@+id/yearPicker"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_117sdp"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_weight="1"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/monthPicker"
        app:layout_constraintTop_toTopOf="parent"
        app:np_dividerColor="@color/transparent"
        app:np_dividerThickness="0.3dp"
        app:np_selectedTextAlign="selectedTextAlignCenter"
        app:np_selectedTextColor="@color/white"
        app:np_selectedTextSize="@dimen/_9sdp"
        app:np_textAlign="textAlignCenter"
        app:np_textColor="@color/app_content_text_disable_color"
        app:np_textSize="@dimen/_7sdp"
        app:np_wheelItemCount="5" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_date_picker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="dayPicker,monthPicker,yearPicker" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_confirm"
        android:layout_width="@dimen/_187sdp"
        android:layout_height="@dimen/_23sdp"
        android:layout_marginTop="@dimen/_8sdp"
        android:layout_marginBottom="@dimen/_8sdp"
        android:background="@drawable/round_button_account_info"
        android:text="@string/done"
        android:textAllCaps="false"
        android:textColor="@color/app_content_text_color"
        android:textSize="@dimen/_7sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier_date_picker" />
</androidx.constraintlayout.widget.ConstraintLayout>