<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardThumb"
        android:layout_width="@dimen/_74sdp"
        android:layout_height="@dimen/_42sdp"
        app:cardCornerRadius="@dimen/_3sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivThumb"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cardCover"
        android:layout_width="@dimen/_74sdp"
        android:layout_height="@dimen/_42sdp"
        app:cardCornerRadius="@dimen/_3sdp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:cardBackgroundColor="#99000000">

        <ImageView
            android:id="@+id/ivStatus"
            android:layout_width="@dimen/_8sdp"
            android:layout_height="@dimen/_8sdp"
            android:scaleType="centerInside"
            android:layout_gravity="center"
            android:src="@drawable/ic_download"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.cardview.widget.CardView>
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_4sdp"
        android:layout_marginBottom="@dimen/_1sdp"
        android:fontFamily="sans-serif-medium"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="Tập 1"
        android:textColor="#DEFFFFFF"
        android:textSize="@dimen/_6sdp"
        app:layout_constraintEnd_toStartOf="@+id/ivBtnMore"
        app:layout_constraintStart_toEndOf="@id/cardThumb"
        app:layout_constraintTop_toTopOf="@id/cardThumb"
        app:layout_constraintBottom_toTopOf="@+id/tvSubTitle"
        app:layout_constraintVertical_chainStyle="packed"/>

    <TextView
        android:id="@+id/tvSubTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_4sdp"
        android:layout_marginBottom="@dimen/_1sdp"
        android:fontFamily="sans-serif-medium"
        android:textColor="#99FFFFFF"
        android:textSize="@dimen/_6sdp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/ivBtnMore"
        app:layout_constraintStart_toEndOf="@id/cardThumb"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_constraintBottom_toTopOf="@+id/layoutSubTitle"
        tools:text="Subtitle for movie collection" />

    <LinearLayout
        android:id="@+id/layoutSubTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5sdp"
        android:layout_marginBottom="@dimen/_1sdp"
        android:orientation="horizontal"
        app:layout_constraintStart_toEndOf="@id/cardThumb"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
        app:layout_constraintBottom_toTopOf="@+id/tvStatus"
        >

        <TextView
            android:id="@+id/tvDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            tools:text="3 tập"
            android:textColor="#99FFFFFF"
            android:textSize="@dimen/_6sdp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_3sdp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:fontFamily="sans-serif-medium"
            android:text="•"
            android:textAlignment="center"
            android:textColor="#756F6F"
            android:textSize="@dimen/_6sdp" />

        <TextView
            android:id="@+id/tvSize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            tools:text="265MB"
            android:textColor="#99FFFFFF"
            android:textSize="@dimen/_6sdp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_5sdp"
        android:fontFamily="sans-serif"
        android:textColor="@color/app_orange_text_color2"
        android:textSize="@dimen/_5sdp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/cardThumb"
        app:layout_constraintTop_toBottomOf="@id/layoutSubTitle"
        app:layout_constraintBottom_toBottomOf="@id/cardThumb"
        tools:ignore="RtlHardcoded"
        tools:text="Đang tải xuống..."/>

    <ImageView
        android:id="@+id/ivBtnMore"
        android:layout_width="@dimen/_7sdp"
        android:layout_height="@dimen/_7sdp"
        android:src="@drawable/ic_download_more"
        android:layout_marginEnd="@dimen/_2sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>