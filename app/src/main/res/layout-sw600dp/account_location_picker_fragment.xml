<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/round_behind_account_info">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_location"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_14sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginBottom="@dimen/_8sdp"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_confirm"/>

    <include
        android:id="@+id/pb_loading"
        layout="@layout/all_view_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/rv_location"
        app:layout_constraintEnd_toEndOf="@+id/rv_location"
        app:layout_constraintStart_toStartOf="@+id/rv_location"
        app:layout_constraintTop_toTopOf="@+id/rv_location" />

    <Button
        android:id="@+id/btn_confirm"
        android:layout_width="@dimen/_187sdp"
        android:layout_height="@dimen/_23sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginBottom="@dimen/_8sdp"
        android:background="@drawable/round_button_account_info"
        android:text="@string/done"
        android:textAllCaps="false"
        android:textColor="@color/app_content_text_color"
        android:textSize="@dimen/_7sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
