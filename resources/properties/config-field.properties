# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.daemon=true
org.gradle.parallel=true

KSA=fpt telecom
KSAP=123456
KSF=../resources/properties/fplay.keystore
KSP=123456

#Product
ZENDESK_URL=https://hotro.fptplay.vn/
ZENDESK_APP_ID=d7895862a7454a91e6882d2e46c12594dd2ca6d650d6431d
ZENDESK_CLIENT_ID=mobile_sdk_client_9a465e1af04352d55c07
ZENDESK_KEY_VALUE_ENCRYPT=&3RGk45JG#2G3Tp$

#Dev
#ZENDESK_URL_DEV=https://fptplay.zendesk.com
#ZENDESK_APP_ID_DEV=aa4e1dbe35c11f31704a2ae30ad15f2f760b989ebead1182
#ZENDESK_CLIENT_ID_DEV=mobile_sdk_client_d51feb3ce422cf753a65
#ZENDESK_KEY_VALUE_ENCRYPT_DEV=&3RGk45JG#2G3Tp$
